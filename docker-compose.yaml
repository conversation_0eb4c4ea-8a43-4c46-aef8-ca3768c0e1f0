services:
  # ingestion_service:
  #   image: 905418064502.dkr.ecr.ap-south-1.amazonaws.com/ingestion-1:latest
  #   # container_name: ingestion_service
  #   ports:
  #     - "${INGESTION_APP_PORT:-8005}:8000"
  #   depends_on:
  #     ingestion_postgresql:
  #       condition: service_healthy
  #     minio:
  #       condition: service_started

  #   environment:
  #     WEB_ROOT_PATH: ${WEB_ROOT_PATH:-/ingestion-1}
  #     RUN_ALEMBIC: ${LOG_LEVEL:-false}
  #     LOG_LEVEL: ${LOG_LEVEL:-DEBUG}
  #     RUN_MIGRATION=${RUN_MIGRATION:-false}
  #     LOG_DIRNAME: ${LOG_DIRNAME:-.log}
  #     LOG_FILENAME: ${LOG_FILENAME:-log.log}
  #     LOG_DEFAULT_NAME: ${LOG_DEFAULT_NAME:-"Ingestion Log"}
  #     RABBITMQ_USER: ${RABBITMQ_USER:-user}
  #     RABBITMQ_PASSWORD: ${RABBITMQ_PASSWORD:-password}
  #     RABBITMQ_HOST: ${RABBITMQ_HOST:-rabbitmq}
  #     RABBITMQ_PORT: ${RABBITMQ_PORT:-5672}
  #     PROFILER_REQUEST_QUEUE: ${PROFILER_REQUEST_QUEUE:-profiler.request}
  #     PROFILER_RESPONSE_QUEUE: ${PROFILER_RESPONSE_QUEUE:-profiler.ingestion.response}

  #     DB_USER: ${DB_USER:-postgres}
  #     DB_PASSWORD: ${DB_PASSWORD:-password}
  #     DB_HOST: ${DB_HOST:-ingestion_postgresql}
  #     DB_PORT: ${DB_PORT:-5432}
  #     DB_NAME: ${DB_NAME:-ingestion}
  #     DB_POOL_SIZE: ${DB_POOL_SIZE:-10}
  #     DB_MAX_OVERFLOW: ${DB_MAX_OVERFLOW:-5}

  #     MINIO_ACCESS_KEY: ${MINIO_ACCESS_KEY:-minio}
  #     MINIO_SECRET_KEY: ${MINIO_SECRET_KEY:-password}
  #     MINIO_ENDPOINT: ${MINIO_ENDPOINT:-minio:9000}
  #     MINIO_BUCKET_NAME: ${MINIO_BUCKET_NAME:-data-discovery}
  #     MINIO_SECURE: ${MINIO_SECURE:-False}
  #     CONCURRENT_LIMIT: ${CONCURRENT_LIMIT:-50}
  #     WORKFLOW_DELAY: ${WORKFLOW_DELAY:-2}
  #     WAREHOUSE_BASE_URL: ${WAREHOUSE_BASE_URL:-http://warehouse:8000/warehouse}
  #     PROFILER_BASE_URL: ${PROFILER_BASE_URL:-http://profiler:8000/profile}
  #     PROFILE_MAX_SIZE_THRESHOLD: ${PROFILE_MAX_SIZE_THRESHOLD:-1048576}
  #     PROFILE_SUPPORTED_FILE_TYPES: ${PROFILE_SUPPORTED_FILE_TYPES:-txt,csv,json,pdf,xlsx,parquet,sql,diff,pst,docx,docs,unknown}
  #     DIRECTORY_PATH: ${CONTAINER_DIRECTORY_PATH:-/app/dd_test}

  #   volumes:
  #     - ${HOST_DIRECTORY_PATH:-/home/<USER>/dd_test}:${CONTAINER_DIRECTORY_PATH:-/app/dd_test}

  #   restart: unless-stopped
  #   networks:
  #     - dd_unstruct


  warehouse:
    image: 905418064502.dkr.ecr.ap-south-1.amazonaws.com/warehouse-1:latest
    # container_name: warehouse
    ports:
      - "${WAREHOUSE_APP_PORT:-8006}:8000"
    depends_on:
      ingestion_postgresql:
        condition: service_healthy
      minio:
        condition: service_started
      rabbitmq:
        condition: service_started
    restart: unless-stopped
    environment:
      WEB_ROOT_PATH: ${WEB_ROOT_PATH:-/warehouse-1}
      RUN_ALEMBIC: ${LOG_LEVEL:-false}
      LOG_LEVEL: ${LOG_LEVEL:-DEBUG}
      LOG_DIRNAME: ${LOG_DIRNAME:-.log}
      LOG_FILENAME: ${LOG_FILENAME:-log.log}
      LOG_DEFAULT_NAME: ${LOG_DEFAULT_NAME:-"Warehouse Log"}
      DB_USER: ${DB_USER:-postgres}
      DB_PASSWORD: ${DB_PASSWORD:-password}
      DB_HOST: ${DB_HOST:-ingestion_postgresql}
      DB_PORT: ${DB_PORT:-5432}
      DB_NAME: ${DB_NAME:-postgres}
      DB_POOL_SIZE: ${DB_POOL_SIZE:-10}
      DB_MAX_OVERFLOW: ${DB_MAX_OVERFLOW:-5}
      DB_PGRST_READONLY_ROLE: ${DB_PGRST_READONLY_ROLE:-anon}
      RABBITMQ_USER: ${RABBITMQ_USER:-user}
      RABBITMQ_PASSWORD: ${RABBITMQ_PASSWORD:-password}
      RABBITMQ_HOST: ${RABBITMQ_HOST:-rabbitmq}
      RABBITMQ_PORT: ${RABBITMQ_PORT:-5672}
      WAREHOUSE_REQUEST_QUEUE: ${WAREHOUSE_REQUEST_QUEUE:-warehouse.request}
      AUDIT_LOG_QUEUE: ${AUDIT_LOG_QUEUE:-common.audit_log}

      MINIO_ACCESS_KEY: ${MINIO_ACCESS_KEY:-minio}
      MINIO_SECRET_KEY: ${MINIO_SECRET_KEY:-password}
      MINIO_ENDPOINT: ${MINIO_ENDPOINT:-minio:9000}
      MINIO_BUCKET_NAME: ${MINIO_BUCKET_NAME:-data-discovery}
      MINIO_SECURE: ${MINIO_SECURE:-False}

      WORKFLOW_DELAY: ${WORKFLOW_DELAY:-10}
    networks:
      - dd_unstruct

  profiler:
    image: 905418064502.dkr.ecr.ap-south-1.amazonaws.com/profiler-1:latest
    ports:
    - ${PROFILER_APP_PORT:-8007}:8000
    depends_on:
      minio:
        condition: service_started
      rabbitmq:
        condition: service_started
    restart: unless-stopped
    environment:
      WEB_ROOT_PATH: ${WEB_ROOT_PATH:-/profiler-1}
      RUN_ALEMBIC: ${LOG_LEVEL:-false}
      MAIN_WORKER_COUNT: ${MAIN_WORKER_COUNT:-3}
      LOG_LEVEL: ${LOG_LEVEL:-DEBUG}
      LOG_DIRNAME: ${LOG_DIRNAME:-.log}
      LOG_FILENAME: ${LOG_FILENAME:-log.log}
      LOG_DEFAULT_NAME: ${LOG_DEFAULT_NAME:-"Profile Log"}
      MINIO_ACCESS_KEY: ${MINIO_ACCESS_KEY:-minio}
      MINIO_SECRET_KEY: ${MINIO_SECRET_KEY:-password}
      MINIO_ENDPOINT: ${MINIO_ENDPOINT:-minio:9000}
      MINIO_BUCKET_NAME: ${MINIO_BUCKET_NAME:-data-discovery}
      MINIO_SECURE: ${MINIO_SECURE:-False}
      PROFILE_ASYNC_DELAY: ${PROFILE_ASYNC_DELAY:-1}
      PROFILE_CHUNK_SIZE: ${PROFILE_CHUNK_SIZE:-10240}
      PROCESS_FILE_EXPIRY: ${PROCESS_FILE_EXPIRY:-2}
      PROFILE_PARALLEL_WORKERS: ${PROFILE_PARALLEL_WORKERS:-5}
      RABBITMQ_USER: ${RABBITMQ_USER:-user}
      RABBITMQ_PASSWORD: ${RABBITMQ_PASSWORD:-password}
      RABBITMQ_HOST: ${RABBITMQ_HOST:-rabbitmq}
      RABBITMQ_PORT: ${RABBITMQ_PORT:-5672}
      OCR_REQUEST_QUEUE: ${OCR_REQUEST_QUEUE:-ocr.request}
      OCR_RESPONSE_QUEUE: ${OCR_RESPONSE_QUEUE:-ocr.profiler.response}
      PROFILER_REQUEST_QUEUE: ${PROFILER_REQUEST_QUEUE:-profiler.request}
      SLOW_FILE_QUEUE: ${SLOW_FILE_QUEUE:-profiler.slow.request}
      AUDIT_LOG_QUEUE: ${AUDIT_LOG_QUEUE:-common.audit_log}
      WORKFLOW_DELAY: ${WORKFLOW_DELAY:-10}
      PROFILE_MAX_SIZE_THRESHOLD: ${PROFILE_MAX_SIZE_THRESHOLD:-1048576}
      PROFILE_SUPPORTED_FILE_TYPES: ${PROFILE_SUPPORTED_FILE_TYPES:-txt,csv,json,pdf,xlsx,parquet,sql,diff,pst,docx,docs,unknown}
    networks:
    - dd_unstruct
  ingestion_postgresql:
    image: postgres:16
    container_name: ingestion_postgresql
    restart: unless-stopped
    command: --work_mem=10MB
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-password}
      POSTGRES_DB: ${POSTGRES_DB:-ingestion}
    expose:
    - 5433
    ports:
    - 5433:5432
    volumes:
    - db-data-postgres:/var/lib/postgresql/data
    networks:
    - dd_unstruct
    healthcheck:
      test:
      - CMD-SHELL
      - pg_isready -U postgres -d ingestion
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 5s
  minio:
    image: minio/minio
    ports:
    - 9008:9000
    - 9007:9001
    environment:
    - MINIO_ROOT_USER=${MINIO_ROOT_USER:-minio}
    - MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD:-password}
    - MINIO_SERVER_URL=${MINIO_SERVER_URL:-http://minio:9000}
    volumes:
    - minio_data:/data
    command: server --console-address ":9001" /data
    healthcheck:
      test:
      - CMD
      - curl
      - -f
      - http://minio:9000/minio/health/live
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
    - dd_unstruct
  ingestion_grafana:
    image: 905418064502.dkr.ecr.ap-south-1.amazonaws.com/monitor-grafana:latest
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin123
      GF_SERVER_SERVE_FROM_SUB_PATH: 'true'
    ports:
    - 3006:3000
    volumes:
    - grafana-data:/var/lib/grafana
    restart: unless-stopped
    networks:
    - dd_unstruct
  warehouse_postgrest:
    image: 905418064502.dkr.ecr.ap-south-1.amazonaws.com/warehouse-postgrest:latest
    ports:
    - 3000:3000
    environment:
      PGRST_DB_URI: ******************************************************/postgres
      PGRST_OPENAPI_SERVER_PROXY_URI: http://127.0.0.1:3000
      PGRST_DB_SCHEMA: public
      PGRST_DB_ANON_ROLE: anon
    depends_on:
    - ingestion_postgresql
    networks:
    - dd_unstruct
  rabbitmq:
    image: 905418064502.dkr.ecr.ap-south-1.amazonaws.com/rabbitmq:3.13.7-management
    ports:
    - 5672:5672
    - 15672:15672
    environment:
      RABBITMQ_DEFAULT_USER: user
      RABBITMQ_DEFAULT_PASS: password
    volumes:
    - rabbitmq_data:/var/lib/rabbitmq
    restart: unless-stopped
    networks:
    - dd_unstruct
  cassandra:
    image: cassandra:5.0
    container_name: cassandra
    ports:
      - "9042:9042"
    environment:
      - CASSANDRA_CLUSTER_NAME=test_cluster
      - CASSANDRA_PASSWORD_SEEDER=yes
      # ENABLE AUTH
      - CASSANDRA_AUTHENTICATOR=PasswordAuthenticator
      - CASSANDRA_AUTHORIZER=CassandraAuthorizer
      - CASSANDRA_ROLE_MANAGER=CassandraRoleManager
    volumes:
      - cassandra_data:/var/lib/cassandra
    restart: unless-stopped
    networks:
      - dd_unstruct
    healthcheck:
      test: ["CMD", "cqlsh", "-u", "cassandra", "-p", "cassandra", "-e", "describe keyspaces"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
volumes:
  minio_data:
  db-data-postgres:
  grafana-data:
  rabbitmq_data:
  cassandra_data:
networks:
  dd_unstruct:
    driver: bridge
