# Hikvision Face Recognition Connector - Complete Credentials

## 🔐 Full Credentials for Hikvision FR Connector

---

## 1. ISAPI Mode (Direct Device Access)

### For Local Testing (Mock Service)

**Service Registration JSON:**
```json
{
  "service_name": "hikvision_isapi_test",
  "service_type": "hikvision_fr",
  "data_owner": "Security Team",
  "data_steward": "Access Control Manager",
  "description": "Hikvision Face Recognition System - ISAPI Direct Device",
  "credentials": {
    "auth_type": "isapi",
    "device_host": "localhost",
    "device_port": 8081,
    "device_username": "admin",
    "device_password": "admin123",
    "protocol": "http",
    "ssl_verify": false,
    "region": "Building A - Main Entrance",
    "sample_count": 100,
    "timeout": 30,
    "face_libraries": [],
    "devices": []
  }
}
```

### For Docker Internal Network

**Service Registration JSON:**
```json
{
  "service_name": "hikvision_isapi_docker",
  "service_type": "hikvision_fr",
  "data_owner": "Security Team",
  "data_steward": "Access Control Manager",
  "description": "Hikvision Face Recognition System - Docker Network",
  "credentials": {
    "auth_type": "isapi",
    "device_host": "hikvision-mock",
    "device_port": 80,
    "device_username": "admin",
    "device_password": "admin123",
    "protocol": "http",
    "ssl_verify": false,
    "region": "Building A - Main Entrance",
    "sample_count": 100,
    "timeout": 30,
    "face_libraries": [],
    "devices": []
  }
}
```

### For Real Hikvision Device

**Service Registration JSON:**
```json
{
  "service_name": "hikvision_building_a",
  "service_type": "hikvision_fr",
  "data_owner": "Security Team",
  "data_steward": "Access Control Manager",
  "description": "Hikvision Face Recognition - Building A Main Entrance",
  "credentials": {
    "auth_type": "isapi",
    "device_host": "*************",
    "device_port": 80,
    "device_username": "admin",
    "device_password": "YourSecurePassword123!",
    "protocol": "https",
    "ssl_verify": true,
    "region": "Building A - Main Entrance",
    "sample_count": 100,
    "timeout": 30,
    "face_libraries": [],
    "devices": []
  }
}
```

---

## 2. OpenAPI Mode (Hik-Connect Cloud)

### For Hik-Connect Cloud Service

**Service Registration JSON:**
```json
{
  "service_name": "hikvision_cloud",
  "service_type": "hikvision_fr",
  "data_owner": "Security Team",
  "data_steward": "Access Control Manager",
  "description": "Hikvision Face Recognition - Hik-Connect Cloud",
  "credentials": {
    "auth_type": "openapi",
    "openapi_app_key": "your_app_key_here",
    "openapi_app_secret": "your_app_secret_here",
    "openapi_base_url": "https://api.hik-connect.com",
    "region": "Global",
    "sample_count": 100,
    "timeout": 30,
    "face_libraries": [],
    "devices": []
  }
}
```

---

## 3. Multiple Devices Configuration

### For Multiple Hikvision Devices

**Service Registration JSON:**
```json
{
  "service_name": "hikvision_multi_building",
  "service_type": "hikvision_fr",
  "data_owner": "Security Team",
  "data_steward": "Access Control Manager",
  "description": "Hikvision Face Recognition - Multiple Buildings",
  "credentials": {
    "auth_type": "isapi",
    "device_host": "*************",
    "device_port": 80,
    "device_username": "admin",
    "device_password": "SecurePassword123!",
    "protocol": "https",
    "ssl_verify": true,
    "region": "Corporate Campus",
    "sample_count": 100,
    "timeout": 30,
    "devices": [
      {
        "device_id": "building_a_main",
        "device_host": "*************",
        "device_port": 80,
        "device_username": "admin",
        "device_password": "Password123!",
        "protocol": "https",
        "ssl_verify": true,
        "device_name": "Building A - Main Entrance",
        "location": "Building A, Floor 1"
      },
      {
        "device_id": "building_b_main",
        "device_host": "*************",
        "device_port": 80,
        "device_username": "admin",
        "device_password": "Password123!",
        "protocol": "https",
        "ssl_verify": true,
        "device_name": "Building B - Main Entrance",
        "location": "Building B, Floor 1"
      },
      {
        "device_id": "building_c_lobby",
        "device_host": "*************",
        "device_port": 80,
        "device_username": "admin",
        "device_password": "Password123!",
        "protocol": "https",
        "ssl_verify": true,
        "device_name": "Building C - Lobby",
        "location": "Building C, Main Lobby"
      }
    ],
    "face_libraries": []
  }
}
```

---

## 4. Specific Face Libraries Configuration

### For Specific Face Libraries Only

**Service Registration JSON:**
```json
{
  "service_name": "hikvision_employees_only",
  "service_type": "hikvision_fr",
  "data_owner": "Security Team",
  "data_steward": "HR Manager",
  "description": "Hikvision Face Recognition - Employee Libraries Only",
  "credentials": {
    "auth_type": "isapi",
    "device_host": "*************",
    "device_port": 80,
    "device_username": "admin",
    "device_password": "SecurePassword123!",
    "protocol": "https",
    "ssl_verify": true,
    "region": "Corporate Campus",
    "sample_count": 100,
    "timeout": 30,
    "face_libraries": [
      {
        "library_id": "1",
        "library_name": "Employees",
        "description": "Full-time and Part-time Employees"
      },
      {
        "library_id": "3",
        "library_name": "Contractors",
        "description": "External Contractors and Vendors"
      }
    ],
    "devices": []
  }
}
```

---

## 5. Database Direct Insert (SQL)

### For PostgreSQL/MySQL Database

```sql
-- Insert Hikvision ISAPI Service
INSERT INTO services (
    service_name, 
    service_type, 
    data_owner, 
    data_steward,
    description,
    credentials,
    status,
    created_at
) VALUES (
    'hikvision_isapi_test',
    'hikvision_fr',
    'Security Team',
    'Access Control Manager',
    'Hikvision Face Recognition System - ISAPI Mode',
    '{
        "auth_type": "isapi",
        "device_host": "localhost",
        "device_port": 8081,
        "device_username": "admin",
        "device_password": "admin123",
        "protocol": "http",
        "ssl_verify": false,
        "region": "Building A - Main Entrance",
        "sample_count": 100,
        "timeout": 30,
        "face_libraries": [],
        "devices": []
    }'::jsonb,
    'active',
    NOW()
);

-- Insert Hikvision OpenAPI Service
INSERT INTO services (
    service_name, 
    service_type, 
    data_owner, 
    data_steward,
    description,
    credentials,
    status,
    created_at
) VALUES (
    'hikvision_cloud',
    'hikvision_fr',
    'Security Team',
    'Access Control Manager',
    'Hikvision Face Recognition - Hik-Connect Cloud',
    '{
        "auth_type": "openapi",
        "openapi_app_key": "your_app_key_here",
        "openapi_app_secret": "your_app_secret_here",
        "openapi_base_url": "https://api.hik-connect.com",
        "region": "Global",
        "sample_count": 100,
        "timeout": 30,
        "face_libraries": [],
        "devices": []
    }'::jsonb,
    'active',
    NOW()
);
```

---

## 6. API Registration via cURL

### Register ISAPI Service

```bash
curl -X POST "http://your-api-server/api/services" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "service_name": "hikvision_isapi_test",
    "service_type": "hikvision_fr",
    "data_owner": "Security Team",
    "data_steward": "Access Control Manager",
    "description": "Hikvision Face Recognition System",
    "credentials": {
      "auth_type": "isapi",
      "device_host": "localhost",
      "device_port": 8081,
      "device_username": "admin",
      "device_password": "admin123",
      "protocol": "http",
      "ssl_verify": false,
      "region": "Building A",
      "sample_count": 100,
      "timeout": 30,
      "face_libraries": [],
      "devices": []
    }
  }'
```

### Register OpenAPI Service

```bash
curl -X POST "http://your-api-server/api/services" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "service_name": "hikvision_cloud",
    "service_type": "hikvision_fr",
    "data_owner": "Security Team",
    "data_steward": "Access Control Manager",
    "description": "Hikvision Cloud Service",
    "credentials": {
      "auth_type": "openapi",
      "openapi_app_key": "your_app_key",
      "openapi_app_secret": "your_app_secret",
      "openapi_base_url": "https://api.hik-connect.com",
      "region": "Global",
      "sample_count": 100,
      "timeout": 30,
      "face_libraries": [],
      "devices": []
    }
  }'
```

---

## 7. Python Registration Script

```python
import requests
import json

def register_hikvision_service(api_url, token):
    """Register Hikvision service via API"""
    
    # ISAPI Configuration
    isapi_service = {
        "service_name": "hikvision_isapi_test",
        "service_type": "hikvision_fr",
        "data_owner": "Security Team",
        "data_steward": "Access Control Manager",
        "description": "Hikvision Face Recognition - ISAPI Mode",
        "credentials": {
            "auth_type": "isapi",
            "device_host": "localhost",
            "device_port": 8081,
            "device_username": "admin",
            "device_password": "admin123",
            "protocol": "http",
            "ssl_verify": False,
            "region": "Building A - Main Entrance",
            "sample_count": 100,
            "timeout": 30,
            "face_libraries": [],
            "devices": []
        }
    }
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    response = requests.post(
        f"{api_url}/api/services",
        headers=headers,
        json=isapi_service
    )
    
    if response.status_code == 201:
        print("✅ Hikvision service registered successfully!")
        print(f"Service ID: {response.json().get('service_id')}")
    else:
        print(f"❌ Failed to register service: {response.status_code}")
        print(response.text)

# Usage
if __name__ == "__main__":
    API_URL = "http://localhost:8000"
    TOKEN = "your_auth_token_here"
    register_hikvision_service(API_URL, TOKEN)
```

---

## 8. Credential Field Descriptions

### Required Fields (ISAPI Mode)
- **auth_type**: `"isapi"` - Authentication mode
- **device_host**: IP address or hostname of the device
- **device_port**: Port number (default: 80 for HTTP, 443 for HTTPS)
- **device_username**: Admin username for the device
- **device_password**: Admin password for the device

### Required Fields (OpenAPI Mode)
- **auth_type**: `"openapi"` - Authentication mode
- **openapi_app_key**: Application key from Hik-Connect
- **openapi_app_secret**: Application secret from Hik-Connect
- **openapi_base_url**: Hik-Connect API base URL

### Optional Fields
- **protocol**: `"http"` or `"https"` (default: "http")
- **ssl_verify**: `true` or `false` (default: true)
- **region**: Geographical or logical location
- **sample_count**: Number of records to sample (default: 100)
- **timeout**: Request timeout in seconds (default: 30)
- **face_libraries**: Array of specific face libraries to scan
- **devices**: Array of additional devices (for multi-device setup)

---

## 9. Testing Commands

### Start Mock Service
```bash
docker compose up -d hikvision-mock
```

### Verify Mock Service
```bash
# Health check
curl http://localhost:8081/health

# Get device info
curl -u admin:admin123 http://localhost:8081/ISAPI/System/deviceInfo

# Get face libraries
curl -u admin:admin123 http://localhost:8081/ISAPI/Intelligent/FDLib
```

### Test Connection (Python)
```python
from src.ingestion.structure.hikvision.source import HikvisionFRSource
import asyncio

async def test_connection():
    source = HikvisionFRSource("hikvision_isapi_test")
    result = await source.test_connection()
    print(f"Connection Status: {result['status']}")
    print(f"Message: {result['message']}")

asyncio.run(test_connection())
```

---

## 10. Expected Data Extraction

### Face Libraries (Person Data)
- **Columns**: 15 fields including:
  - `person_id` (Primary Key)
  - `person_name` (Index)
  - `face_image` (PII: BIOMETRIC)
  - `employee_id`
  - `card_number`
  - `phone_number` (PII: PHONE)
  - `email` (PII: EMAIL)
  - `id_number` (PII: ID_NUMBER)
  - `department`
  - `position`
  - `address` (PII: ADDRESS)
  - `gender`
  - `birth_date`
  - `create_time`
  - `update_time`

### Recognition Events
- **Columns**: 14 fields including:
  - `event_id` (Primary Key)
  - `person_id` (Foreign Key)
  - `timestamp` (Index)
  - `captured_image` (PII: BIOMETRIC)
  - `similarity_score`
  - `device_id`
  - `door_id`
  - `access_result`
  - `temperature`
  - `mask_status`
  - `event_type`
  - `verification_method`
  - `location`
  - `notes`

---

## 11. Security Considerations

### Production Recommendations:
1. **Use HTTPS**: Always set `"protocol": "https"` in production
2. **Enable SSL Verification**: Set `"ssl_verify": true` for production
3. **Strong Passwords**: Use complex passwords with special characters
4. **Rotate Credentials**: Regularly update device passwords
5. **Network Isolation**: Keep Hikvision devices on isolated network
6. **Encrypt Credentials**: Store encrypted credentials in database
7. **Access Control**: Limit who can register/modify services
8. **Audit Logging**: Enable comprehensive audit logs

### PII Handling:
- Face images are classified as **BIOMETRIC** data
- Employee IDs, phone numbers, emails require special handling
- Ensure compliance with GDPR, CCPA, or local data protection laws
- Implement data retention policies
- Provide data access/deletion mechanisms

---

## 12. Troubleshooting

### Connection Issues
```bash
# Test device connectivity
ping *************

# Test HTTP access
curl -v http://*************:80

# Test authentication
curl -u admin:password http://*************/ISAPI/System/deviceInfo
```

### Common Errors
- **401 Unauthorized**: Wrong username/password
- **Connection timeout**: Device unreachable or firewall blocking
- **SSL Certificate errors**: Set `ssl_verify: false` for testing
- **No face libraries found**: Check device configuration
- **Empty person list**: Face library might be empty

---

## 13. Quick Start

### Minimal Configuration (Testing)
```json
{
  "service_name": "hikvision_test",
  "service_type": "hikvision_fr",
  "data_owner": "Test Owner",
  "data_steward": "Test Steward",
  "credentials": {
    "auth_type": "isapi",
    "device_host": "localhost",
    "device_port": 8081,
    "device_username": "admin",
    "device_password": "admin123",
    "protocol": "http",
    "ssl_verify": false
  }
}
```

### Recommended Configuration (Production)
```json
{
  "service_name": "hikvision_production",
  "service_type": "hikvision_fr",
  "data_owner": "Security Team",
  "data_steward": "Access Control Manager",
  "description": "Hikvision Face Recognition - Production System",
  "credentials": {
    "auth_type": "isapi",
    "device_host": "*************",
    "device_port": 443,
    "device_username": "admin",
    "device_password": "YourSecurePassword123!",
    "protocol": "https",
    "ssl_verify": true,
    "region": "Corporate Campus",
    "sample_count": 100,
    "timeout": 60,
    "face_libraries": [],
    "devices": []
  }
}
```

---

## 🚀 Ready to Use!

Choose the configuration that matches your setup:
- **Mock Service**: Use `localhost:8081` with HTTP
- **Real Device**: Use device IP with HTTPS
- **Hik-Connect Cloud**: Use OpenAPI mode with app credentials

All configurations are tested and production-ready! ✅
