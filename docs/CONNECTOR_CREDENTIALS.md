# SAP ERP & Hikvision Connectors - Credential Requirements

## 🔧 SAP ERP Connector (PyOData)

### Service Type: `sap_erp`

### Required Fields ✅
| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `base_url` | string | SAP OData service base URL | `https://sap-server.company.com:8000/sap/opu/odata/sap` |
| **Authentication** | | **Must provide ONE of the following:** | |
| `user` + `password` | string | SAP username and password | `user: "ODATA_USER"`, `password: "SecurePass123"` |
| **OR** | | | |
| `cert_path` + `key_path` | string | Certificate file paths for cert auth | `cert_path: "/path/client.crt"`, `key_path: "/path/client.key"` |

### Optional Fields 🔵
| Field | Type | Default | Description | Example |
|-------|------|---------|-------------|---------|
| `client` | string | `"800"` | SAP client number | `"100"`, `"200"`, `"800"` |
| `language` | string | `"EN"` | SAP language code | `"EN"`, `"DE"`, `"FR"` |
| `service_path` | string | `""` | Specific OData service path | `"API_BUSINESS_PARTNER"` |
| `verify_ssl` | boolean | `true` | SSL certificate verification | `true`, `false` |
| `sample_count` | integer | `10` | Number of sample records to extract | `5`, `10`, `50` |
| `timeout` | integer | `30` | Request timeout in seconds | `15`, `30`, `60` |
| `entity_sets` | array | `[]` | Specific entity sets to scan | `["A_BusinessPartner", "A_Product"]` |

### Example Credential Object:
```json
{
  "service_name": "sap_production",
  "description": "Production SAP ERP System",
  "service_type": "sap_erp",
  "credentials": {
    "base_url": "https://sap-prod.company.com:44300/sap/opu/odata/sap",
    "user": "ODATA_USER",
    "password": "SecurePassword123",
    "client": "100",
    "language": "EN",
    "verify_ssl": true,
    "sample_count": 20,
    "entity_sets": [
      "API_BUSINESS_PARTNER/A_BusinessPartner",
      "API_MATERIAL_SRV/A_Product"
    ]
  }
}
```

---

## 📹 Hikvision Face Recognition Connector

### Service Type: `hikvision_fr`

The Hikvision connector supports two authentication methods. The `auth_type` field determines which credentials are required.

---

### 🔌 ISAPI Mode (Direct Device Connection)

#### Required Fields ✅
| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `auth_type` | string | Must be `"isapi"` | `"isapi"` |
| `device_host` | string | IP address or hostname of Hikvision device | `"*************"`, `"hikvision-device.local"` |
| `device_username` | string | Device login username | `"admin"` |
| `device_password` | string | Device login password | `"admin123456"` |

#### Optional Fields 🔵
| Field | Type | Default | Description | Example |
|-------|------|---------|-------------|---------|
| `device_port` | integer | `80` | Device HTTP/HTTPS port | `80`, `443`, `8000` |
| `protocol` | string | `"http"` | Connection protocol | `"http"`, `"https"` |
| `ssl_verify` | boolean | `false` | SSL certificate verification | `true`, `false` |
| `region` | string | `"Unknown"` | Device location/region | `"Main Office"`, `"Building A"` |
| `sample_count` | integer | `100` | Number of sample records | `50`, `100`, `200` |
| `timeout` | integer | `30` | Request timeout in seconds | `15`, `30`, `45` |
| `face_libraries` | array | `[]` | Specific face libraries to scan | `["MainLibrary", "VIPLibrary"]` |
| `devices` | array | `[]` | Device filter (not used in ISAPI) | `[]` |

#### ISAPI Example:
```json
{
  "service_name": "hikvision_main_entrance",
  "description": "Main Entrance Face Recognition Terminal",
  "service_type": "hikvision_fr",
  "credentials": {
    "auth_type": "isapi",
    "device_host": "*************",
    "device_username": "admin",
    "device_password": "admin123456",
    "device_port": 80,
    "protocol": "http",
    "ssl_verify": false,
    "region": "Main Office",
    "sample_count": 100
  }
}
```

---

### ☁️ OpenAPI Mode (Hik-Connect Cloud)

#### Required Fields ✅
| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `auth_type` | string | Must be `"openapi"` | `"openapi"` |
| `base_url` | string | Hikvision OpenAPI endpoint | `"https://openapi.hik-connect.com"` |
| `api_key` | string | API key from Hikvision Developer Portal | `"your-api-key-123"` |
| `api_secret` | string | API secret from Hikvision Developer Portal | `"your-api-secret-456"` |

#### Optional Fields 🔵
| Field | Type | Default | Description | Example |
|-------|------|---------|-------------|---------|
| `ssl_verify` | boolean | `true` | SSL certificate verification | `true`, `false` |
| `region` | string | `"Unknown"` | Service region | `"US-East"`, `"Europe"`, `"Asia"` |
| `sample_count` | integer | `100` | Number of sample records | `50`, `100`, `200` |
| `timeout` | integer | `30` | Request timeout in seconds | `15`, `30`, `45` |
| `face_libraries` | array | `[]` | Specific face libraries to scan | `["Library1", "Library2"]` |
| `devices` | array | `[]` | Specific device IDs to scan | `["device-id-1", "device-id-2"]` |

#### Valid OpenAPI Base URLs:
- **Global/Europe**: `https://openapi.hik-connect.com`
- **United States**: `https://openapi.hikvision-us.com`
- **China**: `https://open.ys7.com`

#### OpenAPI Example:
```json
{
  "service_name": "hikvision_cloud_system",
  "description": "Cloud-based Hikvision Face Recognition",
  "service_type": "hikvision_fr",
  "credentials": {
    "auth_type": "openapi",
    "base_url": "https://openapi.hik-connect.com",
    "api_key": "your-api-key-from-developer-portal",
    "api_secret": "your-api-secret-from-developer-portal",
    "ssl_verify": true,
    "region": "Europe",
    "sample_count": 50,
    "devices": ["device-12345", "device-67890"]
  }
}
```

---

## 🚨 Important Notes for Frontend Team

### SAP ERP:
1. **Authentication**: Must provide either `user`+`password` OR `cert_path`+`key_path`
2. **URL Format**: `base_url` must start with `http://` or `https://`
3. **Client**: Must be a valid number (e.g., "100", "800")
4. **Testing**: Use SAP ES5 demo system for testing: `https://sapes5.sapdevcenter.com/sap/opu/odata/sap`

### Hikvision:
1. **Auth Type**: Must choose either `"isapi"` or `"openapi"` - this determines which other fields are required
2. **ISAPI**: For direct device connections (local network)
3. **OpenAPI**: For cloud-based Hik-Connect integration (requires developer account)
4. **Port Range**: `device_port` must be 1-65535 if specified
5. **URL Format**: `base_url` (OpenAPI) must start with `http://` or `https://`

### Validation Rules:
- Missing required fields will return error: `"Missing required credentials: field1, field2"`
- Invalid formats will return specific error messages
- Empty strings count as missing fields
- All fields are case-sensitive

---

## 🧪 Testing Credentials

### SAP ERP Test (Public Demo):
```json
{
  "credentials": {
    "base_url": "https://sapes5.sapdevcenter.com/sap/opu/odata/sap",
    "user": "ES5USER", 
    "password": "your-es5-password",
    "client": "002"
  }
}
```

### Hikvision ISAPI Test:
```json
{
  "credentials": {
    "auth_type": "isapi",
    "device_host": "demo.hikvision.com",
    "device_username": "admin",
    "device_password": "12345"
  }
}
```

*Note: Replace with actual device credentials for real testing*
