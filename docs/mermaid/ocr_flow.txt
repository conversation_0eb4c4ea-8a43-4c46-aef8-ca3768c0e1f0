flowchart TD
    subgraph OCR_Service
        direction TB

        %% Entry point
        A["📥 Main Processor<br/>Reads: ocr.requests queue"] --> B["⚙️ OCR Engine<br/>Extract text from image/PDF page"]

        %% Processing path
        B --> C["📤 Publish → ocr.response queue"]
    end

    %% MongoDB + Audit Log
    subgraph Storage_and_Logs
        M["🗄️ MongoDB<br/>Stores OCR extracted text"]
        L["📝 Audit Log<br/>Stores OCR processing status"]
    end

    %% After results (OCR always writes to DB + Audit)
    B --> M
    B --> L

    %% Notes
    classDef note fill:#f9f9f9,stroke:#555,stroke-width:1px,color:#111,border-radius:8px

    N1["📌 OCR Request:<br/>external_id: 46427492424248290482<br/>image_id: 284280284242042<br/>input_type: file<br/>input_file: data.png<br/>embeded_image: true"]:::note

    N2["📌 OCR Response:<br/>external_id: 46427492424248290482<br/>image_id: 284280284242042<br/>input_type: file<br/>input_file: data.png<br/>embeded_image: true<br/>extracted_text: gdgdkgdgjdklg jfiwjgosjgogoe"]:::note

    A -.-> N1
    C -.-> N2
