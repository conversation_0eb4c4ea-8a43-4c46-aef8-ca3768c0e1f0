---
config:
  layout: dagre
---
flowchart TD
 subgraph Ingestion_API["🌐 Ingestion Service (REST API)"]
    direction TB
        B["🛠️ Create Ingestion Job<br>with service provider credentials"]
        A["📥 User/API Client<br>POST /ingestion/jobs"]
        C["🔗 Connect to Service Provider<br>(S3, OneDrive, PostgreSQL, MySQL, ...)"]
        D["📂 Extract files from source"]
        E{"File already processed?"}
        F["⏭️ Skip file (No new processing)"]
        G["📝 New file to process"]
        H["📤 Publish file metadata → warehouse.loader.requests queue"]
        I["📝 Audit Log: File metadata sent to warehouse"]
        J["📤 Publish file → profiler.requests queue"]
        K["📝 Audit Log: File sent to profiler"]
        L["📩 Profiler publishes results → profiler.response queue"]
        M["📤 Profiler results consumed by ingestion → attach PII info"]
        N["📝 Audit Log: Profiler results attached"]
        O["📤 Send job update → ingestion.response queue (if reply_to present)"]
  end
 subgraph Databases["Databases"]
        P["🗄️ Warehouse DB<br>Stores file metadata + profiling results"]
        Q["🗄️ Ingestion DB<br>Stores ingestion jobs + file references"]
  end
 subgraph Audit_Databases["Audit_Databases"]
        R["📝 Ingestion Audit DB<br>Logs only file-level events:<br>- Sent to warehouse<br>- Sent to profiler<br>- Profiler results attached"]
        S["📝 Warehouse Audit DB<br>Logs loader processing,<br>queue consumption,<br>DB updates"]
  end
    A --> B
    B --> C & Q
    C --> D
    D --> E & Q
    E -- ✅ Yes --> F
    E -- ❌ No --> G
    G --> H & J & Q
    H --> I & P & S
    J --> K
    K --> L & R
    L --> M
    M --> N & O & P & S
    I --> R
    N --> R
    A -.-> N1@{ label: "📌 Ingestion Job Request (API):<br>POST /ingestion/jobs<br>{ job_id: 12345,<br>service_provider: 's3',<br>bucket: 'customer-data',<br>reply_to: 'ingestion.response' }" }
    K -.-> N2@{ label: "📌 Ingestion Audit Log Entry:<br>{ job_id: 12345,<br>file: 'customer1.pdf',<br>status: 'sent_to_profiler' }" }
    O -.-> N3@{ label: "📌 Final Ingestion Response:<br>{ job_id: 12345,<br>file: 'customer1.pdf',<br>status: 'processed',<br>metadata: {...},<br>pii: {...} }" }
    N1@{ shape: rect}
    N2@{ shape: rect}
    N3@{ shape: rect}
     N1:::note
     N2:::note
     N3:::note
    classDef note fill:#f9f9f9,stroke:#555,stroke-width:1px,color:#111,border-radius:8px
