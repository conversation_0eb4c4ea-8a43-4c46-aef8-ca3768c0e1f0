flowchart TD
    subgraph Warehouse_API["🌐 Warehouse Service (REST API)"]
        direction TB

        %% A["📥 User/API Client<br/>POST /warehouse/load"] --> B["🛠️ Create Warehouse Load Job"]

        %% Loader picks from queue
        C["📩 Warehouse Loader Processor<br/>listens to warehouse.loader.requests queue"]

        %% File metadata ingestion
        C --> D["📂 Insert/Update File Metadata in Warehouse DB"]
        D --> E["📝 Audit Log: Metadata saved"]

        %% Profiler results ingestion
        C --> F["📂 Insert/Update Profiler Results in Warehouse DB"]
        F --> G["📝 Audit Log: Profiler results saved"]

        %% Final job status
        G --> H["📤 Update Job Status (success/failure)"]
        H --> I["📝 Audit Log: Job completed"]
    end

    %% Databases
    subgraph Databases
        J["🗄️ Warehouse DB<br/>Stores file metadata + profiler results"]
    end

    %% Audit DB
    subgraph Audit_Databases
        K["📝 Warehouse Audit DB<br/>Logs only major events:<br/>- Metadata saved<br/>- Profiler results saved<br/>- Job completed"]
    end

    %% Links
    D --> J
    F --> J

    E --> K
    G --> K
    I --> K

    %% Notes
    classDef note fill:#f9f9f9,stroke:#555,stroke-width:1px,color:#111,border-radius:8px

    %% N1["📌 Warehouse Load Request (API):<br/>POST /warehouse/load<br/>{ job_id: 67890,<br/>files: ['customer1.pdf', 'customer2.pdf'] }"]:::note

    N2["📌 Warehouse Audit Log Entry:<br/>{ job_id: 67890,<br/>file: 'customer1.pdf',<br/>status: 'metadata_saved' }"]:::note

    N3["📌 Final Warehouse Response:<br/>{ job_id: 67890,<br/>status: 'completed',<br/>files_loaded: 2 }"]:::note

    %% A -.-> N1
    D -.-> N2
    H -.-> N3
