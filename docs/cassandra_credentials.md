## Cassandra Service Credentials - Quick Reference

### 🔑 For Local Docker Container (from Host Machine)

```json
{
    "service_name": "cassandra_local",
    "description": "Local Cassandra test cluster",
    "service_type": "cassandra",
    "credentials": {
        "auth_type": "basic",
        "host": "localhost",
        "port": 9042,
        "user": "cassandra",
        "password": "cassandra",
        "keyspace": null,
        "sample_count": 10,
        "keyspaces": [],
        "region": "local",
        "ssl_enabled": false,
        "ca_certificate": null
    },
    "data_owner": "admin",
    "data_steward": "data_team"
}
```

### 🐳 For Docker Network (from other containers)

```json
{
    "service_name": "cassandra_docker",
    "description": "Cassandra via Docker network",
    "service_type": "cassandra",
    "credentials": {
        "auth_type": "basic",
        "host": "cassandra",
        "port": 9042,
        "user": "cassandra",
        "password": "cassandra",
        "keyspace": null,
        "sample_count": 10,
        "keyspaces": [],
        "region": "local",
        "ssl_enabled": false,
        "ca_certificate": null
    },
    "data_owner": "admin",
    "data_steward": "data_team"
}
```

---

## 📝 Configuration Notes

- **host**: Use `"localhost"` from host machine, `"cassandra"` from Docker network
- **port**: `9042` is the CQL native protocol port
- **user/password**: Default Cassandra credentials
- **keyspace**: Leave `null` to scan all keyspaces, or specify a specific keyspace
- **keyspaces**: Empty array `[]` scans all, or provide list like `["test_data", "prod_data"]`
- **sample_count**: Number of rows to sample per table (default: 10)

---

## 🧪 Quick Test Commands

### Start Cassandra
```bash
cd /home/<USER>/unstructured-Ingestion
docker-compose up -d cassandra
```

### Check Status
```bash
docker ps | grep cassandra
docker logs cassandra
```

### Connect via cqlsh
```bash
docker exec -it cassandra cqlsh -u cassandra -p cassandra
```

### Create Test Data
```sql
CREATE KEYSPACE IF NOT EXISTS test_data 
WITH replication = {'class': 'SimpleStrategy', 'replication_factor': 1};

USE test_data;

CREATE TABLE users (
    user_id UUID PRIMARY KEY,
    username TEXT,
    email TEXT,
    created_at TIMESTAMP
);

INSERT INTO users (user_id, username, email, created_at) 
VALUES (uuid(), 'john_doe', '<EMAIL>', toTimestamp(now()));
```
