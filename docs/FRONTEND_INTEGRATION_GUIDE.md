# Frontend Integration Guide - SAP ERP & Hikvision Connectors

## Quick Reference for UI Forms

### 🔧 SAP ERP Connector

**Service Type**: `sap_erp`

**Form Fields:**
```javascript
// Required fields (show validation errors if empty)
{
  base_url: {
    type: "text",
    required: true,
    placeholder: "https://sap-server.company.com:8000/sap/opu/odata/sap",
    validation: "Must start with http:// or https://"
  },
  
  // Authentication - show either user/password OR cert fields
  user: {
    type: "text", 
    required: true, // if not using cert auth
    placeholder: "ODATA_USER"
  },
  password: {
    type: "password",
    required: true, // if not using cert auth
    placeholder: "Enter SAP password"
  },
  
  // OR Certificate Authentication
  cert_path: {
    type: "text",
    required: true, // if not using user/password
    placeholder: "/path/to/client.crt"
  },
  key_path: {
    type: "text", 
    required: true, // if not using user/password
    placeholder: "/path/to/client.key"
  }
}

// Optional fields (provide defaults, allow empty)
{
  client: {
    type: "text",
    required: false,
    default: "800",
    placeholder: "800",
    validation: "Must be a number"
  },
  language: {
    type: "select",
    required: false,
    default: "EN",
    options: ["EN", "DE", "FR", "ES", "IT"]
  },
  service_path: {
    type: "text",
    required: false,
    placeholder: "API_BUSINESS_PARTNER (optional)"
  },
  verify_ssl: {
    type: "checkbox",
    required: false,
    default: true
  },
  sample_count: {
    type: "number",
    required: false,
    default: 10,
    min: 1,
    max: 1000
  }
}
```

---

### 📹 Hikvision Connector

**Service Type**: `hikvision_fr`

**Dynamic Form Based on Auth Type:**

```javascript
// Always show first
{
  auth_type: {
    type: "select",
    required: true,
    options: [
      {value: "isapi", label: "ISAPI (Direct Device)"},
      {value: "openapi", label: "OpenAPI (Cloud)"}
    ],
    onChange: "showConditionalFields" // Show different fields based on selection
  }
}

// If auth_type === "isapi"
{
  device_host: {
    type: "text",
    required: true,
    placeholder: "************* or hikvision-device.local"
  },
  device_username: {
    type: "text", 
    required: true,
    default: "admin",
    placeholder: "admin"
  },
  device_password: {
    type: "password",
    required: true,
    placeholder: "Enter device password"
  },
  device_port: {
    type: "number",
    required: false,
    default: 80,
    min: 1,
    max: 65535
  },
  protocol: {
    type: "select",
    required: false,
    default: "http",
    options: ["http", "https"]
  }
}

// If auth_type === "openapi"  
{
  base_url: {
    type: "select", // or text with validation
    required: true,
    options: [
      {value: "https://openapi.hik-connect.com", label: "Global/Europe"},
      {value: "https://openapi.hikvision-us.com", label: "United States"}, 
      {value: "https://open.ys7.com", label: "China"}
    ]
  },
  api_key: {
    type: "text",
    required: true,
    placeholder: "API Key from Hikvision Developer Portal"
  },
  api_secret: {
    type: "password",
    required: true, 
    placeholder: "API Secret from Hikvision Developer Portal"
  }
}

// Common optional fields (show for both auth types)
{
  region: {
    type: "text",
    required: false,
    placeholder: "Main Office, Building A, etc."
  },
  ssl_verify: {
    type: "checkbox",
    required: false,
    default: true // for openapi, false for isapi
  },
  sample_count: {
    type: "number",
    required: false,
    default: 100,
    min: 1,
    max: 1000
  }
}
```

---

## 🎨 UI/UX Recommendations

### Form Layout:
1. **Group related fields** (Authentication, Connection, Advanced)
2. **Show/hide sections** based on auth_type selection
3. **Provide helpful tooltips** for technical fields
4. **Use placeholders** with real examples
5. **Validate on blur/change** not just on submit

### Error Messages:
```javascript
// SAP ERP
{
  "base_url_required": "Base URL is required",
  "base_url_format": "Base URL must start with http:// or https://", 
  "auth_required": "Either username/password OR certificate files are required",
  "client_format": "Client must be a valid number"
}

// Hikvision
{
  "auth_type_required": "Please select authentication method",
  "device_host_required": "Device IP address or hostname is required",
  "api_credentials_required": "API Key and Secret are required for OpenAPI",
  "port_range": "Port must be between 1 and 65535"
}
```

### Conditional Logic:
```javascript
// SAP ERP - Authentication toggle
if (authMethod === "username") {
  show: ["user", "password"]
  hide: ["cert_path", "key_path"]
} else if (authMethod === "certificate") {
  show: ["cert_path", "key_path"] 
  hide: ["user", "password"]
}

// Hikvision - Auth type toggle
if (auth_type === "isapi") {
  show: ["device_host", "device_username", "device_password", "device_port", "protocol"]
  hide: ["base_url", "api_key", "api_secret"]
  ssl_verify.default = false
} else if (auth_type === "openapi") {
  show: ["base_url", "api_key", "api_secret"]
  hide: ["device_host", "device_username", "device_password", "device_port", "protocol"] 
  ssl_verify.default = true
}
```

---

## 📋 Testing Values

### Quick Test Configurations:

**SAP ERP (Demo System):**
```json
{
  "base_url": "https://sapes5.sapdevcenter.com/sap/opu/odata/sap",
  "user": "ES5USER",
  "password": "[Get from SAP Developer Account]",
  "client": "002"
}
```

**Hikvision ISAPI (Local Device):**
```json
{
  "auth_type": "isapi", 
  "device_host": "*************",
  "device_username": "admin",
  "device_password": "12345"
}
```

**Hikvision OpenAPI (Cloud):**
```json
{
  "auth_type": "openapi",
  "base_url": "https://openapi.hik-connect.com", 
  "api_key": "[From Hikvision Developer Portal]",
  "api_secret": "[From Hikvision Developer Portal]"
}
```

---

## 🔗 External Links for Users

- **SAP ES5 Demo Account**: https://developers.sap.com/tutorials/gateway-demo-signup.html
- **Hikvision Developer Portal**: https://open.hikvision.com/
- **Hik-Connect Cloud**: https://www.hik-connect.com/
