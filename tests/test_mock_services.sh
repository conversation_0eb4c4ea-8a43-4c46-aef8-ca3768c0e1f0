#!/bin/bash

# Test script for SAP ERP and Hikvision mock services

echo "=========================================="
echo "Testing SAP ERP and Hikvision Mock Services"
echo "=========================================="
echo ""

# Check if services are running
echo "1. Checking if services are running..."
echo ""

# Check SAP ERP
echo "   SAP ERP Mock Service:"
if curl -s http://localhost:8080/health > /dev/null 2>&1; then
    echo "   ✓ SAP ERP mock service is running on port 8080"
else
    echo "   ✗ SAP ERP mock service is NOT running"
    echo "   Start with: docker-compose up -d sap-erp-mock"
fi
echo ""

# Check Hikvision
echo "   Hikvision Mock Service:"
if curl -s http://localhost:8081/health > /dev/null 2>&1; then
    echo "   ✓ Hikvision mock service is running on port 8081"
else
    echo "   ✗ Hikvision mock service is NOT running"
    echo "   Start with: docker-compose up -d hikvision-mock"
fi
echo ""

# Test SAP ERP endpoints
echo "2. Testing SAP ERP endpoints..."
echo ""

echo "   a) Service Document:"
curl -s "http://localhost:8080/sap/opu/odata/sap/API_BUSINESS_PARTNER/" | head -n 5
echo ""

echo "   b) Business Partner Data (first record):"
curl -s "http://localhost:8080/sap/opu/odata/sap/API_BUSINESS_PARTNER/BusinessPartnerSet" | jq '.d.results[0] | {BusinessPartner, BusinessPartnerName, EmailAddress}' 2>/dev/null || echo "   (Install jq for formatted output)"
echo ""

echo "   c) Metadata (first 10 lines):"
curl -s "http://localhost:8080/sap/opu/odata/sap/API_BUSINESS_PARTNER/\$metadata" | head -n 10
echo ""

# Test Hikvision endpoints
echo "3. Testing Hikvision endpoints..."
echo ""

echo "   a) Device Info:"
curl -s -u admin:admin123 "http://localhost:8081/ISAPI/System/deviceInfo" | grep -E "<deviceName>|<model>|<serialNumber>" | head -n 3
echo ""

echo "   b) Face Libraries:"
curl -s "http://localhost:8081/ISAPI/Intelligent/FDLib" | grep -E "<FDID>|<name>" | head -n 6
echo ""

echo "   c) Person List (first person):"
curl -s "http://localhost:8081/ISAPI/Intelligent/FDLib/FDSearch/personList" | grep -E "<id>|<name>|<email>" | head -n 9
echo ""

echo "   d) Recognition Events (count):"
EVENT_COUNT=$(curl -s "http://localhost:8081/ISAPI/Smart/FaceRecognition/events" | grep -c "<Event>")
echo "   Found $EVENT_COUNT recognition events"
echo ""

echo "=========================================="
echo "Test Complete!"
echo "=========================================="
echo ""
echo "To view full data:"
echo "  SAP Business Partners: curl http://localhost:8080/sap/opu/odata/sap/API_BUSINESS_PARTNER/BusinessPartnerSet | jq"
echo "  Hikvision Persons: curl http://localhost:8081/ISAPI/Intelligent/FDLib/FDSearch/personList"
echo "  Hikvision Events: curl http://localhost:8081/ISAPI/Smart/FaceRecognition/events"
echo ""
