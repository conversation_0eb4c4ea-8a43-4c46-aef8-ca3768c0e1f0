# ✅ SAP ERP and Hikvision Mock Services - Setup Complete

## What Was Created

### 1. Docker Compose Configuration
**File**: `docker-compose.yaml`

Added two new services:
- `sap-erp-mock` - Running on port **8080**
- `hikvision-mock` - Running on port **8081**

Both services use Nginx Alpine containers serving static mock data.

### 2. SAP ERP Mock Service

**Directory**: `tests/mock_services/sap_erp/`

**Files Created**:
- `nginx.conf` - Nginx configuration for OData endpoints
- `service_document.json` - OData service catalog
- `business_partner_data.json` - **5 business partner records with PII**
- `sales_order_data.json` - **3 sales order records**
- `metadata.xml` - OData metadata definition

**Dummy Data Includes**:
- ✅ 5 Business Partners (3 individuals, 2 corporations)
- ✅ Email addresses (PII)
- ✅ Phone numbers (PII)
- ✅ Tax/SSN numbers (PII)
- ✅ Physical addresses (PII)
- ✅ Names (PII)
- ✅ Financial data (credit limits)

### 3. Hikvision Mock Service

**Directory**: `tests/mock_services/hikvision/`

**Files Created**:
- `nginx.conf` - Nginx configuration for ISAPI endpoints
- `device_info.xml` - Device information
- `face_libraries.xml` - **3 face recognition libraries**
- `person_list.xml` - **5 registered persons with biometric data**
- `recognition_events.xml` - **6 access control events**

**Dummy Data Includes**:
- ✅ 5 Registered persons with complete profiles
- ✅ ID card numbers (PII)
- ✅ Passport numbers (PII)
- ✅ Driver license numbers (PII)
- ✅ Email addresses (PII)
- ✅ Phone numbers (PII)
- ✅ Physical addresses (PII)
- ✅ Employee numbers (PII)
- ✅ Biometric data (face recognition)
- ✅ 6 Recognition events with access logs

### 4. Documentation

**Files Created**:
- `tests/mock_services/README.md` - Comprehensive documentation
- `tests/MOCK_SERVICES_QUICKSTART.md` - Quick start guide
- `tests/test_mock_services.sh` - Automated test script

## 🚀 How to Use

### Start the Services
```bash
cd /home/<USER>/unstructured-Ingestion
docker-compose up -d sap-erp-mock hikvision-mock
```

### Verify Services Are Running
```bash
# Check status
docker-compose ps sap-erp-mock hikvision-mock

# Run automated tests
./tests/test_mock_services.sh
```

### Test the Endpoints

**SAP ERP**:
```bash
# Health check
curl http://localhost:8080/health

# Get business partners
curl http://localhost:8080/sap/opu/odata/sap/API_BUSINESS_PARTNER/BusinessPartnerSet

# Get metadata
curl http://localhost:8080/sap/opu/odata/sap/API_BUSINESS_PARTNER/\$metadata
```

**Hikvision**:
```bash
# Health check
curl http://localhost:8081/health

# Get device info
curl http://localhost:8081/ISAPI/System/deviceInfo

# Get persons
curl http://localhost:8081/ISAPI/Intelligent/FDLib/FDSearch/personList

# Get events
curl http://localhost:8081/ISAPI/Smart/FaceRecognition/events
```

## 📊 Data Summary

| Service | Tables | Records | PII Types |
|---------|--------|---------|-----------|
| SAP ERP | 2 | 8 total | Email, Phone, SSN/Tax, Address, Name |
| Hikvision | 3 | 14 total | Biometric, ID Numbers, Email, Phone, Address, Name, Employee ID |

### SAP ERP Tables
1. **BusinessPartnerSet** - 5 records
   - Columns: 18 fields including BusinessPartner, Name, Email, Phone, Address, Tax Number
   
2. **SalesOrderSet** - 3 records
   - Columns: 15 fields including Order details, Customer info, Product, Amounts

### Hikvision Tables
1. **FaceLibraries** - 3 records
   - Columns: FDID, name, threshold, timestamps
   
2. **FaceLibraryPersons** - 5 records  
   - Columns: 15 fields including ID, name, certificate numbers, contact info, employee data
   
3. **RecognitionEvents** - 6 records
   - Columns: 14 fields including event details, person info, access results, biometric scores

## 🧪 Testing Your Connectors

Both connectors are ready to test against these mock services:

### SAP ERP Connector
- Connects to `http://sap-erp-mock/sap/opu/odata/sap`
- Extracts BusinessPartnerSet and SalesOrderSet
- Generates CSV samples with PII data
- Creates TableMetadata with 18 columns for BusinessPartnerSet

### Hikvision Connector
- Connects to `http://hikvision-mock/ISAPI`
- Extracts 3 face libraries
- Scans 5 persons with biometric data
- Captures 6 recognition events
- Generates CSV samples for all tables

## 📁 Complete File Structure

```
/home/<USER>/unstructured-Ingestion/
├── docker-compose.yaml (✅ Updated with new services)
└── tests/
    ├── MOCK_SERVICES_QUICKSTART.md (✅ Quick start guide)
    ├── test_mock_services.sh (✅ Automated test script)
    └── mock_services/
        ├── README.md (✅ Detailed documentation)
        ├── sap_erp/
        │   ├── nginx.conf
        │   ├── service_document.json
        │   ├── business_partner_data.json (5 records)
        │   ├── sales_order_data.json (3 records)
        │   └── metadata.xml
        └── hikvision/
            ├── nginx.conf
            ├── device_info.xml
            ├── face_libraries.xml (3 libraries)
            ├── person_list.xml (5 persons)
            └── recognition_events.xml (6 events)
```

## ✅ Verification Steps

1. **Start Services**:
   ```bash
   docker-compose up -d sap-erp-mock hikvision-mock
   ```

2. **Check Health**:
   ```bash
   curl http://localhost:8080/health
   curl http://localhost:8081/health
   ```

3. **Run Test Script**:
   ```bash
   ./tests/test_mock_services.sh
   ```

4. **Test Connectors** (see Quick Start guide for Python examples)

5. **Verify Data Flow**:
   - Connectors extract table metadata
   - CSV files generated with sample data
   - Column definitions include all fields
   - PII data properly classified

## 🎯 Expected Results

When you run your connectors against these services:

### SAP ERP:
- Discovers 2 tables (BusinessPartnerSet, SalesOrderSet)
- Extracts 18 columns from BusinessPartnerSet
- Generates CSV with 5 business partner records
- Identifies PII: emails, phones, tax numbers, addresses

### Hikvision:
- Discovers 3 face libraries
- Extracts 15 columns from person data
- Generates CSV with 5 person records
- Identifies PII: biometric, ID numbers, emails, phones, addresses
- Captures 6 recognition events

## 📝 Next Actions

1. ✅ Mock services created and configured
2. ✅ Dummy data with PII included
3. ✅ Documentation complete
4. ⏭️ Start services: `docker-compose up -d sap-erp-mock hikvision-mock`
5. ⏭️ Run test script: `./tests/test_mock_services.sh`
6. ⏭️ Test your connectors
7. ⏭️ Verify data storage in warehouse

---

**Everything is ready for testing!** 🚀

Your SAP ERP and Hikvision connectors can now be tested against local mock services with realistic dummy data including PII.
