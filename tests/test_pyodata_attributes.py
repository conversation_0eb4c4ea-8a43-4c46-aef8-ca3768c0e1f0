#!/usr/bin/env python3
"""
Test PyOData library to understand attribute names
"""
import requests
from pyodata import Client

def test_pyodata_attributes():
    """Test to see what attributes PyOData EntityType has"""
    
    # Mock service URL
    base_url = "http://localhost:8080/sap/opu/odata/sap"
    service_name = "API_BUSINESS_PARTNER"
    
    # Create session with basic auth
    session = requests.Session()
    session.auth = ('testuser', 'testpass')
    session.verify = False
    
    try:
        # Get OData client
        service_url = f"{base_url}/{service_name}"
        print(f"Connecting to: {service_url}")
        client = Client(service_url, session)
        
        print("\n=== Schema Information ===")
        print(f"Schema available attributes: {[attr for attr in dir(client.schema) if not attr.startswith('_')]}")
        
        print("\n=== Entity Sets ===")
        for es in client.schema.entity_sets:
            print(f"  - {es.name}")
            
            # Get entity type
            entity_type = es.entity_type
            print(f"\n=== Entity Type: {entity_type.name} ===")
            print(f"Available attributes: {[attr for attr in dir(entity_type) if not attr.startswith('_')]}")
            
            # Try different property attribute names
            print("\n=== Trying to access properties ===")
            
            # Test: proprties (with typo)
            if hasattr(entity_type, 'proprties'):
                print("✅ Has 'proprties' attribute (typo version)")
                props = entity_type.proprties
                print(f"   Type: {type(props)}")
                if callable(props):
                    print("   It's a method - calling it...")
                    props = props()
                print(f"   Properties count: {len(props)}")
                for prop in props:
                    print(f"      - {prop.name}: {prop.typ}")
            else:
                print("❌ No 'proprties' attribute")
            
            # Test: properties (correct spelling)
            if hasattr(entity_type, 'properties'):
                print("✅ Has 'properties' attribute")
                props = entity_type.properties
                print(f"   Type: {type(props)}")
                if callable(props):
                    print("   It's a method - calling it...")
                    props = props()
                print(f"   Properties count: {len(props)}")
                for prop in props:
                    print(f"      - {prop.name}: {prop.typ}")
            else:
                print("❌ No 'properties' attribute")
            
            # Test key properties
            print("\n=== Trying to access key properties ===")
            if hasattr(entity_type, 'key_proprties'):
                print("✅ Has 'key_proprties' attribute (typo version)")
                keys = entity_type.key_proprties
                print(f"   Keys: {[k.name for k in keys]}")
            elif hasattr(entity_type, 'key_properties'):
                print("✅ Has 'key_properties' attribute")
                keys = entity_type.key_properties
                print(f"   Keys: {[k.name for k in keys]}")
            else:
                print("❌ No key properties attribute found")
            
            break  # Just test first entity set
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Testing PyOData attribute names...")
    test_pyodata_attributes()
