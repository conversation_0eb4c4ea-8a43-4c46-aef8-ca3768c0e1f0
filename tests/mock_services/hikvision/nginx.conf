server {
    listen 80;
    server_name hikvision-mock;
    
    # Enable CORS
    add_header 'Access-Control-Allow-Origin' '*' always;
    add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
    add_header 'Access-Control-Allow-Headers' 'Authorization, Content-Type' always;
    
    # Health check endpoint
    location /health {
        return 200 "OK\n";
        add_header Content-Type text/plain;
    }
    
    # Mock device info endpoint
    location /ISAPI/System/deviceInfo {
        default_type application/xml;
        alias /usr/share/nginx/html/device_info.xml;
    }
    
    # Mock face library list
    location /ISAPI/Intelligent/FDLib {
        default_type application/xml;
        alias /usr/share/nginx/html/face_libraries.xml;
    }
    
    # Mock person list with pagination
    location ~* /ISAPI/Intelligent/FDLib/FDSearch/personList {
        default_type application/xml;
        alias /usr/share/nginx/html/person_list.xml;
    }
    
    # Mock specific library person list
    location ~* /ISAPI/Intelligent/FDLib/1/FDSearch/personList {
        default_type application/xml;
        alias /usr/share/nginx/html/person_list_lib1.xml;
    }
    
    location ~* /ISAPI/Intelligent/FDLib/2/FDSearch/personList {
        default_type application/xml;
        alias /usr/share/nginx/html/person_list_lib2.xml;
    }
    
    # Mock recognition events
    location ~* /ISAPI/Smart/FaceRecognition/events {
        default_type application/xml;
        alias /usr/share/nginx/html/recognition_events.xml;
    }
    
    # Mock system capabilities
    location /ISAPI/System/capabilities {
        default_type application/xml;
        alias /usr/share/nginx/html/capabilities.xml;
    }
    
    # Root endpoint
    location / {
        default_type application/json;
        return 200 '{"message": "Hikvision Mock ISAPI Service", "status": "running"}';
    }
}
