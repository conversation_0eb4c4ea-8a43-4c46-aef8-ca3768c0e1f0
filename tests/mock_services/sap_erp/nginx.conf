server {
    listen 80;
    server_name sap-erp-mock;
    
    # Enable CORS for testing
    add_header 'Access-Control-Allow-Origin' '*' always;
    add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
    add_header 'Access-Control-Allow-Headers' 'Authorization, Content-Type, sap-client, sap-language' always;
    
    # Health check endpoint
    location /health {
        return 200 "OK\n";
        add_header Content-Type text/plain;
    }
    
    # Mock SAP OData service metadata endpoint
    location ~* ^/sap/opu/odata/sap/(.+)/\$metadata {
        default_type application/xml;
        alias /usr/share/nginx/html/metadata.xml;
    }
    
    # Mock SAP OData entity set data
    location ~* ^/sap/opu/odata/sap/API_BUSINESS_PARTNER/BusinessPartnerSet {
        default_type application/json;
        alias /usr/share/nginx/html/business_partner_data.json;
    }
    
    location ~* ^/sap/opu/odata/sap/API_SALES_ORDER/SalesOrderSet {
        default_type application/json;
        alias /usr/share/nginx/html/sales_order_data.json;
    }
    
    # Mock SAP service document
    location ~* ^/sap/opu/odata/sap/(.+)/\$ {
        default_type application/json;
        alias /usr/share/nginx/html/service_document.json;
    }
    
    # Mock service catalog
    location ~* ^/sap/opu/odata/sap/\$ {
        default_type application/json;
        alias /usr/share/nginx/html/service_catalog.json;
    }
    
    # Root endpoint
    location / {
        default_type application/json;
        return 200 '{"message": "SAP ERP Mock Service", "status": "running"}';
    }
}
