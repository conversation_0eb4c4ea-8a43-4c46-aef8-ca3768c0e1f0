<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="1.0" xmlns:edmx="http://schemas.microsoft.com/ado/2007/06/edmx">
  <edmx:DataServices m:DataServiceVersion="2.0" xmlns:m="http://schemas.microsoft.com/ado/2007/08/dataservices/metadata">
    <Schema Namespace="SAP" xmlns="http://schemas.microsoft.com/ado/2008/09/edm">
      
      <!-- Business Partner Entity Type -->
      <EntityType Name="BusinessPartner">
        <Key>
          <PropertyRef Name="BusinessPartner"/>
        </Key>
        <Property Name="BusinessPartner" Type="Edm.String" Nullable="false" MaxLength="10"/>
        <Property Name="BusinessPartnerName" Type="Edm.String" MaxLength="80"/>
        <Property Name="FirstName" Type="Edm.String" MaxLength="40"/>
        <Property Name="LastName" Type="Edm.String" MaxLength="40"/>
        <Property Name="EmailAddress" Type="Edm.String" MaxLength="241"/>
        <Property Name="PhoneNumber" Type="Edm.String" MaxLength="30"/>
        <Property Name="MobileNumber" Type="Edm.String" MaxLength="30"/>
        <Property Name="Country" Type="Edm.String" MaxLength="3"/>
        <Property Name="City" Type="Edm.String" MaxLength="40"/>
        <Property Name="PostalCode" Type="Edm.String" MaxLength="10"/>
        <Property Name="StreetAddress" Type="Edm.String" MaxLength="60"/>
        <Property Name="Region" Type="Edm.String" MaxLength="3"/>
        <Property Name="TaxNumber" Type="Edm.String" MaxLength="20"/>
        <Property Name="CustomerType" Type="Edm.String" MaxLength="20"/>
        <Property Name="CreditLimit" Type="Edm.Decimal" Precision="15" Scale="2"/>
        <Property Name="Currency" Type="Edm.String" MaxLength="5"/>
        <Property Name="CreatedDate" Type="Edm.DateTime"/>
        <Property Name="ModifiedDate" Type="Edm.DateTime"/>
      </EntityType>
      
      <!-- Sales Order Entity Type -->
      <EntityType Name="SalesOrder">
        <Key>
          <PropertyRef Name="SalesOrder"/>
          <PropertyRef Name="SalesOrderItem"/>
        </Key>
        <Property Name="SalesOrder" Type="Edm.String" Nullable="false" MaxLength="10"/>
        <Property Name="SalesOrderItem" Type="Edm.String" Nullable="false" MaxLength="6"/>
        <Property Name="BusinessPartner" Type="Edm.String" MaxLength="10"/>
        <Property Name="CustomerName" Type="Edm.String" MaxLength="80"/>
        <Property Name="OrderDate" Type="Edm.DateTime"/>
        <Property Name="DeliveryDate" Type="Edm.DateTime"/>
        <Property Name="ProductID" Type="Edm.String" MaxLength="40"/>
        <Property Name="ProductName" Type="Edm.String" MaxLength="40"/>
        <Property Name="Quantity" Type="Edm.Decimal" Precision="13" Scale="3"/>
        <Property Name="UnitPrice" Type="Edm.Decimal" Precision="15" Scale="2"/>
        <Property Name="TotalAmount" Type="Edm.Decimal" Precision="15" Scale="2"/>
        <Property Name="Currency" Type="Edm.String" MaxLength="5"/>
        <Property Name="OrderStatus" Type="Edm.String" MaxLength="20"/>
        <Property Name="PaymentTerms" Type="Edm.String" MaxLength="40"/>
        <Property Name="ShippingAddress" Type="Edm.String" MaxLength="255"/>
      </EntityType>
      
      <!-- Entity Container -->
      <EntityContainer Name="SAP_Container" m:IsDefaultEntityContainer="true">
        <EntitySet Name="BusinessPartnerSet" EntityType="SAP.BusinessPartner"/>
        <EntitySet Name="SalesOrderSet" EntityType="SAP.SalesOrder"/>
      </EntityContainer>
    </Schema>
  </edmx:DataServices>
</edmx:Edmx>
