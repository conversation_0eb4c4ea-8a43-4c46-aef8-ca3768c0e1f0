# SAP ERP and Hikvision Mock Services - Quick Start

## 🚀 Quick Start

### 1. Start Mock Services
```bash
cd /home/<USER>/unstructured-Ingestion
docker-compose up -d sap-erp-mock hikvision-mock
```

### 2. Check Service Status
```bash
# Check if services are healthy
docker-compose ps sap-erp-mock hikvision-mock

# Or use the test script
./tests/test_mock_services.sh
```

### 3. Access Services

**SAP ERP Mock**:
- URL: http://localhost:8080
- Health: http://localhost:8080/health
- Business Partners: http://localhost:8080/sap/opu/odata/sap/API_BUSINESS_PARTNER/BusinessPartnerSet

**Hikvision Mock**:
- URL: http://localhost:8081
- Health: http://localhost:8081/health
- Device Info: http://localhost:8081/ISAPI/System/deviceInfo

## 📊 Dummy Data Summary

### SAP ERP Data
- **Business Partners**: 5 records
  - 3 Individual customers (with PII: emails, phones, SSN)
  - 2 Corporate customers
  - Fields: Name, Email, Phone, Address, Tax Number, Credit Limit

- **Sales Orders**: 3 records
  - Order details with customer references
  - Product information and pricing
  - Delivery addresses

### Hikvision Data
- **Face Libraries**: 3 libraries (Employee, VIP, Visitor)
- **Registered Persons**: 5 persons
  - Complete biometric profiles
  - PII data: ID numbers, passports, phone, email, addresses
  - Employee information: department, position, employee number
  
- **Recognition Events**: 6 events
  - Access logs with timestamps
  - Face recognition results with similarity scores
  - Temperature readings and mask status
  - Door access information

## 🧪 Testing Connectors

### Test SAP ERP Connector
```bash
# Activate virtual environment
source env/bin/activate

# Run Python test
python -c "
import asyncio
from src.ingestion.structure.saperp.source import SAPERPSource

async def test():
    source = SAPERPSource('sap_test')
    # Configure credentials manually for testing
    source.base_url = 'http://localhost:8080/sap/opu/odata/sap'
    source.service_path = 'API_BUSINESS_PARTNER'
    source.user = 'testuser'
    source.password = 'testpass'
    source.client_number = '100'
    source.sample_count = 10
    
    await source.connect()
    
    async for table in source.deep_scan():
        print(f'Found table: {table.table_name}')
        print(f'Columns: {len(table.columns)}')
        for col in table.columns[:5]:
            print(f'  - {col.column_name} ({col.data_type})')

asyncio.run(test())
"
```

### Test Hikvision Connector
```bash
# Run Python test
python -c "
import asyncio
from src.ingestion.structure.hikvision.source import HikvisionFRSource

async def test():
    source = HikvisionFRSource('hikvision_test')
    # Configure credentials manually for testing
    source.auth_type = 'isapi'
    source.device_host = 'localhost'
    source.device_port = 8081
    source.device_username = 'admin'
    source.device_password = 'admin123'
    source.protocol = 'http'
    source.sample_count = 50
    
    await source.connect()
    
    async for table in source.deep_scan():
        print(f'Found table: {table.table_name}')
        print(f'Columns: {len(table.columns)}')
        for col in table.columns[:5]:
            print(f'  - {col.column_name} ({col.data_type})')

asyncio.run(test())
"
```

## 🔍 Manual Testing

### SAP ERP Endpoints
```bash
# Get all business partners (JSON)
curl http://localhost:8080/sap/opu/odata/sap/API_BUSINESS_PARTNER/BusinessPartnerSet | jq

# Get metadata (XML)
curl http://localhost:8080/sap/opu/odata/sap/API_BUSINESS_PARTNER/\$metadata

# Get sales orders
curl http://localhost:8080/sap/opu/odata/sap/API_SALES_ORDER/SalesOrderSet | jq
```

### Hikvision Endpoints
```bash
# Get device information
curl http://localhost:8081/ISAPI/System/deviceInfo

# Get face libraries
curl http://localhost:8081/ISAPI/Intelligent/FDLib

# Get registered persons
curl http://localhost:8081/ISAPI/Intelligent/FDLib/FDSearch/personList

# Get recognition events
curl http://localhost:8081/ISAPI/Smart/FaceRecognition/events
```

## 📝 Expected Column Data

### SAP ERP - BusinessPartnerSet
| Column Name | Data Type | PII Type | Example |
|-------------|-----------|----------|---------|
| BusinessPartner | Edm.String | - | 0000100001 |
| BusinessPartnerName | Edm.String | NAME | John Doe |
| EmailAddress | Edm.String | EMAIL | <EMAIL> |
| PhoneNumber | Edm.String | PHONE | ******-0100 |
| TaxNumber | Edm.String | SSN/TAX_ID | *********** |
| StreetAddress | Edm.String | ADDRESS | 123 Main Street |

### Hikvision - FaceLibraryPersons
| Column Name | Data Type | PII Type | Example |
|-------------|-----------|----------|---------|
| id | Integer | - | 1001 |
| name | String | NAME | John Doe |
| certificateNumber | String | ID_NUMBER | 123456789012345 |
| phoneNumber | String | PHONE | ******-0100 |
| email | String | EMAIL | <EMAIL> |
| address | String | ADDRESS | 123 Main Street... |
| employeeNo | String | EMPLOYEE_ID | EMP-001 |

### Hikvision - RecognitionEvents
| Column Name | Data Type | PII Type | Example |
|-------------|-----------|----------|---------|
| eventID | String | - | event_20251024_001 |
| personID | Integer | - | 1001 |
| personName | String | NAME | John Doe |
| eventTime | DateTime | - | 2025-10-24T08:30:15 |
| similarity | Integer | BIOMETRIC | 95 |
| employeeNo | String | EMPLOYEE_ID | EMP-001 |

## 🛠️ Troubleshooting

### Services not starting
```bash
# Check Docker logs
docker-compose logs sap-erp-mock
docker-compose logs hikvision-mock

# Restart services
docker-compose restart sap-erp-mock hikvision-mock
```

### Port conflicts
```bash
# Check if ports are in use
netstat -tulpn | grep -E '8080|8081'

# Change ports in docker-compose.yaml if needed
# From: "8080:80" to "8082:80"
```

### Clear and rebuild
```bash
# Stop and remove containers
docker-compose down

# Remove volumes if needed
docker volume prune

# Start fresh
docker-compose up -d sap-erp-mock hikvision-mock
```

## 📦 Files Created

```
tests/
├── mock_services/
│   ├── README.md                     # Detailed documentation
│   ├── sap_erp/
│   │   ├── nginx.conf                # Nginx config
│   │   ├── service_document.json     # OData catalog
│   │   ├── business_partner_data.json # 5 business partners
│   │   ├── sales_order_data.json     # 3 sales orders
│   │   └── metadata.xml              # OData metadata
│   └── hikvision/
│       ├── nginx.conf                # Nginx config
│       ├── device_info.xml           # Device details
│       ├── face_libraries.xml        # 3 libraries
│       ├── person_list.xml           # 5 persons
│       └── recognition_events.xml    # 6 events
└── test_mock_services.sh             # Automated test script
```

## ✅ Verification Checklist

- [ ] Services start successfully
- [ ] Health checks pass
- [ ] SAP ERP returns JSON data
- [ ] Hikvision returns XML data
- [ ] Connectors can scan tables
- [ ] Column metadata is extracted
- [ ] CSV sample files are generated
- [ ] PII data is identified correctly

## 🎯 Next Steps

1. Start the mock services
2. Run the test script: `./tests/test_mock_services.sh`
3. Test individual connectors
4. Verify column data storage in warehouse
5. Check PII classification results
6. Review audit logs

---

**Ready to test!** 🚀

Start with: `docker-compose up -d sap-erp-mock hikvision-mock`
