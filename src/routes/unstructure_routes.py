from fastapi import APIRouter
from uuid import uuid4
from src.utils.logger import get_unstructur_logger
from src.utils.exceptions import CustomBaseException
from src.utils.helpers import (
    generate_http_success_response,
    generate_http_error_response,
    read_file_last_n_lines,
    file_exists,
)
from src.common.constants import ServiceTypes, IngestionJobStatus
from src.schemas.ingestion_schemas import (
    UserRequest,
    UserUpdateRequest,
    ServiceRequest,
    ServiceUpdateRequest,
    IngestionRequest,
    IngestionUpdateRequest,
    UserStateRequest,
    ServiceStateRequest,
    IngestionStateRequest,
    IngestionJobRequest,
    IngestionJobUpdateRequest,
)
from src.schemas.dsr_schemas import DSRCreateRequest
from src.modules.repos import DatabaseManager
from src.ingestion.scheduler import schedule_ingestion_task, schedule_dsr_task
from src.utils.heath_check import check_db_health, check_minio_health
from src.utils.minio_client import MinioClient
from src.utils.process_util import kill_process
from src.ingestion.structure.mysql.test_connection import (
    test_mysql_connection,
)
from src.ingestion.structure.postgres.test_connection import (
    test_postgres_connection,
)
from src.ingestion.structure.oracle.test_connection import (
    test_oracle_connection,
)
from src.ingestion.unstructure.directory_scan.test_connection import (
    test_directory_connection,
)
from src.ingestion.unstructure.sftp_server.test_connection import (
    test_sftp_connection,
)
from src.ingestion.structure.mssql.test_connection import (
    test_mssql_connection,
)
from src.ingestion.structure.sqlite.test_connection import (
    test_sqlite_connection,
)
from src.ingestion.unstructure.adls.test_connection import (
    test_adls_connection,
)
from src.ingestion.unstructure.aws.s3.test_connection import (
    test_aws_connection,
)
from src.ingestion.unstructure.office_365.onedrive.test_connection import (
    test_onedrive_connection,
)
from src.ingestion.unstructure.office_365.teams.test_connection import (
    test_teams_connection,
)
from src.ingestion.unstructure.office_365.sharepoint.test_connection import (
    test_sharepoint_connection,
)
from src.ingestion.unstructure.office_365.outlook.test_connection import (
    test_outlook_connection,
)


logger = get_unstructur_logger()

unstructure_router = APIRouter()


@unstructure_router.get("/health", tags=["health"])
async def health():
    minio_status = await check_minio_health()
    db_status = await check_db_health()

    detailed = {**minio_status, **db_status}
    status_code = 200 if detailed["minio"] and detailed["postgres"] else 503
    return generate_http_success_response(status_code, "", detailed)


@unstructure_router.post("/service/test-connection", tags=["service"])
async def test_service_connection(service: ServiceRequest):
    try:
        data = service.model_dump()
        logger.info(f"Testing connection for service: {data}")

        service_type = data.get("service_type", "")

        service_map = {
            ServiceTypes.POSTGRES.value: test_postgres_connection,
            ServiceTypes.MYSQL.value: test_mysql_connection,
            ServiceTypes.ORACLE.value: test_oracle_connection,
            ServiceTypes.DIRECTORY_SCAN.value: test_directory_connection,
            ServiceTypes.SFTP.value: test_sftp_connection,
            ServiceTypes.MS_365_ONEDRIVE.value: test_onedrive_connection,
            ServiceTypes.MS_365_TEAMS.value: test_teams_connection,
            ServiceTypes.MS_365_SHAREPOINT.value: test_sharepoint_connection,
            ServiceTypes.MS_365_OUTLOOK.value: test_outlook_connection,
            ServiceTypes.AWS_S3.value: test_aws_connection,
            ServiceTypes.AZURE_DATA_LAKE.value: test_adls_connection,
            ServiceTypes.MSSQL.value: test_mssql_connection,
            ServiceTypes.SQLITE.value: test_sqlite_connection,
        }

        test_function = service_map.get(service_type)
        if not test_function:
            return generate_http_error_response(
                400, f"Unsupported service_type: {service_type}"
            )

        result = await test_function(data)
        return generate_http_success_response(200, "Connection successful", result)

    except CustomBaseException as exc:
        return generate_http_error_response(status_code=exc.code, message=exc.message)
    except Exception as exc:
        logger.exception("Unexpected error during test connection")
        return generate_http_error_response(
            status_code=500, message=f"Failed to test connection: {exc}"
        )


@unstructure_router.get("/user", tags=["user"])
async def get_users_route(start: int = 0, limit: int = 100):
    try:
        db = DatabaseManager()
        data = await db.get_all_users(start=start, limit=limit)
        logger.info(f"{data}")
        return generate_http_success_response(200, "", data)
    except CustomBaseException as exc:
        return generate_http_error_response(status_code=exc.code, message=exc.message)
    except Exception as exc:
        return generate_http_error_response(
            status_code=500, message=f"Failed to fetch all users {exc}"
        )


@unstructure_router.get("/user/{user_id}", tags=["user"])
async def get_user_route(user_id: str):
    try:
        db = DatabaseManager()
        data = await db.get_user_by_id(user_id)
        return generate_http_success_response(200, "", data)
    except CustomBaseException as exc:
        return generate_http_error_response(status_code=exc.code, message=exc.message)
    except Exception as exc:
        return generate_http_error_response(
            status_code=500, message=f"Failed to fetch user {user_id} {exc}"
        )


@unstructure_router.post("/user", tags=["user"])
async def create_user_route(user: UserRequest):
    try:
        data = user.model_dump()
        db = DatabaseManager()
        logger.info(f"{data}")
        await db.create_user(data)
        return generate_http_success_response(200, "", data)
    except CustomBaseException as exc:
        return generate_http_error_response(
            status_code=exc.code,
            message=exc.message,
        )
    except Exception as exc:
        return generate_http_error_response(
            status_code=500, message=f"Failed to create user {exc}"
        )


@unstructure_router.patch("/user/{user_id}", tags=["user"])
async def update_user_route(user_id: str, user: UserUpdateRequest):
    try:
        data = user.model_dump()
        logger.info(f"{data}")
        db = DatabaseManager()
        data = {k: v for k, v in data.items() if v is not None}
        data = await db.update_user(user_id, data)
        return generate_http_success_response(200, "", data)
    except CustomBaseException as exc:
        return generate_http_error_response(status_code=exc.code, message=exc.message)
    except Exception as exc:
        return generate_http_error_response(
            status_code=500, message=f"Failed to update user {user_id} {exc}"
        )


@unstructure_router.patch("/user/{user_id}/state", tags=["user"])
async def state_user_route(user_id: str, user: UserStateRequest):
    try:
        data = user.model_dump()
        logger.info(f"{data}")
        db = DatabaseManager()
        data = await db.update_user(user_id, data)
        # Perform the steps after service state change
        return generate_http_success_response(200, "", data)
    except CustomBaseException as exc:
        return generate_http_error_response(status_code=exc.code, message=exc.message)
    except Exception as exc:
        return generate_http_error_response(
            status_code=500,
            message=f"Failed to update user state. user-id {user_id} {exc}",
        )


@unstructure_router.post("/service", tags=["service"])
async def create_service_route(service: ServiceRequest):
    try:
        data = service.model_dump()
        logger.info(f"{data}")
        db = DatabaseManager()
        data = await db.create_service(data)

        return generate_http_success_response(200, "", data)
    except CustomBaseException as exc:
        return generate_http_error_response(status_code=exc.code, message=exc.message)
    except Exception as exc:
        return generate_http_error_response(
            status_code=500, message=f"Failed to create service {exc}"
        )


@unstructure_router.get("/service", tags=["service"])
async def get_services_route(start: int = 0, limit: int = 100):
    try:
        db = DatabaseManager()
        result = await db.get_all_services(start=start, limit=limit)
        return generate_http_success_response(
            200, "Services fetched successfully", result
        )
    except CustomBaseException as exc:
        return generate_http_error_response(status_code=exc.code, message=exc.message)
    except Exception as exc:
        logger.error(f"Unexpected error in get_services_route: {exc}")
        return generate_http_error_response(
            status_code=500, message=f"Failed to fetch all services: {exc}"
        )


@unstructure_router.get("/service/{service_name}", tags=["service"])
async def get_service_route(service_name: str):
    try:
        db = DatabaseManager()
        data = await db.get_service_by_service_name(service_name)
        logger.info(f"{data}")
        return generate_http_success_response(200, "", data)
    except CustomBaseException as exc:
        return generate_http_error_response(status_code=exc.code, message=exc.message)
    except Exception as exc:
        return generate_http_error_response(
            status_code=500,
            message=f"Failed to fetch service {service_name} {exc}",
        )


@unstructure_router.patch("/service/{service_name}", tags=["service"])
async def update_service_route(service_name: str, service: ServiceUpdateRequest):
    try:
        data = service.model_dump()
        db = DatabaseManager()
        data = {k: v for k, v in data.items() if v is not None}
        data = await db.update_service(service_name, data)
        logger.info(f"{data}")
        return generate_http_success_response(200, "", data)
    except CustomBaseException as exc:
        return generate_http_error_response(status_code=exc.code, message=exc.message)
    except Exception as exc:
        return generate_http_error_response(
            status_code=500,
            message=f"Failed to update service {service_name} {exc}",
        )


@unstructure_router.patch("/service/{service_name}/state", tags=["service"])
async def state_service_route(service_name: str, service: ServiceStateRequest):
    try:
        data = service.model_dump()
        db = DatabaseManager()
        data = await db.update_service(service_name, data)
        logger.info(f"{data}")
        return generate_http_success_response(200, "", data)
    except CustomBaseException as exc:
        return generate_http_error_response(status_code=exc.code, message=exc.message)
    except Exception as exc:
        return generate_http_error_response(
            status_code=500,
            message=f"Failed to update service state. service: {service_name} {exc}",
        )


@unstructure_router.post("/ingestion/{service_name}", tags=["ingestion"])
async def create_ingestion_route(service_name: str, ingestion: IngestionRequest):
    try:
        data = ingestion.model_dump()
        db = DatabaseManager()
        data["ingestion_id"] = str(uuid4())
        data = await db.create_ingestion(data)
        logger.info(f"{data}")
        return generate_http_success_response(200, "", data)
    except CustomBaseException as exc:
        return generate_http_error_response(status_code=exc.code, message=exc.message)
    except Exception as exc:
        return generate_http_error_response(
            status_code=500,
            message=f"Failed to create ingestion on service {service_name} {exc}",
        )


@unstructure_router.get("/ingestion/{service_name}", tags=["ingestion"])
async def get_service_ingestions_route(
    service_name: str, start: int = 0, limit: int = 100
):
    try:
        db = DatabaseManager()
        result = await db.get_service_ingestions(service_name, start=start, limit=limit)
        logger.info(f"Fetched ingestions: {result}")

        # Always return 200 with services list (even if empty)
        return generate_http_success_response(
            200, "Ingestions fetched successfully", result
        )
    except CustomBaseException as exc:
        return generate_http_error_response(status_code=exc.code, message=exc.message)
    except Exception as exc:
        logger.error(f"Unexpected error in get_service_ingestions_route: {exc}")
        return generate_http_error_response(
            status_code=500,
            message=f"Failed to fetch service {service_name} ingestions {exc}",
        )


@unstructure_router.get("/ingestion/{ingestion_id}", tags=["ingestion"])
async def get_ingestion_route(ingestion_id: str):
    try:
        db = DatabaseManager()
        data = await db.get_ingestion_by_id(ingestion_id)
        logger.info(f"{data}")

        return generate_http_success_response(200, "", data)
    except CustomBaseException as exc:
        return generate_http_error_response(status_code=exc.code, message=exc.message)
    except Exception as exc:
        return generate_http_error_response(
            status_code=500,
            message=f"Failed to fetch ingestion {ingestion_id} {exc}",
        )


@unstructure_router.patch("/ingestion/{ingestion_id}", tags=["ingestion"])
async def update_service_ingestion_route(
    ingestion_id: str, ingestion: IngestionUpdateRequest
):
    try:
        data = ingestion.dict()
        db = DatabaseManager()
        data = {k: v for k, v in data.items() if v is not None}
        data = await db.update_ingestion(ingestion_id, data)
        return generate_http_success_response(200, "", data)
    except CustomBaseException as exc:
        return generate_http_error_response(status_code=exc.code, message=exc.message)
    except Exception as exc:
        return generate_http_error_response(
            status_code=500,
            message=f"Failed to update ingestion {ingestion_id} {exc}",
        )


@unstructure_router.patch("/ingestion/{ingestion_id}/state", tags=["ingestion"])
async def state_ingestion_route(ingestion_id: str, ingestion: IngestionStateRequest):
    try:
        data = ingestion.dict()
        db = DatabaseManager()
        data = await db.update_ingestion(ingestion_id, data)
        return generate_http_success_response(200, "", data)
    except CustomBaseException as exc:
        return generate_http_error_response(status_code=exc.code, message=exc.message)
    except Exception as exc:
        return generate_http_error_response(
            status_code=500,
            message=f"Failed to update ingestion {ingestion_id} {exc}",
        )


@unstructure_router.post("/ingestion/{ingestion_id}/jobs", tags=["Ingestion Jobs"])
async def create_ingestion_job_route(ingestion_id: str, job_data: IngestionJobRequest):
    try:
        data = await schedule_ingestion_task(ingestion_id)
        return generate_http_success_response(200, "", data)
    except CustomBaseException as exc:
        return generate_http_error_response(status_code=exc.code, message=exc.message)
    except Exception as exc:
        return generate_http_error_response(
            status_code=500,
            message=f"Failed to run ingestion {ingestion_id} {exc}",
        )


@unstructure_router.patch(
    "/ingestion/{ingestion_id}/jobs/{job_id}", tags=["Ingestion Jobs"]
)
async def update_ingestion_job_route(
    ingestion_id: str, job_id: str, job_data: IngestionJobUpdateRequest
):
    try:
        data = {}

        db = DatabaseManager()
        job_details = await db.get_ingestion_job_by_id(ingestion_id, job_id)
        current_status = job_details.get("status")
        if current_status in [
            IngestionJobStatus.Cancelled.value,
            IngestionJobStatus.Rejected.value,
        ]:
            return generate_http_success_response(
                400, f"No action allowed. ingestion Status: {current_status}", {}
            )

        status = job_data.status
        if status.lower() != IngestionJobStatus.Cancelled.value.lower():
            return generate_http_success_response(
                400, "Unsupported Action on Ingestion Job.", {}
            )

        await db.update_ingestion_job(
            job_id, {"status": IngestionJobStatus.Cancelled.value}
        )
        logger.info(f"Ingestion job {job_id} status updated to CANCELLED")
        job_pid = job_details.get("job_pid")
        if job_pid:
            kill_process(job_pid)
            logger.info(f"Killed running job PID: {job_pid}")
        await db.cancel_ingestion_job_and_delete_files(ingestion_id, job_id)
        logger.info(f"Audit logs updated: {job_id}, {IngestionJobStatus.Cancelled.value}")
        #  update all file/table audit logs with profile-pending status/warehouse pending status as cancelled
        #   Delete all the files from minio using minio file
        return generate_http_success_response(200, "Ingestion cancelled successfully", data)
    except CustomBaseException as exc:
        return generate_http_error_response(status_code=exc.code, message=exc.message)
    except Exception as exc:
        return generate_http_error_response(
            status_code=500,
            message=f"Failed to run ingestion {ingestion_id} {exc}",
        )


@unstructure_router.get(
    "/ingestion/{ingestion_id}/jobs/{job_id}", tags=["Ingestion Jobs"]
)
async def get_ingestion_job_route(ingestion_id: str, job_id: str):
    try:
        db = DatabaseManager()
        result = await db.get_ingestion_job_by_id(ingestion_id, job_id)
        return generate_http_success_response(200, "Jobs fetched successfully", result)
    except CustomBaseException as exc:
        return generate_http_error_response(status_code=exc.code, message=exc.message)
    except Exception as exc:
        logger.error(f"Unexpected error in get_ingestion_job_route: {exc}")
        return generate_http_error_response(
            status_code=500,
            message=f"Failed to fetch ingestion {ingestion_id} {exc}",
        )


@unstructure_router.get("/ingestion/{ingestion_id}/jobs", tags=["Ingestion Jobs"])
async def get_ingestion_jobs_route(ingestion_id: str, start: int = 0, limit: int = 100):
    try:
        db = DatabaseManager()
        data = await db.get_ingestion_jobs(ingestion_id, start=start, limit=limit)
        logger.info(f"{data}")
        return generate_http_success_response(200, "", data)
    except CustomBaseException as exc:
        return generate_http_error_response(status_code=exc.code, message=exc.message)
    except Exception as exc:
        return generate_http_error_response(
            status_code=500,
            message=f"Failed to fetch Ingestion Jobs. Ingestion id: {ingestion_id}. {exc}",
        )


@unstructure_router.get(
    "/ingestion_logs/{ingestion_id}/{job_id}", tags=["Ingestion Logs"]
)
async def get_ingestion_logs_route(
    ingestion_id: str, job_id: str, no_of_lines: int = 100, download: bool = False
):
    try:
        filepath = f".log/{ingestion_id}/{job_id}.log"

        if not file_exists(filepath):
            return generate_http_error_response(200, "No log found", [])

        resp = []
        if download:
            minio_client = MinioClient()
            minio_filepath = f"ingestion_logs/{ingestion_id}/{job_id}.log"
            minio_client.upload_file(filepath, minio_filepath)
            log_download_url = minio_client.get_log_download_url(minio_filepath)
            resp.append(log_download_url)

        else:
            resp = read_file_last_n_lines(filepath, no_of_lines)

        return generate_http_success_response(200, "", resp)
    except CustomBaseException as exc:
        return generate_http_error_response(status_code=exc.code, message=exc.message)
    except Exception as exc:
        return generate_http_error_response(
            status_code=500,
            message=f"Failed to fetch Ingestion Jobs. Ingestion id: {ingestion_id}. {exc}",
        )


@unstructure_router.post("/dsr", tags=["dsr"])
async def create_dsr_request(dsr: DSRCreateRequest):
    try:
        data = dsr.model_dump()
        logger.info(f"{data}")
        db = DatabaseManager()
        dsr_id = str(uuid4())
        data["dsr_id"] = dsr_id
        await schedule_dsr_task(dsr_id)
        data = await db.create_dsr_request(data)
        return generate_http_success_response(200, "", data)
    except CustomBaseException as exc:
        return generate_http_error_response(status_code=exc.code, message=exc.message)
    except Exception as exc:
        return generate_http_error_response(
            status_code=500, message=f"Failed to create DSR request. {exc}"
        )

@unstructure_router.get("/dsr/{dsr_id}", tags=["dsr"])
async def get_dsr_request(dsr_id: str):
    try:
        db = DatabaseManager()

        # Check if dsr_id exists
        exists = await db.check_dsr_exists(dsr_id)
        if not exists:
            return generate_http_error_response(
                status_code=400,
                message=f"Invalid DSR ID: {dsr_id} does not exist",
            )

        data = await db.get_dsr_request(dsr_id)

        return generate_http_success_response(200, "", data)
    except CustomBaseException as exc:
        return generate_http_error_response(status_code=exc.code, message=exc.message)
    except Exception as exc:
        logger.error(f"Unexpected error in get_dsr_request: {exc}")
        return generate_http_error_response(
            status_code=500, message=f"Failed to fetch DSR request: {exc}"
        )



@unstructure_router.get("/dsr/{dsr_id}/status", tags=["dsr"])
async def get_dsr_request_status(dsr_id: str):
    try:
        db = DatabaseManager()
        exists = await db.check_dsr_exists(dsr_id)
        if not exists:
            return generate_http_error_response(
                status_code=400,
                message=f"Invalid DSR ID: {dsr_id} does not exist",
            )

        data = await db.get_dsr_request_status(dsr_id)

        return generate_http_success_response(200, "", data)
    except CustomBaseException as exc:
        return generate_http_error_response(status_code=exc.code, message=exc.message)
    except Exception as exc:
        logger.error(f"Unexpected error in get_dsr_request_status: {exc}")
        return generate_http_error_response(
            status_code=500, message=f"Failed to fetch DSR status: {exc}"
        )


@unstructure_router.get("/dsr", tags=["dsr"])
async def get_dsr_requests(start: int = 0, limit: int = 100):
    try:
        db = DatabaseManager()
        data = await db.get_dsr_requests(start=start, limit=limit)
        return generate_http_success_response(200, "", data)
    except CustomBaseException as exc:
        return generate_http_error_response(status_code=exc.code, message=exc.message)
    except Exception as exc:
        logger.error(f"Unexpected error in get_dsr_requests: {exc}")
        return generate_http_error_response(
            status_code=500, message=f"Failed to fetch DSR data: {exc}"
        )


@unstructure_router.get("/dsr/request/{request_id}", tags=["dsr"])
async def get_dsr_by_request_id(request_id: str):
    try:
        db = DatabaseManager()
        data = await db.get_dsr_by_request_id(request_id)

        return generate_http_success_response(200, "", data)

    except CustomBaseException as exc:
        return generate_http_error_response(status_code=exc.code, message=exc.message)
    except Exception as exc:
        logger.error(f"Unexpected error in get_dsr_by_request_id: {exc}")
        return generate_http_error_response(
            status_code=500,
            message="An unexpected error occurred while fetching DSR data",
        )

@unstructure_router.get("/dsr/{dsr_id}/pii-matching-tables", tags=["dsr"])
async def fetch_dsr_pii_matching_tables(dsr_id: str):
    db = DatabaseManager()
    try:
        # Step 1: check if DSR exists
        exists = await db.check_dsr_exists(dsr_id)
        if not exists:
            return generate_http_error_response(
                status_code=404,
                message=f"DSR with {dsr_id} not found"
            )

        # Step 2: fetch pii matching tables
        tables = await db.get_dsr_pii_matching_tables(dsr_id)

        if not tables:
            return generate_http_success_response(
                200,
                f"No PII matching tables found yet for {dsr_id}",
                []
            )

        return generate_http_success_response(
            200,
            f"PII matching tables for dsr_id={dsr_id}",
            tables
        )

    except CustomBaseException as exc:
        return generate_http_error_response(
            status_code=exc.code,
            message=exc.message
        )
    except Exception as exc:
        logger.error(f"Unexpected error in fetch_dsr_pii_matching_tables: {exc}")
        return generate_http_error_response(
            status_code=500,
            message=f"Failed to fetch PII matching tables: {exc}"
        )
