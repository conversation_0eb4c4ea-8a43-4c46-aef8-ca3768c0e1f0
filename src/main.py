from fastapi import <PERSON>AP<PERSON>
from fastapi.exceptions import RequestValidationError
from src.routes.unstructure_routes import unstructure_router
from src.ui.routes import dd_router
from src.middleware.middleware import CustomCORSMiddleware, GatekeeperMiddleware
from src.utils.database import Database
from src.models.ingestion_models import *
from src.utils.helpers import generate_http_error_response, create_folder
from src.utils.logger import get_exception_logger
from src.common.config import (
    WORKFLOW_SCHEDULE_FOLDER,
    TEMP_DATA_DIR,
    WAREHOUSE_FOLDER,
    WEB_ROOT_PATH,
)

logger = get_exception_logger()

# CORS settings
allow_origins = ["*"]
allow_methods = ["*"]
allow_headers = ["*"]
allow_credentials = False


def init():
    try:
        create_folder(f"{WORKFLOW_SCHEDULE_FOLDER}/ingestion")
        create_folder(f"{WORKFLOW_SCHEDULE_FOLDER}/dsr")
        create_folder(TEMP_DATA_DIR)
        create_folder(WAREHOUSE_FOLDER)
        db = Database()
        db.create_tables()
    except Exception as e:
        logger.critical("Initialization failed..................", exc_info=e)


init()

app = FastAPI(
    title="Ingestion",
    description="API for database scan",
    version="0.0.1",
    root_path=WEB_ROOT_PATH,
)


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request, exc):
    errors = ", ".join([f"{err['loc'][-1]}: {err['msg']}" for err in exc.errors()])
    logger.error(f"{exc.errors()}")
    logger.error(errors)
    return generate_http_error_response(status_code=422, message=errors)


# Add CORS Middleware
app.add_middleware(
    CustomCORSMiddleware,
    allow_origins=allow_origins,
    allow_methods=allow_methods,
    allow_headers=allow_headers,
    allow_credentials=allow_credentials,
)

# Add Gatekeeper Middleware
app.add_middleware(GatekeeperMiddleware)

# Include routers
app.include_router(unstructure_router, prefix="/unstructure")
app.include_router(dd_router, prefix="/dd")
