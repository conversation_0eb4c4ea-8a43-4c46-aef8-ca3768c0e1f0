import uuid
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy import (
    Column,
    ForeignKey,
    String,
    JSON,
    DateTime,
    Boolean,
    func,
    Enum,
    ARRAY,
    Text,
    Integer,
    Index
)
from uuid import uuid4
from sqlalchemy.dialects.postgresql import UUID
from src.common.config import DB_SCHEMA
from sqlalchemy.orm import validates, relationship
from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime
from src.utils.database import Base
from src.common.constants import (
    ServiceTypes,
    ServiceStatus,
    IngestionJobStatus,
    ServiceProviders,
    IngestionStatus,
    UserRoleEnum,
    UserStatus,
    StatusEnum,
    DSRJobStatus,
    ShareMethod,
    ShareTarget,
    AssetStatus
)


class Microsoft365Credentials(BaseModel):
    client_id: str
    client_secret: str
    tenant_id: str
    include_emails: Optional[str]
    exclude_emails: Optional[str]


class S3Credentials(BaseModel):
    access_key: str = ""
    secret_key: str = ""
    region: str
    buckets: List[str] = Field(default_factory=list)


class PostgresCredentials(BaseModel):
    auth_type: str
    host: str
    port: int
    user: str
    dbname: str
    password: str
    sample_count: int
    databases: List[str] = []
    region: Optional[str] = None


class MySQLCredentials(BaseModel):
    auth_type: str
    host: str
    port: int
    user: str
    dbname: str
    password: str
    sample_count: int
    databases: List[str] = []
    region: Optional[str] = None


class MSSQLCredentials(BaseModel):
    auth_type: str
    host: str
    port: int
    user: str
    dbname: str
    password: str
    sample_count: int
    databases: List[str] = []
    region: Optional[str] = None


class SQLiteCredentials(BaseModel):
    auth_type: str = Field(default="basic")
    host: str = Field(default="localhost")
    port: int = Field(default=0)
    user: str = Field(default="")
    password: str = Field(default="")
    dbname: str
    sample_count: int = Field(default=10)
    databases: List[str] = Field(default_factory=list)
    region: Optional[str] = None
    db_mode: Optional[str] = Field(default=":memory:")
    readonly: Optional[bool] = Field(default=False)


class GoogleWorkspaceCredentials(BaseModel):
    type: str = Field("service_account")
    project_id: str
    private_key_id: str
    private_key: str
    client_email: str
    client_id: str
    auth_uri: str = Field("https://accounts.google.com/o/oauth2/auth")
    token_uri: str = Field("https://oauth2.googleapis.com/token")
    auth_provider_x509_cert_url: str = Field(
        "https://www.googleapis.com/oauth2/v1/certs"
    )
    client_x509_cert_url: str
    admin_email: str


class AzureDataLakeCredentials(BaseModel):
    tenant_id: str
    client_id: str
    client_secret: str
    storage_account_name: str
    container_name: str


class DirectoryScanCredentials(BaseModel):
    directory_path: str


class User(Base):
    __tablename__ = "users"
    user_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username = Column(String(256), nullable=False)
    email = Column(String(256), nullable=False)
    phone = Column(String(256), nullable=False)
    full_name = Column(String(255), nullable=True)
    hashed_password = Column(String(255), nullable=False)
    status = Column(String(255), server_default=UserStatus.Active.value)
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    role = Column(Enum(UserRoleEnum), default=UserRoleEnum.VIEWER, nullable=False)

    # Profile information
    department = Column(String(100))
    job_title = Column(String(100))
    
    # Security settings
    mfa_enabled = Column(Boolean, default=False, nullable=False)
    mfa_secret = Column(String(255))
    last_login = Column(DateTime(timezone=True))
    password_changed_at = Column(DateTime(timezone=True))
    failed_login_attempts = Column(Integer, default=0)
    locked_until = Column(DateTime(timezone=True))

    sessions = relationship("UserSession", back_populates="user", cascade="all, delete-orphan")

    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    def as_dict(self):
        return {
            column.name: (
                getattr(self, column.name).isoformat()
                if isinstance(getattr(self, column.name), datetime)
                else getattr(self, column.name)
            )
            for column in self.__table__.columns
        }

class UserRole(Base):
    """User role definitions and permissions."""
    
    __tablename__ = "user_roles"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(50), unique=True, nullable=False)
    description = Column(Text)
    permissions = Column(JSONB, nullable=False, default=list)
    is_system_role = Column(Boolean, default=False, nullable=False)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    def __repr__(self):
        return f"<UserRole(id={self.id}, name={self.name})>"

class UserSession(Base):
    """User session tracking for security and audit purposes."""
    
    __tablename__ = "user_sessions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True),ForeignKey(f"{DB_SCHEMA}.users.user_id"),nullable=False)
    refresh_token = Column(String(512), unique=True, nullable=False, index=True)
    
    # Session metadata
    ip_address = Column(String(45))  # IPv6 compatible
    user_agent = Column(Text)
    device_info = Column(JSONB)
    location = Column(JSONB)
    
    # Session lifecycle
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    last_activity = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    revoked_at = Column(DateTime(timezone=True))
    revoked_reason = Column(String(100))
    
    # Relationships
    user = relationship("User", back_populates="sessions")
    
    def __repr__(self):
        return f"<UserSession(id={self.id}, user_id={self.user_id}, active={self.is_active})>"


class UserPreferences(Base):
    """User preferences and settings."""
    
    __tablename__ = "user_preferences"
    #  __table_args__ = {"schema": DB_SCHEMA}
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True),ForeignKey(f"{DB_SCHEMA}.users.user_id"),nullable=False)
    # UI preferences
    theme = Column(String(20), default="light")
    language = Column(String(10), default="en")
    timezone = Column(String(50), default="UTC")
    
    # Notification preferences
    email_notifications = Column(Boolean, default=True)
    push_notifications = Column(Boolean, default=True)
    notification_frequency = Column(String(20), default="immediate")
    
    # Dashboard preferences
    dashboard_layout = Column(JSONB, default=dict)
    default_filters = Column(JSONB, default=dict)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    user = relationship("User", backref="preferences")
    
    def __repr__(self):
        return f"<UserPreferences(id={self.id}, user_id={self.user_id})>"

class Service(Base):
    __tablename__ = "services"
    __table_args__ = {"schema": DB_SCHEMA}
    service_name = Column(String(256), primary_key=True, index=True)
    description = Column(String(255), nullable=True)
    service_type = Column(String(255), nullable=False)  # MS_365, AWS_S3,..
    credentials = Column(JSON, nullable=False)
    status = Column(String(255), server_default=ServiceStatus.Active.value)
    # created_by = Column(String(255), ForeignKey("users.user_id"))
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    ingestions = relationship("Ingestion", back_populates="service")
    location = Column(String(255), nullable=True)
    data_owner = Column(String(255), nullable=True)
    data_steward = Column(String(255), nullable=True)

    @validates("credentials")
    def validate_credentials(self, key, value):
        """Validate the credentials structure"""
        if not isinstance(value, dict):
            raise ValueError("Credentials must be a dictionary")

        if self.service_type.lower() == ServiceTypes.MS_365_ONEDRIVE.value:
            Microsoft365Credentials(**value)
        elif self.service_type.lower() == ServiceTypes.MS_365_SHAREPOINT.value:
            Microsoft365Credentials(**value)
        elif self.service_type.lower() == ServiceTypes.MS_365_OUTLOOK.value:
            Microsoft365Credentials(**value)
        elif self.service_type.lower() == ServiceTypes.MS_365_TEAMS.value:
            Microsoft365Credentials(**value)
        elif self.service_type.lower() == ServiceTypes.AWS_S3.value:
            S3Credentials(**value)
        elif self.service_type.lower() == ServiceTypes.GOOGLE_GMAIL.value:
            GoogleWorkspaceCredentials(**value)
        elif self.service_type.lower() == ServiceTypes.GOOGLE_DRIVE.value:
            GoogleWorkspaceCredentials(**value)
        elif self.service_type.lower() == ServiceTypes.AZURE_DATA_LAKE.value:
            AzureDataLakeCredentials(**value)
        elif self.service_type.lower() == ServiceTypes.POSTGRES.value:
            PostgresCredentials(**value)
        elif self.service_type.lower() == ServiceTypes.MYSQL.value:
            MySQLCredentials(**value)
        elif self.service_type.lower() == ServiceTypes.MSSQL.value:
            MSSQLCredentials(**value)
        elif self.service_type.lower() == ServiceTypes.SQLITE.value:
            SQLiteCredentials(**value)
        elif self.service_type.lower() == ServiceTypes.DIRECTORY_SCAN.value:
            DirectoryScanCredentials(**value)
        else:
            raise ValueError("Unknown service type")

        return value

    def as_dict(self):
        return {
            column.name: (
                getattr(self, column.name).isoformat()
                if isinstance(getattr(self, column.name), datetime)
                else getattr(self, column.name)
            )
            for column in self.__table__.columns
        }


class Ingestion(Base):
    __tablename__ = "ingestions"
    __table_args__ = {"schema": DB_SCHEMA}
    ingestion_id = Column(String(256), primary_key=True, index=True)
    service_name = Column(
        String(256), ForeignKey(f"{DB_SCHEMA}.services.service_name"), index=True
    )
    ingestion_type = Column(String(256), nullable=False)
    status = Column(String(255), server_default=IngestionStatus.Active.value)
    schedule = Column(String(255))  # hourly, weekly, daily, monthly
    profile_scan = Column(Boolean, default=False)
    start_date = Column(DateTime, server_default=func.now())
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    service = relationship("Service", back_populates="ingestions")

    def as_dict(self):
        return {
            column.name: (
                getattr(self, column.name).isoformat()
                if isinstance(getattr(self, column.name), datetime)
                else getattr(self, column.name)
            )
            for column in self.__table__.columns
        }


class IngestionJob(Base):
    __tablename__ = "ingestion_jobs"
    __table_args__ = {"schema": DB_SCHEMA}
    job_id = Column(String(256), primary_key=True, index=True)
    ingestion_id = Column(
        String(256), ForeignKey(f"{DB_SCHEMA}.ingestions.ingestion_id"), index=True
    )
    job_pid = Column(Integer)
    status = Column(String(255), server_default=IngestionJobStatus.Queued.value)
    errors = Column(String(255))
    start_at = Column(DateTime, server_default=func.now())
    end_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    def as_dict(self):
        return {
            column.name: (
                getattr(self, column.name).isoformat()
                if isinstance(getattr(self, column.name), datetime)
                else getattr(self, column.name)
            )
            for column in self.__table__.columns
        }


class TableAuditLog(Base):
    __tablename__ = "table_audit_logs"

    correlation_id = Column(String(255), nullable=False, index=True)
    service_name = Column(String(255), nullable=False)
    service_type = Column(String(255), nullable=False)
    service_provider = Column(String(255), nullable=False)
    sub_service = Column(String(255), nullable=False)

    table_uri = Column(String(512), primary_key=True)
    db_name = Column(String(255), nullable=False)
    table_name = Column(String(255), nullable=False)

    ingestion_id = Column(String(255), nullable=True)
    job_id = Column(String(255), nullable=True)
    error_message = Column(Text)

    ingestion_start_time = Column(DateTime)
    ingestion_status = Column(Enum(StatusEnum, name="status_enum"), nullable=False)
    ingestion_end_time = Column(DateTime)

    profile_start_time = Column(DateTime)
    profile_status = Column(Enum(StatusEnum, name="status_enum"), nullable=True)
    profile_end_time = Column(DateTime)
    minio_file = Column(String(255))

    warehouse_start_time = Column(DateTime)
    warehouse_status = Column(Enum(StatusEnum, name="status_enum"), nullable=True)
    warehouse_end_time = Column(DateTime)

    extra_metadata = Column("metadata", JSON)

    def as_dict(self):
        return {
            column.name: (
                getattr(self, column.name).isoformat()
                if isinstance(getattr(self, column.name), datetime)
                else getattr(self, column.name)
            )
            for column in self.__table__.columns
        }


class FileAuditLog(Base):
    __tablename__ = "file_audit_logs"

    correlation_id = Column(String(255), nullable=False, index=True)
    service_name = Column(String(255), nullable=False)
    service_type = Column(String(255), nullable=False)
    service_provider = Column(String(255), nullable=False)
    sub_service = Column(String(255), nullable=False)
    file_key = Column(String(512), nullable=False)
    file_uri = Column(String(512), primary_key=True)
    file_type = Column(String(512))

    ingestion_id = Column(String(255), nullable=True)
    job_id = Column(String(255), nullable=True)
    error_message = Column(Text)

    ingestion_start_time = Column(DateTime)
    ingestion_status = Column(Enum(StatusEnum, name="status_enum"), nullable=False)
    ingestion_end_time = Column(DateTime)

    profile_start_time = Column(DateTime)
    profile_status = Column(Enum(StatusEnum, name="status_enum"), nullable=True)
    profile_end_time = Column(DateTime)
    minio_file = Column(String(255))

    warehouse_start_time = Column(DateTime)
    warehouse_status = Column(Enum(StatusEnum, name="status_enum"), nullable=True)
    warehouse_end_time = Column(DateTime)

    extra_metadata = Column("metadata", JSON)

    def as_dict(self):
        return {
            column.name: (
                getattr(self, column.name).isoformat()
                if isinstance(getattr(self, column.name), datetime)
                else getattr(self, column.name)
            )
            for column in self.__table__.columns
        }


class AuditEvent(Base):
    __tablename__ = "audit_event"

    correlation_id = Column(String(255), nullable=False, index=True)
    event_id = Column(String(255), primary_key=True)
    event_name = Column(String(255), nullable=False)
    msg = Column(String(255), nullable=False)
    extra_metadata = Column("extra_metadata", JSON)
    timestamp = Column(DateTime, server_default=func.now())

    def as_dict(self):
        return {
            column.name: (
                getattr(self, column.name).isoformat()
                if isinstance(getattr(self, column.name), datetime)
                else getattr(self, column.name)
            )
            for column in self.__table__.columns
        }


class DSR(Base):
    __tablename__ = "data_subject_request"
    dsr_id = Column(String(255), primary_key=True)
    request_id = Column(String(255))
    request_type = Column(String(255))
    customer_id = Column(String(255))
    # form_id = Column(String(255))
    search_data = Column(JSON, nullable=False)
    personal_data = Column(JSON, nullable=True)
    pii_matching_tables = Column(JSON, nullable=True)  
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    status = Column(String(255), server_default=DSRJobStatus.Queued.value)
    error = Column(String(255))
    logs = Column(ARRAY(String), default=[])
    result_file = Column(String(255))
    result_url = Column(String(1024))
    log_file_content = Column(Text)
    start_at = Column(DateTime)
    end_at = Column(DateTime)

    def as_dict(self):
        return {
            column.name: (
                getattr(self, column.name).isoformat()
                if isinstance(getattr(self, column.name), datetime)
                else getattr(self, column.name)
            )
            for column in self.__table__.columns
        }

class AccessControls(Base):
    __tablename__ = "access_controls"
    __allow_unmapped__ = True  

    id = Column(String(255), primary_key=True)
    asset_name = Column(String(255))
    user_or_role = Column(String(255))
    role = Column(String(255))
    access = Column(String(255))
    user_type = Column(String(255))
    group = Column(String(255))
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    def as_dict(self):
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)  # Only ONE call per column
            if isinstance(value, datetime):
                result[column.name] = value.isoformat()
            else:
                result[column.name] = value
        return result


class AssetsDetails(Base):
    __tablename__ = "assets_details"
    asset_id = Column(String(255), primary_key=True)
    asset_name = Column(String(255), index=True)
    service_provider = Column(Enum(ServiceProviders))
    type = Column(String(255))
    category = Column(String(255), index=True)
    location = Column(String(255))
    owner = Column(String(255))
    security = Column(String(255))
    size = Column(String(255))
    count = Column(String(255))
    access_category = Column(String(255),nullable=True)
    last_accessed = Column(String(50), nullable=True)
    
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    custom_tags = Column(ARRAY(String), nullable=True, default=None)
    service_name = Column(String(255), nullable=True, default=None)
    custodian = Column(String(255), nullable=True, default=None)
    description = Column(Text, nullable=True, default=None)
    steward = Column(String(255), nullable=True, default=None)
    linked_policy = Column(ARRAY(String), nullable=True, default=None)
    dept_tags = Column(ARRAY(String), nullable=True, default=None)
    custom_name = Column(String(255), nullable=True, default=None)
    status = Column(String(255), server_default=AssetStatus.Active.value)
    sharing_type = Column(Enum(ShareTarget, values_callable=lambda obj: [e.value for e in obj]), nullable=True, default=None)
    sharing_method = Column(Enum(ShareMethod, values_callable=lambda obj: [e.value for e in obj]), nullable=True, default=None)
    __table_args__ = (
        Index("ix_assetsdetails_asset_name_category", "asset_name", "category"),
    )
    

    def as_dict(self):
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)  # Only ONE call per column
            if isinstance(value, datetime):
                result[column.name] = value.isoformat()
            else:
                result[column.name] = value
        return result    
    

class GrafanaServiceAccount(Base):
    __tablename__ = "grafana_service_accounts"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    grafana_url = Column(String, nullable=False)
    token = Column(String, nullable=False)
    org_id = Column(Integer, nullable=True)
    dashboards = Column(JSON, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), server_default=func.now())
    deleted_at = Column(DateTime(timezone=True), nullable=True)

    def as_dict(self):
        return {c.name: getattr(self, c.name) for c in self.__table__.columns}