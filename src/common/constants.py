# constants

from fastapi import status
from enum import Enum

HTTP_200 = status.HTTP_200_OK
HTTP_201 = status.HTTP_201_CREATED
HTTP_400 = status.HTTP_400_BAD_REQUEST
HTTP_404 = status.HTTP_404_NOT_FOUND
HTTP_401 = status.HTTP_401_UNAUTHORIZED
HTTP_403 = status.HTTP_403_FORBIDDEN
HTTP_409 = status.HTTP_409_CONFLICT
HTTP_408 = status.HTTP_408_REQUEST_TIMEOUT
HTTP_500 = status.HTTP_500_INTERNAL_SERVER_ERROR

HTTP_BAD_REQUEST = "Bad Request"
SUCCESS = "SUCCESS"
FAILED = "FAILED"
STARTED = "STARTED"
RUNNING = "RUNNING"


class DBTypes(str, Enum):
    POSTGRES = "postgres"
    MYSQL = "mysql"
    ORACLE = "oracle"
    MSSQL = "mssql"
    SQLITE = "sqlite"
    DATABRICKS = "databricks"
    SNOWFLAKE = "snowflake"
    SALESFORCE = "salesforce"
    ORACLE11G = "oracle11g"
    MONGODB = "mongodb"
    TCS_CHROMA = "tcs_chroma"
    ONEX = "onex"
    SAP_ERP = "sap_erp"
    HIKVISION_FR = "hikvision_fr"

class AssetStatus(str,Enum):
    Active = "Active"
    Inactive = "Inactive"
    Stale = "Stale"

class ShareTarget(str, Enum):
    """Enum for sharing_type - who is data shared with"""
    PUBLIC = "Public"
    INTERNAL = "Internal"
    THIRD_PARTY = "Third Party"
    PARTNER = "Partner"
    VENDOR = "Vendor"
    CUSTOMER = "Customer"
    CROSS_REGION = "Cross-Region"
    CROSS_ORG = "Cross-Org"
    REGULATOR = "Regulator"
    EMPLOYEE = "Employee"

class ShareMethod(str, Enum):
    """Enum for sharing_method - how is data shared"""
    SECURE_SHARE = "secure_share"
    DIRECT_SHARE = "direct_share"
    PUBLIC_SHARE = "public_share"
    API_SHARE = "api_share"
    FEDERATED_ACCESS = "federated_access"
    FILE_EXPORT = "file_export"
    LINK_SHARE = "link_share"
    FDW_SHARE = "fdw_share"
    MASKED_SHARE = "masked_share"
    TOKENIZED_SHARE = "tokenized_share"
    ANONYMIZED_SHARE = "anonymized_share"
    CONTRACTUAL_SHARE = "contractual_share"
    TRANSIT_SHARE = "transit_share"
    REPLICATION_SHARE = "replication_share"



class ServiceTypes(str, Enum):
    MS_365 = "microsoft_365"
    MS_365_ONEDRIVE = "ms_365_onedrive"
    MS_365_TEAMS = "ms_365_teams"
    MS_365_OUTLOOK = "ms_365_outlook"
    MS_365_SHAREPOINT = "ms_365_sharepoint"
    AWS_S3 = "aws_s3"
    GOOGLE_WORKSPACE = "google_workspace"
    AZURE_DATA_LAKE = "azure_data_lake"
    POSTGRES = "postgres"
    MYSQL = "mysql"
    MSSQL = "mssql"
    SQLITE = "sqlite"
    DIRECTORY_SCAN = "directory_scan"
    GOOGLE_GMAIL = "google_gmail"
    GOOGLE_DRIVE = "google_drive"
    SFTP = "sftp"
    ORACLE = "oracle"
    DATABRICKS = "databricks"
    NAS = "nas"
    SNOWFLAKE = "snowflake"
    SALESFORCE = "salesforce"
    ORACLE11G = "oracle11g"
    MONGODB = "mongodb"
    TCS_CHROMA = "tcs_chroma"
    ONEX = "onex"
    HAPPAY = "happay"
    SAP_ERP = "sap_erp"
    HIKVISION_FR = "hikvision_fr"


class ServiceProviders(str, Enum):
    M365 = "M365"
    AWS = "AWS"
    GCP = "GCP"
    AZURE = "AZURE"
    GWS = "GWS"
    LOCAL = "LOCAL"
    SFTP = "SFTP"
    SAP = "SAP"
    HIKVISION = "HIKVISION"


class LocalSubservice(str, Enum):
    POSTGRES = "POSTGRES"
    MYSQL = "MYSQL"
    MSSQL = "mssql"
    SQLITE = "sqlite"
    DIRECTORY_SCAN = "directory_scan"
    SFTP = "SFTP"
    ORACLE = "ORACLE"
    DATABRICKS = "DATABRICKS"
    NAS = "NAS"
    SNOWFLAKE = "SNOWFLAKE"
    SALESFORCE = "SALESFORCE"
    ORACLE11G = "ORACLE11G"
    MONGODB = "MONGODB"
    TCS_CHROMA = "TCS_CHROMA"
    ONEX = "ONEX"
    HAPPAY = "HAPPAY"
    SAP_ERP = "SAP_ERP"
    HIKVISION_FR = "HIKVISION_FR"



class AWSSubServices(str, Enum):
    AWS_S3 = "AWS_S3"
    AWS_RDS = "AWS_RDS"
    AWS_EC2 = "AWS_EC2"
    AWS_LAMBDA = "AWS_LAMBDA"
    AWS_EBS = "AWS_EBS"
    AWS_DYNAMODB = "AWS_DYNAMO_DB"


class AzureSubServices(str, Enum):
    AZURE_BLOB_STORAGE = "AZURE_BLOB_STORAGE"
    AZURE_SQL = "AZURE_SQL"
    AZURE_DATA_LAKE = "azure_data_lake"
    AZURE_VIRTUAL_MACHINE = "AZURE_VIRTUAL_MACHINE"


class GCPSubServices(str, Enum):
    GCP_CLOUD_STORAGE = "GCP_CLOUD_STORAGE"
    GCP_CLOUD_SQL = "GCP_CLOUD_SQL"
    GCP_COMPUTE_ENGINE = "GCP_COMPUTE_ENGINE"
    GCP_CLOUD_FUNCTIONS = "GCP_CLOUD_FUNCTIONS"
    
class SAPSubServices(str, Enum):
    SAP_ERP = "SAP_ERP"

class HikvisionSubServices(str, Enum):
    HIKVISION_FR = "HIKVISION_FR"


class GWSSubServices(str, Enum):
    GOOGLE_DRIVE = "GOOGLE_DRIVE"
    GOOGLE_GMAIL = "GOOGLE_GMAIL"
    GOOGLE_DOCS = "GOOGLE_DOCS"
    GOOGLE_MEET = "GOOGLE_MEET"
    GOOGLE_CHAT = "GOOGLE_CHAT"


class M365SubServices(str, Enum):
    MS_ONEDRIVE = "MS_ONEDRIVE"
    MS_OUTLOOK = "MS_OUTLOOK"
    MS_TEAMS = "MS_TEAMS"
    MS_SHAREPOINT = "MS_SHAREPOINT"


class UserStatus(str, Enum):
    Active = "Active"
    Inactive = "Inactive"


class ServiceStatus(str, Enum):
    Active = "Active"
    Inactive = "Inactive"


class IngestionStatus(str, Enum):
    Active = "Active"
    Inactive = "Inactive"
    Closed = "Closed"


class IngestionJobStatus(str, Enum):
    Queued = "Queued"
    Running = "Running"
    Failed = "Failed"
    Partial = "Partial"
    Completed = "Completed"
    Rejected = "Rejected"
    Cancelled = "Cancelled"


class ScanTypes(str, Enum):
    Infra = "infra"
    FileMetadata = "file_metadata"
    TableMetadata = "table_metadata"


class StatusEnum(str, Enum):
    INITIATED = "INITIATED"
    RUNNING = "RUNNING"
    FINISHED = "FINISHED"
    PARTIAL = "PARTIAL"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"


class AuditEventNames(str, Enum):
    IngestionStart = "IngestionStart"
    IngestionFail = "IngestionFail"
    IngestionEnd = "IngestionEnd"
    IngestionCancel = "IngestionCancel"

    ProfileQueue = "ProfileQueue"
    ProfileSkip = "ProfileSkip"
    ProfileStart = "ProfileStart"
    ProfileFail = "ProfileFail"
    ProfileEnd = "ProfileEnd"
    ProfileCancel = "ProfileCancel"

    WarehouseMetadataQueue = "WarehouseMetadataQueue"
    WarehouseMetadataStart = "WarehouseMetadataStart"
    WarehouseMetadataFail = "WarehouseMetadataFail"
    WarehouseMetadataEnd = "WarehouseMetadataEnd"
    WarehouseMetadataCancel = "WarehouseMetadataCancel"

    WarehousePIIQueue = "WarehousePIIQueue"
    WarehousePIIStart = "WarehousePIIStart"
    WarehousePIISkip = "WarehousePIISkip"
    WarehousePIIFail = "WarehousePIIFail"
    WarehousePIIEnd = "WarehousePIIEnd"
    WarehousePIICancel = "WarehousePIICancel"

    OCRQueue = "OCRQueue"
    OCRStart = "OCRStart"
    OCRFail = "OCRFail"
    OCREnd = "OCREnd"
    OCRCancel = "OCRCancel"


class SourceName(str, Enum):
    INGESTION = "INGESTION"
    WAREHOUSE = "WAREHOUSE"
    PROFILER = "PROFILER"
    OCR = "OCR"


class AuthTypes(str, Enum):
    IAM = "iam"
    BASIC = "basic"
    OAUTH = "oauth"


class DSRJobStatus(str, Enum):
    Queued = "Queued"
    Running = "Running"
    Failed = "Failed"
    Partial = "Partial"
    Completed = "Completed"
    Rejected = "Rejected"
    Cancelled = "Cancelled"


class MessageTyeps(str, Enum):
    FILEMETADATA = "file_metadata"
    TABLEMETADATA = "table_metadata"
    PII = "pii"

class AssetStatus(str,Enum):
    Active = "Active"
    Inactive = "Inactive"
    Stale = "Stale"

class ShareTarget(str, Enum):
    """Enum for sharing_type - who is data shared with"""
    PUBLIC = "Public"
    INTERNAL = "Internal"
    THIRD_PARTY = "Third Party"
    PARTNER = "Partner"
    VENDOR = "Vendor"
    CUSTOMER = "Customer"
    CROSS_REGION = "Cross-Region"
    CROSS_ORG = "Cross-Org"
    REGULATOR = "Regulator"
    EMPLOYEE = "Employee"

class ShareMethod(str, Enum):
    """Enum for sharing_method - how is data shared"""
    SECURE_SHARE = "secure_share"
    DIRECT_SHARE = "direct_share"
    PUBLIC_SHARE = "public_share"
    API_SHARE = "api_share"
    FEDERATED_ACCESS = "federated_access"
    FILE_EXPORT = "file_export"
    LINK_SHARE = "link_share"
    FDW_SHARE = "fdw_share"
    MASKED_SHARE = "masked_share"
    TOKENIZED_SHARE = "tokenized_share"
    ANONYMIZED_SHARE = "anonymized_share"
    CONTRACTUAL_SHARE = "contractual_share"
    TRANSIT_SHARE = "transit_share"
    REPLICATION_SHARE = "replication_share"

class UserRoleEnum(str,Enum):
    """User role enumeration."""
    VIEWER = "viewer"
    DATA_OWNER = "data_owner"
    COMPLIANCE_OFFICER = "compliance_officer"
    SECURITY_ANALYST = "security_analyst"
    ADMIN = "admin"
