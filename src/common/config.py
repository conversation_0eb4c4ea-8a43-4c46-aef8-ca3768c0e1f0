import os

WEB_ROOT_PATH = os.getenv("WEB_ROOT_PATH", "/")
LOG_LEVEL = os.getenv("LOG_LEVEL", "DEBUG")
LOG_DIRNAME = os.getenv("LOG_DIRNAME", ".log")
LOG_FILENAME = os.getenv("LOG_FILENAME", "")
LOG_DEFAULT_NAME = os.getenv("LOG_DEFAULT_NAME", "Ingestion Log")

DB_USER = os.getenv("DB_USER", "postgres")
DB_PASSWORD = os.getenv("DB_PASSWORD", "password")
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_SCHEMA = os.getenv("DB_SCHEMA", "public")
DB_PORT = int(os.getenv("DB_PORT", 5433))
DB_NAME = os.getenv("DB_NAME", "ingestion")
DB_POOL_SIZE = int(os.getenv("DB_POOL_SIZE", 10))
DB_MAX_OVERFLOW = int(os.getenv("DB_MAX_OVERFLOW", 5))
DB_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
MINIO_STORAGE_LIMIT_BYTES = int(
    os.getenv("MINIO_STORAGE_LIMIT_BYTES", 20 * 1024 * 1024 * 1024)
)
MINIMUM_REQ_STORAGE_FOR_INGESTION = int(
    os.getenv("MINIMUM_REQ_STORAGE_FOR_INGESTION", 5 * 1024 * 1024 * 1024)
)
INGESTION_RESTART_DURATION_IN_HOUR = int(
    os.getenv("INGESTION_RESTART_DURATION_IN_HOUR", 2)
)

PROFILE_MAX_SIZE_THRESHOLD = int(
    os.getenv("PROFILE_MAX_SIZE_THRESHOLD", 10 * 1024 * 1024)
)
RABBITMQ_USER = os.getenv("RABBITMQ_USER", "user")
RABBITMQ_PASSWORD = os.getenv("RABBITMQ_PASSWORD", "password")
RABBITMQ_HOST = os.getenv("RABBITMQ_HOST", "localhost")
RABBITMQ_PORT = int(os.getenv("RABBITMQ_PORT", 5672))


PROFILER_REQUEST_QUEUE = os.getenv("PROFILER_REQUEST_QUEUE", "profiler.request")
PROFILER_RESPONSE_QUEUE = os.getenv(
    "PROFILER_RESPONSE_QUEUE", "profiler.ingestion.response"
)
WAREHOUSE_REQUEST_QUEUE = os.getenv("WAREHOUSE_REQUEST_QUEUE", "warehouse.request")
AUDIT_LOG_QUEUE = os.getenv("AUDIT_LOG_QUEUE", "common.audit_log")


MINIO_ACCESS_KEY = os.getenv("MINIO_ACCESS_KEY", "minio")
MINIO_SECRET_KEY = os.getenv("MINIO_SECRET_KEY", "password")
MINIO_ENDPOINT = os.getenv("MINIO_ENDPOINT", "localhost:9008")
# MINIO_PUBLIC_ENDPOINT = os.getenv("MINIO_PUBLIC_ENDPOINT", "storage-dev.gotrust.tech")
MINIO_BUCKET_NAME = os.getenv("MINIO_BUCKET_NAME", "data-discovery")
MINIO_SECURE = os.getenv("MINIO_SECURE", "False").lower() == "true"

WEBHOOK_URL = os.getenv("WEBHOOK_URL", "")

CONCURRENT_LIMIT = int(os.getenv("CONCURRENT_LIMIT", 50))

TEMP_DATA_DIR = os.getenv("TEMP_DATA_DIR", "./temp")

WORKFLOW_INGESTION_SCRIPT = os.getenv(
    "WORKFLOW_SCRIPT", "src/ingestion/start_ingestion.py"
)
WORKFLOW_DSR_SCRIPT = os.getenv("WORKFLOW_DSR_SCRIPT", "src/ingestion/start_dsr.py")
WORKFLOW_DELAY = int(os.getenv("WORKFLOW_DELAY", 10))
WORKFLOW_SCHEDULE_FOLDER = os.getenv("WORKFLOW_SCHEDULE_FOLDER", "./.schedule")
UNSTRUCTURED_STORAGE_LIMIT_BYTES = int(
    os.getenv("UNSTRUCTURED_STORAGE_LIMIT_BYTES", 10 * 1024 * 1024 * 1024)
)
WAREHOUSE_FOLDER = os.getenv("WAREHOUSE_FOLDER", "./.warehouse")
PROFILE_PENDING = os.getenv("PROFILE_PENDING", "./.profile_pending")
WAREHOUSE_LOADER_DELAY = 2
WAREHOUSE_BASE_URL = os.getenv("WAREHOUSE_BASE_URL", "http://localhost:8006/warehouse")
PROFILER_BASE_URL = os.getenv("PROFILER_BASE_URL", "http://localhost:8007/profile")
PROFILE_MAX_SIZE_THRESHOLD = int(
    os.getenv("PROFILE_MAX_SIZE_THRESHOLD", 1024 * 1024 * 10)
)

PROFILE_SUPPORTED_FILE_TYPES = os.getenv(
    "PROFILE_SUPPORTED_FILE_TYPES",
    "txt,csv,json,pdf,xlsx,parquet,sql,zip,diff,pst,docx,docs,unknown,jpg,jpeg,png,key,crt,mail,ods,xls,log,html,doc,dbs,pem,spreadsheet,document",
).split(
    ","
)  # Convert comma-separated string to list
