from enum import Enum
from typing import List, Optional
from pydantic import BaseModel


class DSRRequestType(str, Enum):
    DELETION = "deletion"
    ADDITION = "addition"
    VIEW = "view"
    SEARCH = "search"
    UPDATE = "update"


class PIIItem(BaseModel):
    pii_type: Optional[str] = None
    value: str


class DSRCreateRequest(BaseModel):
    request_type: DSRRequestType
    request_id: str
    customer_id: str
    search_data: dict


class DSRRequest(BaseModel):
    request_type: DSRRequestType
    request_id: str
    customer_id: str
    form_id: str
    piis: List[PIIItem]
