from sqlalchemy.orm import validates
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from src.common.constants import (
    ServiceTypes,
    ServiceStatus,
    UserStatus,
    IngestionStatus,
    AuthTypes,
)


class Microsoft365Credentials(BaseModel):
    client_id: str
    client_secret: str
    tenant_id: str
    include_emails: Optional[str]
    exclude_emails: Optional[str]


class S3Credentials(BaseModel):
    access_key: str = ""
    secret_key: str = ""
    region: str
    buckets: List[str] = Field(default_factory=list)


class GoogleWorkspaceCredentials(BaseModel):
    type: str = Field("service_account")
    project_id: str
    private_key_id: str
    private_key: str
    client_email: str
    client_id: str
    auth_uri: str = Field("https://accounts.google.com/o/oauth2/auth")
    token_uri: str = Field("https://oauth2.googleapis.com/token")
    auth_provider_x509_cert_url: str = Field(
        "https://www.googleapis.com/oauth2/v1/certs"
    )
    client_x509_cert_url: str
    admin_email: str
    user_email: Optional[str] = None


class MySQLCredentials(BaseModel):
    auth_type: AuthTypes  # basic | iam
    host: str
    port: int
    user: str
    dbname: str
    password: str
    sample_count: int
    databases: List[str] = []
    region: Optional[str] = None


class PostgresCredentials(BaseModel):
    auth_type: AuthTypes  # basic | iam
    host: str
    port: int
    user: str
    dbname: str
    password: str
    sample_count: int
    databases: List[str] = []
    region: Optional[str] = None


class MSSQLCredentials(BaseModel):
    auth_type: str
    host: str
    port: int
    user: str
    dbname: str
    password: str
    sample_count: int
    databases: List[str] = []
    region: Optional[str] = None


class SQLiteCredentials(BaseModel):
    auth_type: str = Field(default=AuthTypes.BASIC.value)
    host: str = Field(default="localhost")
    port: int = Field(default=0)
    user: str = Field(default="")
    password: str = Field(default="")
    dbname: str
    sample_count: int = Field(default=10)
    databases: List[str] = Field(default_factory=list)
    region: Optional[str] = None
    db_mode: Optional[str] = Field(default=":memory:")
    readonly: Optional[bool] = Field(default=False)


class SnowflakeCredentials(BaseModel):
    user: str
    password: str
    account: str
    warehouse: str
    databases: List[str] = Field(default_factory=list)
    region: Optional[str] = None
    sample_count: int = Field(default=10)


class SalesforceCredentials(BaseModel):
    username: str
    password: str
    security_token: Optional[str] = None
    organization_id: str  # Required
    domain: str = Field(default="login")
    object_name: Optional[str] = None
    sample_count: int = Field(default=10)
    region: Optional[str] = None


class UserRequest(BaseModel):
    username: str
    email: str
    phone: str


class UserUpdateRequest(BaseModel):
    email: Optional[str] = None
    phone: Optional[str] = None


class UserStateRequest(BaseModel):
    status: UserStatus


class DirectoryScanCredentials(BaseModel):
    directory_path: str


class ServiceRequest(BaseModel):
    service_name: str
    description: Optional[str] = None
    service_type: str
    credentials: dict

    @validates("credentials")
    def validate_credentials(self, key, value):
        """Validate the credentials structure"""
        if not isinstance(value, dict):
            raise ValueError("Credentials must be a dictionary")

        if self.service_type.lower() == ServiceTypes.MS_365_ONEDRIVE.value:
            Microsoft365Credentials(**value)
        elif self.service_type.lower() == ServiceTypes.MS_365_OUTLOOK.value:
            Microsoft365Credentials(**value)
        elif self.service_type.lower() == ServiceTypes.MS_365_SHAREPOINT.value:
            Microsoft365Credentials(**value)
        elif self.service_type.lower() == ServiceTypes.MS_365_TEAMS.value:
            Microsoft365Credentials(**value)
        elif self.service_type.lower() == ServiceTypes.AWS_S3.value:
            S3Credentials(**value)
        elif self.service_type.lower() == ServiceTypes.GOOGLE_GMAIL.value:
            GoogleWorkspaceCredentials(**value)
        elif self.service_type.lower() == ServiceTypes.GOOGLE_DRIVE.value:
            GoogleWorkspaceCredentials(**value)
        elif self.service_type.lower() == ServiceTypes.GOOGLE_WORKSPACE.value:
            GoogleWorkspaceCredentials(**value)
        elif self.service_type.lower() == ServiceTypes.POSTGRES.value:
            PostgresCredentials(**value)
        elif self.service_type.lower() == ServiceTypes.MYSQL.value:
            MySQLCredentials(**value)
        elif self.service_type.lower() == ServiceTypes.DIRECTORY_SCAN.value:
            DirectoryScanCredentials(**value)
        elif self.service_type.lower() == ServiceTypes.SQLITE.value:
            SQLiteCredentials(**value)
        elif self.service_type.lower() == ServiceTypes.MSSQL.value:
            MSSQLCredentials(**value)
        elif self.service_type.lower() == ServiceTypes.SNOWFLAKE.value:
            SnowflakeCredentials(**value)
        elif self.service_type.lower() == ServiceTypes.SALESFORCE.value:
            SalesforceCredentials(**value)
        elif self.service_type.lower() == ServiceTypes.NAS.value:
            NASCredentials(**value)
        elif self.service_type.lower() == ServiceTypes.ORACLE11G.value:
            Oracle11gCredentials(**value)
        else:
            raise ValueError("Unknown service type")

        return value


class ServiceUpdateRequest(BaseModel):
    service_type: Optional[str] = None
    description: Optional[str] = None
    credentials: Optional[dict] = None

    @validates("credentials")
    def validate_credentials(self, key, value):
        """Validate the credentials structure"""
        if not isinstance(value, dict):
            raise ValueError("Credentials must be a dictionary")

        if not self.service_type:
            return value

        if self.service_type.lower() == ServiceTypes.MS_365_ONEDRIVE.value:
            Microsoft365Credentials(**value)
        elif self.service_type.lower() == ServiceTypes.MS_365_OUTLOOK.value:
            Microsoft365Credentials(**value)
        elif self.service_type.lower() == ServiceTypes.MS_365_SHAREPOINT.value:
            Microsoft365Credentials(**value)
        elif self.service_type.lower() == ServiceTypes.MS_365_TEAMS.value:
            Microsoft365Credentials(**value)
        elif self.service_type.lower() == ServiceTypes.AWS_S3.value:
            S3Credentials(**value)
        elif self.service_type.lower() == ServiceTypes.GOOGLE_GMAIL.value:
            GoogleWorkspaceCredentials(**value)
        elif self.service_type.lower() == ServiceTypes.GOOGLE_DRIVE.value:
            GoogleWorkspaceCredentials(**value)
        elif self.service_type.lower() == ServiceTypes.GOOGLE_WORKSPACE.value:
            GoogleWorkspaceCredentials(**value)
        elif self.service_type.lower() == ServiceTypes.POSTGRES.value:
            PostgresCredentials(**value)
        elif self.service_type.lower() == ServiceTypes.MYSQL.value:
            MySQLCredentials(**value)
        elif self.service_type.lower() == ServiceTypes.DIRECTORY_SCAN.value:
            DirectoryScanCredentials(**value)
        elif self.service_type.lower() == ServiceTypes.SQLITE.value:
            SQLiteCredentials(**value)
        elif self.service_type.lower() == ServiceTypes.MSSQL.value:
            MSSQLCredentials(**value)
        elif self.service_type.lower() == ServiceTypes.SNOWFLAKE.value:
            SnowflakeCredentials(**value)
        elif self.service_type.lower() == ServiceTypes.SALESFORCE.value:
            SalesforceCredentials(**value)
        elif self.service_type.lower() == ServiceTypes.NAS.value:
            NASCredentials(**value)
        elif self.service_type.lower() == ServiceTypes.ORACLE11G.value:
            Oracle11gCredentials(**value)
        else:
            raise ValueError("Unknown service type")

        return value


class ServiceStateRequest(BaseModel):
    status: ServiceStatus


class IngestionRequest(BaseModel):
    service_name: str
    ingestion_type: str
    schedule: str
    profile_scan: bool = False


class IngestionUpdateRequest(BaseModel):
    schedule: Optional[str] = None
    profile_scan: Optional[bool] = None


class IngestionStateRequest(BaseModel):
    status: IngestionStatus


class IngestionJobRequest(BaseModel):
    pass


class IngestionJobUpdateRequest(BaseModel):
    status: str


class PIIItem(BaseModel):
    type: str
    pii_type: str
    value: str
    pii_value: Optional[List[Any]] = None
    score: Optional[float] = 0


class ProfileStatusRequest(BaseModel):
    status_code: int
    message: str
    token: str
    status: str
    piis: List[PIIItem]


class PaginationQueryParams(BaseModel):
    page: int = 1
    page_size: int = 10
    search: Optional[str] = None


class NASCredentials(BaseModel):
    hostname: str
    port: int = Field(default=445)
    share_name: str
    root_path: str = Field(default="")
    username: str
    password: str


class Oracle11gCredentials(BaseModel):
    host: str
    port: int = Field(default=1521)
    user: str
    password: str
    sid: str
    is_sid: bool = Field(default=True)
    sample_count: int = Field(default=10)
    databases: Optional[List[str]] = None



class GrafanaServiceAccountCreate(BaseModel):
    name: str
    token: str
    org_id: Optional[int]