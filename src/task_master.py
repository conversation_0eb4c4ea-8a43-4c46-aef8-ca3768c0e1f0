import sys
import os
import time
import asyncio
from datetime import datetime

sys.path.insert(0, os.getcwd())

from src.utils.logger import get_task_logger
from src.utils.exceptions import ServerException, CustomBaseException
from src.ingestion.scheduler import (
    get_next_dsr_task,
    get_next_ingestion_tasks,
    schedule_ingestion_task,
)
from src.common.config import (
    WORKFLOW_DSR_SCRIPT,
    WORKFLOW_INGESTION_SCRIPT,
    WORKFLOW_DELAY,
)
from src.utils.process_util import launch_detached_process
from src.modules.repos import DatabaseManager
from src.common.constants import IngestionJobStatus
from src.utils.process_util import is_process_running

logger = get_task_logger()


async def handle_running_ingestion_tasks():
    db = DatabaseManager()
    running_tasks = await db.get_all_ingestion_jobs_by_status(
        IngestionJobStatus.Running.value
    )
    for task in running_tasks:
        ingestion_id = task.get("ingestion_id")
        job_id = task.get("job_id")
        job_pid = task.get("job_pid")
        if job_pid and is_process_running(job_pid):
            continue

        await db.update_ingestion_job(
            job_id=job_id, job_data={"status": IngestionJobStatus.Failed.value}
        )
        await schedule_ingestion_task(ingestion_id)


async def handle_ingestion_tasks():
    db = DatabaseManager()
    tasks = await get_next_ingestion_tasks()
    logger.info(f"Tasks: {tasks}")

    # Convert start_at to datetime before comparing
    tasks = [
        task
        for task in tasks
        if datetime.fromisoformat(task.get("start_at")) <= datetime.now()
    ]

    for task in tasks:
        ingestion_id = task.get("ingestion_id")
        job_id = task.get("job_id")
        if ingestion_id is None or job_id is None:
            continue

        os.environ["INGESTION_ID"] = ingestion_id
        os.environ["JOB_ID"] = job_id
        os.environ["LOG_DIRNAME"] = f".log/{ingestion_id}"
        os.environ["LOG_FILENAME"] = f"{job_id}.log"
        job_pid = launch_detached_process(WORKFLOW_INGESTION_SCRIPT)

        await db.update_ingestion_job(job_id=job_id, job_data={"job_pid": job_pid})
        time.sleep(5)


async def handle_dsr_tasks():
    dsr_id = get_next_dsr_task()
    if dsr_id:
        os.environ["DSR_ID"] = dsr_id
        os.environ["LOG_DIRNAME"] = ".log/dsr"
        os.environ["LOG_FILENAME"] = f"{dsr_id}.log"
        launch_detached_process(WORKFLOW_DSR_SCRIPT)
        time.sleep(5)


async def main():
    while True:
        try:
            await handle_running_ingestion_tasks()

            await handle_ingestion_tasks()

            await handle_dsr_tasks()

            logger.info(
                f"Task Master: No tasks available. Sleeping for {WORKFLOW_DELAY}"
            )
            time.sleep(WORKFLOW_DELAY)
            continue
        except CustomBaseException:
            time.sleep(WORKFLOW_DELAY)
        except Exception as e:
            logger.error("failed to launch process in detached mode", exc_info=e)
            time.sleep(WORKFLOW_DELAY)


if __name__ == "__main__":
    asyncio.run(main())
