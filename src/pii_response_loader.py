import sys
import os
import json
from pika.adapters.blocking_connection import BlockingChannel
from pika import spec
import time
from datetime import datetime

sys.path.insert(0, os.getcwd())

from src.utils.message_publisher import publish_message
from src.utils.rabbitmq_client import RabbitMQ
from src.common.config import PROFILER_RESPONSE_QUEUE, WAREHOUSE_REQUEST_QUEUE
from src.utils.logger import get_warehouse_logger
from src.common.constants import MessageTyeps, AuditEventNames, StatusEnum
from src.modules.audit_log import AuditLogger

logger = get_warehouse_logger()

audit_logger = AuditLogger()


def process_request(
    ch: BlockingChannel,
    method: spec.Basic.Deliver,
    properties: spec.BasicProperties,
    body: bytes,
):
    try:
        message = json.loads(body.decode())
        payload = message.get("payload")
        correlation_id = message.get("correlation_id")
        piis = payload.get("piis", [])
        partial = payload.get("partial")
        print(f"Pii data recieved: {message}")
        if len(piis) == 0:
            audit_logger.create_audit_event(
                correlation_id, AuditEventNames.WarehousePIISkip.value, "No PIIs Found"
            )
            ch.basic_ack(delivery_tag=method.delivery_tag)
            audit_logger.update_audit_log_util(
                correlation_id,
                {
                    "warehouse_end_time": datetime.now(),
                    "warehouse_status": StatusEnum.FINISHED.value,
                },
            )
            return None
        data = {"correlation_id": correlation_id, "piis": piis, "partial": partial}
        publish_message(
            correlation_id=correlation_id,
            queue_name=WAREHOUSE_REQUEST_QUEUE,
            message_type=MessageTyeps.PII.value,
            payload=data,
        )
        audit_logger.create_audit_event(
            correlation_id, AuditEventNames.WarehousePIIQueue.value, "No PIIs Found"
        )
        ch.basic_ack(delivery_tag=method.delivery_tag)
    except Exception as e:
        # Add logic to requeue it
        logger.error(f"Failed to process request: {e}", exc_info=e)
        ch.basic_ack(delivery_tag=method.delivery_tag)


def main():
    while True:
        try:
            rabbitmq = RabbitMQ()
            logger.info("Connection to RabbitMQ established successfully.")
            rabbitmq.consume(
                queue_name=PROFILER_RESPONSE_QUEUE, callback=process_request
            )
        except Exception as e:
            logger.error(f"Failed to messages from RabbitMQ: {e}", exc_info=e)

        time.sleep(5)


if __name__ == "__main__":
    main()
