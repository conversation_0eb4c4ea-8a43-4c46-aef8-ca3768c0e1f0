from time import sleep
import sys
import os
from typing import Tuple

sys.path.insert(0, os.getcwd())

from src.common.config import (
    WAREHOUSE_FOLDER,
    WAREHOUSE_BASE_URL,
    PROFILER_BASE_URL,
    WAREHOUSE_LOADER_DELAY,
)
from src.utils.helpers import (
    list_files,
    file_exists,
    rename_file,
    read_json_file,
    remove_file,
    send_request,
    create_folder,
)
from src.common.constants import (
    ServiceProviders,
    AWSSubServices,
    M365SubServices,
    ScanTypes,
    LocalSubservice,
    AzureSubServices,
    
)
from src.utils.logger import get_warehouse_logger

logger = get_warehouse_logger()


warehouse_endoints = {
    "ms_365_onedrive": {
        "url": "/ms_onedrive",
        "service_provider": ServiceProviders.M365.value,
        "sub_service": M365SubServices.MS_ONEDRIVE.value,
    },
    "ms_365_teams": {
        "url": "/ms_teams",
        "service_provider": ServiceProviders.M365.value,
        "sub_service": M365SubServices.MS_TEAMS.value,
    },
    "ms_365_outlook": {
        "url": "/ms_outlook",
        "service_provider": ServiceProviders.M365.value,
        "sub_service": M365SubServices.MS_OUTLOOK.value,
    },
    "ms_365_sharepoint": {
        "url": "/ms_sharepoint",
        "service_provider": ServiceProviders.M365.value,
        "sub_service": M365SubServices.MS_SHAREPOINT.value,
    },
    "aws_s3": {
        "url": "/aws_s3",
        "service_provider": ServiceProviders.AWS.value,
        "sub_service": AWSSubServices.AWS_S3.value,
    },
    "postgres": {
        "url": "/postgres",
        "service_provider": ServiceProviders.LOCAL.value,
        "sub_service": LocalSubservice.POSTGRES.value,
    },
    "mysql": {
        "url": "/mysql",
        "service_provider": ServiceProviders.LOCAL.value,
        "sub_service": LocalSubservice.MYSQL.value,
    },
    "mssql": {
        "url": "/mssql",
        "service_provider": ServiceProviders.LOCAL.value,
        "sub_service": LocalSubservice.MSSQL.value,
    },
    "sqlite": {
        "url": "/sqlite",
        "service_provider": ServiceProviders.LOCAL.value,
        "sub_service": LocalSubservice.SQLITE.value,
    },
    "azure_data_lake": {
        "url": "/azure",
        "service_provider": ServiceProviders.AZURE.value,
        "sub_service": AzureSubServices.AZURE_BLOB_STORAGE.value,
    },
    "directory_scan": {
        "url": "/directory_scan",
        "service_provider": ServiceProviders.LOCAL.value,
        "sub_service": LocalSubservice.DIRECTORY_SCAN.value,
    },
    "sftp": {
        "url": "/sftp",
        "service_provider": ServiceProviders.LOCAL.value,
        "sub_service": LocalSubservice.SFTP.value,
    },
    "oracle":{
        "url": "/oracle",
        "service_provider": ServiceProviders.LOCAL.value,
        "sub_service": LocalSubservice.ORACLE.value,
    }
}


def get_pii_reponse_from_profiler(token) -> Tuple[int, dict, dict]:
    url = f"{PROFILER_BASE_URL}/async/result/{token}"

    status_code, headers, resp = send_request(url, method="GET")

    return status_code, headers, resp


def main():
    while True:
        try:
            create_folder(WAREHOUSE_FOLDER)
            files = list_files(WAREHOUSE_FOLDER)
            files = list(sorted(files))
            if len(files) == 0:
                logger.info(
                    f"Warehouse Loader: No pending tasks. sleeping for {WAREHOUSE_FOLDER} sec"
                )
                sleep(WAREHOUSE_LOADER_DELAY)
                continue

            for file in files:
                if not file_exists(f"{WAREHOUSE_FOLDER}/{file}"):
                    continue
                try:

                    rename_file(
                        f"{WAREHOUSE_FOLDER}/{file}",
                        f"{WAREHOUSE_FOLDER}/.{file}",
                    )

                    data = read_json_file(f"{WAREHOUSE_FOLDER}/.{file}")
                    service_type = data.get("service_type")
                    data["service_provider"] = warehouse_endoints.get(
                        service_type, {}
                    ).get("service_provider")
                    data["sub_service"] = warehouse_endoints.get(service_type, {}).get(
                        "sub_service"
                    )
                    endpoint = warehouse_endoints.get(service_type, {}).get("url")
                    url = f"{WAREHOUSE_BASE_URL}{endpoint}"
                    token = data.get("token")
                    if token:
                        (
                            status_code,
                            _,
                            pii_reps,
                        ) = get_pii_reponse_from_profiler(token)
                        if status_code == 201:
                            rename_file(
                                f"{WAREHOUSE_FOLDER}/.{file}",
                                f"{WAREHOUSE_FOLDER}/{file}",
                            )
                            sleep(WAREHOUSE_LOADER_DELAY * 3)
                            continue
                        piis = pii_reps.get("data", {}).get("piis", [])
                        data["piis"] = piis

                    scan_type = data.get("scan_type")
                    if scan_type == ScanTypes.TableMetadata.value:
                        url = f"{WAREHOUSE_BASE_URL}/table_metadata"

                    elif scan_type == ScanTypes.FileMetadata.value:
                        url = f"{WAREHOUSE_BASE_URL}/file_metadata"

                    status_code, _, resp = send_request(url, "POST", data=data)
                    if status_code != 200:
                        logger.error(
                            f"Data loading to warehouse failed. for file:{file}, Error: {resp}, Data: {data}"
                        )
                    remove_file(f"{WAREHOUSE_FOLDER}/.{file}")

                except Exception as e:
                    logger.error(f"Failed to process pending task: {file}.", exc_info=e)
        except Exception as e:
            logger.error("Data loading to warehouse failed.", exc_info=e)


if __name__ == "__main__":
    main()
