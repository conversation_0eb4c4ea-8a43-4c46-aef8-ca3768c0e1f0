from sqlalchemy import text
import sys
import os

sys.path.insert(0, os.getcwd())

from src.utils.database import Database
from src.utils.logger import get_util_logger


logger = get_util_logger()

db = Database()


def get_enum_values(enum_name: str):
    """Fetch existing values for the given enum."""
    with db.session_scope() as session:
        result = session.execute(text(f"SELECT unnest(enum_range(NULL::{enum_name}));"))
        values = [row[0] for row in result]
        return values


def add_enum_value(enum_name: str, new_value: str):
    """Add new value to enum if it doesn't exist."""
    values = get_enum_values(enum_name)
    if new_value in values:
        logger.info(f"'{new_value}' already exists in {enum_name}")
        return

    # Enum DDL requires autocommit
    with db.session_scope() as session:
        session.execute(text("COMMIT"))
        session.execute(
            text(f"ALTER TYPE {enum_name} ADD VALUE :val"), {"val": new_value}
        )
        logger.info(f"Added '{new_value}' to {enum_name}")


if __name__ == "__main__":
    ENUM_NAME = "status_enum"
    NEW_VALUE = "PARTIAL"

    logger.info(f"Before: {get_enum_values(ENUM_NAME)}")
    add_enum_value(ENUM_NAME, NEW_VALUE)
    logger.info(f"After: {get_enum_values(ENUM_NAME)}")
