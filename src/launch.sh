#!/bin/sh
set -e

if [ "${RUN_ALEMBIC:-false}" = "true" ]; then
  echo "Running Alembic upgrade..."
  alembic upgrade head
else
  echo "Skipping Alembic migration (RUN_ALEMBIC=$RUN_ALEMBIC)"
fi

echo "Starting app and background services..."
uvicorn src.main:app --host 0.0.0.0 --port 8000 &

# "Running Migration"
if [ "${RUN_MIGRATION:-false}" = "true" ]; then
  echo "Running custom migration script (main.py)..."
  python3 src/migration/main.py
else
  echo "Skipping custom migration (RUN_MIGRATION=$RUN_MIGRATION)"
fi

# Start Task master
echo "Starting task master"
python src/task_master.py &

# Warehouse loader
# echo "Starting warehouse loader"
# python src/warehouse_loader.py &

# PII loader
echo "Starting PII Response loader"
python src/pii_response_loader.py

# # audit log loader
# echo "Starting Audit log loader"
# python src/audit_log_consumer.py