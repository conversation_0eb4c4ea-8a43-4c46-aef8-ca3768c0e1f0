import aiohttp
from typing import List, Dict, Optional
import logging
from datetime import datetime
from src.common.config import WAREHOUSE_BASE_URL
from src.modules.repos import DatabaseManager
from src.common.constants import StatusEnum
from src.schemas.ingestion_schemas import PIIItem
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def update_warehouse_metadata(
    warehouse_reference_id: str,
    piis: List[Dict],
    is_table: bool,
    session: aiohttp.ClientSession,
    uri: str
) -> Dict:
    """
    Update warehouse metadata with PII information
    Args:
        warehouse_reference_id: The reference ID from warehouse
        piis: List of PII information
        is_table: Whether this is a table or file
        session: aiohttp client session
        uri: The table_uri or file_uri
    """
    try:
        endpoint = (
            f"{WAREHOUSE_BASE_URL}/table_metadata"
            if is_table
            else f"{WAREHOUSE_BASE_URL}/file_metadata"
        )
        
        # Construct payload based on whether it's a table or file
        payload = {
            "piis": piis,
            "updated_at": datetime.utcnow().isoformat()
        }
        
        if is_table:
            payload.update({
                "table_hash": warehouse_reference_id,
                "table_uri": uri
            })
        else:
            payload.update({
                "file_hash": warehouse_reference_id,
                "file_uri": uri
            })
        
        async with session.patch(endpoint, json=payload) as response:
            response_data = await response.json()
            
            if response.status == 200:
                return {
                    "status": "Success",
                    "data": response_data
                }
            elif response.status == 404:
                return {
                    "status": "Failed",
                    "error": "Warehouse reference not found"
                }
            else:
                error_msg = (
                    f"Warehouse API error: {response.status} - "
                    f"{response_data.get('message', 'Unknown error')}"
                )
                logger.error(error_msg)
                return {
                    "status": "Failed",
                    "error": error_msg
                }
                
    except aiohttp.ClientError as e:
        error_msg = f"Warehouse API connection error: {str(e)}"
        logger.error(error_msg)
        return {
            "status": "Failed",
            "error": error_msg
        }
    except Exception as e:
        error_msg = f"Error updating warehouse metadata: {str(e)}"
        logger.error(error_msg)
        return {
            "status": "Failed",
            "error": error_msg
        }


async def process_profile_status(
    token: str,
    status: str,
    piis: Optional[List[PIIItem]] = None,
    status_code: Optional[int] = None,
    message: Optional[str] = None
) -> Dict:
    """
    Main function to process profile status and update warehouse
    """
    try:
        db = DatabaseManager()
        table_entry = await db.get_table_audit_by_profiler_id(token)
        if table_entry:
            # Use status_code to determine profile status
            profile_status = (
                StatusEnum.FINISHED if status_code == 200 
                else StatusEnum.FAILED
            )
            
            await db.update_table_audit_log(
                table_uri=table_entry.get('table_uri'),
                file_log={
                    "profile_status": profile_status,
                    "profiling_end_time": datetime.now()
                }
            )
            is_table = True
            warehouse_reference_id = table_entry.get('warehouse_reference_id')
            uri = table_entry.get('table_uri')
        else:
            file_entry = await db.get_file_audit_by_profiler_id(token)
            if file_entry:
                # Use status_code to determine profile status
                profile_status = (
                    StatusEnum.FINISHED if status_code == 200 
                    else StatusEnum.FAILED
                )
                
                await db.update_file_audit_log(
                    file_uri=file_entry.get('file_uri'),
                    file_log={
                        "profile_status": profile_status,
                        "profiling_end_time": datetime.now()
                    }
                )
                is_table = False
                warehouse_reference_id = file_entry.get('warehouse_reference_id')
                uri = file_entry.get('file_uri')
            else:
                return {
                    "status_code": 404,
                    "message": "No matching audit log entry found",
                    "status": "FAILED",
                    "token": token,
                    "piis": []
                }

        if piis and warehouse_reference_id:
            # Convert PII items to dictionaries
            pii_list = [pii.dict() for pii in piis]
            
            async with aiohttp.ClientSession() as session:
                result = await update_warehouse_metadata(
                    session=session,
                    warehouse_reference_id=warehouse_reference_id,
                    piis=pii_list,
                    is_table=is_table,
                    uri=uri
                )
                if result.get("status") == "Failed":
                    return {
                        "status_code": 400,
                        "message": result.get("error", "Failed to update warehouse metadata"),
                        "status": "FAILED",
                        "token": token,
                        "piis": []
                    }

        return {
            "status_code": status_code or 200,
            "message": message or "Profile status updated successfully",
            "status": "SUCCESS" if status_code == 200 else "FAILED",
            "token": token,
            "piis": piis or []
        }
    except Exception as e:
        logger.error(f"Error in process_profile_status: {str(e)}")
        return {
            "status_code": 500,
            "message": f"Unexpected error: {str(e)}",
            "status": "FAILED",
            "token": token,
            "piis": []
        }