from sqlalchemy import select, insert, update, func, or_
from typing import Optional, List, Dict
from uuid import uuid4
from src.utils.database import Database
from src.models.ingestion_models import (
    User,
    Service,
    Ingestion,
    IngestionJob,
    AssetsDetails,
    AccessControls,
    FileAuditLog,
    TableAuditLog,
    DSR,
    GrafanaServiceAccount,
)
from src.common.constants import StatusEnum
from src.utils.helpers import get_data_category
from src.utils.exceptions import ServerException
import json
import decimal
import httpx
import uuid
from datetime import datetime, date, time
from src.utils.exceptions import CustomBaseException
from src.utils.minio_client import MinioClient

class DatabaseManager:
    """DatabaseManager class for user-related database operations"""

    def __init__(self, db: Optional[Database] = None):
        if db is None:
            db = Database()

        self.db = Database()

    async def get_no_of_users(self) -> int:
        with self.db.session_scope() as session:
            result = session.execute(select(func.count()).select_from(User))
            no_of_user = result.scalars()
            return no_of_user

    async def get_user_by_id(self, user_id: str) -> dict:
        with self.db.session_scope() as session:
            result = session.execute(select(User).where(User.user_id == user_id))
            user = result.scalars().first()
            return user.as_dict() if user else {}
        
    def asset_exists(self, name: str, provider: str, type_: str) -> bool:
        try:
            with self.db.session_scope() as session:
                stmt = (
                select(AssetsDetails)
                .where(
                    AssetsDetails.asset_name == name,
                    AssetsDetails.service_provider == provider,
                    AssetsDetails.type == type_
                )
                .limit(1)
                )
                result = session.execute(stmt)
                record = result.scalars().first()
                if record:
                    record.updated_at = datetime.utcnow() 
                    session.add(record)
                    session.commit()
                    return True
                return False
        except Exception as ex:
            raise ServerException("Error while checking asset existence", excep=ex)
        

    def insert_asset_details(self, assets: List[dict]):
        with self.db.session_scope() as session:
            session.execute(insert(AssetsDetails), assets)           
            return f"{len(assets)} Assets Inserted"
        

    def access_control_exists(self, asset_name: str, user_or_role: str, role: str, access: str) -> bool:
        """Check if an access control record already exists for given parameters."""
        try:
            with self.db.session_scope() as session:
                stmt = (
                    select(AccessControls)
                    .where(
                        AccessControls.asset_name == asset_name,
                        AccessControls.user_or_role == user_or_role,
                        AccessControls.role == role,
                        AccessControls.access == access
                    )
                    .limit(1)
                )
                result = session.execute(stmt)
                record = result.scalars().first()
                if record:
                    record.updated_at = datetime.utcnow()  
                    session.add(record)
                    session.commit()
                    return True
                return False
        except Exception as ex:
            raise ServerException("Error while checking access control existence", excep=ex)
        
    def insert_access_controls(self, access_records: List[dict]):
        """Insert new access control records into the DB."""
        try:
            with self.db.session_scope() as session:
                session.execute(insert(AccessControls), access_records)
        except Exception as ex:
            raise ServerException("Error while inserting access controls", excep=ex)

    async def get_all_users(self, start: int = 0, limit: int = 100) -> List[dict]:
        with self.db.session_scope() as session:
            result = session.execute(select(User).offset(start).limit(limit))
            users = [user.as_dict() for user in result.scalars().fetchall()]
            return users

    async def create_user(self, user_data: dict) -> dict:
        user_data["user_id"] = user_data.get("username")
        with self.db.session_scope() as session:
            session.execute(insert(User).values(user_data))

        return user_data

    async def update_user(self, user_id: str, user_data: dict):
        with self.db.session_scope() as session:
            result = session.execute(
                update(User)
                .where(User.user_id == user_id)
                .values(user_data)
                .returning(User)
            )
            user = result.scalars().first()
            return user.as_dict() if user else {}

    async def get_service_by_service_name(self, service_name: str) -> dict:
        with self.db.session_scope() as session:
            result = session.execute(
                select(Service).where(Service.service_name == service_name)
            )
            service = result.scalars().first()
            return service.as_dict() if service else {}

    async def get_all_services(self, start: int = 0, limit: int = 100) -> List[dict]:
        with self.db.session_scope() as session:
            result = session.execute(
                select(Service).order_by(Service.created_at).offset(start).limit(limit)
            )
            services = [service.as_dict() for service in result.scalars().fetchall()]
            return services

    async def create_service(self, service_data: dict) -> dict:
        with self.db.session_scope() as session:
            session.execute(insert(Service).values(service_data))

        return service_data

    async def update_service(self, service_name: str, service_data: dict):
        with self.db.session_scope() as session:
            result = session.execute(
                update(Service)
                .where(Service.service_name == service_name)
                .values(service_data)
                .returning(Service)
            )
            service = result.scalars().first()
            return service.as_dict() if service else {}

    async def get_ingestion_by_id(self, ingestion_id: str) -> dict:
        with self.db.session_scope() as session:
            result = session.execute(
                select(Ingestion).where(Ingestion.ingestion_id == ingestion_id)
            )
            ingestion = result.scalars().first()
            return ingestion.as_dict() if ingestion else {}

    async def get_all_ingestions(self, start: int = 0, limit: int = 100) -> List[dict]:
        with self.db.session_scope() as session:
            result = session.execute(
                select(Ingestion)
                .order_by(Ingestion.created_at)
                .offset(start)
                .limit(limit)
            )
            ingestions = [
                ingestion.as_dict() for ingestion in result.scalars().fetchall()
            ]
            return ingestions

    async def get_service_ingestions(
        self, service_name: str, start: int = 0, limit: int = 100
    ) -> List[dict]:
        with self.db.session_scope() as session:
            result = session.execute(
                select(Ingestion)
                .where(Ingestion.service_name == service_name)
                .offset(start)
                .limit(limit)
            )
            ingestions = [
                ingestion.as_dict() for ingestion in result.scalars().fetchall()
            ]
            return ingestions

    async def create_ingestion(self, ingestion_data: dict) -> dict:
        if "ingestion_id" not in ingestion_data:
            ingestion_data["ingestion_id"] = str(uuid4())
        with self.db.session_scope() as session:
            session.execute(insert(Ingestion).values(ingestion_data))

        return ingestion_data

    async def update_ingestion(self, ingestion_id: str, ingestion_data: dict):
        with self.db.session_scope() as session:
            result = session.execute(
                update(Ingestion)
                .where(Ingestion.ingestion_id == ingestion_id)
                .values(ingestion_data)
                .returning(Ingestion)
            )
            ingestion = result.scalars().first()
            return ingestion.as_dict() if ingestion else {}

    async def create_ingestion_job(self, job_data: dict) -> dict:
        if "job_id" not in job_data:
            job_data["job_id"] = str(uuid4())

        with self.db.session_scope() as session:
            session.execute(insert(IngestionJob).values(job_data))

        return job_data

    async def update_ingestion_job(self, job_id: str, job_data: dict):
        with self.db.session_scope() as session:
            result = session.execute(
                update(IngestionJob)
                .where(IngestionJob.job_id == job_id)
                .values(job_data)
                .returning(IngestionJob)
            )
            job = result.scalars().first()
            return job.as_dict() if job else {}

    async def get_ingestion_jobs(
        self, ingestion_id: str, start: int = 0, limit: int = 100
    ) -> List[dict]:
        with self.db.session_scope() as session:
            result = session.execute(
                select(IngestionJob)
                .where(IngestionJob.ingestion_id == ingestion_id)
                .offset(start)
                .limit(limit)
            )
            jobs = [job.as_dict() for job in result.scalars().fetchall()]
            return jobs

    async def get_all_ingestion_jobs_by_status(self, status: str) -> List[dict]:
        with self.db.session_scope() as session:
            result = session.execute(
                select(IngestionJob)
                .where(IngestionJob.status == status)
                .order_by(IngestionJob.start_at)
            )
            jobs = [job.as_dict() for job in result.scalars().fetchall()]
            return jobs

    async def get_ingestion_jobs_by_status(
        self, ingestion_id: str, status: str
    ) -> List[dict]:
        with self.db.session_scope() as session:
            result = session.execute(
                select(IngestionJob)
                .where(
                    IngestionJob.ingestion_id == ingestion_id
                    and IngestionJob.status == status
                )
                .order_by(IngestionJob.start_at)
            )
            jobs = [job.as_dict() for job in result.scalars().fetchall()]
            return jobs

    async def get_ingestion_job_by_id(self, ingestion_id: str, job_id: str) -> dict:
        with self.db.session_scope() as session:
            result = session.execute(
                select(IngestionJob)
                .where(IngestionJob.ingestion_id == ingestion_id)
                .where(IngestionJob.job_id == job_id)
            )
            ingestion = result.scalars().first()
            return ingestion.as_dict() if ingestion else {}

    ## ui listing api

    async def get_all_services_details(
        self, page: int = 1, page_size: int = 10, search: Optional[str] = None
    ) -> dict:
        offset = (page - 1) * page_size
        with self.db.session_scope() as session:
            query = select(Service).where(Service.status == "Active")
            count_query = select(func.count(Service.service_name)).where(
                Service.status == "Active"
            )
            if search:
                query = query.where(Service.service_name.ilike(f"%{search}%"))
                count_query = count_query.where(
                    Service.service_name.ilike(f"%{search}%")
                )
            query = (
                query.order_by(Service.created_at.desc())
                .offset(offset)
                .limit(page_size)
            )
            result = session.execute(query)
            services_raw = result.scalars().fetchall()
            services = []
            for service in services_raw:
                service_dict = service.as_dict()
                data_category = await get_data_category(service.service_type)
                service_dict["data_category"] = data_category
                services.append(service_dict)

            total_result = session.execute(count_query)
            total_count = total_result.scalar_one()

            return {"services": services, "total_count": total_count}

    async def get_service_ingestions_with_category(
        self,
        service_name: str,
        page: int = 1,
        page_size: int = 10,
        search: Optional[str] = None,
    ) -> Dict:
        offset = (page - 1) * page_size

        with self.db.session_scope() as session:
            service = session.execute(
                select(Service).where(Service.service_name == service_name)
            ).scalar_one_or_none()

            if not service:
                return {
                    "service_name": service_name,
                    "page": page,
                    "page_size": page_size,
                    "total_count": 0,
                    "ingestions": [],
                }

            category = await get_data_category(service.service_type)

            query = select(Ingestion).where(Ingestion.service_name == service_name)
            count_query = select(func.count(Ingestion.ingestion_id)).where(
                Ingestion.service_name == service_name
            )

            if search:
                query = query.where(Ingestion.ingestion_id.ilike(f"%{search}%"))
                count_query = count_query.where(
                    Ingestion.ingestion_id.ilike(f"%{search}%")
                )

            query = (
                query.order_by(Ingestion.created_at.desc())
                .offset(offset)
                .limit(page_size)
            )

            result = session.execute(query)
            ingestion_rows = result.scalars().fetchall()
            ingestions: List[Dict] = []
            for ingestion in ingestion_rows:
                ing_dict = ingestion.as_dict()
                ing_dict["data_category"] = category
                ingestions.append(ing_dict)

            total_count = session.execute(count_query).scalar_one()

            return {
                "service_name": service_name,
                "ingestions": ingestions,
                "total_count": total_count,
            }

    async def get_jobs_by_ingestion_id(
        self,
        ingestion_id: str,
        page: int = 1,
        page_size: int = 10,
        search: Optional[str] = None,
    ) -> Dict:
        offset = (page - 1) * page_size

        with self.db.session_scope() as session:
            query = select(IngestionJob).where(
                IngestionJob.ingestion_id == ingestion_id
            )
            count_query = select(func.count(IngestionJob.job_id)).where(
                IngestionJob.ingestion_id == ingestion_id
            )
            if search:
                query = query.where(IngestionJob.job_id.ilike(f"%{search}%"))
                count_query = count_query.where(
                    IngestionJob.job_id.ilike(f"%{search}%")
                )

            query = (
                query.order_by(IngestionJob.start_at.desc())
                .offset(offset)
                .limit(page_size)
            )

            result = session.execute(query)
            jobs = [job.as_dict() for job in result.scalars().fetchall()]
            total_count = session.execute(count_query).scalar_one()

            return {
                "ingestion_id": ingestion_id,
                "jobs": jobs,
                "total_count": total_count,
            }

    def skip_file(self, file_uri: str) -> bool:
        """Check if file should be skipped based on audit logs"""
        try:
            file_log = (
                self.db.query(FileAuditLog)
                .filter(FileAuditLog.file_uri == file_uri)
                .first()
            )

            if not file_log:
                return False
            return (
                file_log.ingestion_status == StatusEnum.FINISHED.value
                and file_log.profile_status == StatusEnum.FINISHED.value
                and file_log.warehouse_status == StatusEnum.FINISHED.value
            )
        except Exception:
            return False

    def skip_table(self, table_uri: str) -> bool:
        """Check if table should be skipped based on audit logs"""
        try:
            table_log = (
                self.db.query(TableAuditLog)
                .filter(TableAuditLog.table_uri == table_uri)
                .first()
            )
            if not table_log:
                return False
            return (
                table_log.ingestion_status == StatusEnum.FINISHED.value
                and table_log.profile_status == StatusEnum.FINISHED.value
                and table_log.warehouse_status == StatusEnum.FINISHED.value
            )
        except Exception:
            return False

    async def get_table_audit_by_profiler_id(self, profiler_reference_id: str) -> dict:
        """Get table audit log entry by profiler reference ID"""
        with self.db.session_scope() as session:
            result = session.execute(
                select(TableAuditLog).where(
                    TableAuditLog.profiler_reference_id == profiler_reference_id
                )
            )
            audit_log = result.scalars().first()
            return audit_log.as_dict() if audit_log else {}

    async def get_table_audit_by_correlation_id(self, correlation_id: str) -> dict:
        """Get table audit log entry by profiler reference ID"""
        with self.db.session_scope() as session:
            result = session.execute(
                select(TableAuditLog).where(
                    TableAuditLog.correlation_id == correlation_id
                )
            )
            audit_log = result.scalars().first()
            return audit_log.as_dict() if audit_log else {}

    async def get_file_audit_by_profiler_id(self, profiler_reference_id: str) -> dict:
        """Get file audit log entry by profiler reference ID"""
        with self.db.session_scope() as session:
            result = session.execute(
                select(FileAuditLog).where(
                    FileAuditLog.profiler_reference_id == profiler_reference_id
                )
            )
            audit_log = result.scalars().first()
            return audit_log.as_dict() if audit_log else {}

    async def create_dsr_request(self, dsr_data: dict) -> dict:

        try:
            with self.db.session_scope() as session:
                session.execute(insert(DSR).values(dsr_data))
            return dsr_data
        except Exception as e:
            raise ServerException(
                code=500, message=f"Failed to create DSR request: {str(e)}"
            )

    async def get_dsr_request(self, dsr_id: str) -> dict:
        """Get DSR request by request_id"""
        try:
            with self.db.session_scope() as session:
                result = session.execute(select(DSR).where(DSR.dsr_id == dsr_id))
                dsr = result.scalars().first()
                return dsr.as_dict()
        except Exception as e:
            raise ServerException(
                code=500, message=f"Failed to fetch DSR request: {str(e)}"
            )

    async def get_dsr_requests(self, start: int = 0, limit: int = 100) -> dict:
        """Get DSR request by request_id"""
        try:
            with self.db.session_scope() as session:
                result = session.execute(select(DSR).offset(start).limit(limit))
                dsrs = [
                    ingestion.as_dict() for ingestion in result.scalars().fetchall()
                ]
            return dsrs
        except Exception as e:
            raise ServerException(
                code=500, message=f"Failed to fetch DSR request: {str(e)}"
            )

    async def update_dsr_request(self, dsr_id: str, data: dict):
        with self.db.session_scope() as session:
            result = session.execute(
                update(DSR).where(DSR.dsr_id == dsr_id).values(data).returning(DSR)
            )
            job = result.scalars().first()
            return job.as_dict() if job else {}

    async def update_dsr_personal_data(self, dsr_id: str, personal_data: dict):
        def custom_serializer(obj):
            if isinstance(obj, (datetime, date)):
                return obj.isoformat()
            elif isinstance(obj, time):
                return obj.strftime("%H:%M:%S")
            elif isinstance(obj, decimal.Decimal):
                return float(obj)
            elif isinstance(obj, uuid.UUID):
                return str(obj)
            raise TypeError(f"Type {type(obj)} not serializable")

        serializable_data = json.loads(
            json.dumps(personal_data, default=custom_serializer)
        )
        with self.db.session_scope() as session:
            session.execute(
                update(DSR)
                .where(DSR.dsr_id == dsr_id)
                .values(personal_data=serializable_data)
            )

    async def get_dsr_request_status(self, dsr_id: str):
        with self.db.session_scope() as session:
            result = session.execute(
                select(DSR.dsr_id, DSR.status, DSR.start_at, DSR.end_at).where(
                    DSR.dsr_id == dsr_id
                )
            )
            row = result.first()
            if row:
                return {
                    "dsr_id": row.dsr_id,
                    "status": row.status,
                    "start_at": row.start_at,
                    "end_at": row.end_at,
                }
            return {}

    async def get_dsr_by_request_id(self, request_id: str) -> list[dict]:
        """Get all DSR entries by request_id -> return dsr_id and status"""
        try:
            with self.db.session_scope() as session:
                result = session.execute(
                    select(DSR.dsr_id, DSR.status, DSR.start_at, DSR.end_at).where(
                        DSR.request_id == request_id
                    )
                )
                rows = result.all()

                if not rows:
                    raise CustomBaseException(
                        code=404,
                        message=f"No DSR entries found with request_id={request_id}",
                    )

                return [
                    {
                        "dsr_id": row.dsr_id,
                        "status": row.status,
                        "start_at": row.start_at,
                        "end_at": row.end_at,
                    }
                    for row in rows
                ]

        except CustomBaseException:
            raise
        except Exception as e:
            raise CustomBaseException(
                code=500,
                message=f"Database error while fetching DSR by request_id: {str(e)}",
            )

    async def update_dsr_pii_matching_tables(self, dsr_id: str, tables: list[dict]):
        """Update PII matching tables for a DSR"""
        try:
            with self.db.session_scope() as session:
                session.execute(
                    update(DSR)
                    .where(DSR.dsr_id == dsr_id)
                    .values(
                        pii_matching_tables=tables,
                        updated_at=datetime.now()
                    )
                )
        except Exception as e:
            raise CustomBaseException(
                code=500,
                message=f"Database error while updating PII matching tables: {str(e)}"
            )

    async def get_dsr_pii_matching_tables(self, dsr_id: str) -> list[dict]:
        """Fetch PII matching tables for a DSR (assumes DSR exists)"""
        try:
            with self.db.session_scope() as session:
                dsr_entry = session.execute(
                    select(DSR).where(DSR.dsr_id == dsr_id)
                ).scalar_one_or_none()

                # Since existence is checked separately, this is just a safety net
                if not dsr_entry:
                    raise CustomBaseException(
                        code=404,
                        message=f"DSR with dsr_id={dsr_id} not found"
                    )

                return dsr_entry.pii_matching_tables or []

        except CustomBaseException:
            raise
        except Exception as e:
            raise CustomBaseException(
                code=500,
                message=f"Database error while fetching PII matching tables: {str(e)}"
            )


    async def check_dsr_exists(self, dsr_id: str) -> bool:
        """Check if a DSR with given dsr_id exists"""
        try:
            with self.db.session_scope() as session:
                dsr_entry = session.execute(
                    select(DSR).where(DSR.dsr_id == dsr_id)
                ).scalar_one_or_none()

                return dsr_entry is not None

        except Exception as e:
            raise CustomBaseException(
                code=500,
                message=f"Database error while checking DSR existence: {str(e)}"
            )


    async def cancel_ingestion_job_and_delete_files(self, ingestion_id: str, job_id: str) -> dict:
        """
        Cancel an ingestion job and delete all associated MinIO files.
        Works for both TableAuditLog and FileAuditLog.
        Returns: {"audit_log_type": "table"|"file"|None, "files_deleted": int}
        """
        from sqlalchemy import or_
        from datetime import datetime

        minio_client = MinioClient()
        now = datetime.utcnow()

        try:
            with self.db.session_scope() as session:

                def update_and_delete_files(audit_log_model, primary_key_column):
                    # Update statuses
                    session.execute(
                        update(audit_log_model)
                        .where(audit_log_model.ingestion_id == ingestion_id)
                        .where(audit_log_model.job_id == job_id)
                        .where(
                            or_(
                                audit_log_model.profile_status.in_([StatusEnum.INITIATED, StatusEnum.RUNNING]),
                                audit_log_model.warehouse_status.in_([StatusEnum.INITIATED, StatusEnum.RUNNING])
                            )
                        )
                        .values(
                            profile_status=StatusEnum.CANCELLED,
                            warehouse_status=StatusEnum.CANCELLED,
                            profile_end_time=now,
                            warehouse_end_time=now,
                        )
                    )
                    session.commit()

                    # Fetch files to delete (only CANCELLED)
                    results = session.execute(
                        select(audit_log_model.minio_file)
                        .where(
                            audit_log_model.ingestion_id == ingestion_id,
                            audit_log_model.minio_file.isnot(None),
                            or_(
                                audit_log_model.profile_status == StatusEnum.CANCELLED,
                                audit_log_model.warehouse_status == StatusEnum.CANCELLED
                            )
                        )
                    )
                    files = [row[0] for row in results.fetchall()]

                    # Delete files from MinIO
                    deleted_count = 0
                    for file in files:
                        try:
                            minio_client.client.remove_object(minio_client.bucket, file)
                            deleted_count += 1
                        except Exception as e:
                            raise CustomBaseException(
                                code=500,
                                message=f"Failed to delete {file} from MinIO: {str(e)}"
                            )

                    return deleted_count

                # --- Check TableAuditLog ---
                table_exists = session.execute(
                    select(TableAuditLog.table_uri).where(TableAuditLog.ingestion_id == ingestion_id)
                ).first()

                if table_exists:
                    deleted_count = update_and_delete_files(TableAuditLog, TableAuditLog.table_uri)
                    return {"audit_log_type": "table", "files_deleted": deleted_count}

                # --- Check FileAuditLog ---
                file_exists = session.execute(
                    select(FileAuditLog.file_uri).where(FileAuditLog.ingestion_id == ingestion_id)
                ).first()

                if file_exists:
                    deleted_count = update_and_delete_files(FileAuditLog, FileAuditLog.file_uri)
                    return {"audit_log_type": "file", "files_deleted": deleted_count}

                # Nothing found
                return {"audit_log_type": None, "files_deleted": 0}

        except CustomBaseException:
            raise
        except Exception as e:
            raise CustomBaseException(
                code=500,
                message=f"Failed to cancel ingestion and delete MinIO files: {str(e)}"
            )



    async def create_grafana_account(self, account_data: dict) -> dict:
        """Create a new Grafana service account or return existing one if token exists"""
        try:
            with self.db.session_scope() as session:
                # Check if token already exists
                existing = session.query(GrafanaServiceAccount).filter(
                    GrafanaServiceAccount.token == account_data["token"]
                ).first()
                if existing:
                    return {"exists": True, "account": existing.as_dict()}

                # Create new account
                account = GrafanaServiceAccount(
                    name=account_data["name"],
                    grafana_url=account_data["grafana_url"],
                    token=account_data["token"],
                    org_id=account_data.get("org_id"),
                    dashboards=None
                )
                session.add(account)
                session.commit()
                session.refresh(account)
                return {"exists": False, "account": account.as_dict()}

        except Exception as e:
            raise ServerException(
                code=500,
                message=f"Failed to create Grafana account: {str(e)}"
            )
    async def get_grafana_accounts(self) -> list[dict]:
        """Fetch all Grafana service accounts (not deleted)"""
        try:
            with self.db.session_scope() as session:
                result = session.execute(
                    select(GrafanaServiceAccount).where(GrafanaServiceAccount.deleted_at == None)
                )
                accounts = result.scalars().all()
                return [a.as_dict() for a in accounts]
        except Exception as e:
            raise ServerException(
                code=500,
                message=f"Failed to fetch Grafana accounts: {str(e)}"
            )
    
    async def fetch_and_update_grafana_dashboards(self, account_id: int) -> dict:
            """
            Fetch dashboards from Grafana using the stored URL & token and update the DB.
            Returns the updated account as a dictionary.
            """
            try:
                with self.db.session_scope() as session:
                    # Fetch the Grafana service account
                    account = session.query(GrafanaServiceAccount).filter(
                        GrafanaServiceAccount.id == account_id
                    ).first()
                    if not account or not account.token or not account.grafana_url:
                        return {}

                    # Fetch dashboards from Grafana API
                    async with httpx.AsyncClient() as client:
                        response = await client.get(
                            f"{account.grafana_url}/api/search?query=&type=dash-db",
                            headers={"Authorization": f"Bearer {account.token}"}
                        )

                    if response.status_code != 200:
                        return {}

                    # Process dashboard data
                    dashboards = []
                    for d in response.json():
                        path = d["url"].replace("/grafana", "", 1)
                        full_url = f"{account.grafana_url}{path}"
                        dashboards.append({
                            "title": d["title"],
                            "uid": d["uid"],
                            "url": full_url
                        })

                    # Update the dashboards in the DB
                    account.dashboards = dashboards
                    account.updated_at = datetime.utcnow()
                    session.commit()
                    session.refresh(account)

                    return account.as_dict()

            except Exception:
                # silently ignore errors
                return {}
