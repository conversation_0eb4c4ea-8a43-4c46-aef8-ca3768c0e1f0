from typing import Optional
from uuid import uuid4
from src.utils.logger import get_util_logger
from src.common.constants import StatusEnum
from src.utils.database import Database
from sqlalchemy import select, insert, update
from datetime import datetime
from src.models.ingestion_models import FileAuditLog, TableAuditLog, AuditEvent

logger = get_util_logger()


class AuditLogger:
    """Handles audit logging for both file and table processing operations."""

    def __init__(self, db: Optional[Database] = None):
        if db is None:
            db = Database()

        self.db = Database()

    def get_file_audit_log_by_uri(self, file_uri: str) -> dict:
        with self.db.session_scope() as session:
            result = session.execute(
                select(FileAuditLog).where(FileAuditLog.file_uri == file_uri)
            )
            user = result.scalars().first()
            return user.as_dict() if user else {}

    def get_file_audit_log_by_correlation_id(self, correlation_id: str) -> dict:
        with self.db.session_scope() as session:
            result = session.execute(
                select(FileAuditLog).where(
                    FileAuditLog.correlation_id == correlation_id
                )
            )
            user = result.scalars().first()
            return user.as_dict() if user else {}

    def create_file_audit_log(self, file_log: dict) -> dict:
        with self.db.session_scope() as session:
            session.execute(insert(FileAuditLog).values(file_log))
        return file_log

    def update_file_audit_log_by_file_uri(self, file_uri: str, file_log: dict):
        with self.db.session_scope() as session:
            result = session.execute(
                update(FileAuditLog)
                .where(FileAuditLog.file_uri == file_uri)
                .values(file_log)
                .returning(FileAuditLog)
            )
            user = result.scalars().first()
            return user.as_dict() if user else {}

    def update_file_audit_log_by_correlation_id(
        self, correlation_id: str, file_log: dict
    ):
        with self.db.session_scope() as session:
            result = session.execute(
                update(FileAuditLog)
                .where(FileAuditLog.correlation_id == correlation_id)
                .values(file_log)
                .returning(FileAuditLog)
            )
            user = result.scalars().first()
            return user.as_dict() if user else {}

    def get_table_audit_log_by_uri(self, table_uri: str) -> dict:
        with self.db.session_scope() as session:
            result = session.execute(
                select(TableAuditLog).where(TableAuditLog.table_uri == table_uri)
            )
            user = result.scalars().first()
            return user.as_dict() if user else {}

    def get_table_audit_log_by_correlation_id(self, correlation_id: str) -> dict:
        with self.db.session_scope() as session:
            result = session.execute(
                select(TableAuditLog).where(
                    TableAuditLog.correlation_id == correlation_id
                )
            )
            user = result.scalars().first()
            return user.as_dict() if user else {}

    def create_table_audit_log(self, file_log: dict) -> dict:
        with self.db.session_scope() as session:
            session.execute(insert(TableAuditLog).values(file_log))
        return file_log

    def update_table_audit_log_by_table_uri(self, table_uri: str, file_log: dict):
        with self.db.session_scope() as session:
            result = session.execute(
                update(TableAuditLog)
                .where(TableAuditLog.table_uri == table_uri)
                .values(file_log)
                .returning(TableAuditLog)
            )
            user = result.scalars().first()
            return user.as_dict() if user else {}

    def update_table_audit_log_by_correlation_id(
        self, correlation_id: str, table_log: dict
    ):
        with self.db.session_scope() as session:
            result = session.execute(
                update(TableAuditLog)
                .where(TableAuditLog.correlation_id == correlation_id)
                .values(table_log)
                .returning(TableAuditLog)
            )
            user = result.scalars().first()
            return user.as_dict() if user else {}

    def get_audit_event_by_event_id(self, event_id: str) -> dict:
        with self.db.session_scope() as session:
            result = session.execute(
                select(AuditEvent).where(AuditEvent.event_id == event_id)
            )
            user = result.scalars().first()
            return user.as_dict() if user else {}

    def get_audit_events_by_correlation_id(self, correlation_id: str) -> dict:
        with self.db.session_scope() as session:
            result = session.execute(
                select(AuditEvent).where(AuditEvent.correlation_id == correlation_id)
            )
            audit_events = [event.as_dict() for event in result.scalars().fetchall()]
            return audit_events

    def create_audit_event(
        self, correlation_id: str, event_name: str, msg: str = "", metadata: dict = {}
    ) -> dict:
        event = {
            "correlation_id": correlation_id,
            "event_name": event_name,
            "event_id": str(uuid4()),
            "msg": msg,
            "extra_metadata": metadata,
        }
        with self.db.session_scope() as session:
            session.execute(insert(AuditEvent).values(event))
        return event

    def create_file_audit_log_util(
        self,
        correlation_id: str,
        file_uri: str,
        metadata: dict,
        ingestion_id: str,
        job_id: Optional[str] = None,
    ) -> None:
        """Create initial file audit log entry when processing starts."""
        try:
            file_log = {
                "correlation_id": correlation_id,
                "service_name": metadata.get("service_name"),
                "service_type": metadata.get("service_type"),
                "service_provider": metadata.get("service_provider"),
                "sub_service": metadata.get("sub_service"),
                "file_key": metadata.get("file_key", ""),
                "file_uri": file_uri,
                "file_type": metadata.get("file_type"),
                "ingestion_id": ingestion_id,
                "job_id": job_id,
                "ingestion_start_time": datetime.now(),
                "ingestion_status": StatusEnum.INITIATED.value,
            }
            self.create_file_audit_log(file_log)
            logger.info(f"Created file audit log for {file_uri}")
        except Exception as e:
            logger.error(f"Failed to create file audit log for {file_uri}: {str(e)}")

    def create_table_audit_log_util(
        self,
        correlation_id,
        table_uri: str,
        metadata: dict,
        ingestion_id: str,
        job_id: Optional[str] = None,
    ) -> None:
        """Create initial table audit log entry when processing starts."""
        try:
            table_log = {
                "correlation_id": correlation_id,
                "service_name": metadata.get("service_name"),
                "service_type": metadata.get("service_type"),
                "service_provider": metadata.get("service_provider"),
                "sub_service": metadata.get("sub_service"),
                "table_uri": table_uri,
                "db_name": metadata.get("db_name"),
                "table_name": metadata.get("table_name"),
                "ingestion_id": ingestion_id,
                "job_id": job_id,
                "ingestion_start_time": datetime.now(),
                "ingestion_status": StatusEnum.INITIATED.value,
            }
            self.create_table_audit_log(table_log)
            logger.info(f"Created table audit log for {table_uri}")
        except Exception as e:
            logger.error(f"Failed to create table audit log for {table_uri}: {str(e)}")

    def update_audit_log_util(self, correlation_id, log) -> None:
        """Create initial table audit log entry when processing starts."""
        try:
            if correlation_id.lower().startswith("file"):
                self.update_file_audit_log_by_correlation_id(
                    correlation_id=correlation_id, file_log=log
                )
            else:
                self.update_table_audit_log_by_correlation_id(
                    correlation_id=correlation_id, table_log=log
                )
        except Exception as e:
            logger.error(f"Failed to update audit log for {correlation_id}: {str(e)}")

    def get_audit_log_util(self, correlation_id) -> None:
        """Create initial table audit log entry when processing starts."""
        try:
            if correlation_id.lower().startswith("file"):
                return self.get_file_audit_log_by_correlation_id(
                    correlation_id=correlation_id
                )

            return self.get_table_audit_log_by_correlation_id(
                correlation_id=correlation_id
            )
        except Exception as e:
            logger.error(f"Failed to update audit log for {correlation_id}: {str(e)}")
