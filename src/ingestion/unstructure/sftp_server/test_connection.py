import asyncssh
import logging
from src.utils.exceptions import ClientException, ServerException

logger = logging.getLogger("Unstructure")


async def test_sftp_connection(data: dict) -> dict:
    creds = data.get("credentials", {})
    try:
        username = creds.get("username")
        password = creds.get("password")
        hostname = creds.get("hostname")
        port = creds.get("port", 22)
        root_path = creds.get("root_path", "/")
        if not all([username, password, hostname]):
            logger.warning("Missing required SFTP credentials")
            raise ClientException(
                "Missing required credentials: username, password, hostname", 
                code=400, 
                excep=None
            )
        logger.info(f"Testing SFTP connection to {hostname}:{port}")
        try:
            connection = await asyncssh.connect(
                hostname,
                username=username,
                password=password,
                port=port,
                known_hosts=None,
                connect_timeout=30
            )
        except asyncssh.AuthenticationError as e:
            logger.warning(f"SFTP authentication failed: {str(e)}")
            raise ClientException(
                "Invalid SFTP credentials", code=401, excep=e
            )
        except asyncssh.ConnectionLostError as e:
            logger.warning(f"SFTP connection lost: {str(e)}")
            raise ClientException(
                "SFTP connection failed", code=503, excep=e
            )
        except asyncssh.HostKeyNotVerifiableError as e:
            logger.warning(f"SFTP host key verification failed: {str(e)}")
            raise ClientException(
                "SFTP host key verification failed", code=403, excep=e
            )
        except asyncssh.TimeoutError as e:
            logger.warning(f"SFTP connection timeout: {str(e)}")
            raise ClientException(
                "SFTP connection timeout", code=408, excep=e
            )
        try:
            sftp_client = await connection.start_sftp_client()
        except Exception as e:
            logger.warning(f"Failed to start SFTP client: {str(e)}")
            connection.close()
            raise ClientException(
                "Failed to start SFTP client", code=500, excep=e
            )
        try:
            entries = await sftp_client.listdir(root_path)
            logger.info(f"SFTP connection test successful. Found {len(entries)} items")
        except Exception as e:
            logger.warning(f"Failed to list directory {root_path}: {str(e)}")
            sftp_client.exit()
            connection.close()
            raise ClientException(
                f"Cannot access root path: {root_path}", code=403, excep=e
            )
        sftp_client.exit()
        connection.close()
        return {
            "status": True,
            "message": f"SFTP connection successful. Found {len(entries)} items"
        }
    except ClientException:
        raise
    except Exception as e:
        logger.exception("Unexpected error during SFTP connection test")
        raise ServerException("SFTP test failed", excep=e) 