import os
import asyncio
import zipfile
import tempfile
import shutil
from uuid import uuid4
import asyncssh
from collections import defaultdict
from typing import AsyncGenerator, Dict, Any, List
from src.ingestion.data_class import AssetsDetails as AssetDetailsSchema
from src.modules.repos import DatabaseManager
from src.common.constants import ServiceTypes, ServiceProviders, LocalSubservice
from src.ingestion.base_source import UnstructureSource
from src.ingestion.data_class import FileMetadata
from src.utils.logger import get_ingestion_logger
from src.utils.helpers import create_folder, get_file_extension
from src.utils.loaders import load_access_controls,load_asset_details
from src.utils.minio_client import MinioClient
from src.common.config import (
    TEMP_DATA_DIR,
    PROFILE_SUPPORTED_FILE_TYPES,
    PROFILE_MAX_SIZE_THRESHOLD,
    CONCURRENT_LIMIT,
)

logger = get_ingestion_logger()


class SFTPSource(UnstructureSource):
    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.SFTP.value, service_name)
        self.db = DatabaseManager()
        self.service_provider = ServiceProviders.LOCAL
        self.sub_service = LocalSubservice.SFTP
        self.service = None
        self.sftp_client = None
        self.connection = None
        self.root_path = ""
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"
        self.visited_paths = set()
        self.access_errors = []
        self.file_cache = {}  
        # self.storage_limit_bytes = 10 * 1024 * 1024 * 1024  # 50GB
        # self.current_storage_used = 0
        # self.minio_client = None

    def normalize_sftp_path(self, *parts):
        """Properly normalize SFTP paths using forward slashes"""
        path = '/'.join(str(part).strip('/') for part in parts if part)
        if not path.startswith('/'):
            path = '/' + path
        return path.replace('//', '/')

    async def check_file_exists(self, path: str) -> bool:
        """Check if file exists and is accessible"""
        try:
            stat = await self.sftp_client.stat(path)
            return True
        except Exception as e:
            logger.debug(f"File existence check failed for {path}: {e}")
            return False

    async def check_file_permissions(self, path: str) -> dict:
        """Check file permissions and accessibility"""
        try:
            stat = await self.sftp_client.stat(path)
            permissions = stat.permissions
            # Defensive: permissions may be None
            is_directory = False
            is_file = False
            owner_read = False
            group_read = False
            other_read = False
            permissions_str = None
            if permissions is not None:
                # Check if it's a file or directory
                is_directory = (permissions & 0o170000) == 0o040000
                is_file = (permissions & 0o170000) == 0o100000
                # Check read permissions (owner, group, others)
                owner_read = bool(permissions & 0o400)
                group_read = bool(permissions & 0o040)
                other_read = bool(permissions & 0o004)
                permissions_str = oct(permissions)
            return {
                "exists": True,
                "is_directory": is_directory,
                "is_file": is_file,
                "readable": owner_read or group_read or other_read,
                "permissions": permissions_str if permissions_str is not None else "",
                "size": stat.size,
                "mtime": stat.mtime,
                "atime": stat.atime
            }
        except Exception as e:
            logger.debug(f"Permission check failed for {path}: {e}")
            return {
                "exists": False,
                "error": str(e),
                "readable": False
            }

    async def safe_listdir(self, path: str) -> tuple:
        """Safely list directory contents with error handling"""
        try:
            entries = await self.sftp_client.listdir(path)
            return entries, None
        except PermissionError as e:
            logger.warning(f"Permission denied accessing directory {path}: {e}")
            return [], f"Permission denied: {e}"
        except FileNotFoundError as e:
            logger.warning(f"Directory not found {path}: {e}")
            return [], f"Directory not found: {e}"
        except Exception as e:
            logger.warning(f"Error accessing directory {path}: {e}")
            return [], f"Error: {e}"

    async def validate_file_before_download(self, remote_path: str) -> dict:
        """Validate file before attempting download"""
        validation_result = {
            "valid": False,
            "error": None,
            "file_info": None
        }
        
        try:
            # Check if file exists and get its info
            file_info = await self.check_file_permissions(remote_path)
            
            if not file_info["exists"]:
                validation_result["error"] = f"File does not exist: {remote_path}"
                return validation_result
            
            if not file_info["readable"]:
                validation_result["error"] = f"File is not readable: {remote_path}"
                return validation_result
            
            if file_info["is_directory"]:
                validation_result["error"] = f"Path is a directory, not a file: {remote_path}"
                return validation_result
            
            # Check file size
            if file_info["size"] > PROFILE_MAX_SIZE_THRESHOLD:
                validation_result["error"] = f"File exceeds size limit ({file_info['size']} > {PROFILE_MAX_SIZE_THRESHOLD}): {remote_path}"
                return validation_result
            
            # Check file type
            file_name = os.path.basename(remote_path)
            file_ext = get_file_extension(file_name)
            
            # Allow ZIP files for processing, even if not in PROFILE_SUPPORTED_FILE_TYPES
            if file_ext not in PROFILE_SUPPORTED_FILE_TYPES and not self.is_zip_file(file_name):
                validation_result["error"] = f"Unsupported file type ({file_ext}): {remote_path}"
                return validation_result
            
            validation_result["valid"] = True
            validation_result["file_info"] = file_info
            return validation_result
            
        except Exception as e:
            validation_result["error"] = f"Validation failed: {e}"
            return validation_result

    def is_zip_file(self, filename: str) -> bool:
        """Check if file is a ZIP file based on extension"""
        return filename.lower().endswith(('.zip', '.zipx'))

    # def check_storage_limit(self, file_size: int) -> bool:
    #     """Check if adding this file would exceed the 50GB storage limit"""
    #     if self.minio_client is None:
    #         logger.warning("MinIO client not available, storage limit checking disabled")
    #         return True  # Allow download if we can't check limits
        
    #     # Check if adding this file would exceed the limit
    #     if (self.current_storage_used + file_size) > self.storage_limit_bytes:
    #         logger.warning(
    #             f"Storage limit would be exceeded: current={self.current_storage_used / (1024**3):.2f}GB, "
    #             f"file={file_size / (1024**3):.2f}GB, limit={self.storage_limit_bytes / (1024**3):.2f}GB"
    #         )
    #         return False
        
    #     return True

    # def update_storage_usage(self, file_size: int):
    #     """Update the current storage usage counter"""
    #     self.current_storage_used += file_size
    #     logger.info(
    #         f"Storage usage updated: {self.current_storage_used / (1024**3):.2f}GB / "
    #         f"{self.storage_limit_bytes / (1024**3):.2f}GB ({(self.current_storage_used / self.storage_limit_bytes) * 100:.1f}%)"
    #     )

    # def get_storage_status(self) -> dict:
    #     """Get current storage usage status"""
    #     return {
    #         "current_usage_gb": self.current_storage_used / (1024**3),
    #         "limit_gb": self.storage_limit_bytes / (1024**3),
    #         "remaining_gb": (self.storage_limit_bytes - self.current_storage_used) / (1024**3),
    #         "usage_percentage": (self.current_storage_used / self.storage_limit_bytes) * 100
    #     }

    # def reset_storage_counter(self):
    #     """Reset the storage usage counter (useful for testing or new ingestion runs)"""
    #     self.current_storage_used = 0
    #     logger.info("Storage usage counter reset to 0")

    def process_zip_file(self, zip_path: str, extract_to: str) -> list:
        """Extract ZIP file and return list of extracted files"""
        extracted_files = []
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                for member in zip_ref.namelist():
                    if member.endswith('/'):
                        # Skip directories
                        continue
                    
                    try:
                        # Extract file
                        zip_ref.extract(member, extract_to)
                        extracted_path = os.path.join(extract_to, member)
                        
                        if os.path.exists(extracted_path):
                            extracted_files.append({
                                'path': extracted_path,
                                'name': os.path.basename(member),
                                'size': os.path.getsize(extracted_path),
                                'relative_path': member
                            })
                            logger.debug(f"Extracted file: {member}")
                    except Exception as e:
                        logger.warning(f"Failed to extract {member} from {zip_path}: {e}")
                        continue
                        
            logger.info(f"Successfully extracted {len(extracted_files)} files from {zip_path}")
            return extracted_files
            
        except Exception as e:
            logger.error(f"Failed to process ZIP file {zip_path}: {e}")
            return []

    async def   process_extracted_files(self, extracted_files: list, semaphore, parent_zip_path: str = "") -> AsyncGenerator[FileMetadata, None]:
        """Process extracted files, including recursive ZIP processing"""
        for file_info in extracted_files:
            file_path = file_info['path']
            file_name = file_info['name']
            file_size = file_info['size']
            
            try:
                # Check if this extracted file is also a ZIP
                if self.is_zip_file(file_name):
                    logger.info(f"Found nested ZIP file: {file_name}")
                    
                    # Create subdirectory for nested ZIP extraction
                    nested_extract_dir = tempfile.mkdtemp(dir=os.path.dirname(file_path))
                    nested_files = self.process_zip_file(file_path, nested_extract_dir)
                    
                    # Build nested ZIP path for recursive processing
                    if parent_zip_path:
                        nested_zip_path = f"{parent_zip_path}/{file_info['relative_path']}"
                    else:
                        nested_zip_path = file_info['relative_path']
                    
                    # Recursively process nested files
                    async for nested_metadata in self.process_extracted_files(nested_files, semaphore, nested_zip_path):
                        yield nested_metadata
                else:
                    # Process regular file
                    file_ext = get_file_extension(file_name)
                    
                    # Construct file URI similar to NAS pattern: /service_name/parent_zip_path/extracted_file_path
                    if parent_zip_path:
                        # Remove leading slash from parent_zip_path if it exists to avoid double slashes
                        clean_parent_path = parent_zip_path.lstrip('/')
                        file_uri = f"/{self.service_name}/{clean_parent_path}/{file_info['relative_path']}"
                    else:
                        file_uri = f"/{self.service_name}/{file_info['relative_path']}"
                    
                    # Check storage limit for extracted file
                    # if not self.check_storage_limit(file_size):
                    #     # logger.warning(f"Storage limit reached (50GB). Stopping extracted file processing at: {file_name}")
                    #     # logger.info(f"Final storage status: {self.get_storage_status()}")
                    #     return  # Stop processing extracted files
                    
                    # Create final destination in local_data_dir
                    temp_dir = self.local_data_dir
                    create_folder(temp_dir)
                    final_path = f"{temp_dir}/{uuid4()}_{file_name}"
                    
                    # Move file to final location
                    shutil.move(file_path, final_path)
                    
                    # Update storage usage for extracted file
                    # self.update_storage_usage(file_size)
                    yield FileMetadata(
                        service_name=self.service_name,
                        service_type=self.service_type,
                        service_provider=self.service_provider,
                        sub_service=self.sub_service,
                        file_key=file_name,
                        file_size=file_size,
                        file_type=file_ext,
                        file_uri=file_uri,
                        local_filepath=final_path,
                        details={"created": None, "modified": None, "region": "India", "extracted_from_zip": True}
                    )
                        
            except Exception as e:
                logger.error(f"Error processing extracted file {file_name}: {e}")
                continue

    async def get_service_details(self):
        if self.sftp_client:
            try:
                await self.sftp_client.listdir(self.root_path)
                return self.service
            except Exception as e:
                logger.warning(f"SFTP client might be disconnected: {e}")
                try:
                    if self.connection:
                        self.connection.close()
                except Exception:
                    pass
                self.connection = None
                self.sftp_client = None

        # Fetch service and credentials
        self.service = await self.db.get_service_by_service_name(self.service_name)
        credentials = self.service.get("credentials", {})
        self.username = credentials.get("username")
        self.password = credentials.get("password")
        self.hostname = credentials.get("host") or credentials.get("hostname")
        self.service_steward = self.service.get("data_steward")

        
        port_value = credentials.get("port", 22)
        try:
            self.port = int(port_value)
        except (ValueError, TypeError):
            logger.warning(f"Invalid port value: {port_value}, using default port 22")
            self.port = 22
            
        self.root_path = credentials.get("root_path", "/")
        # Normalize root path
        self.root_path = self.normalize_sftp_path(self.root_path)
        logger.info(f"Root path set to: {self.root_path}")

        try:
            # PRIMARY CONNECTION - with ssh-rsa priority (like your terminal command)
            primary_options = {
                "username": self.username,
                "password": self.password,
                "port": self.port,
                "known_hosts": None,
                # Prioritize ssh-rsa algorithms first (like -oHostKeyAlgorithms=+ssh-rsa)
                "server_host_key_algs": [
                    'ssh-rsa',           # Highest priority
                    'rsa-sha2-256', 
                    'rsa-sha2-512',
                    'ecdsa-sha2-nistp256',
                    'ecdsa-sha2-nistp384', 
                    'ecdsa-sha2-nistp521',
                    'ssh-ed25519',
                    'ssh-dss'
                ],
                # Prioritize ssh-rsa for public key acceptance (like -oPubkeyAcceptedKeyTypes=+ssh-rsa)
                "signature_algs": [
                    'ssh-rsa',           # Highest priority  
                    'rsa-sha2-256',
                    'rsa-sha2-512', 
                    'ecdsa-sha2-nistp256',
                    'ecdsa-sha2-nistp384',
                    'ecdsa-sha2-nistp521',
                    'ssh-ed25519'
                ],
                # Add key exchange algorithms that work well with ssh-rsa
                "kex_algs": [
                    'diffie-hellman-group14-sha256',
                    'diffie-hellman-group14-sha1',
                    'diffie-hellman-group16-sha512',
                    'ecdh-sha2-nistp256',
                    'ecdh-sha2-nistp384',
                    'ecdh-sha2-nistp521'
                ],
                # Encryption algorithms
                "encryption_algs": [
                    'aes128-ctr', 
                    'aes192-ctr', 
                    'aes256-ctr',
                    'aes128-cbc', 
                    'aes192-cbc', 
                    'aes256-cbc'
                ],
                # MAC algorithms
                "mac_algs": [
                    'hmac-sha2-256', 
                    'hmac-sha2-512', 
                    'hmac-sha1'
                ]
            }

            logger.info(f"Attempting SFTP connection to {self.hostname}:{self.port} with ssh-rsa priority")
            
            self.connection = await asyncssh.connect(self.hostname, **primary_options)
            self.sftp_client = await self.connection.start_sftp_client()
            logger.info(f"SFTP connected successfully to {self.hostname}:{self.port} with ssh-rsa algorithms")
            
        except Exception as e:
            logger.error(f"Failed to connect with ssh-rsa priority to {self.hostname}:{self.port}: {str(e)}")
            
            # FALLBACK CONNECTION - Legacy algorithms
            try:
                logger.info("Attempting connection with legacy algorithms...")
                fallback_options = {
                    "username": self.username,
                    "password": self.password,
                    "port": self.port,
                    "known_hosts": None,
                    "server_host_key_algs": ['ssh-rsa', 'ssh-dss'],
                    "kex_algs": ['diffie-hellman-group14-sha256', 'diffie-hellman-group14-sha1'],
                    "encryption_algs": ['aes128-ctr', 'aes192-ctr', 'aes256-ctr', 'aes128-cbc', 'aes192-cbc', 'aes256-cbc'],
                    "mac_algs": ['hmac-sha2-256', 'hmac-sha2-512', 'hmac-sha1'],
                    "signature_algs": ['rsa-sha2-256', 'rsa-sha2-512', 'ssh-rsa']
                }
                
                self.connection = await asyncssh.connect(self.hostname, **fallback_options)
                self.sftp_client = await self.connection.start_sftp_client()
                logger.info(f"SFTP connected successfully with fallback settings to {self.hostname}:{self.port}")
                
            except Exception as fallback_error:
                final_error = f"Failed to connect with both ssh-rsa priority and fallback settings. SSH-RSA error: {str(e)}, Fallback error: {str(fallback_error)}"
                logger.error(final_error)
                raise Exception(final_error)

        return self.service

    async def test_connection(self):
        try:
            await self.get_service_details()
            # Test basic directory listing
            entries, error = await self.safe_listdir(self.root_path)
            if error:
                return False, f"Authentication failed: {error}"
            
            logger.info(f"SFTP test connection success for {self.service_name}. Found {len(entries)} items in root.")
            return True, f"Authentication successful. Found {len(entries)} items in root directory."
        except Exception as e:
            error_msg = f"SFTP test failed: {str(e)}"
            logger.error(error_msg)
            return False, f"Authentication failed: {str(e)}"

    async def scan_sftp_infra(self, path: str) -> dict:
        """Infrastructure scan - counts files and calculates total size"""
        total_files = 0
        total_size = 0
        scanned_directories = 0
        accessible_folders = []
        inaccessible_folders = []
        permission_errors = []

        try:
            logger.info(f"Starting infrastructure scan from path: {path}")
            entries, error = await self.safe_listdir(path)
            if error:
                logger.warning(f"Cannot access directory: {path} - {error}")
                return {
                    "total_files": 0, 
                    "total_size": 0, 
                    "scanned_directories": 0,
                    "accessible_folders": [],
                    "inaccessible_folders": [{"path": path, "error": error}],
                    "permission_errors": [{"path": path, "error": error}]
                }
            
            logger.info(f"Found {len(entries)} entries in {path}")
        except Exception as e:
            logger.warning(f"Cannot access directory: {path} - {e}")
            return {
                "total_files": 0, 
                "total_size": 0, 
                "scanned_directories": 0,
                "accessible_folders": [],
                "inaccessible_folders": [{"path": path, "error": str(e)}],
                "permission_errors": [{"path": path, "error": str(e)}]
            }

        for entry in entries:
            if entry in ('.', '..'):
                continue
                
            full_path = self.normalize_sftp_path(path, entry)
            
            if full_path in self.visited_paths:
                continue
            self.visited_paths.add(full_path)

            try:
                file_info = await self.check_file_permissions(full_path)
                
                if not file_info["exists"]:
                    inaccessible_folders.append({"path": full_path, "error": file_info.get("error", "File does not exist")})
                    continue
                
                # if not file_info["readable"]:
                #     permission_errors.append({"path": full_path, "error": "No read permission"})
                #     inaccessible_folders.append({"path": full_path, "error": "No read permission"})
                #     continue
                
                if file_info["is_directory"]:
                    logger.debug(f"Found directory: {full_path}")
                    scanned_directories += 1
                    accessible_folders.append(full_path)
                    
                    # Try to access the directory
                    try:
                        subdir_data = await self.scan_sftp_infra(full_path)
                        total_files += subdir_data["total_files"]
                        total_size += subdir_data["total_size"]
                        scanned_directories += subdir_data.get("scanned_directories", 0)
                        accessible_folders.extend(subdir_data.get("accessible_folders", []))
                        inaccessible_folders.extend(subdir_data.get("inaccessible_folders", []))
                        permission_errors.extend(subdir_data.get("permission_errors", []))
                    except Exception as dir_error:
                        logger.warning(f"Cannot access subdirectory {full_path}: {dir_error}")
                        inaccessible_folders.append({"path": full_path, "error": str(dir_error)})
                        if full_path in accessible_folders:
                            accessible_folders.remove(full_path)
                elif file_info["is_file"]:
                    logger.debug(f"Found file: {full_path} (size: {file_info['size']})")
                    total_files += 1
                    total_size += file_info["size"]
                else:
                    logger.warning(f"Unknown file type: {full_path}")
                    inaccessible_folders.append({"path": full_path, "error": "Unknown file type"})
                    
            except Exception as e:
                logger.warning(f"Error processing {full_path}: {e}")
                inaccessible_folders.append({"path": full_path, "error": str(e)})
                continue

        result = {
            "total_files": total_files, 
            "total_size": total_size,
            "scanned_directories": scanned_directories,
            "accessible_folders": accessible_folders,
            "inaccessible_folders": inaccessible_folders,
            "permission_errors": permission_errors
        }
        
        logger.info(f"Infrastructure scan completed for {path}: {total_files} files, {total_size} bytes, {scanned_directories} directories")
        logger.info(f"Accessible folders: {len(accessible_folders)}, Inaccessible folders: {len(inaccessible_folders)}")
        
        return result

    async def fetch_file_from_sftp(self, remote_path: str, semaphore) -> dict:
        """Download and process individual file from SFTP with enhanced error handling"""
        file_name = os.path.basename(remote_path)
        file_ext = get_file_extension(file_name)
        file_uri = remote_path

        # Skip logic
        if self.rule_manager.skip_file(file_uri):
            logger.debug(f"Skipping file {file_name} based on skip rules")
            return None

        # Validate file before download
        validation = await self.validate_file_before_download(remote_path)
        if not validation["valid"]:
            logger.warning(f"File validation failed for {remote_path}: {validation['error']}")
            return {
                "name": file_name,
                "size": 0,
                "file_type": file_ext,
                "file_uri": file_uri,
                "filepath": "",
                "success": False,
                "error": validation["error"]
            }

        file_info = validation["file_info"]
        size = file_info["size"]

        try:
            # Create temp directory
            temp_dir = self.local_data_dir
            create_folder(temp_dir)
            local_path = f"{temp_dir}/{uuid4()}_{file_name}"

            logger.debug(f"Downloading {remote_path} ({size} bytes)")
            
            async with semaphore:
                try:
                    # Double-check file exists before opening
                    if not await self.check_file_exists(remote_path):
                        raise FileNotFoundError(f"File does not exist: {remote_path}")
                    
                    # Try to open file for reading
                    if self.sftp_client is None:
                        raise Exception("SFTP client is not connected.")
                    async with self.sftp_client.open(remote_path, "rb") as rfile:
                        with open(local_path, "wb") as outfile:
                            downloaded = 0
                            chunk_size = 1024 * 1024  # 1MB chunks
                            
                            while True:
                                try:
                                    chunk = await rfile.read(chunk_size)
                                    if not chunk:
                                        break
                                    outfile.write(chunk)
                                    downloaded += len(chunk)
                                    
                                    # Log progress for large files
                                    if size > 10 * 1024 * 1024:  # 10MB
                                        progress = (downloaded / size) * 100
                                        if downloaded % (5 * 1024 * 1024) == 0:  # Log every 5MB
                                            logger.info(f"Download progress for {file_name}: {progress:.1f}%")
                                            
                                except Exception as read_error:
                                    logger.error(f"Error reading chunk from {remote_path}: {read_error}")
                                    # Clean up partial file
                                    if os.path.exists(local_path):
                                        os.remove(local_path)
                                    raise read_error
                                    
                    # Verify downloaded file size
                    if os.path.exists(local_path):
                        actual_size = os.path.getsize(local_path)
                        if actual_size != size:
                            logger.warning(f"Size mismatch for {remote_path}: expected {size}, got {actual_size}")
                            # Don't fail, just log the warning
                    
                    logger.info(f"Successfully downloaded {remote_path} ({downloaded} bytes)")
                    
                    # Update storage usage counter
                    # self.update_storage_usage(size)
                    
                    return {
                        "name": file_name,
                        "size": size,
                        "file_type": file_ext,
                        "file_uri": file_uri,
                        "filepath": local_path,
                        "created": str(file_info["atime"]),
                        "modified": str(file_info["mtime"]),
                        "success": True,
                        "downloaded_size": downloaded
                    }
                    
                except PermissionError as perm_error:
                    logger.error(f"Permission denied downloading {remote_path}: {perm_error}")
                    return {
                        "name": file_name,
                        "size": size,
                        "file_type": file_ext,
                        "file_uri": file_uri,
                        "filepath": "",
                        "success": False,
                        "error": f"Permission denied: {perm_error}"
                    }
                except FileNotFoundError as fnf_error:
                    logger.error(f"File not found during download {remote_path}: {fnf_error}")
                    return {
                        "name": file_name,
                        "size": size,
                        "file_type": file_ext,
                        "file_uri": file_uri,
                        "filepath": "",
                        "success": False,
                        "error": f"File not found: {fnf_error}"
                    }
                except Exception as download_error:
                    logger.error(f"Unexpected error downloading {remote_path}: {download_error}")
                    return {
                        "name": file_name,
                        "size": size,
                        "file_type": file_ext,
                        "file_uri": file_uri,
                        "filepath": "",
                        "success": False,
                        "error": f"Download failed: {download_error}"
                    }
                    
        except Exception as e:
            logger.error(f"Failed downloading {remote_path}: {e}")
            return {
                "name": file_name,
                "size": 0,
                "file_type": file_ext,
                "file_uri": file_uri,
                "filepath": "",
                "success": False,
                "error": str(e)
            }

    async def deep_walk(self, path: str, semaphore) -> AsyncGenerator[FileMetadata, None]:
        """Recursively walk through SFTP directory structure and yield file metadata"""
        try:
            logger.info(f"Deep walking directory: {path}")
            entries, error = await self.safe_listdir(path)
            if error:
                logger.warning(f"Cannot access directory {path}: {error}")
                # Yield folder metadata even if we can't access it
                yield FileMetadata(
                    service_name=self.service_name,
                    service_type=self.service_type,
                    service_provider=self.service_provider,
                    sub_service=self.sub_service,
                    file_key=os.path.basename(path),
                    file_size=0,
                    file_type="folder",
                    file_uri=path,
                    local_filepath="",
                    details={
                        "error": error,
                        "access_denied": True,
                        "region": "India"
                    }
                )
                return
            
            logger.info(f"Processing {len(entries)} entries in {path}")
        except Exception as e:
            logger.warning(f"Cannot access directory {path}: {e}")
            yield FileMetadata(
                service_name=self.service_name,
                service_type=self.service_type,
                service_provider=self.service_provider,
                sub_service=self.sub_service,
                file_key=os.path.basename(path),
                file_size=0,
                file_type="folder",
                file_uri=path,
                local_filepath="",
                details={
                    "error": str(e),
                    "access_denied": True,
                    "region": "India"
                }
            )
            return

        for entry in entries:
            if entry in ('.', '..'):
                continue
                
            full_path = self.normalize_sftp_path(path, entry)
            
            if full_path in self.visited_paths:
                logger.debug(f"Skipping already processed file: {full_path}")
                continue
            self.visited_paths.add(full_path)

            try:
                file_info = await self.check_file_permissions(full_path)
                
                if not file_info["exists"]:
                    # Skip non-existent files - no metadata saved
                    logger.warning(f"Skipping non-existent file: {full_path}")
                    continue
                
                if not file_info["readable"]:
                    # Skip files without read permission - no metadata saved
                    logger.warning(f"Skipping file without read permission: {full_path}")
                    continue
                
                if file_info["is_directory"]:
                    logger.debug(f"Found directory: {full_path}")
                    
                    # Yield folder metadata
                    yield FileMetadata(
                        service_name=self.service_name,
                        service_type=self.service_type,
                        service_provider=self.service_provider,
                        sub_service=self.sub_service,
                        file_key=entry,
                        file_size=0,
                        file_type="folder",
                        file_uri=full_path,
                        local_filepath="",
                        details={
                            "created": str(file_info["atime"]),
                            "modified": str(file_info["mtime"]),
                            "permissions": file_info["permissions"],
                            "region": "India"
                        }
                    )
                    
                    # Recursively walk subdirectories
                    async for file_metadata in self.deep_walk(full_path, semaphore):
                        yield file_metadata
                        
                elif file_info["is_file"]:
                    # Check if file type is supported or is ZIP
                    file_name = os.path.basename(full_path)
                    file_ext = get_file_extension(file_name)
                    
                    if file_ext not in PROFILE_SUPPORTED_FILE_TYPES and not self.is_zip_file(file_name):
                        logger.debug(f"Skipping unsupported file type: {full_path}")
                        continue
                    
                    # Check storage limit before processing
                    # if not self.check_storage_limit(file_info["size"]):
                    #     logger.warning(f"Storage limit reached (50GB). Stopping ingestion at file: {full_path}")
                    #     logger.info(f"Final storage status: {self.get_storage_status()}")
                    #     return  # Stop the entire ingestion process
                    
                    # Process file
                    logger.debug(f"Processing file: {full_path}")
                    file_result = await self.fetch_file_from_sftp(full_path, semaphore)
                    
                    if file_result:
                        if file_result.get("success"):
                            # Check if downloaded file is a ZIP
                            if self.is_zip_file(file_result["name"]):
                                try:
                                    # Check storage limit for ZIP file
                                    # if not self.check_storage_limit(file_result["size"]):
                                    #     logger.warning(f"Storage limit reached (50GB). Stopping ZIP processing at: {file_result['name']}")
                                    #     logger.info(f"Final storage status: {self.get_storage_status()}")
                                    #     # Clean up the ZIP file
                                    #     if os.path.exists(file_result["filepath"]):
                                    #         os.remove(file_result["filepath"])
                                    #     return  # Stop the entire ingestion process
                                    
                                    # Process ZIP file
                                    temp_dir = tempfile.mkdtemp()
                                    extracted_files = self.process_zip_file(file_result["filepath"], temp_dir)
                                    
                                    # Process extracted files recursively with parent ZIP path
                                    async for extracted_metadata in self.process_extracted_files(extracted_files, semaphore, file_result["file_uri"]):
                                        yield extracted_metadata
                                    
                                    # Clean up temporary directory
                                    shutil.rmtree(temp_dir, ignore_errors=True)
                                    
                                    # Delete original ZIP file after processing - NO metadata for ZIP file itself
                                    if os.path.exists(file_result["filepath"]):
                                        os.remove(file_result["filepath"])
                                        logger.info(f"Deleted ZIP file after processing: {file_result['filepath']}")
                                    
                                    # Yield metadata for the ZIP file itself only if extraction was successful
                                    if extracted_files:
                                        yield FileMetadata(
                                            service_name=self.service_name,
                                            service_type=self.service_type,
                                            service_provider=self.service_provider,
                                            sub_service=self.sub_service,
                                            file_key=file_result["name"],
                                            file_size=file_result["size"],
                                            file_type=file_result["file_type"],
                                            file_uri=file_result["file_uri"],
                                            local_filepath=file_result["filepath"],
                                            details={
                                                "created": file_result["created"],
                                                "modified": file_result["modified"],
                                                "downloaded_size": file_result.get("downloaded_size", 0),
                                                "region": "India",
                                                "is_zip": True
                                            }
                                        )
                                    
                                except Exception as e:
                                    logger.error(f"Failed to process ZIP file {file_result['name']}: {e}")
                                    if os.path.exists(file_result["filepath"]):
                                        os.remove(file_result["filepath"])
                                        logger.warning(f"Deleted ZIP file after failure: {file_result['filepath']}")
                            else:
                                # Yield file metadata for non-ZIP files
                                yield FileMetadata(
                                    service_name=self.service_name,
                                    service_type=self.service_type,
                                    service_provider=self.service_provider,
                                    sub_service=self.sub_service,
                                    file_key=file_result["name"],
                                    file_size=file_result["size"],
                                    file_type=file_result["file_type"],
                                    file_uri=file_result["file_uri"],
                                    local_filepath=file_result["filepath"],
                                    details={
                                        "created": file_result["created"],
                                        "modified": file_result["modified"],
                                        "downloaded_size": file_result.get("downloaded_size", 0),
                                        "region": "India"
                                    }
                                )
                        else:
                            # Skip failed downloads - no metadata saved
                            logger.warning(f"Skipping failed download: {file_result.get('name', 'unknown')}")
                else:
                    # Skip unknown file types - no metadata saved
                    logger.warning(f"Skipping unknown file type: {full_path}")
                    
            except Exception as e:
                # Skip processing errors - no metadata saved
                logger.warning(f"Skipping item due to processing error {full_path}: {e}")

    async def infra_scan(self):
        """Simple infrastructure scan: counts files and folders only."""
        try:
            logger.info("Starting SFTP infrastructure scan...")
            await self.get_service_details()
            self.visited_paths = set()
            data = await self.scan_sftp_infra(self.root_path)
            return {
                "total_files": data.get("total_files", 0),
                "total_folders": data.get("scanned_directories", 0)
            }
        except Exception as e:
            logger.error(f"SFTP infra scan error: {e}")
            return {
                "total_files": 0,
                "total_folders": 0,
                "error": str(e)
            }

    async def deep_scan(self) -> AsyncGenerator[FileMetadata, None]:
        """Perform deep scan of SFTP server - downloads and processes all files"""
        try:
            logger.info("Starting SFTP deep scan...")
            await self.get_service_details()
            self.visited_paths = set()
            
            # Test root path accessibility
            try:
                entries, error = await self.safe_listdir(self.root_path)
                if error:
                    logger.error(f"Root path {self.root_path} is not accessible: {error}")
                    return
                logger.info(f"Root path {self.root_path} is accessible with {len(entries)} entries")
            except Exception as e:
                logger.error(f"Root path {self.root_path} is not accessible: {e}")
                return
            
            # Create semaphore for concurrent downloads
            semaphore = asyncio.Semaphore(CONCURRENT_LIMIT)
            logger.info(f"Using concurrent limit: {CONCURRENT_LIMIT}")
            
            file_count = 0
            folder_count = 0
            folder_stats = defaultdict(lambda: {"count": 0, "size": 0})

            async for file_metadata in self.deep_walk(self.root_path, semaphore):
                if file_metadata.file_type == "folder":
                    folder_count += 1
                    logger.info(f"Found folder {folder_count}: {file_metadata.file_key} at {file_metadata.file_uri}")
                else:
                    file_count += 1
                    size = int(file_metadata.file_size or 0)

                    # Determine top-level folder for asset grouping
                    uri = file_metadata.file_uri or ""
                    parts = uri.strip("/").split("/")
                    folder = parts[0] if parts else "root"

                    folder_key = f"sftp_{folder}"
                    folder_stats[folder_key]["count"] += 1
                    folder_stats[folder_key]["size"] += size

                yield file_metadata
                
            logger.info(f"Deep scan completed. Total files processed: {file_count}, Total folders found: {folder_count}")

            # --- Save asset_details and access_control ---
            assets, access = [], []
            for folder_key, stats in folder_stats.items():
                assets.append(
                    AssetDetailsSchema(
                        asset_name=folder_key,
                        service_provider=self.service_provider,
                        type="file_storage",
                        category="unstructured",
                        location="Global",
                        owner="system",
                        security=True,
                        size=stats["size"],
                        count=stats["count"],
                        access_category="restricted",
                        service_name=str(self.service_name),
                        steward=str(self.service_steward),
                    )
                )
                access.append({
                    "asset_name": folder_key,
                    "user_or_role": getattr(self, "username", "unknown"),
                    "role": "owner",
                    "access": "full",
                })

            if assets:
                try:
                    load_asset_details(assets)
                    logger.info(f"[SFTP] Created {len(assets)} asset(s) in asset_details")
                except Exception as e:
                    logger.error(f"[SFTP] Failed to save asset_details: {e}")

            if access:
                try:
                    load_access_controls(access)
                    logger.info(f"[SFTP] Created {len(access)} access_control entries")
                except Exception as e:
                    logger.error(f"[SFTP] Failed to save access_controls: {e}")
            # ------------------------------------------------
            
            # Log final storage status
            # storage_status = self.get_storage_status()
            # logger.info(f"Final storage status: {storage_status}")
            
        except Exception as e:
            logger.error(f"SFTP deep scan error: {e}")
        finally:
            # Clean up connection
            try:
                if self.sftp_client:
                    self.sftp_client.exit()
                    logger.info("SFTP client closed")
            except Exception:
                pass

    async def user_scan(self, search_files: List[Dict]):
        """Download only specified files for DSR. Supports ZIP member selection via
        file_uri like: /{service_name}/path/to/archive.zip!/internal/path/file.xlsx
        For SFTP, file_uri is expected as absolute remote path under root_path.
        """
        try:
            await self.get_service_details()

            results: List[Dict] = []
            semaphore = asyncio.Semaphore(CONCURRENT_LIMIT)

            def parse_uri(uri: str):
                """Split outer path and optional zip inner path.
                Supports explicit "!/" and implicit zip/member paths.
                Also strips a leading "/{service_name}" prefix if present.
                """
                outer, inner = uri, None
                # Strip service-name prefix if present
                prefix = f"/{self.service_name}"
                if outer.startswith(prefix):
                    outer = outer[len(prefix):]
                if "!/" in outer:
                    parts = outer.split("!/", 1)
                    return parts[0], parts[1]
                norm = normalize_path(outer)
                for ext in ('.zip', '.zipx'):
                    marker = f"{ext}/"
                    if marker in norm.lower():
                        idx = norm.lower().index(marker) + len(ext)
                        return norm[:idx], norm[idx:].lstrip('/')
                return outer, inner

            def normalize_path(p: str) -> str:
                if not p:
                    return p
                p = p.replace('\\', '/').strip()
                while '//' in p:
                    p = p.replace('//', '/')
                return p if p.startswith('/') else '/' + p

            for file_info in search_files:
                file_uri = str(file_info.get("file_uri", ""))
                file_name = str(file_info.get("file_name", ""))
                if not file_uri or not file_name:
                    continue

                if self.rule_manager and self.rule_manager.skip_file(file_uri):
                    logger.info(f"Skipping by rule: {file_uri}")
                    continue

                outer_uri, inner_path = parse_uri(file_uri)
                remote_path = normalize_path(outer_uri)

                # Prefix with root_path if not already
                if self.root_path and not remote_path.startswith(self.root_path):
                    remote_path = self.normalize_sftp_path(self.root_path, remote_path)

                validation = await self.validate_file_before_download(remote_path)
                if not validation.get("valid"):
                    logger.warning(f"Validation failed for {remote_path}: {validation.get('error')}")
                    continue

                temp_dir = self.local_data_dir
                create_folder(temp_dir)
                local_path = f"{temp_dir}/{uuid4()}_{file_name or os.path.basename(remote_path)}"

                async with semaphore:
                    try:
                        async with self.sftp_client.open(remote_path, "rb") as rfile:
                            with open(local_path, "wb") as outfile:
                                while True:
                                    chunk = await rfile.read(1024 * 1024)
                                    if not chunk:
                                        break
                                    outfile.write(chunk)
                    except Exception as e:
                        logger.error(f"Download failed for {remote_path}: {e}")
                        if os.path.exists(local_path):
                            os.remove(local_path)
                        continue

                if inner_path and self.is_zip_file(local_path):
                    try:
                        with zipfile.ZipFile(local_path, 'r') as zip_ref:
                            norm_inner = normalize_path(inner_path).lstrip('/')
                            # Match exactly
                            candidates = [m for m in zip_ref.namelist() if normalize_path(m).lstrip('/') == norm_inner]
                            if not candidates:
                                logger.warning(f"Inner member not found in zip: {inner_path}")
                            else:
                                member = candidates[0]
                                extract_dir = tempfile.mkdtemp()
                                zip_ref.extract(member, extract_dir)
                                extracted_path = os.path.join(extract_dir, member)
                                final_dir = os.path.join(self.local_data_dir, "extracted")
                                create_folder(final_dir)
                                final_path = f"{final_dir}/{uuid4()}_{os.path.basename(member)}"
                                shutil.move(extracted_path, final_path)
                                shutil.rmtree(extract_dir, ignore_errors=True)
                                # Replace to extracted member and remove the outer zip
                                try:
                                    if os.path.exists(local_path):
                                        os.remove(local_path)
                                except Exception:
                                    pass
                                local_path = final_path
                    except Exception as e:
                        logger.error(f"Zip member extraction failed: {e}")

                results.append({
                    "service_name": self.service_name,
                    "service_provider": str(self.service_provider),
                    "sub_service": str(self.sub_service),
                    "file_name": file_name or os.path.basename(remote_path),
                    "file_uri": file_uri,
                    "file_type": get_file_extension(file_name or remote_path),
                    "file_size": os.path.getsize(local_path) if os.path.exists(local_path) else 0,
                    "local_filepath": local_path,
                })

            if results:
                yield results
        except Exception as e:
            logger.error(f"SFTP user_scan failed: {e}", exc_info=True)
            try:
                if self.connection:
                    self.connection.close()
                    logger.info("SFTP connection closed")
            except Exception:
                pass

    async def list_directory_structure(self, path: str, max_depth: int = 3, current_depth: int = 0) -> dict:
        """List directory structure for debugging purposes"""
        if current_depth >= max_depth:
            return {"name": os.path.basename(path), "type": "directory", "truncated": True}
        
        try:
            entries, error = await self.safe_listdir(path)
            if error:
                return {
                    "name": os.path.basename(path), 
                    "type": "directory", 
                    "accessible": False,
                    "error": error
                }

            structure = {
                "name": os.path.basename(path) or path,
                "type": "directory",
                "children": [],
                "accessible": True
            }
            
            for entry in entries:
                if entry in ('.', '..'):
                    continue
                    
                full_path = self.normalize_sftp_path(path, entry)
                try:
                    stat = await self.sftp_client.stat(full_path)
                    if stat.permissions & 0o170000 == 0o040000:  # Directory
                        child_structure = await self.list_directory_structure(
                            full_path, max_depth, current_depth + 1
                        )
                        structure["children"].append(child_structure)
                    else:
                        structure["children"].append({
                            "name": entry,
                            "type": "file",
                            "size": stat.size,
                            "accessible": True
                        })
                except Exception as stat_error:
                    # Try to access as directory
                    try:
                        await self.sftp_client.listdir(full_path)
                        structure["children"].append({
                            "name": entry,
                            "type": "directory",
                            "accessible": True,
                            "stat_error": str(stat_error)
                        })
                    except Exception as list_error:
                        structure["children"].append({
                            "name": entry,
                            "type": "unknown",
                            "accessible": False,
                            "error": str(list_error)
                        })
            
            return structure
            
        except Exception as e:
            return {
                "name": os.path.basename(path), 
                "type": "directory", 
                "accessible": False,
                "error": str(e)
            }

    async def debug_scan(self):
        """Debug method to check directory structure"""
        try:
            await self.get_service_details()
            structure = await self.list_directory_structure(self.root_path)
            logger.info(f"Directory structure: {structure}")
            return structure
        except Exception as e:
            logger.error(f"Debug scan error: {e}")
            return {"error": str(e)}