import aiohttp
from src.utils.logger import get_ingestion_logger
from src.utils.exceptions import ClientException, ServerException

logger = get_ingestion_logger()

async def test_cloudflare_connection(data: dict) -> dict:
    creds = data.get("credentials", {})
    
    api_token = creds.get("api_token")
    
    if not api_token:
        raise ClientException("Missing required Cloudflare credential (api_token)", code=400)
    
    try:
        headers = {
            "Authorization": f"Bearer {api_token}",
            "Content-Type": "application/json",
        }
        
        test_url = "https://api.cloudflare.com/client/v4/user"
        
        async with aiohttp.ClientSession() as session:
            async with session.get(test_url, headers=headers) as response:
                response_data = await response.json()
                
                if response.status == 200 and response_data.get("success", False):
                    user_data = response_data.get("result", {})
                    user_name = user_data.get("first_name", "")
                    return {
                        "status": True,
                        "message": f"Successfully connected to <PERSON>flare for {user_name}"
                    }
                else:
                    error_msg = response_data.get("errors", [{"message": "Unknown error"}])[0].get("message")
                    logger.error(f"Cloudflare connection test failed: {error_msg}")
                    return {
                        "status": False,
                        "message": f"Failed to connect to Cloudflare API: {error_msg}"
                    }
    except Exception as e:
        logger.error(f"Error testing Cloudflare connection: {str(e)}")
        return {
            "status": False,
            "message": f"Error connecting to Cloudflare: {str(e)}"
        }   