import os
import json
import aiohttp
import aiofiles
from datetime import datetime
from uuid import uuid4
from typing import AsyncGenerator, Dict, List, Optional
import aiobotocore
from src.utils.logger import get_ingestion_logger
from src.common.config import (
    TEMP_DATA_DIR,
    PROFILE_SUPPORTED_FILE_TYPES,
    PROFILE_MAX_SIZE_THRESHOLD,
)
from src.common.constants import ServiceTypes, ServiceProviders, ScanTypes, LocalSubservice
from src.ingestion.base_source import UnstructureSource
from src.ingestion.data_class import FileMetadata
from src.modules.repos import DatabaseManager
from src.utils.helpers import create_folder, file_exists, get_file_extension

logger = get_ingestion_logger()

class CloudflareSource(UnstructureSource):
    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.CLOUDFLARE.value, service_name)
        self.db = DatabaseManager()
        self.service_provider = ServiceProviders.LOCAL
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"
        create_folder(self.local_data_dir)

    async def get_service_details(self) -> Dict:
        self.service = await self.db.get_service_by_service_name(self.service_name)
        credentials = self.service.get("credentials", {})
        self.api_token = credentials.get("api_token")
        self.account_id = credentials.get("account_id")
        self.zone_ids = credentials.get("zone_ids", [])
        self.access_key = credentials.get("access_key")
        self.secret_key = credentials.get("secret_key")
        self.r2_endpoint = credentials.get("r2_endpoint")
        return self.service

    async def get_r2_client(self):
        session = aiobotocore.get_session()
        return session.create_client(
            "s3",
            region_name="auto",
            endpoint_url=self.r2_endpoint,
            aws_access_key_id=self.access_key,
            aws_secret_access_key=self.secret_key,
        )

    async def test_connection(self) -> dict:
        try:
            headers = {
                "Authorization": f"Bearer {self.api_token}",
                "Content-Type": "application/json",
            }
            test_url = "https://api.cloudflare.com/client/v4/user"

            async with aiohttp.ClientSession() as session:
                async with session.get(test_url, headers=headers) as response:
                    response_data = await response.json()
                    if response.status == 200 and response_data.get("success", False):
                        user_data = response_data.get("result", {})
                        user_name = user_data.get("first_name", "Unknown")
                        logger.info(f"Successfully connected to Cloudflare for user: {user_name}")
                        return {"status": True, "message": f"Connection successful for user: {user_name}"}
                    else:
                        error_msg = response_data.get("errors", [{"message": "Unknown error"}])[0].get("message")
                        logger.error(f"Cloudflare connection test failed: {error_msg}")
                        return {"status": False, "message": f"Failed to connect to Cloudflare API: {error_msg}"}
        except Exception as e:
            logger.error(f"Error testing Cloudflare connection: {str(e)}")
            return {"status": False, "message": f"Error connecting to Cloudflare: {str(e)}"}

    async def download_r2_file(self, s3_client, bucket_name: str, object_key: str, filepath: str) -> str:
        if file_exists(filepath):
            return filepath
        try:
            response = await s3_client.get_object(Bucket=bucket_name, Key=object_key)
            body = response["Body"]
            async with aiofiles.open(filepath, "wb") as f:
                while True:
                    chunk = await body.read(8192)
                    if not chunk:
                        break
                    await f.write(chunk)
        except Exception as e:
            logger.error(f"Error downloading R2 object {object_key}: {str(e)}")
        return filepath

    async def deep_scan(self) -> AsyncGenerator[FileMetadata, None]:
        await self.get_service_details()
        create_folder(self.local_data_dir)

        try:
            async with await self.get_r2_client() as s3_client:
                response = await s3_client.list_buckets()
                for bucket in response.get("Buckets", []):
                    bucket_name = bucket["Name"]
                    logger.info(f"Scanning R2 bucket: {bucket_name}")

                    paginator = s3_client.get_paginator("list_objects_v2")
                    async for page in paginator.paginate(Bucket=bucket_name):
                        for obj in page.get("Contents", []):
                            object_key = obj["Key"]
                            object_size = obj.get("Size", 0)

                            if object_key.endswith("/"):
                                logger.info(f"Skipping folder-like key: {object_key}")
                                continue

                            file_ext = get_file_extension(object_key)
                            file_uri = f"r2://{bucket_name}/{object_key}"

                            if self.rule_manager.skip_file(file_uri):
                                logger.info(f"Skipped by rule manager: {object_key}")
                                continue

                            if file_ext not in PROFILE_SUPPORTED_FILE_TYPES:
                                logger.info(f"Skipping unsupported type: {object_key}")
                                continue

                            if object_size > PROFILE_MAX_SIZE_THRESHOLD:
                                logger.info(f"Skipping large file {object_key} ({object_size})")
                                continue

                            filepath = os.path.join(self.local_data_dir, f"{uuid4()}.{file_ext}")
                            await self.download_r2_file(s3_client, bucket_name, object_key, filepath)

                            yield FileMetadata(
                                service_name=self.service_name,
                                service_type=self.service_type,
                                service_provider=ServiceProviders.LOCAL,
                                sub_service=LocalSubservice.CLOUDFLARE.value,
                                file_key=object_key,
                                file_size=object_size,
                                file_type=file_ext,
                                file_uri=file_uri,
                                local_filepath=filepath,
                                piis=[],
                                scan_type=ScanTypes.FileMetadata.value,
                                details={
                                    "bucket": bucket_name,
                                    "timestamp": datetime.now().isoformat()
                                },
                            )
        except Exception as e:
            logger.error(f"Error in Cloudflare deep scan with file extraction: {str(e)}")

    async def infra_scan(self) -> Dict:
        await self.get_service_details()

        headers = {
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json",
        }

        zones = []

        try:
            async with aiohttp.ClientSession() as session:
                if self.zone_ids:
                    for zone_id in self.zone_ids:
                        zone_url = f"https://api.cloudflare.com/client/v4/zones/{zone_id}"
                        async with session.get(zone_url, headers=headers) as response:
                            if response.status == 200:
                                data = await response.json()
                                if data.get("success"):
                                    zone_data = data.get("result", {})
                                    zones.append({
                                        "id": zone_data.get("id"),
                                        "name": zone_data.get("name"),
                                        "status": zone_data.get("status"),
                                        "paused": zone_data.get("paused"),
                                        "type": zone_data.get("type"),
                                        "plan": zone_data.get("plan", {}).get("name")
                                    })
                else:
                    zones_url = "https://api.cloudflare.com/client/v4/zones"
                    async with session.get(zones_url, headers=headers) as response:
                        if response.status == 200:
                            data = await response.json()
                            if data.get("success"):
                                for zone_data in data.get("result", []):
                                    zones.append({
                                        "id": zone_data.get("id"),
                                        "name": zone_data.get("name"),
                                        "status": zone_data.get("status"),
                                        "paused": zone_data.get("paused"),
                                        "type": zone_data.get("type"),
                                        "plan": zone_data.get("plan", {}).get("name")
                                    })

            return {
                "zones": zones,
                "total_zones": len(zones)
            }
        except Exception as e:
            logger.error(f"Error in Cloudflare infra scan: {str(e)}")
            return {
                "error": str(e),
                "zones": [],
                "total_zones": 0
            }