import asyncio
import json
import time
from typing import AsyncGenerator, Dict, List, Optional
from uuid import uuid4
from datetime import datetime
import hashlib

import aiohttp

from src.common.config import (
    CONCURRENT_LIMIT,
    TEMP_DATA_DIR,
    PROFILE_SUPPORTED_FILE_TYPES,
    PROFILE_MAX_SIZE_THRESHOLD,
)
from src.common.constants import ServiceProviders, ServiceTypes, LocalSubservice
from src.ingestion.base_source import UnstructureSource
from src.ingestion.data_class import FileMetadata
from src.modules.repos import DatabaseManager
from src.utils.helpers import create_folder, get_file_extension
from src.utils.minio_client import MinioClient
from src.utils.logger import get_ingestion_logger
from src.utils.loaders import load_access_controls, load_asset_details
from src.ingestion.data_class import AssetsDetails as AssetDetailsSchema

logger = get_ingestion_logger()


class HumanFirewallSource(UnstructureSource):
    """
    Human Firewall Connector - Security Awareness Training Platform Integration
    
    This connector integrates with the Human Firewall API to fetch:
    - Organization information
    - User data and enrollment status
    - Training campaigns and their status
    - Phishing simulation reports
    - Training completion records
    - User activity and engagement metrics
    - Security awareness scores
    - Department-level analytics
    """
    
    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.HUMAN_FIREWALL.value, service_name)
        self.db = DatabaseManager()
        self.service_provider = ServiceProviders.HUMAN_FIREWALL
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"
        
        # Authentication related
        self.access_token = None
        self.token_expires_at = None
        self.refresh_token = None
        
        # Service configuration
        self.api_base_url = None
        self.tenant_id = None
        self.client_id = None
        self.client_secret = None
        self.api_key = None
        
        # Rate limiting and retry configuration
        self.max_retries = 3
        self.retry_delay = 2
        self.request_timeout = 30
        self.max_concurrent_requests = CONCURRENT_LIMIT or 5
        
        # Cache for processed file URIs to prevent duplicates
        self.processed_uris = set()
        
        # Statistics tracking
        self.stats = {
            "total_files": 0,
            "total_size": 0,
            "campaigns": 0,
            "phishing_reports": 0,
            "training_records": 0,
            "user_activity": 0,
            "errors": 0
        }
        # MinIO client
        self.minio_client = MinioClient()

    async def get_service_details(self) -> Dict:
        """Gets service details from database and initializes authentication"""
        try:
            self.service = await self.db.get_service_by_service_name(self.service_name)
            if not self.service:
                raise Exception(f"Service {self.service_name} not found in database")
            
            credentials = self.service.get("credentials", {})
            
            # Extract credentials with multiple possible field names
            self.tenant_id = (
                credentials.get("tenant_id") or 
                credentials.get("organization_id") or 
                credentials.get("org_id")
            )
            self.client_id = credentials.get("client_id")
            self.client_secret = credentials.get("client_secret")
            self.api_key = credentials.get("api_key")
            self.api_base_url = credentials.get("api_base_url", "").rstrip("/")
            self.refresh_token = credentials.get("refresh_token")
            self.service_steward = self.service.get("data_steward")
            
            if not self.api_base_url:
                raise Exception("API base URL is required in credentials")
            
            # Authenticate based on available credentials
            if self.api_key:
                logger.info("Using API Key authentication")
                self.access_token = self.api_key
                self.token_expires_at = None  # API keys don't expire
            elif self.client_id and self.client_secret:
                logger.info("Using OAuth2 Client Credentials authentication")
                auth_result = await self.authenticate()
                if not auth_result:
                    raise Exception("Failed to authenticate with Human Firewall API")
            else:
                raise Exception(
                    "Either 'api_key' or ('client_id' + 'client_secret') is required in credentials"
                )
            
            logger.info(f"Successfully initialized service: {self.service_name}")
            return self.service
            
        except Exception as e:
            logger.error(f"Error getting service details: {str(e)}", exc_info=True)
            raise

    async def authenticate(self) -> bool:
        """
        Authenticate and get access token using OAuth2 Client Credentials flow
        Tries multiple common authentication endpoints
        """
        auth_endpoints = [
            "/oauth/token",
            "/auth/token",
            "/api/v1/oauth/token",
            "/api/oauth/token",
            "/v1/auth/token",
            "/api/v1/auth/token",
            "/token"
        ]
        
        payload = {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "grant_type": "client_credentials"
        }
        
        # Add tenant_id if provided (multi-tenant support)
        if self.tenant_id:
            payload["tenant_id"] = self.tenant_id
        
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        for endpoint in auth_endpoints:
            try:
                url = f"{self.api_base_url}{endpoint}"
                logger.debug(f"Attempting authentication at: {url}")
                
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        url, 
                        json=payload, 
                        headers=headers,
                        timeout=aiohttp.ClientTimeout(total=self.request_timeout)
                    ) as response:
                        response_text = await response.text()
                        
                        if response.status == 200:
                            data = json.loads(response_text)
                            self.access_token = data.get("access_token")
                            
                            if not self.access_token:
                                logger.error(f"No access_token in response from {endpoint}")
                                continue
                            
                            expires_in = data.get("expires_in", 3600)
                            self.token_expires_at = time.time() + expires_in - 60  # Refresh 60s early
                            
                            # Store refresh token if provided
                            if "refresh_token" in data:
                                self.refresh_token = data.get("refresh_token")
                            
                            logger.info(
                                f"Authentication successful using {endpoint}. "
                                f"Token expires in {expires_in} seconds"
                            )
                            return True
                        else:
                            logger.debug(
                                f"Authentication failed at {endpoint} - "
                                f"Status: {response.status}, Response: {response_text[:200]}"
                            )
                            
            except Exception as e:
                logger.debug(f"Authentication attempt failed at {endpoint}: {str(e)}")
                continue
        
        logger.error("All authentication endpoints failed")
        return False

    async def ensure_valid_token(self) -> bool:
        """Ensure we have a valid access token, refresh if needed"""
        try:
            # If using API key, always valid
            if self.api_key:
                return True
            
            # Check if token needs refresh
            if not self.access_token:
                logger.info("No access token found, authenticating...")
                return await self.authenticate()
            
            if self.token_expires_at and time.time() >= self.token_expires_at:
                logger.info("Access token expired or expiring soon, refreshing...")
                
                # Try refresh token flow first
                if self.refresh_token:
                    if await self.refresh_access_token():
                        return True
                
                # Fallback to full authentication
                return await self.authenticate()
            
            return True
            
        except Exception as e:
            logger.error(f"Error ensuring valid token: {str(e)}")
            return await self.authenticate()

    async def refresh_access_token(self) -> bool:
        """Refresh access token using refresh token"""
        refresh_endpoints = [
            "/oauth/token",
            "/auth/token",
            "/api/v1/oauth/token"
        ]
        
        payload = {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "refresh_token": self.refresh_token,
            "grant_type": "refresh_token"
        }
        
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        for endpoint in refresh_endpoints:
            try:
                url = f"{self.api_base_url}{endpoint}"
                
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        url, 
                        json=payload, 
                        headers=headers,
                        timeout=aiohttp.ClientTimeout(total=self.request_timeout)
                    ) as response:
                        if response.status == 200:
                            data = await response.json()
                            self.access_token = data.get("access_token")
                            expires_in = data.get("expires_in", 3600)
                            self.token_expires_at = time.time() + expires_in - 60
                            
                            # Update refresh token if new one provided
                            new_refresh_token = data.get("refresh_token")
                            if new_refresh_token:
                                self.refresh_token = new_refresh_token
                            
                            logger.info("Access token refreshed successfully")
                            return True
                            
            except Exception as e:
                logger.debug(f"Refresh failed at {endpoint}: {str(e)}")
                continue
        
        logger.warning("All refresh token attempts failed")
        return False

    async def make_api_request(
        self, 
        endpoint: str, 
        method: str = "GET", 
        params: Optional[Dict] = None,
        json_data: Optional[Dict] = None,
        retry_count: int = 0
    ) -> Optional[Dict]:
        """
        Make API request with automatic retry, error handling, and token refresh
        """
        try:
            # Ensure valid token
            if not await self.ensure_valid_token():
                logger.error("Failed to ensure valid token")
                return None
            
            # Prepare headers
            if self.api_key:
                headers = {
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json",
                    "Accept": "application/json"
                }
            else:
                headers = {
                    "Authorization": f"Bearer {self.access_token}",
                    "Content-Type": "application/json",
                    "Accept": "application/json"
                }
            
            # Add tenant header if required
            if self.tenant_id:
                headers["X-Tenant-ID"] = self.tenant_id
                headers["X-Organization-ID"] = self.tenant_id
            
            # Build full URL
            url = f"{self.api_base_url}{endpoint}"
            
            logger.debug(f"Making {method} request to: {url}")
            
            async with aiohttp.ClientSession() as session:
                async with session.request(
                    method,
                    url,
                    headers=headers,
                    params=params,
                    json=json_data,
                    timeout=aiohttp.ClientTimeout(total=self.request_timeout)
                ) as response:
                    response_text = await response.text()
                    
                    # Success
                    if response.status == 200:
                        try:
                            return json.loads(response_text) if response_text else {}
                        except json.JSONDecodeError:
                            logger.warning(f"Response is not valid JSON: {response_text[:200]}")
                            return {"raw_response": response_text}
                    
                    # Rate limiting - retry with exponential backoff
                    elif response.status == 429:
                        if retry_count < self.max_retries:
                            wait_time = self.retry_delay * (2 ** retry_count)
                            logger.warning(f"Rate limited. Retrying in {wait_time} seconds...")
                            await asyncio.sleep(wait_time)
                            return await self.make_api_request(
                                endpoint, method, params, json_data, retry_count + 1
                            )
                        else:
                            logger.error(f"Max retries reached for rate limiting on {endpoint}")
                            return None
                    
                    # Authentication error - try to re-authenticate once
                    elif response.status in [401, 403]:
                        if retry_count == 0:
                            logger.warning(f"Authentication error on {endpoint}, attempting to re-authenticate")
                            if await self.authenticate():
                                return await self.make_api_request(
                                    endpoint, method, params, json_data, retry_count + 1
                                )
                        logger.error(f"Authentication failed for {endpoint}")
                        return None
                    
                    # Not found - endpoint doesn't exist
                    elif response.status == 404:
                        logger.debug(f"Endpoint not found: {endpoint}")
                        return None
                    
                    # Server errors - retry
                    elif response.status >= 500:
                        if retry_count < self.max_retries:
                            wait_time = self.retry_delay * (2 ** retry_count)
                            logger.warning(
                                f"Server error {response.status} on {endpoint}. "
                                f"Retrying in {wait_time} seconds..."
                            )
                            await asyncio.sleep(wait_time)
                            return await self.make_api_request(
                                endpoint, method, params, json_data, retry_count + 1
                            )
                        logger.error(f"Server error on {endpoint} after {retry_count} retries")
                        return None
                    
                    # Other errors
                    else:
                        logger.error(
                            f"API request failed - Status: {response.status}, "
                            f"Endpoint: {endpoint}, Response: {response_text[:500]}"
                        )
                        return None
                        
        except asyncio.TimeoutError:
            if retry_count < self.max_retries:
                logger.warning(f"Request timeout for {endpoint}. Retrying...")
                await asyncio.sleep(self.retry_delay)
                return await self.make_api_request(
                    endpoint, method, params, json_data, retry_count + 1
                )
            logger.error(f"Request timeout for {endpoint} after {retry_count} retries")
            return None
            
        except Exception as e:
            logger.error(f"Error making API request to {endpoint}: {str(e)}", exc_info=True)
            return None

    async def test_connection(self) -> Dict:
        """Test API connection and authentication"""
        try:
            await self.get_service_details()
            
            # Try multiple common endpoints for connection testing
            test_endpoints = [
                "/v1/users/me",
                "/api/v1/users/me",
                "/v1/me",
                "/api/v1/me",
                "/v1/organization",
                "/api/v1/organization",
                "/v1/account",
                "/api/v1/account",
                "/v1/tenant",
                "/api/v1/tenant"
            ]
            
            for endpoint in test_endpoints:
                result = await self.make_api_request(endpoint)
                if result and not result.get("error"):
                    logger.info(f"Connection test successful using endpoint: {endpoint}")
                    return {
                        "status": True, 
                        "message": "Authentication successful",
                        "endpoint_used": endpoint,
                        "service_name": self.service_name
                    }
            
            return {
                "status": False,
                "message": "Could not verify connection with any known endpoints"
            }
            
        except Exception as e:
            logger.error(f"Error testing connection: {str(e)}", exc_info=True)
            return {"status": False, "message": str(e)}

    async def get_organization_info(self) -> Dict:
        """Get organization information with multiple endpoint fallbacks"""
        org_endpoints = [
            "/v1/organization",
            "/api/v1/organization",
            "/v1/tenant",
            "/api/v1/tenant",
            "/v1/account",
            "/api/v1/account",
            "/v1/org",
            "/api/v1/org"
        ]
        
        for endpoint in org_endpoints:
            result = await self.make_api_request(endpoint)
            if result and not result.get("error"):
                logger.info(f"Organization info retrieved from: {endpoint}")
                return result
        
        logger.warning("Could not fetch organization info from any endpoint")
        return {
            "id": self.tenant_id or "unknown", 
            "name": "Unknown Organization",
            "region": "Unknown"
        }

    async def fetch_paginated_data(
        self, 
        base_endpoint: str, 
        data_key: Optional[str] = None,
        page_size: int = 100
    ) -> List[Dict]:
        """
        Fetch paginated data from API with auto-detection of pagination format
        """
        all_data = []
        page = 1
        next_url = None
        
        while True:
            try:
                # Determine if we have a next URL or need to paginate manually
                if next_url:
                    # Use provided next URL
                    if next_url.startswith("http"):
                        # Absolute URL - extract endpoint
                        endpoint = next_url.replace(self.api_base_url, "")
                        result = await self.make_api_request(endpoint)
                    else:
                        # Relative URL
                        result = await self.make_api_request(next_url)
                else:
                    # Manual pagination
                    params = {
                        "page": page,
                        "limit": page_size,
                        "per_page": page_size,
                        "pageSize": page_size,
                        "$top": page_size,
                        "$skip": (page - 1) * page_size
                    }
                    result = await self.make_api_request(base_endpoint, params=params)
                
                if not result:
                    break
                
                # Auto-detect data key if not provided
                if not data_key:
                    possible_keys = [
                        "data", "results", "items", "value", "records", 
                        "users", "campaigns", "reports", "activities"
                    ]
                    for key in possible_keys:
                        if key in result and isinstance(result[key], list):
                            data_key = key
                            logger.debug(f"Auto-detected data key: {data_key}")
                            break
                    
                    # If still not found, check if result itself is a list
                    if not data_key and isinstance(result, list):
                        all_data.extend(result)
                        logger.info(f"Fetched {len(result)} records from {base_endpoint} (direct array)")
                        break
                
                # Extract data
                if data_key and data_key in result:
                    page_data = result[data_key]
                    
                    if not page_data or len(page_data) == 0:
                        # Empty page, end pagination
                        break
                    
                    all_data.extend(page_data)
                    
                    # Check for next page indicators
                    next_url = (
                        result.get("@odata.nextLink") or 
                        result.get("nextLink") or
                        result.get("next") or 
                        result.get("nextPage") or
                        result.get("next_page") or
                        result.get("pagination", {}).get("next")
                    )
                    
                    if next_url:
                        logger.debug(f"Found next page URL: {next_url}")
                    elif len(page_data) < page_size:
                        # Last page (received fewer items than page size)
                        break
                    else:
                        # Continue with next page number
                        page += 1
                else:
                    logger.warning(f"Could not find data in response for {base_endpoint}")
                    break
                
                # Log progress
                if len(all_data) % 1000 == 0 and len(all_data) > 0:
                    logger.info(f"Fetched {len(all_data)} records from {base_endpoint}...")
                
                # Prevent infinite loops
                if page > 1000:
                    logger.warning(f"Reached maximum page limit (1000) for {base_endpoint}")
                    break
                    
            except Exception as e:
                logger.error(f"Error fetching paginated data from {base_endpoint}: {str(e)}")
                break
        
        logger.info(f"Total records fetched from {base_endpoint}: {len(all_data)}")
        return all_data

    async def fetch_users(self) -> List[Dict]:
        """Fetch all users with multiple endpoint fallbacks"""
        endpoints_to_try = [
            "/v1/users",
            "/api/v1/users",
            "/v1/employees",
            "/api/v1/employees",
            "/v1/members",
            "/api/v1/members",
            "/users",
            "/api/users"
        ]
        
        for endpoint in endpoints_to_try:
            users = await self.fetch_paginated_data(endpoint)
            if users and len(users) > 0:
                logger.info(f"Fetched {len(users)} users from {endpoint}")
                return users
        
        logger.warning("Could not fetch users from any known endpoint")
        return []

    async def fetch_campaigns(self) -> List[Dict]:
        """Fetch all training campaigns with multiple endpoint fallbacks"""
        endpoints_to_try = [
            "/v1/campaigns",
            "/api/v1/campaigns",
            "/v1/training/campaigns",
            "/api/v1/training/campaigns",
            "/v1/simulations",
            "/api/v1/simulations",
            "/campaigns",
            "/api/campaigns"
        ]
        
        for endpoint in endpoints_to_try:
            campaigns = await self.fetch_paginated_data(endpoint)
            if campaigns and len(campaigns) > 0:
                logger.info(f"Fetched {len(campaigns)} campaigns from {endpoint}")
                return campaigns
        
        logger.warning("Could not fetch campaigns from any known endpoint")
        return []

    async def fetch_phishing_reports(self) -> List[Dict]:
        """Fetch phishing simulation reports with multiple endpoint fallbacks"""
        endpoints_to_try = [
            "/v1/phishing/reports",
            "/api/v1/phishing/reports",
            "/v1/phishing/simulations",
            "/api/v1/phishing/simulations",
            "/v1/simulations/results",
            "/api/v1/simulations/results",
            "/v1/reports/phishing",
            "/api/v1/reports/phishing",
            "/phishing/reports",
            "/api/phishing/reports"
        ]
        
        for endpoint in endpoints_to_try:
            reports = await self.fetch_paginated_data(endpoint)
            if reports and len(reports) > 0:
                logger.info(f"Fetched {len(reports)} phishing reports from {endpoint}")
                return reports
        
        logger.warning("Could not fetch phishing reports from any known endpoint")
        return []

    async def fetch_training_status(self) -> List[Dict]:
        """Fetch training completion status with multiple endpoint fallbacks"""
        endpoints_to_try = [
            "/v1/training/status",
            "/api/v1/training/status",
            "/v1/training/progress",
            "/api/v1/training/progress",
            "/v1/users/training",
            "/api/v1/users/training",
            "/v1/completions",
            "/api/v1/completions",
            "/training/status",
            "/api/training/status"
        ]
        
        for endpoint in endpoints_to_try:
            status = await self.fetch_paginated_data(endpoint)
            if status and len(status) > 0:
                logger.info(f"Fetched {len(status)} training status records from {endpoint}")
                return status
        
        logger.warning("Could not fetch training status from any known endpoint")
        return []

    async def fetch_user_activity(self) -> List[Dict]:
        """Fetch user activity logs with multiple endpoint fallbacks"""
        endpoints_to_try = [
            "/v1/users/activity",
            "/api/v1/users/activity",
            "/v1/activity",
            "/api/v1/activity",
            "/v1/audit/logs",
            "/api/v1/audit/logs",
            "/v1/logs",
            "/api/v1/logs",
            "/users/activity",
            "/api/users/activity"
        ]
        
        for endpoint in endpoints_to_try:
            activity = await self.fetch_paginated_data(endpoint)
            if activity and len(activity) > 0:
                logger.info(f"Fetched {len(activity)} activity records from {endpoint}")
                return activity
        
        logger.warning("Could not fetch user activity from any known endpoint")
        return []

    async def fetch_roles(self) -> List[Dict]:
        """Fetch roles and permissions with multiple endpoint fallbacks"""
        endpoints_to_try = [
            "/v1/roles",
            "/api/v1/roles",
            "/v1/permissions",
            "/api/v1/permissions",
            "/v1/access/roles",
            "/api/v1/access/roles",
            "/roles",
            "/api/roles"
        ]
        
        for endpoint in endpoints_to_try:
            roles = await self.fetch_paginated_data(endpoint)
            if roles and len(roles) > 0:
                logger.info(f"Fetched {len(roles)} roles from {endpoint}")
                return roles
        
        logger.warning("Could not fetch roles from any known endpoint")
        return []

    def generate_unique_file_uri(
        self, 
        category: str, 
        record_id: str, 
        record_type: str = "json"
    ) -> str:
        """
        Generate a unique, collision-resistant file URI for a record
        Uses date-based organization and sanitized identifiers
        """
        # Sanitize inputs
        category = category.strip().lower().replace(" ", "_").replace("/", "_")
        
        # Sanitize record_id
        record_id_clean = str(record_id).strip().replace("/", "_").replace("\\", "_").replace(" ", "_")
        
        # Add hash suffix to ensure uniqueness
        hash_suffix = hashlib.md5(f"{category}{record_id}{time.time()}".encode()).hexdigest()[:8]
        
        # Generate URI with timestamp for organization
        timestamp = datetime.utcnow().strftime("%Y%m%d")
        uri = f"humanfirewall/{category}/{timestamp}/{record_id_clean}_{hash_suffix}.{record_type}"
        
        # Ensure uniqueness with cache check
        counter = 1
        original_uri = uri
        while uri in self.processed_uris:
            uri = f"humanfirewall/{category}/{timestamp}/{record_id_clean}_{hash_suffix}_{counter}.{record_type}"
            counter += 1
            if counter > 100:
                logger.error(f"Could not generate unique URI after 100 attempts for {record_id}")
                break
        
        self.processed_uris.add(uri)
        return uri

    async def save_record_to_file(
        self,
        record: Dict,
        category: str,
        record_id: str,
        record_name: str
    ) -> Optional[FileMetadata]:
        """
        Save a single record to file and create FileMetadata
        Handles all file operations and metadata extraction
        """
        try:
            # Generate unique file URI
            file_uri = self.generate_unique_file_uri(category, record_id)
            # Determine file extension from URI
            file_ext = get_file_extension(file_uri) or "json"

            # Skip if unsupported file type
            if file_ext not in PROFILE_SUPPORTED_FILE_TYPES:
                logger.info(f"Skipping {record_name}: Unsupported file type .{file_ext}")
                return None
            
            # Check skip rules
            if self.rule_manager.skip_file(file_uri):
                logger.debug(f"Skipping {record_name} based on skip rules")
                return None
            
            # Prepare file path
            filename = f"{category}_{record_id}_{int(time.time())}.json"
            filepath = f"{self.local_data_dir}/{filename}"
            
            # Serialize once and check size threshold before writing
            serialized = json.dumps(record, indent=2, ensure_ascii=False)
            planned_size = len(serialized.encode('utf-8'))
            if planned_size > PROFILE_MAX_SIZE_THRESHOLD:
                logger.info(
                    f"Skipping {record_name}: Size {planned_size} exceeds threshold {PROFILE_MAX_SIZE_THRESHOLD}"
                )
                return None

            # Write file with proper encoding
            with open(filepath, "w", encoding="utf-8") as f:
                f.write(serialized)
            
            # Calculate file size
            file_size = planned_size

            # Final size guard (defensive)
            if file_size > PROFILE_MAX_SIZE_THRESHOLD:
                logger.info(
                    f"Removing {record_name}: Final size {file_size} exceeds threshold {PROFILE_MAX_SIZE_THRESHOLD}"
                )
                try:
                    import os
                    if os.path.exists(filepath):
                        os.remove(filepath)
                except Exception:
                    pass
                return None

            # Upload to MinIO using file_uri as object key
            try:
                self.minio_client.upload_file(filepath, file_uri)
                minio_filepath = file_uri
            except Exception as e:
                logger.error(f"Failed to upload {filename} to MinIO: {str(e)}")
                # keep local file for diagnostics but still return metadata
                minio_filepath = ""
            
            # Extract common metadata fields with multiple fallbacks
            created_at = (
                record.get("created_at") or 
                record.get("createdDateTime") or 
                record.get("created") or 
                record.get("createdDate") or
                record.get("created_date") or
                record.get("creationDate") or
                datetime.utcnow().isoformat()
            )
            
            modified_at = (
                record.get("updated_at") or 
                record.get("modifiedDateTime") or 
                record.get("modified") or 
                record.get("lastModified") or
                record.get("updated") or
                record.get("last_modified") or
                created_at
            )
            
            status = (
                record.get("status") or 
                record.get("state") or 
                record.get("lifecycle_state") or
                "unknown"
            )
            
            # Create FileMetadata with comprehensive details
            metadata = FileMetadata(
                service_name=self.service_name,
                service_type=self.service_type,
                service_provider=self.service_provider,
                sub_service=LocalSubservice.HUMAN_FIREWALL,
                file_key=record_name,
                file_size=file_size,
                file_type=file_ext,
                file_uri=file_uri,
                local_filepath=filepath,
                details={
                    "record_id": record_id,
                    "category": category,
                    "status": status,
                    "created": created_at,
                    "modified": modified_at,
                    "record_type": record.get("type", category),
                    "name": record_name,
                    "source": "humanfirewall_api",
                    "minio_file": minio_filepath,
                },
            )
            
            # Update statistics
            self.stats["total_files"] += 1
            self.stats["total_size"] += file_size
            
            return metadata
            
        except Exception as e:
            logger.error(f"Error saving record {record_name}: {str(e)}", exc_info=True)
            self.stats["errors"] += 1
            return None

    async def infra_scan(self) -> Dict:
        """Perform infrastructure scan to gather metadata and statistics"""
        try:
            await self.get_service_details()
            
            logger.info("Starting infrastructure scan...")
            
            # Fetch organization info
            org_info = await self.get_organization_info()
            
            # Fetch basic data for infrastructure scan
            users = await self.fetch_users()
            campaigns = await self.fetch_campaigns()
            
            # Calculate statistics
            active_campaigns = len([
                c for c in campaigns 
                if str(c.get("status", "")).lower() in ["active", "running", "in_progress"]
            ])
            
            enrolled_users = len([
                u for u in users 
                if u.get("enrolled", True) or u.get("is_enrolled", True) or u.get("active", True)
            ])
            
            # Extract organization details
            org_name = org_info.get("name") or org_info.get("organizationName") or "Unknown"
            org_id = org_info.get("id") or self.tenant_id or "Unknown"
            org_region = org_info.get("region") or org_info.get("location") or "Unknown"
            
            infra = {
                "organization_name": org_name,
                "organization_id": org_id,
                "region": org_region,
                "total_users": len(users),
                "enrolled_users": enrolled_users,
                "total_campaigns": len(campaigns),
                "active_campaigns": active_campaigns,
                "scan_timestamp": datetime.utcnow().isoformat(),
                "api_base_url": self.api_base_url,
                "service_name": self.service_name
            }
            
            logger.info(
                f"Infrastructure scan completed - "
                f"{len(users)} users, {len(campaigns)} campaigns found"
            )
            return infra
            
        except Exception as e:
            logger.error(f"Error during infra scanning: {str(e)}", exc_info=True)
            return {
                "organization_name": "Unknown",
                "organization_id": self.tenant_id or "Unknown",
                "region": "Unknown",
                "total_users": 0,
                "enrolled_users": 0,
                "total_campaigns": 0,
                "active_campaigns": 0,
                "error": str(e),
                "scan_timestamp": datetime.utcnow().isoformat()
            }

    async def deep_scan(self) -> AsyncGenerator[FileMetadata, None]:
        """
        Performs a comprehensive deep scan on Human Firewall data
        Fetches and saves all available data types with proper error handling
        """
        total_file_count = 0
        total_file_size = 0
        org_region = "Unknown"
        
        try:
            # Initialize service and authentication
            await self.get_service_details()
            
            # Get organization info
            org_info = await self.get_organization_info()
            org_region = org_info.get("region") or org_info.get("location") or "Global"
            
            logger.info("=" * 80)
            logger.info("Starting Human Firewall deep scan")
            logger.info(f"Service: {self.service_name}")
            logger.info(f"Organization: {org_info.get('name', 'Unknown')}")
            logger.info(f"Region: {org_region}")
            logger.info("=" * 80)
            
            # Create temp directory
            create_folder(self.local_data_dir)
            logger.info(f"Created temporary directory: {self.local_data_dir}")
            
            # Fetch all data types concurrently for better performance
            logger.info("Fetching data from Human Firewall API...")
            
            campaigns, phishing_reports, training_status, user_activity = await asyncio.gather(
                self.fetch_campaigns(),
                self.fetch_phishing_reports(),
                self.fetch_training_status(),
                self.fetch_user_activity(),
                return_exceptions=True
            )
            
            # Handle exceptions from concurrent fetches
            if isinstance(campaigns, Exception):
                logger.error(f"Error fetching campaigns: {str(campaigns)}")
                campaigns = []
            
            if isinstance(phishing_reports, Exception):
                logger.error(f"Error fetching phishing reports: {str(phishing_reports)}")
                phishing_reports = []
            
            if isinstance(training_status, Exception):
                logger.error(f"Error fetching training status: {str(training_status)}")
                training_status = []
            
            if isinstance(user_activity, Exception):
                logger.error(f"Error fetching user activity: {str(user_activity)}")
                user_activity = []
            
            logger.info(
                f"Data fetched - Campaigns: {len(campaigns)}, "
                f"Phishing Reports: {len(phishing_reports)}, "
                f"Training Status: {len(training_status)}, "
                f"User Activity: {len(user_activity)}"
            )
            
            # Process campaigns
            if campaigns:
                logger.info(f"Processing {len(campaigns)} campaigns...")
                for idx, campaign in enumerate(campaigns, 1):
                    try:
                        campaign_id = (
                            campaign.get("id") or 
                            campaign.get("campaignId") or 
                            campaign.get("campaign_id") or
                            str(uuid4())
                        )
                        
                        campaign_name = (
                            campaign.get("name") or 
                            campaign.get("title") or 
                            campaign.get("campaign_name") or 
                            campaign.get("campaignName") or
                            f"Campaign {campaign_id}"
                        )
                        
                        file_metadata = await self.save_record_to_file(
                            record=campaign,
                            category="campaigns",
                            record_id=campaign_id,
                            record_name=campaign_name
                        )
                        
                        if file_metadata:
                            total_file_count += 1
                            total_file_size += file_metadata.file_size
                            self.stats["campaigns"] += 1
                            yield file_metadata
                            
                            if idx % 100 == 0:
                                logger.info(f"Processed {idx}/{len(campaigns)} campaigns")
                    
                    except Exception as e:
                        logger.error(f"Error processing campaign {idx}: {str(e)}")
                        self.stats["errors"] += 1
                        continue
                
                logger.info(f"Completed processing {self.stats['campaigns']} campaigns")
            
            # Process phishing reports
            if phishing_reports:
                logger.info(f"Processing {len(phishing_reports)} phishing reports...")
                for idx, report in enumerate(phishing_reports, 1):
                    try:
                        report_id = (
                            report.get("id") or 
                            report.get("reportId") or 
                            report.get("report_id") or
                            str(uuid4())
                        )
                        
                        report_name = (
                            report.get("title") or 
                            report.get("name") or 
                            report.get("report_name") or 
                            report.get("simulationName") or
                            f"Phishing Report {report_id}"
                        )
                        
                        file_metadata = await self.save_record_to_file(
                            record=report,
                            category="phishing_reports",
                            record_id=report_id,
                            record_name=report_name
                        )
                        
                        if file_metadata:
                            total_file_count += 1
                            total_file_size += file_metadata.file_size
                            self.stats["phishing_reports"] += 1
                            yield file_metadata
                            
                            if idx % 100 == 0:
                                logger.info(f"Processed {idx}/{len(phishing_reports)} phishing reports")
                    
                    except Exception as e:
                        logger.error(f"Error processing phishing report {idx}: {str(e)}")
                        self.stats["errors"] += 1
                        continue
                
                logger.info(f"Completed processing {self.stats['phishing_reports']} phishing reports")
            
            # Process training status
            if training_status:
                logger.info(f"Processing {len(training_status)} training status records...")
                for idx, status in enumerate(training_status, 1):
                    try:
                        status_id = (
                            status.get("id") or 
                            status.get("statusId") or 
                            status.get("status_id") or
                            str(uuid4())
                        )
                        
                        user_email = (
                            status.get("user_email") or 
                            status.get("email") or 
                            status.get("userEmail") or
                            status.get("user") or
                            f"user_{status_id}"
                        )
                        
                        training_name = (
                            status.get("training_name") or
                            status.get("course_name") or
                            status.get("courseName") or
                            "training"
                        )
                        
                        file_metadata = await self.save_record_to_file(
                            record=status,
                            category="training_status",
                            record_id=status_id,
                            record_name=f"training_{training_name}_{user_email}"
                        )
                        
                        if file_metadata:
                            total_file_count += 1
                            total_file_size += file_metadata.file_size
                            self.stats["training_records"] += 1
                            yield file_metadata
                            
                            if idx % 100 == 0:
                                logger.info(f"Processed {idx}/{len(training_status)} training records")
                    
                    except Exception as e:
                        logger.error(f"Error processing training status {idx}: {str(e)}")
                        self.stats["errors"] += 1
                        continue
                
                logger.info(f"Completed processing {self.stats['training_records']} training records")
            
            # Process user activity
            if user_activity:
                logger.info(f"Processing {len(user_activity)} user activity records...")
                for idx, activity in enumerate(user_activity, 1):
                    try:
                        activity_id = (
                            activity.get("id") or 
                            activity.get("activityId") or 
                            activity.get("activity_id") or
                            activity.get("event_id") or
                            str(uuid4())
                        )
                        
                        user_email = (
                            activity.get("user_email") or 
                            activity.get("email") or 
                            activity.get("userEmail") or
                            activity.get("user") or
                            f"user_{activity_id}"
                        )
                        
                        activity_type = (
                            activity.get("type") or
                            activity.get("activity_type") or
                            activity.get("action") or
                            "activity"
                        )
                        
                        file_metadata = await self.save_record_to_file(
                            record=activity,
                            category="user_activity",
                            record_id=activity_id,
                            record_name=f"activity_{activity_type}_{user_email}"
                        )
                        
                        if file_metadata:
                            total_file_count += 1
                            total_file_size += file_metadata.file_size
                            self.stats["user_activity"] += 1
                            yield file_metadata
                            
                            if idx % 100 == 0:
                                logger.info(f"Processed {idx}/{len(user_activity)} activity records")
                    
                    except Exception as e:
                        logger.error(f"Error processing user activity {idx}: {str(e)}")
                        self.stats["errors"] += 1
                        continue
                
                logger.info(f"Completed processing {self.stats['user_activity']} activity records")
            
            # Log final statistics
            logger.info("=" * 80)
            logger.info("Deep scan completed successfully")
            logger.info(f"Total files processed: {total_file_count}")
            logger.info(f"Total data size: {total_file_size:,} bytes ({total_file_size / (1024*1024):.2f} MB)")
            logger.info(f"Campaigns: {self.stats['campaigns']}")
            logger.info(f"Phishing Reports: {self.stats['phishing_reports']}")
            logger.info(f"Training Records: {self.stats['training_records']}")
            logger.info(f"User Activity: {self.stats['user_activity']}")
            logger.info(f"Errors: {self.stats['errors']}")
            logger.info("=" * 80)
            
        except Exception as e:
            logger.error(f"Error during deep scan: {str(e)}", exc_info=True)
            self.stats["errors"] += 1
        
        finally:
            # Load asset details and access controls
            try:
                logger.info("Loading asset details and access controls...")
                
                # Create asset details
                asset = [
                    AssetDetailsSchema(
                        asset_name=f"humanfirewall_{self.service_name}",
                        service_provider=self.service_provider,
                        type="security_awareness",
                        category="unstructured",
                        location=org_region,
                        owner="system",
                        security=True,
                        size=total_file_size,
                        count=total_file_count,
                        access_category="restricted",
                        service_name=str(self.service_name),
                        steward=str(self.service_steward) if self.service_steward else "",
                    )
                ]
                load_asset_details(asset)
                logger.info("Asset details loaded successfully")
                
                # Fetch and load access controls
                roles = await self.fetch_roles()
                access = []
                
                if roles:
                    for role in roles:
                        try:
                            role_name = (
                                role.get("name") or 
                                role.get("role_name") or 
                                role.get("roleName") or 
                                role.get("title") or
                                "unknown_role"
                            )
                            
                            role_type = (
                                role.get("type") or 
                                role.get("role_type") or 
                                role.get("roleType") or 
                                role.get("level") or
                                "user"
                            )
                            
                            role_users = (
                                role.get("users") or 
                                role.get("members") or 
                                role.get("user_emails") or 
                                role.get("userEmails") or
                                role.get("assignedUsers") or
                                []
                            )
                            
                            # Determine access level based on role
                            role_name_lower = role_name.lower()
                            role_type_lower = str(role_type).lower()
                            
                            if "admin" in role_name_lower or "admin" in role_type_lower:
                                access_level = "full"
                            elif any(keyword in role_name_lower for keyword in ["manager", "editor", "write", "modify"]):
                                access_level = "write"
                            elif any(keyword in role_name_lower for keyword in ["viewer", "read", "observer"]):
                                access_level = "read"
                            else:
                                access_level = "read"  # Default to read
                            
                            # Add access control entry for each user in role
                            if role_users and isinstance(role_users, list):
                                for user_email in role_users:
                                    if user_email:  # Skip empty values
                                        access.append({
                                            "asset_name": f"humanfirewall_{self.service_name}",
                                            "user_or_role": str(user_email),
                                            "role": role_name,
                                            "access": access_level,
                                        })
                            else:
                                # Role exists but no users assigned - add role itself
                                access.append({
                                    "asset_name": f"humanfirewall_{self.service_name}",
                                    "user_or_role": role_name,
                                    "role": role_name,
                                    "access": access_level,
                                })
                        
                        except Exception as e:
                            logger.warning(f"Error processing role: {str(e)}")
                            continue
                
                # Add default system access if no roles found
                if not access:
                    access.append({
                        "asset_name": f"humanfirewall_{self.service_name}",
                        "user_or_role": "system",
                        "role": "owner",
                        "access": "full",
                    })
                    logger.info("No roles found, added default system access")
                
                load_access_controls(access)
                logger.info(f"Loaded {len(access)} access control entries")
                
            except Exception as e:
                logger.error(f"Error loading asset details or access controls: {str(e)}", exc_info=True)
            
            # Clean up
            logger.info("Deep scan process completed")

