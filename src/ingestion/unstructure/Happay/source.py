from typing import AsyncGenerator, Dict, Optional, List
import aiohttp
import asyncio
from uuid import uuid4
from datetime import datetime, timedelta
import json
import os
from urllib.parse import urljoin

from src.utils.logger import get_ingestion_logger
from src.common.config import (
    TEMP_DATA_DIR,
    CONCURRENT_LIMIT,
    PROFILE_MAX_SIZE_THRESHOLD,
    PROFILE_SUPPORTED_FILE_TYPES,
)
from src.common.constants import ServiceTypes, ServiceProviders, LocalSubservice
from src.ingestion.base_source import UnstructureSource
from src.ingestion.data_class import FileMetadata
from src.modules.repos import DatabaseManager
from src.utils.helpers import (file_exists, create_folder, get_file_extension)

logger = get_ingestion_logger()


class HappaySource(UnstructureSource):
    """
    Happay Expense Management System Connector
    Supports OAuth 2.0 and API Key authentication
    """
    
    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.HAPPAY.value, service_name)
        self.db = DatabaseManager()
        self.service_provider = ServiceProviders.LOCAL
        self.sub_service = LocalSubservice.HAPPAY
        self.session = None
        self.access_token = None
        self.api_base_url = None
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"
        self.headers = {}
        
        # API endpoints
        self.endpoints = {
            "token": "/oauth/token",
            "profile": "/v2/users/profile",
            "expenses": "/v2/expenses",
            "reports": "/v2/reports",
            "cards": "/v2/cards",
            "users": "/v2/users",
            "attachments": "/v2/attachments",
            "reimbursements": "/v2/reimbursements"
        }

    async def get_service_details(self) -> Dict:
        """Fetches service details and credentials from database"""
        logger.info(f"Fetching service details for {self.service_name}")
        
        self.service = await self.db.get_service_by_service_name(self.service_name)
        credentials = self.service.get("credentials", {})
        
        # Extract credentials
        self.client_id = credentials.get("client_id")
        self.client_secret = credentials.get("client_secret")
        self.api_key = credentials.get("api_key")  # Alternative to OAuth
        self.refresh_token = credentials.get("refresh_token")
        self.api_base_url = credentials.get("api_base_url", "https://uat-isac.happay.in")
        self.tenant_id = credentials.get("tenant_id")
        
        # Ensure base URL doesn't have trailing slash
        self.api_base_url = self.api_base_url.rstrip('/')
        
        logger.info(f"Service details fetched for {self.service_name}")
        return self.service

    async def _get_aiohttp_session(self) -> aiohttp.ClientSession:
        """Creates or returns existing aiohttp session"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=300)
            self.session = aiohttp.ClientSession(timeout=timeout)
        return self.session

    async def _generate_access_token(self) -> bool:
        """Generates OAuth access token using client credentials"""
        logger.info(f"Generating access token for {self.service_name}")
        
        try:
            session = await self._get_aiohttp_session()
            token_url = urljoin(self.api_base_url, self.endpoints["token"])
            
            payload = {
                "grant_type": "client_credentials",
                "client_id": self.client_id,
                "client_secret": self.client_secret,
            }
            
            if self.tenant_id:
                payload["tenant_id"] = self.tenant_id
            
            async with session.post(token_url, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    self.access_token = data.get("access_token")
                    self.headers = {
                        "Authorization": f"Bearer {self.access_token}",
                        "Content-Type": "application/json"
                    }
                    logger.info(f"Access token generated successfully for {self.service_name}")
                    return True
                else:
                    error_text = await response.text()
                    logger.error(f"Failed to generate token: {response.status} - {error_text}")
                    return False
                    
        except Exception as e:
            logger.error(f"Error generating access token: {str(e)}")
            return False

    async def _setup_authentication(self) -> bool:
        """Sets up authentication headers (OAuth or API Key)"""
        if self.api_key:
            # API Key authentication
            self.headers = {
                "X-API-Key": self.api_key,
                "Content-Type": "application/json"
            }
            logger.info(f"Using API Key authentication for {self.service_name}")
            return True
        else:
            # OAuth 2.0 authentication
            return await self._generate_access_token()

    async def test_connection(self) -> Dict:
        """Tests connection by fetching user profile"""
        logger.info(f"Testing connection for {self.service_name}")
        
        try:
            await self.get_service_details()
            auth_success = await self._setup_authentication()
            
            if not auth_success:
                return {"status": False, "message": "Authentication failed"}
            
            session = await self._get_aiohttp_session()
            profile_url = urljoin(self.api_base_url, self.endpoints["profile"])
            
            async with session.get(profile_url, headers=self.headers) as response:
                if response.status == 200:
                    profile_data = await response.json()
                    logger.info(f"Connection test successful for {self.service_name}")
                    return {
                        "status": True,
                        "message": "Authentication successful",
                        "user": profile_data.get("name", "Unknown")
                    }
                else:
                    error_text = await response.text()
                    logger.error(f"Connection test failed: {response.status} - {error_text}")
                    return {
                        "status": False,
                        "message": f"Connection failed: {response.status}"
                    }
                    
        except Exception as e:
            logger.error(f"Error testing connection for {self.service_name}: {str(e)}")
            return {"status": False, "message": f"Connection error: {str(e)}"}

    async def _fetch_paginated_data(
        self, 
        endpoint: str, 
        params: Optional[Dict] = None
    ) -> List[Dict]:
        """Fetches paginated data from Happay API"""
        all_data = []
        page = 1
        page_size = 100
        
        if params is None:
            params = {}
        
        session = await self._get_aiohttp_session()
        url = urljoin(self.api_base_url, endpoint)
        
        while True:
            params_with_page = {**params, "page": page, "page_size": page_size}
            
            try:
                async with session.get(
                    url, 
                    headers=self.headers, 
                    params=params_with_page
                ) as response:
                    if response.status != 200:
                        logger.warning(f"Failed to fetch from {endpoint}: {response.status}")
                        break
                    
                    data = await response.json()
                    items = data.get("data", []) or data.get("results", [])
                    
                    if not items:
                        break
                    
                    all_data.extend(items)
                    logger.debug(f"Fetched page {page} from {endpoint}: {len(items)} items")
                    
                    # Check if more pages exist
                    has_next = data.get("has_next", False)
                    total_pages = data.get("total_pages", page)
                    
                    if not has_next or page >= total_pages:
                        break
                    
                    page += 1
                    
            except Exception as e:
                logger.error(f"Error fetching page {page} from {endpoint}: {str(e)}")
                break
        
        logger.info(f"Total items fetched from {endpoint}: {len(all_data)}")
        return all_data

    async def _download_attachment(
        self, 
        attachment_url: str, 
        attachment_id: str,
        file_type: str
    ) -> Optional[str]:
        """Downloads attachment from Happay and saves locally"""
        
        # Create unique filepath
        temp_dir = self.local_data_dir
        create_folder(temp_dir)
        
        safe_filename = f"{uuid4()}_{attachment_id}.{file_type}"
        filepath = f"{temp_dir}/{safe_filename}"
        
        if file_exists(filepath):
            logger.info(f"File already exists at {filepath}, skipping download")
            return filepath
        
        try:
            session = await self._get_aiohttp_session()
            
            async with session.get(
                attachment_url, 
                headers=self.headers
            ) as response:
                if response.status == 200:
                    content = await response.read()
                    
                    # Check file size
                    if len(content) > PROFILE_MAX_SIZE_THRESHOLD:
                        logger.info(
                            f"Skipping {attachment_id}: Size {len(content)} "
                            f"exceeds threshold {PROFILE_MAX_SIZE_THRESHOLD}"
                        )
                        return None
                    
                    os.makedirs(os.path.dirname(filepath), exist_ok=True)
                    with open(filepath, "wb") as f:
                        f.write(content)
                    
                    logger.info(f"Successfully downloaded attachment to {filepath}")
                    return filepath
                else:
                    logger.warning(
                        f"Failed to download attachment {attachment_id}: "
                        f"{response.status}"
                    )
                    return None
                    
        except Exception as e:
            logger.error(f"Error downloading attachment {attachment_id}: {str(e)}")
            return None

    async def _save_json_data(
        self, 
        data: Dict, 
        data_type: str, 
        record_id: str
    ) -> str:
        """Saves JSON data to local file"""
        temp_dir = self.local_data_dir
        create_folder(temp_dir)
        
        safe_filename = f"{uuid4()}_{data_type}_{record_id}.json"
        filepath = f"{temp_dir}/{safe_filename}"
        
        try:
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.debug(f"Saved JSON data to {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Error saving JSON data to {filepath}: {str(e)}")
            raise

    async def _process_expense_record(
        self, 
        expense: Dict, 
        semaphore: asyncio.Semaphore
    ) -> Optional[FileMetadata]:
        """Processes individual expense record and its attachments"""
        
        async with semaphore:
            try:
                expense_id = expense.get("id") or expense.get("expense_id")
                
                # Save expense JSON
                json_filepath = await self._save_json_data(
                    expense, 
                    "expense", 
                    str(expense_id)
                )
                
                file_uri = f"happay://{self.service_name}/expenses/{expense_id}"
                
                # Check for attachments
                attachments = expense.get("attachments", [])
                attachment_files = []
                
                for attachment in attachments:
                    attachment_url = attachment.get("url") or attachment.get("file_url")
                    attachment_id = attachment.get("id") or attachment.get("attachment_id")
                    file_name = attachment.get("file_name", "")
                    
                    if not attachment_url:
                        continue
                    
                    file_ext = get_file_extension(file_name).lower()
                    
                    # Check if supported file type
                    if file_ext not in PROFILE_SUPPORTED_FILE_TYPES:
                        logger.debug(
                            f"Skipping attachment {attachment_id}: "
                            f"Unsupported file type .{file_ext}"
                        )
                        continue
                    
                    # Download attachment
                    attachment_path = await self._download_attachment(
                        attachment_url,
                        str(attachment_id),
                        file_ext
                    )
                    
                    if attachment_path:
                        attachment_files.append({
                            "path": attachment_path,
                            "type": file_ext,
                            "id": attachment_id
                        })
                
                # Create metadata for JSON file
                file_size = os.path.getsize(json_filepath) if os.path.exists(json_filepath) else 0
                
                return FileMetadata(
                    service_name=self.service_name,
                    service_type=self.service_type,
                    service_provider=self.service_provider,
                    sub_service=self.sub_service,
                    file_key=f"expense_{expense_id}.json",
                    file_size=file_size,
                    file_type="json",
                    file_uri=file_uri,
                    local_filepath=json_filepath,
                    details={
                        "expense_id": expense_id,
                        "amount": expense.get("amount"),
                        "currency": expense.get("currency"),
                        "date": expense.get("expense_date") or expense.get("date"),
                        "status": expense.get("status"),
                        "category": expense.get("category"),
                        "merchant": expense.get("merchant_name"),
                        "user_email": expense.get("user_email"),
                        "attachments": attachment_files,
                        "created": expense.get("created_at"),
                        "modified": expense.get("updated_at"),
                        "data_type": "expense"
                    }
                )
                
            except Exception as e:
                logger.error(f"Error processing expense {expense.get('id')}: {str(e)}")
                return None

    async def scan_happay_infra(self) -> Dict:
        """Scans Happay infrastructure to get counts and summary"""
        logger.info(f"Starting infrastructure scan for {self.service_name}")
        
        total_files = 0
        total_size = 0
        modules_summary = {}
        
        try:
            # Count expenses
            expenses = await self._fetch_paginated_data(
                self.endpoints["expenses"],
                params={"limit": 1}
            )
            expenses_count = len(expenses)
            modules_summary["expenses"] = expenses_count
            
            # Count reports
            reports = await self._fetch_paginated_data(
                self.endpoints["reports"],
                params={"limit": 1}
            )
            reports_count = len(reports)
            modules_summary["reports"] = reports_count
            
            # Count users
            try:
                users = await self._fetch_paginated_data(
                    self.endpoints["users"],
                    params={"limit": 1}
                )
                modules_summary["users"] = len(users)
            except Exception as e:
                logger.warning(f"Could not fetch users count: {str(e)}")
                modules_summary["users"] = 0
            
            total_files = expenses_count + reports_count
            
            logger.info(
                f"Infrastructure scan completed: {total_files} total records, "
                f"Modules: {modules_summary}"
            )
            
            return {
                "total_size": total_size,
                "total_files": total_files,
                "modules": modules_summary
            }
            
        except Exception as e:
            logger.error(f"Happay infra scan failed: {str(e)}")
            return {
                "total_size": 0,
                "total_files": 0,
                "modules": {}
            }

    async def scan_happay_deep(self) -> AsyncGenerator[FileMetadata, None]:
        """Deep scan of Happay data with file downloads"""
        logger.info(f"Starting deep scan for {self.service_name}")
        
        semaphore = asyncio.Semaphore(CONCURRENT_LIMIT)
        
        try:
            # Fetch all expenses
            logger.info("Fetching expenses...")
            expenses = await self._fetch_paginated_data(self.endpoints["expenses"])
            
            # Process expenses with their attachments
            tasks = []
            for expense in expenses:
                # Apply skip rules
                expense_id = expense.get("id") or expense.get("expense_id")
                file_uri = f"happay://{self.service_name}/expenses/{expense_id}"
                
                if self.rule_manager.skip_file(file_uri):
                    logger.info(f"Skipping expense {expense_id} based on skip rules")
                    continue
                
                tasks.append(self._process_expense_record(expense, semaphore))
            
            # Process completed tasks
            for future in asyncio.as_completed(tasks):
                file_metadata = await future
                if file_metadata:
                    yield file_metadata
            
            # Fetch and process reports
            logger.info("Fetching reports...")
            reports = await self._fetch_paginated_data(self.endpoints["reports"])
            
            for report in reports:
                try:
                    report_id = report.get("id") or report.get("report_id")
                    file_uri = f"happay://{self.service_name}/reports/{report_id}"
                    
                    if self.rule_manager.skip_file(file_uri):
                        logger.info(f"Skipping report {report_id} based on skip rules")
                        continue
                    
                    # Save report JSON
                    json_filepath = await self._save_json_data(
                        report,
                        "report",
                        str(report_id)
                    )
                    
                    file_size = os.path.getsize(json_filepath)
                    
                    yield FileMetadata(
                        service_name=self.service_name,
                        service_type=self.service_type,
                        service_provider=self.service_provider,
                        sub_service=self.sub_service,
                        file_key=f"report_{report_id}.json",
                        file_size=file_size,
                        file_type="json",
                        file_uri=file_uri,
                        local_filepath=json_filepath,
                        details={
                            "report_id": report_id,
                            "report_name": report.get("report_name"),
                            "status": report.get("status"),
                            "total_amount": report.get("total_amount"),
                            "currency": report.get("currency"),
                            "submitted_date": report.get("submitted_date"),
                            "approved_date": report.get("approved_date"),
                            "user_email": report.get("user_email"),
                            "created": report.get("created_at"),
                            "modified": report.get("updated_at"),
                            "data_type": "report"
                        }
                    )
                    
                except Exception as e:
                    logger.error(f"Error processing report {report.get('id')}: {str(e)}")
                    continue
            
            logger.info(f"Deep scan completed for {self.service_name}")
            
        except Exception as e:
            logger.error(f"Happay deep scan failed: {str(e)}", exc_info=True)

    async def infra_scan(self) -> Dict:
        """Public interface for infrastructure scan"""
        logger.info(f"Initiating infra scan for {self.service_name}")
        
        try:
            await self.get_service_details()
            auth_success = await self._setup_authentication()
            
            if not auth_success:
                logger.error(f"Authentication failed for {self.service_name}")
                return {"total_size": 0, "total_files": 0}
            
            infra = await self.scan_happay_infra()
            logger.info(f"Infra scan finished for {self.service_name}: {infra}")
            return infra
            
        except Exception as e:
            logger.error(
                f"Error during Happay infra scanning for {self.service_name}: {str(e)}",
                exc_info=True
            )
            return {"total_size": 0, "total_files": 0}

    async def deep_scan(self) -> AsyncGenerator[FileMetadata, None]:
        """Public interface for deep scan"""
        logger.info(f"Initiating deep scan for {self.service_name}")
        
        try:
            await self.get_service_details()
            auth_success = await self._setup_authentication()
            
            if not auth_success:
                logger.error(f"Authentication failed for {self.service_name}")
                return
            
            async for file_metadata in self.scan_happay_deep():
                yield file_metadata
            
            logger.info(f"Deep scan finished for {self.service_name}")
            
        except Exception as e:
            logger.error(
                f"Error during Happay deep scanning for {self.service_name}: {str(e)}",
                exc_info=True
            )
        finally:
            if self.session and not self.session.closed:
                logger.info(f"Closing aiohttp session for {self.service_name}")
                await self.session.close()