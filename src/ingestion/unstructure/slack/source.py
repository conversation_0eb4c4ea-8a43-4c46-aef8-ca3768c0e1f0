
import aiohttp
import asyncio
import json
import os
from uuid import uuid4
from datetime import datetime
from typing import Async<PERSON>enerator, Dict, List
from src.utils.logger import get_ingestion_logger
from src.common.config import TEMP_DATA_DIR, CONCURRENT_LIMIT
from src.common.constants import ServiceTypes, ServiceProviders, LocalSubservice
from src.ingestion.base_source import UnstructureSource
from src.ingestion.data_class import FileMetadata
from src.modules.repos import DatabaseManager
from src.utils.helpers import create_folder

logger = get_ingestion_logger()


class SlackSource(UnstructureSource):
    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.SLACK.value, service_name)
        self.db = DatabaseManager()
        self.service_provider = ServiceProviders.LOCAL
        self.sub_service = LocalSubservice.SLACK.value
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"
        self.include_channels: List[str] = []
        self.exclude_channels: List[str] = []

    async def get_service_details(self):
        self.service = await self.db.get_service_by_service_name(self.service_name)
        credentials = self.service.get("credentials", {})
        self.client_id = credentials.get("client_id")
        self.client_secret = credentials.get("client_secret")
        self.access_token = credentials.get("access_token")

        include_channels = credentials.get("include_channels", [])
        if isinstance(include_channels, str):
            self.include_channels = [c.strip() for c in include_channels.split(",") if c.strip()]
        else:
            self.include_channels = include_channels

        exclude_channels = credentials.get("exclude_channels", [])
        if isinstance(exclude_channels, str):
            self.exclude_channels = [c.strip() for c in exclude_channels.split(",") if c.strip()]
        else:
            self.exclude_channels = exclude_channels

        return self.service

    async def test_connection(self):
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
        }
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get("https://slack.com/api/auth.test", headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("ok"):
                            logger.info("Slack authentication successful")
                            return True, "Authentication successful"
                        else:
                            logger.error(f"Slack authentication failed: {data.get('error')}")
                            return False, f"Authentication failed: {data.get('error')}"
                    else:
                        logger.error(f"Slack auth test failed with status {response.status}")
                        return False, f"HTTP {response.status}"
        except Exception as e:
            logger.error(f"Slack auth test error: {str(e)}")
            return False, str(e)

    async def construct_file_uri(self, channel_name: str, filename: str) -> str:
        return f"slack:/{channel_name}/exported_data/{filename}"

    async def save_channel_data(self, channel_name: str, data: dict) -> tuple:
        create_folder(self.local_data_dir)
        filename = f"slack_channel_{channel_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        filepath = f"{self.local_data_dir}/{filename}"
        try:
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info(f"Saved Slack channel data for '{channel_name}' at {filepath}")
            file_uri = await self.construct_file_uri(channel_name, filename)
            return True, filepath, file_uri
        except Exception as e:
            logger.error(f"Failed to save data for channel {channel_name}: {str(e)}")
            return False, "", ""

    async def fetch_channels(self, session, headers) -> List[dict]:
        url = "https://slack.com/api/conversations.list?types=public_channel,private_channel"
        async with session.get(url, headers=headers) as resp:
            data = await resp.json()
            channels = data.get("channels", [])
            if self.include_channels:
                channels = [c for c in channels if c.get("name") in self.include_channels]
            if self.exclude_channels:
                channels = [c for c in channels if c.get("name") not in self.exclude_channels]
            logger.info(f"Fetched {len(channels)} Slack channels after filtering")
            return channels

    async def fetch_messages(self, session, channel_id, headers) -> List[dict]:
        url = f"https://slack.com/api/conversations.history?channel={channel_id}"
        async with session.get(url, headers=headers) as resp:
            data = await resp.json()
            messages = data.get("messages", [])
            logger.info(f"Fetched {len(messages)} messages from Slack channel ID {channel_id}")
            return messages

    async def download_file(self, session, url, filename, headers) -> str:
        path = f"{self.local_data_dir}/{filename}"
        async with session.get(url, headers=headers) as resp:
            if resp.status == 200:
                with open(path, "wb") as f:
                    f.write(await resp.read())
                logger.info(f"Downloaded Slack file '{filename}' to '{path}'")
                return path
        logger.error(f"Failed to download file '{filename}' from {url} with status {resp.status}")
        return ""

    async def fetch_workspace_data(self, semaphore) -> dict:
        await self.get_service_details()
        headers = {"Authorization": f"Bearer {self.access_token}"}
        create_folder(self.local_data_dir)

        summary = {
            "name": f"slack_workspace_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            "createdDateTime": datetime.now().isoformat(),
            "total_channels": 0,
            "total_messages": 0,
            "total_files": 0,
            "success": False,
        }

        filepath = f"{self.local_data_dir}/{uuid4()}_{summary['name']}"
        summary["filepath"] = filepath

        async with semaphore:
            try:
                async with aiohttp.ClientSession() as session:
                    channels = await self.fetch_channels(session, headers)
                    summary["total_channels"] = len(channels)
                    all_data = []

                    for channel in channels:
                        messages = await self.fetch_messages(session, channel["id"], headers)
                        message_data = []

                        for msg in messages:
                            files_data = []
                            for f in msg.get("files", []):
                                local_path = await self.download_file(session, f.get("url_private"), f.get("name"), headers)
                                if local_path:
                                    summary["total_files"] += 1
                                    files_data.append({
                                        "name": f.get("name"),
                                        "path": local_path,
                                        "type": f.get("mimetype"),
                                    })
                            msg["downloaded_files"] = files_data
                            message_data.append(msg)

                        summary["total_messages"] += len(messages)
                        all_data.append({"channel": channel.get("name"), "messages": message_data})

                    with open(filepath, "w", encoding="utf-8") as f:
                        json.dump(all_data, f, indent=2)

                    summary["success"] = True
                    summary["file_uri"] = await self.construct_file_uri("workspace", summary["name"])
                    logger.info(f"Slack workspace data saved to {filepath}")

            except Exception as e:
                logger.error(f"Slack fetch error: {str(e)}")

        return summary

    async def infra_scan(self) -> Dict:
        await self.get_service_details()
        headers = {"Authorization": f"Bearer {self.access_token}"}
        summary = {"total_channels": 0, "total_messages": 0, "total_files": 0}

        try:
            async with aiohttp.ClientSession() as session:
                channels = await self.fetch_channels(session, headers)
                summary["total_channels"] = len(channels)

                for channel in channels:
                    messages = await self.fetch_messages(session, channel["id"], headers)
                    summary["total_messages"] += len(messages)
                    for msg in messages:
                        summary["total_files"] += len(msg.get("files", []))

            logger.info(f"Slack infra scan completed: {summary['total_channels']} channels, {summary['total_messages']} messages, {summary['total_files']} files")

        except Exception as e:
            logger.error(f"Slack infra scan error: {str(e)}")

        return summary

    async def deep_scan(self) -> AsyncGenerator[FileMetadata, None]:
        await self.get_service_details()
        semaphore = asyncio.Semaphore(CONCURRENT_LIMIT)
        summary = await self.fetch_workspace_data(semaphore)

        if summary.get("success"):
            yield FileMetadata(
                service_name=self.service_name,
                service_type=self.service_type,
                service_provider=self.service_provider,
                sub_service=self.sub_service,
                file_key=summary["name"],
                file_size=0,
                file_type="json",
                file_uri=summary["file_uri"],
                local_filepath=summary["filepath"],
                details={
                    "created": summary["createdDateTime"],
                    "modified": summary["createdDateTime"],
                    "total_channels": summary["total_channels"],
                    "total_messages": summary["total_messages"],
                    "total_files": summary["total_files"],
                    "region": "India",
                },
            )
