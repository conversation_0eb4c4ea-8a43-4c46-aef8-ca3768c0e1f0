import aiohttp
import logging
from src.utils.exceptions import ClientException, ServerException

logger = logging.getLogger("Unstructure")

async def test_slack_connection(data: dict) -> dict:
    creds = data.get("credentials", {})
    try:
        access_token = creds.get("access_token")
        if not access_token:
            raise ClientException("Missing Slack access token", code=400)

        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        async with aiohttp.ClientSession() as session:
            async with session.get("https://slack.com/api/auth.test", headers=headers) as auth_resp:
                auth_data = await auth_resp.json()
                if auth_resp.status != 200 or not auth_data.get("ok"):
                    error_msg = auth_data.get("error", "Unknown error")
                    raise ClientException(f"Slack authentication failed: {error_msg}", code=auth_resp.status)

            async with session.get("https://slack.com/api/conversations.list", headers=headers) as channels_resp:
                channels_data = await channels_resp.json()
                if channels_resp.status == 200 and channels_data.get("ok"):
                    total_channels = len(channels_data.get("channels", []))
                    return {
                        "status": True,
                        "message": f"Slack connection verified successfully. Total channels: {total_channels}",
                    }
                else:
                    error_msg = channels_data.get("error", "Unknown error")
                    raise ClientException(f"Slack API access failed: {error_msg}", code=channels_resp.status)

    except ClientException as ce:
        raise ce
    except Exception as e:
        logger.exception("Unexpected error during Slack connection test")
        raise ServerException("Slack connection test failed", excep=e)