import aioboto3
from uuid import uuid4
import aiofiles
from typing import AsyncGenerator, List, Dict
from src.common.constants import ServiceTypes, ServiceProviders, AWSSubServices
from src.utils.logger import get_ingestion_logger
from src.ingestion.unstructure.aws.s3.connection import get_country_from_aws_region
from src.modules.repos import DatabaseManager
from botocore.exceptions import NoCredentialsError, BotoCoreError, ClientError
from src.ingestion.base_source import UnstructureSource
from src.ingestion.data_class import AssetsDetails
from src.utils.loaders import load_access_controls,load_asset_details
from src.ingestion.data_class import FileMetadata
from src.common.config import (
    PROFILE_MAX_SIZE_THRESHOLD,
    PROFILE_SUPPORTED_FILE_TYPES,
    TEMP_DATA_DIR,
    CONCURRENT_LIMIT,
)
from src.utils.helpers import file_exists, create_folder, get_file_extension
import botocore
import json
import asyncio
import zipfile
import tempfile
import shutil
import os

logger = get_ingestion_logger()


class S3Source(UnstructureSource):
    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.AWS_S3.value, service_name)
        self.db = DatabaseManager()
        self.service_provider = ServiceProviders.AWS
        self.sub_service = AWSSubServices.AWS_S3
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"

    async def get_service_details(self):
        """Gets service details"""
        self.service = await self.db.get_service_by_service_name(self.service_name)
        credentials = self.service.get("credentials", {})
        self.access_key = credentials.get("access_key")
        self.secret_key = credentials.get("secret_key")
        self.region = credentials.get("region")
        self.buckets = credentials.get("buckets", [])

        return self.service

    async def get_session(self) -> aioboto3.Session:
        """Creates connection"""
        session = aioboto3.Session(
            aws_access_key_id=self.access_key,
            aws_secret_access_key=self.secret_key,
            region_name=self.region,
        )
        return session

    async def verify_s3_iam_role_authentication(self) -> dict:
        try:
            await self.get_service_details()
            if self.access_key and self.secret_key:
                logger.info(
                    "Using provided access_key and secret_key for authentication."
                )
                session = aioboto3.Session(
                    aws_access_key_id=self.access_key,
                    aws_secret_access_key=self.secret_key,
                    region_name=self.region,
                )

            elif self.region:
                logger.info("Using IAM role with provided region for authentication")
                session = aioboto3.Session(region_name=self.region)
            else:
                logger.error("Missing both credentials and region ")
                return {
                    "success": False,
                    "message": "Missing both credentials and region.",
                }
            async with session.client("s3") as s3_client:
                response = await s3_client.list_buckets()
                bucket_names = [b["Name"] for b in response.get("Buckets", [])]
                logger.info(f"Authentication successful Buckets:{bucket_names}")
                return {
                    "success": True,
                    "message": "S3 authentication successful.",
                    "buckets": bucket_names,
                }
        except NoCredentialsError:
            logger.warning("NoCredentialsError: IAM Role or keys missing.")
            return {
                "success": False,
                "message": "No AWS credentials or IAM role found.",
            }
        except ClientError as e:
            logger.error(f"ClientError during authentication: {str(e)}")
            return {
                "success": False,
                "message": f"AWS client error: {str(e)}",
            }
        except BotoCoreError as e:
            logger.error(f"BotoCoreError during authentication: {str(e)}")
            return {
                "success": False,
                "message": f"BotoCore error: {str(e)}",
            }
        except Exception as e:
            logger.exception("Unexpected error during S3 authentication.")
            return {
                "success": False,
                "message": f"Unexpected error: {str(e)}",
            }

    async def test_connection(self) -> dict:
        """Test connection by verifying IAM role and region"""
        try:
            session = aioboto3.Session()
            async with session.client("s3", region_name=self.region) as s3_client:
                logger.info("Successfully initialized AWS S3 client.")
                response = await s3_client.list_buckets()
                bucket_count = len(response.get("Buckets", []))
                logger.info(f"IAM Role Verification Successful: {bucket_count}")
                return {
                    "success": True,
                    "message": f"IAM Role Verified! Bucket count: {bucket_count}",
                }

        except botocore.exceptions.NoCredentialsError:
            return {
                "success": False,
                "message": "Ensure an IAM role is attached to the EC2 instance.",
            }
        except botocore.exceptions.ClientError as e:
            return {"success": False, "message": f"AWS Client error: {str(e)}"}
        except botocore.exceptions.BotoCoreError as e:
            return {"success": False, "message": f"AWS Boto3 error: {str(e)}"}
        except Exception as e:
            return {"success": False, "message": f"Unexpected error: {str(e)}"}

    async def list_s3_buckets(self, s3_client, region: str) -> list:
        try:
            response = await s3_client.list_buckets()
            bucket_list = [bucket["Name"] for bucket in response.get("Buckets", [])]

            return bucket_list

        except Exception as e:
            logger.error(f"Error listing S3 buckets: {str(e)}", exc_info=True)

        return []

    # async def infra_scan(self):
    #     await self.get_service_details()
    #     session = await self.get_session()
    #     try:
    #         data = []
    #         async with session.client("s3") as s3_client:
    #             logger.info("Successfully created S3 client for specific bucket scan.")
    #             if not self.buckets:
    #                 self.buckets = await self.list_s3_buckets(s3_client, self.region)

    #             for bucket_name in self.buckets:
    #                 bucket_data = await self.get_bucket_details(
    #                     s3_client, bucket_name, self.region
    #                 )
    #                 region_country = get_country_from_aws_region(self.region)
    #                 bucket_data["region"] = region_country
    #                 logger.info(
    #                     f"AWS region: {self.region}, Mapped country/region: {region_country}"
    #                 )
    #                 data.append(bucket_data)

    #         return data
    #     except Exception as e:
    #         logger.error(f"Error in S3 scan: {str(e)}")

    async def infra_scan(self):
        await self.get_service_details()
        session = await self.get_session()
        try:
            data = []
            global_owner_display_name = "N/A"
            global_owner_id = "N/A"

            async with session.client("s3", region_name="us-east-1") as global_client:
                bucket_response = await global_client.list_buckets()
                global_owner = bucket_response.get("Owner", {})
                global_owner_display_name = global_owner.get("DisplayName", "N/A")
                global_owner_id = global_owner.get("ID", "N/A")

            async with session.client("s3") as s3_client:
                logger.info("INFRA SCAN - Starting bucket discovery and analysis")
                
                if not self.buckets:
                    try:
                        self.buckets = await self.list_s3_buckets(s3_client, self.region)
                        if not self.buckets:
                            logger.info("No S3 buckets found in region")
                            return []
                    except Exception as e:
                        logger.warning(f"Failed to list S3 buckets: {e}")
                        return []
                
                for bucket_name in self.buckets:
                    try:
                        region_resp = await s3_client.get_bucket_location(Bucket=bucket_name)
                        bucket_region = region_resp.get("LocationConstraint") or "us-east-1"
                        region_country = get_country_from_aws_region(bucket_region)

                        metrics = await self.get_bucket_metrics(s3_client, bucket_name)
                        total_size = metrics.get("total_size", 0)
                        object_count = metrics.get("no_of_objects", 0)

                        versioning = await s3_client.get_bucket_versioning(Bucket=bucket_name)
                        versioning_status = versioning.get("Status", "Disabled")

                        encryption_enabled = False
                        try:
                            object_list = await s3_client.list_objects_v2(Bucket=bucket_name, MaxKeys=1)
                            contents = object_list.get("Contents", [])
                            if contents:
                                obj_key = contents[0]["Key"]
                                head_obj = await s3_client.head_object(Bucket=bucket_name, Key=obj_key)
                                encryption_type = head_obj.get("ServerSideEncryption")
                                encryption_enabled = encryption_type in ("AES256", "aws:kms", "aws:kms:dsse")
                        except Exception as enc_err:
                            logger.warning(f"[{bucket_name}] Error checking encryption: {enc_err}")

                        # Get comprehensive access controls and security settings
                        access_controls = await self.get_s3_bucket_access_controls(s3_client, bucket_name)
                        encryption_info = await self.get_s3_bucket_encryption_status(s3_client, bucket_name)
                        versioning_info = await self.get_s3_bucket_versioning_status(s3_client, bucket_name)
                        logging_info = await self.get_s3_bucket_logging_status(s3_client, bucket_name)
                        public_access_block = await self.get_s3_bucket_public_access_block(s3_client, bucket_name)
                        
                        # Determine accessibility level
                        accessibility_info = await self.determine_s3_bucket_accessibility_level(
                            bucket_name, access_controls, public_access_block
                        )
                        
                        # Load access controls to the system
                        access_controls_with_asset = [{**control, "asset_name": bucket_name} for control in access_controls]
                        if access_controls_with_asset:
                            load_access_controls(access_controls_with_asset)
                        
                        base_data = {}
                        if hasattr(self, "get_bucket_details"):
                            base_data = await self.get_bucket_details(s3_client, bucket_name, bucket_region)

                        bucket_data = {
                            "bucket": bucket_name,
                            "region_country": region_country,
                            "aws_region": bucket_region,
                            "size": total_size,
                            "no_of_objects": object_count,
                            "versioning_status": versioning_info.get("status", "Disabled"),
                            "owner_display_name": global_owner_display_name,
                            "owner_id": global_owner_id,
                            "encryption": encryption_info.get("encryption_enabled", False),
                            "encryption_details": encryption_info,
                            "versioning_details": versioning_info,
                            "logging_details": logging_info,
                            "public_access_block": public_access_block,
                            "accessibility_info": accessibility_info,
                            "access_controls": access_controls
                        }
                        bucket_data.update(base_data)

                        data.append(bucket_data)

                        # Create asset with accessibility information
                        asset = AssetsDetails(
                            asset_name=bucket_name,
                            service_provider=self.service_provider,
                            type="file_storage",
                            category="unstructured",
                            location=bucket_region,
                            owner=global_owner_display_name,
                            security=encryption_info.get("encryption_enabled", False),
                            size=total_size,
                            count=object_count,
                            access_category=accessibility_info.get("accessibility_level", "restricted"),
                            service_name=self.service_name,
                           
                        )
                        load_asset_details([asset])
                        
                    except Exception as bucket_err:
                        logger.error(f"[InfraScan] Error processing bucket '{bucket_name}': {bucket_err}", exc_info=True)

            self.metadata = data
            return data

        except Exception as e:
            logger.error(f"[InfraScan] Unexpected error: {e}", exc_info=True)
            return []

    async def deep_scan(self) -> AsyncGenerator[FileMetadata, None]:
        """Deep scan implementation for S3"""
        try:
            # Ensure service details are loaded
            await self.get_service_details()
            session = await self.get_session()
            await self.infra_scan()

            async with session.client("s3") as s3_client:
                logger.info("Successfully created S3 client for deep scan")

                # If no specific buckets provided, list all buckets
                if not self.buckets:
                    self.buckets = await self.list_s3_buckets(s3_client, self.region)

                for bucket_name in self.buckets:
                    try:
                        async for record in self.scan_bucket(s3_client, bucket_name):
                            if not record:
                                continue

                            yield FileMetadata(
                                service_name=self.service_name,
                                service_type=self.service_type,
                                service_provider=self.service_provider,
                                sub_service=self.sub_service,
                                file_key=record.get("key", ""),
                                file_size=record.get("size", 0),
                                file_type=record.get("key", "").split(".")[-1],
                                file_uri=record.get("file_uri", ""),
                                local_filepath=record.get("filepath"),
                                details={"bucket": record.get("bucket")},
                            )
                    except Exception as e:
                        logger.error(f"Error scanning bucket {bucket_name}: {str(e)}")
                        continue

        except Exception as e:
            logger.error(f"Error in S3 deep scan: {str(e)}")
            raise

    async def get_bucket_metrics(self, s3_client, bucket_name: str) -> dict:
        await s3_client.head_bucket(Bucket=bucket_name)
        logger.info(f"Processing bucket: {bucket_name}")
        total_size = 0
        no_of_objects = 0
        try:
            paginator = s3_client.get_paginator("list_objects_v2")
            async for page in paginator.paginate(Bucket=bucket_name):
                objects = page.get("Contents", [])
                for obj in objects:
                    no_of_objects += 1
                    object_size = obj.get("Size", 0)
                    total_size += object_size
        except Exception as e:
            logger.error(f"Error processing bucket {bucket_name}: {str(e)}")

        return {"total_size": total_size, "no_of_objects": no_of_objects}

    async def construct_s3_file_uri(self, bucket_name: str, object_key: str) -> str:
        """Constructs a file URI for S3 objects with folder and subfolder structure."""
        file_uri = f"s3://{bucket_name}/{object_key}"
        logger.info(f"Generated file URI: {file_uri}")
        return file_uri

    async def scan_bucket(self, s3_client, bucket_name: str):
        await s3_client.head_bucket(Bucket=bucket_name)
        logger.info(f"Processing bucket: {bucket_name}")
        try:
            paginator = s3_client.get_paginator("list_objects_v2")
            async for page in paginator.paginate(Bucket=bucket_name):
                objects = page.get("Contents", [])
                for obj in objects:
                    object_key = obj["Key"]
                    object_size = obj.get("Size", 0)
                    logger.info(
                        f"Processing object: {object_key} in bucket: {bucket_name}"
                    )
                    file_data = file_info = {
                        "key": object_key,
                        "size": object_size,
                        "bucket": bucket_name,
                        "file_uri": f"s3://{bucket_name}/{object_key}",
                        "filepath": None,
                    }
                    try:
                        file_uri = await self.construct_s3_file_uri(
                            bucket_name, object_key
                        )
                        file_data["file_uri"] = file_uri

                        if object_key.endswith("/"):
                            logger.info(f"Skipping directory marker: {object_key}")
                            continue

                        file_ext = get_file_extension(object_key)
                        if self.rule_manager.skip_file(file_uri):
                            logger.info(
                                f"Skipping file {object_key} based on skip rules"
                            )
                            continue

                        if file_ext not in PROFILE_SUPPORTED_FILE_TYPES:
                            logger.info(
                                f"Skipping {object_key}: Unsupported file type .{file_ext}"
                            )
                            continue

                        if object_size > PROFILE_MAX_SIZE_THRESHOLD:
                            logger.info(
                                f"Skipping {object_key}: Size {object_size} exceeds threshold "
                                f"{PROFILE_MAX_SIZE_THRESHOLD}"
                            )
                            continue

                        temp_dir = self.local_data_dir
                        create_folder(temp_dir)
                        filepath = f"{self.local_data_dir}/{str(uuid4())}.{file_ext}"
                        await self.download_s3_file(
                            s3_client, bucket_name, object_key, filepath
                        )

                        file_data["filepath"] = filepath
                        logger.info(f"File download: {filepath}")
                        yield file_data

                    except Exception as e:
                        logger.error(
                            f"Error processing {object_key} in bucket {bucket_name}: {str(e)}"
                        )
        except Exception as e:
            logger.error(f"Error processing bucket {bucket_name}: {str(e)}")

    async def get_bucket_details(
        self, s3_client, bucket_name: str, aws_region: str
    ) -> dict:
        try:
            versioning = await s3_client.get_bucket_versioning(Bucket=bucket_name)
            data = await self.get_bucket_metrics(s3_client, bucket_name)
            bucket_info = {
                "bucket": bucket_name,
                "region": aws_region,
                "size": data.get("total_size"),
                "no_of_objects": data.get("no_of_objects"),
                "versioning_status": versioning.get("Status", "Disabled"),
            }
            return bucket_info
        except Exception as e:
            logger.error(f"Error getting bucket details: {str(e)}")
            return {}

    async def get_object_metadata(
        self, s3_client, bucket_name: str, object_key: str
    ) -> dict:
        try:
            metadata = await s3_client.head_object(Bucket=bucket_name, Key=object_key)
            content_type = metadata.get("ContentType", "application/octet-stream")
            return {
                "key": object_key,
                "size": metadata.get("ContentLength", 0),
                "last_modified": metadata.get("LastModified").isoformat(),
                "etag": metadata.get("ETag", "").strip('"'),
                "content_type": content_type,
                "metadata": metadata.get("Metadata", {}),
                "version_id": metadata.get("VersionId", None),
            }
        except Exception as e:
            logger.error(f"Error getting object metadata: {str(e)}")

        return {}

    def _map_s3_permission_to_access(self, permission: str) -> str:
        """Map S3 ACL permission to access level"""
        permission = permission.upper()
        if permission == "FULL_CONTROL":
            return "full"
        elif permission == "WRITE":
            return "write"
        elif permission == "READ":
            return "read"
        else:
            return "read"

    def _map_s3_policy_actions_to_access(self, actions: list) -> str:
        """Map S3 policy actions to access level"""
        if not isinstance(actions, list):
            actions = [actions]
        
        actions_str = " ".join(actions).upper()
        if "PUT" in actions_str or "DELETE" in actions_str or "*" in actions_str:
            return "full" if "DELETE" in actions_str else "write"
        else:
            return "read"
    
    async def get_s3_bucket_access_controls(self, s3_client, bucket_name: str) -> list:
        """Get S3 bucket access controls including bucket policies, ACLs, and IAM permissions"""
        try:
            access_controls = []
            
            # Get bucket ACL
            try:
                acl_response = await s3_client.get_bucket_acl(Bucket=bucket_name)
                grants = acl_response.get("Grants", [])
                
                for grant in grants:
                    grantee = grant.get("Grantee", {})
                    permission = grant.get("Permission", "")
                    
                    if grantee.get("Type") == "CanonicalUser":
                        grantee_id = grantee.get("ID", "Unknown")
                        grantee_display_name = grantee.get("DisplayName", "Unknown")
                        access_controls.append({
                            "user_or_role": grantee_display_name or grantee_id,
                            "role": "user",
                            "access": self._map_s3_permission_to_access(permission),
                            "grantee_type": "CanonicalUser",
                            "grantee_id": grantee_id,
                            "permission": permission
                        })
                    elif grantee.get("Type") == "Group":
                        group_uri = grantee.get("URI", "")
                        if "AllUsers" in group_uri:
                            access_controls.append({
                                "user_or_role": "AllUsers",
                                "role": "public",
                                "access": self._map_s3_permission_to_access(permission),
                                "grantee_type": "Group",
                                "permission": permission
                            })
                        elif "AuthenticatedUsers" in group_uri:
                            access_controls.append({
                                "user_or_role": "AuthenticatedUsers",
                                "role": "authenticated",
                                "access": self._map_s3_permission_to_access(permission),
                                "grantee_type": "Group",
                                "permission": permission
                            })
                
                
            except Exception as e:
                logger.warning(f"Could not retrieve ACL for bucket {bucket_name}: {str(e)}")
            
            # Get bucket policy
            try:
                policy_response = await s3_client.get_bucket_policy(Bucket=bucket_name)
                policy = json.loads(policy_response.get("Policy", "{}"))
                
                statements = policy.get("Statement", [])
                for statement in statements:
                    effect = statement.get("Effect", "Allow")
                    principal = statement.get("Principal", {})
                    action = statement.get("Action", [])
                    
                    if isinstance(action, str):
                        action = [action]
                    
                    # Parse principal
                    if principal == "*":
                        access_controls.append({
                            "user_or_role": "Public",
                            "role": "public",
                            "access": self._map_s3_policy_actions_to_access(action),
                            "grantee_type": "Policy",
                            "effect": effect,
                            "actions": action
                        })
                    elif isinstance(principal, dict):
                        for principal_type, principal_value in principal.items():
                            if principal_type == "AWS":
                                if isinstance(principal_value, list):
                                    for arn in principal_value:
                                        access_controls.append({
                                            "user_or_role": arn,
                                            "role": "iam_role",
                                            "access": self._map_s3_policy_actions_to_access(action),
                                            "grantee_type": "Policy",
                                            "effect": effect,
                                            "actions": action
                                        })
                                else:
                                    access_controls.append({
                                        "user_or_role": principal_value,
                                        "role": "iam_role",
                                        "access": self._map_s3_policy_actions_to_access(action),
                                        "grantee_type": "Policy",
                                        "effect": effect,
                                        "actions": action
                                    })
                
                
            except Exception as e:
                logger.info(f"No bucket policy found for {bucket_name} or error retrieving: {str(e)}")
            
            # Get bucket ownership controls
            try:
                ownership_response = await s3_client.get_bucket_ownership_controls(Bucket=bucket_name)
                ownership_rules = ownership_response.get("OwnershipControls", {}).get("Rules", [])
                
                for rule in ownership_rules:
                    object_ownership = rule.get("ObjectOwnership", "")
                    if object_ownership == "BucketOwnerPreferred":
                        access_controls.append({
                            "user_or_role": "BucketOwner",
                            "role": "owner",
                            "access": "full",
                            "grantee_type": "Ownership",
                            "ownership_rule": object_ownership
                        })
            
                
            except Exception as e:
                logger.info(f"No ownership controls found for {bucket_name} or error retrieving: {str(e)}")
            
            return access_controls
            
        except Exception as e:
            logger.error(f"Error getting access controls for bucket {bucket_name}: {str(e)}")
            return []



    async def get_s3_bucket_encryption_status(self, s3_client, bucket_name: str) -> dict:
        """Get detailed encryption status for S3 bucket"""
        try:
            encryption_info = {
                "encryption_enabled": False,
                "encryption_type": None,
                "kms_key_id": None,
                "encryption_algorithm": None
            }
            
            try:
                response = await s3_client.get_bucket_encryption(Bucket=bucket_name)
                encryption_rules = response.get("ServerSideEncryptionConfiguration", {}).get("Rules", [])
                
                if encryption_rules:
                    encryption_info["encryption_enabled"] = True
                    rule = encryption_rules[0]
                    sse_config = rule.get("ApplyServerSideEncryptionByDefault", {})
                    
                    encryption_info["encryption_type"] = sse_config.get("SSEAlgorithm")
                    encryption_info["kms_key_id"] = sse_config.get("KMSMasterKeyID")
                    
                    if encryption_info["encryption_type"] == "aws:kms":
                        encryption_info["encryption_algorithm"] = "AES-256"
                    elif encryption_info["encryption_type"] == "AES256":
                        encryption_info["encryption_algorithm"] = "AES-256"
                    
                else:
                    logger.info(f"Bucket {bucket_name} has no encryption rules configured")
                    
            except Exception as e:
                logger.info(f"Bucket {bucket_name} encryption check failed: {str(e)}")
            
            return encryption_info
            
        except Exception as e:
            logger.error(f"Error checking encryption for bucket {bucket_name}: {str(e)}")
            return {"encryption_enabled": False, "encryption_type": None}



    async def get_s3_bucket_versioning_status(self, s3_client, bucket_name: str) -> dict:
        """Get versioning status for S3 bucket"""
        try:
            versioning_info = {
                "versioning_enabled": False,
                "mfa_delete": False,
                "status": "Disabled"
            }
            
            try:
                response = await s3_client.get_bucket_versioning(Bucket=bucket_name)
                status = response.get("Status")
                
                if status:
                    versioning_info["versioning_enabled"] = status == "Enabled"
                    versioning_info["status"] = status
                    versioning_info["mfa_delete"] = response.get("MFADelete") == "Enabled"
                else:
                    logger.info(f"Bucket {bucket_name} versioning is disabled")
                    
            except Exception as e:
                logger.info(f"Bucket {bucket_name} versioning check failed: {str(e)}")
            
            return versioning_info
            
        except Exception as e:
            logger.error(f"Error checking versioning for bucket {bucket_name}: {str(e)}")
            return {"versioning_enabled": False, "mfa_delete": False, "status": "Disabled"}


    async def get_s3_bucket_logging_status(self, s3_client, bucket_name: str) -> dict:
        """Get logging status for S3 bucket"""
        try:
            logging_info = {
                "logging_enabled": False,
                "target_bucket": None,
                "target_prefix": None
            }
            
            try:
                response = await s3_client.get_bucket_logging(Bucket=bucket_name)
                logging_config = response.get("LoggingEnabled", {})
                
                if logging_config:
                    logging_info["logging_enabled"] = True
                    logging_info["target_bucket"] = logging_config.get("TargetBucket")
                    logging_info["target_prefix"] = logging_config.get("TargetPrefix")
                    
                else:
                    logger.info(f"Bucket {bucket_name} has no logging configured")
                    
            except Exception as e:
                logger.info(f"Bucket {bucket_name} logging check failed: {str(e)}")
            
            return logging_info
            
        except Exception as e:
            logger.error(f"Error checking logging for bucket {bucket_name}: {str(e)}")
            return {"logging_enabled": False, "target_bucket": None, "target_prefix": None}



    async def get_s3_bucket_public_access_block(self, s3_client, bucket_name: str) -> dict:
        """Get public access block settings for S3 bucket"""
        try:
            public_access_info = {
                "block_public_acls": True,
                "ignore_public_acls": True,
                "block_public_policy": True,
                "restrict_public_buckets": True
            }
            
            try:
                response = await s3_client.get_public_access_block(Bucket=bucket_name)
                public_access_block = response.get("PublicAccessBlockConfiguration", {})
                
                public_access_info.update({
                    "block_public_acls": public_access_block.get("BlockPublicAcls", True),
                    "ignore_public_acls": public_access_block.get("IgnorePublicAcls", True),
                    "block_public_policy": public_access_block.get("BlockPublicPolicy", True),
                    "restrict_public_buckets": public_access_block.get("RestrictPublicBuckets", True)
                })
                
            except Exception as e:
                logger.info(f"Bucket {bucket_name} public access block check failed: {str(e)}")
            
            return public_access_info
            
        except Exception as e:
            logger.error(f"Error checking public access block for bucket {bucket_name}: {str(e)}")
            return {"block_public_acls": True, "ignore_public_acls": True, "block_public_policy": True, "restrict_public_buckets": True}


    async def determine_s3_bucket_accessibility_level(self, bucket_name: str, access_controls: list, public_access_block: dict) -> dict:
        """Determine the accessibility level of an S3 bucket"""
        accessibility_analysis = {
            "bucket_name": bucket_name,
            "accessibility_level": "restricted",  # default
            "risk_factors": [],
            "access_details": {},
            "recommendations": []
        }
        
        try:
            # Check public access block settings
            if not public_access_block["block_public_acls"]:
                accessibility_analysis["risk_factors"].append("Public ACLs are not blocked")
            
            if not public_access_block["block_public_policy"]:
                accessibility_analysis["risk_factors"].append("Public policies are not blocked")
            
            if not public_access_block["restrict_public_buckets"]:
                accessibility_analysis["risk_factors"].append("Public bucket access is not restricted")
            
            # Analyze access controls
            public_access = False
            external_access = False
            
            for control in access_controls:
                if control["role"] == "public":
                    public_access = True
                    accessibility_analysis["risk_factors"].append(f"Public access granted: {control['user_or_role']}")
                elif control["role"] in ["authenticated", "iam_role"]:
                    external_access = True
                    if control["access"] in ["write", "full"]:
                        accessibility_analysis["risk_factors"].append(f"External write access: {control['user_or_role']}")
            
            # Determine accessibility level
            if public_access:
                accessibility_analysis["accessibility_level"] = "public"
            elif external_access:
                accessibility_analysis["accessibility_level"] = "external"
            else:
                logger.info(f"S3 bucket {bucket_name} appears to be internal/restricted")
            
            # Generate recommendations
            if accessibility_analysis["accessibility_level"] == "public":
                accessibility_analysis["recommendations"].extend([
                    "URGENT: Enable all public access block settings",
                    "Remove public ACL grants",
                    "Review and restrict bucket policies",
                    "Enable access logging for monitoring",
                    "Consider using S3 Block Public Access"
                ])
            elif accessibility_analysis["accessibility_level"] == "external":
                accessibility_analysis["recommendations"].extend([
                    "Review external access permissions",
                    "Enable access logging",
                    "Consider restricting to specific IAM roles/users",
                    "Enable CloudTrail for API monitoring"
                ])
            
            accessibility_analysis["access_details"]["public_access_block"] = public_access_block
            accessibility_analysis["access_details"]["access_controls"] = access_controls
            
        except Exception as e:
            logger.error(f"Error determining accessibility level for S3 bucket {bucket_name}: {e}")
            accessibility_analysis["error"] = str(e)
        
        return accessibility_analysis

    async def download_s3_file(
        self, s3_client, bucket_name: str, object_key: str, filepath: str
    ) -> str:
        logger.info(f"Starting download for from bucket: {bucket_name}")

        if file_exists(filepath):
            return filepath

        try:
            response = await s3_client.get_object(Bucket=bucket_name, Key=object_key)
            body = response["Body"]
            async with aiofiles.open(filepath, "wb") as f:
                while True:
                    chunk = await body.read(8192)
                    if not chunk:
                        break
                    await f.write(chunk)
        except Exception as e:
            logger.error(f"Error in download process: {str(e)}")

        return filepath

    def parse_s3_uri(self, uri: str):
        """Parse S3 URI to extract bucket name, object key, and optional inner zip path.
        Supports formats:
        - s3://bucket-name/path/to/file
        - s3://bucket-name/path/to/file.zip!/inner/path/file.xlsx
        """
        def normalize_path(p: str) -> str:
            if not p:
                return p
            p = p.replace('\\', '/').strip()
            while '//' in p:
                p = p.replace('//', '/')
            return p

        try:
            # Remove s3:// prefix
            if uri.startswith("s3://"):
                uri = uri[5:]
            
            # Check for zip member path
            if "!/" in uri:
                parts = uri.split("!/", 1)
                bucket_path = parts[0]
                inner_path = parts[1]
                
                # Extract bucket name and key
                slash_index = bucket_path.find('/')
                if slash_index == -1:
                    bucket_name = bucket_path
                    object_key = ""
                else:
                    bucket_name = bucket_path[:slash_index]
                    object_key = bucket_path[slash_index+1:]
                
                return {
                    "bucket_name": bucket_name,
                    "object_key": normalize_path(object_key),
                    "inner_path": normalize_path(inner_path)
                }
            else:
                # Extract bucket name and key
                slash_index = uri.find('/')
                if slash_index == -1:
                    bucket_name = uri
                    object_key = ""
                else:
                    bucket_name = uri[:slash_index]
                    object_key = uri[slash_index+1:]
                
                # Check if object_key contains zip extension
                for ext in ('.zip', '.zipx'):
                    marker = f"{ext}/"
                    if marker in object_key.lower():
                        idx = object_key.lower().index(marker) + len(ext)
                        return {
                            "bucket_name": bucket_name,
                            "object_key": normalize_path(object_key[:idx]),
                            "inner_path": normalize_path(object_key[idx:])
                        }
                
                return {
                    "bucket_name": bucket_name,
                    "object_key": normalize_path(object_key),
                    "inner_path": None
                }
        except Exception as e:
            logger.error(f"Error parsing S3 URI '{uri}': {e}")
            return None

    def is_zip_file(self, filename: str) -> bool:
        """Check if file is a ZIP file based on extension"""
        return filename.lower().endswith(('.zip', '.zipx'))

    async def user_scan(self, search_files: List[Dict]):
        """Download only specified files for DSR. Supports ZIP member selection via
        file_uri like: s3://bucket-name/path/to/archive.zip!/internal/path/file.xlsx
        For S3, file_uri is expected as s3://bucket-name/path/to/file.
        """
        try:
            await self.get_service_details()
            session = await self.get_session()

            results: List[Dict] = []
            semaphore = asyncio.Semaphore(CONCURRENT_LIMIT)

            async with session.client("s3") as s3_client:
                for file_info in search_files:
                    file_uri = str(file_info.get("file_uri", ""))
                    file_name = str(file_info.get("file_name", ""))
                    
                    if not file_uri:
                        logger.warning(f"Skipping file with missing URI: {file_info}")
                        continue

                    # Parse S3 URI
                    parsed = self.parse_s3_uri(file_uri)
                    if not parsed:
                        logger.error(f"Failed to parse S3 URI: {file_uri}")
                        continue

                    bucket_name = parsed["bucket_name"]
                    object_key = parsed["object_key"]
                    inner_path = parsed["inner_path"]

                    if not bucket_name:
                        logger.error(f"No bucket name found in URI: {file_uri}")
                        continue

                    # Check skip rules
                    if self.rule_manager and self.rule_manager.skip_file(file_uri):
                        logger.info(f"Skipping by rule: {file_uri}")
                        continue

                    # Download the file
                    temp_dir = self.local_data_dir
                    create_folder(temp_dir)
                    local_filename = f"{uuid4()}_{file_name or os.path.basename(object_key)}"
                    local_path = os.path.join(temp_dir, local_filename)

                    async with semaphore:
                        try:
                            # Download file from S3
                            logger.info(f"Downloading {object_key} from bucket {bucket_name}")
                            
                            if not object_key or object_key.endswith("/"):
                                logger.warning(f"Skipping directory marker or empty key: {object_key}")
                                continue
                            
                            response = await s3_client.get_object(Bucket=bucket_name, Key=object_key)
                            body = response["Body"]
                            
                            async with aiofiles.open(local_path, "wb") as f:
                                while True:
                                    chunk = await body.read(1024 * 1024)
                                    if not chunk:
                                        break
                                    await f.write(chunk)
                            
                            logger.info(f"Successfully downloaded file to: {local_path}")
                        except Exception as e:
                            logger.error(f"Download failed for {file_uri}: {e}")
                            if os.path.exists(local_path):
                                os.remove(local_path)
                            continue

                    # Handle ZIP extraction if inner path exists
                    if inner_path and self.is_zip_file(local_path):
                        try:
                            with zipfile.ZipFile(local_path, 'r') as zip_ref:
                                norm_inner = inner_path.replace('\\', '/').lstrip('/')
                                
                                # Match exactly or find closest match
                                candidates = [m for m in zip_ref.namelist() if m.replace('\\', '/').lstrip('/') == norm_inner]
                                if not candidates:
                                    # Try to find partial matches
                                    candidates = [m for m in zip_ref.namelist() if norm_inner in m.replace('\\', '/')]
                                
                                if not candidates:
                                    logger.warning(f"Inner member '{inner_path}' not found in zip")
                                    if os.path.exists(local_path):
                                        os.remove(local_path)
                                    continue
                                
                                member = candidates[0]
                                extract_dir = tempfile.mkdtemp()
                                zip_ref.extract(member, extract_dir)
                                extracted_path = os.path.join(extract_dir, member)
                                
                                final_dir = os.path.join(self.local_data_dir, "extracted")
                                create_folder(final_dir)
                                final_path = os.path.join(final_dir, f"{uuid4()}_{os.path.basename(member)}")
                                shutil.move(extracted_path, final_path)
                                shutil.rmtree(extract_dir, ignore_errors=True)
                                
                                # Remove the outer zip file
                                try:
                                    if os.path.exists(local_path):
                                        os.remove(local_path)
                                except Exception:
                                    pass
                                
                                local_path = final_path
                                logger.info(f"Successfully extracted ZIP member: {member}")
                        except Exception as e:
                            logger.error(f"ZIP member extraction failed for {inner_path}: {e}")
                            if os.path.exists(local_path):
                                os.remove(local_path)
                            continue

                    # Add to results if file exists
                    if os.path.exists(local_path):
                        results.append({
                            "service_name": self.service_name,
                            "service_provider": str(self.service_provider),
                            "sub_service": str(self.sub_service),
                            "file_name": file_name or os.path.basename(object_key),
                            "file_uri": file_uri,
                            "file_type": get_file_extension(file_name or object_key),
                            "file_size": os.path.getsize(local_path),
                            "local_filepath": local_path,
                        })

            if results:
                yield results
        except Exception as e:
            logger.error(f"S3 user_scan failed: {e}", exc_info=True)
