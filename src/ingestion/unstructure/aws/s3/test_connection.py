import aioboto3
from botocore.exceptions import ClientError, NoCredentialsError, EndpointConnectionError
import logging
from src.utils.exceptions import ClientException, ServerException

logger = logging.getLogger("Unstructure")


async def test_aws_connection(data: dict) -> dict:
    creds = data.get("credentials", {})
    access_key = creds.get("access_key", "").strip()
    secret_key = creds.get("secret_key", "").strip()
    region = creds.get("region", "").strip()

    if not region:
        raise ClientException("Missing required AWS region in credentials", code=400)

    try:
        if access_key and secret_key:
            logger.info("Using AWS Access Key authentication")
            session = aioboto3.Session(
                aws_access_key_id=access_key,
                aws_secret_access_key=secret_key,
                region_name=region,
            )
            async with session.client("sts") as sts_client:
                identity = await sts_client.get_caller_identity()
                logger.info(f"AWS Access Key authentication successful - ARN: {identity['Arn']}")
                return {
                    "status": True,
                    "auth_mechanism": "access_key",
                    "account_id": identity.get("Account"),
                    "user_arn": identity.get("Arn"),
                    "message": "AWS Access Key authentication successful",
                }

        else:
            logger.info("Using IAM Role for AWS authentication")
            session = aioboto3.Session(region_name=region)
            async with session.client("s3") as s3_client:
                response = await s3_client.list_buckets()
                bucket_count = len(response.get("Buckets", []))
                logger.info(f"IAM Role authentication successful - Buckets found: {bucket_count}")
                return {
                    "status": True,
                    "auth_mechanism": "iam_role",
                    "bucket_count": bucket_count,
                    "message": f"IAM Role verified successfully. Bucket count: {bucket_count}",
                }

    except NoCredentialsError:
        logger.error("No AWS credentials found or accessible")
        raise ClientException("No valid AWS credentials found", code=401)

    except ClientError as e:
        logger.error(f"AWS ClientError: {str(e)}")
        raise ClientException(f"AWS client error: {str(e)}", code=400)

    except EndpointConnectionError as e:
        logger.error(f"Endpoint connection error: {str(e)}")
        raise ClientException(f"Invalid region or endpoint: {str(e)}", code=400)

    except Exception as e:
        logger.exception("Unexpected error during AWS connection test")
        raise ServerException("AWS test connection failed", excep=e)
