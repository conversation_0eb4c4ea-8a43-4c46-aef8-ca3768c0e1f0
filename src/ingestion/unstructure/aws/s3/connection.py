AWS_REGION_TO_COUNTRY = {
    "ap-south-1": "India",
    "ap-south-2": "India",
    "ap-southeast-1": "Singapore",
    "ap-southeast-2": "Australia",
    "ap-northeast-1": "Japan",
    "ap-northeast-2": "South Korea",
    "ap-northeast-3": "Japan",
    "ap-northeast-4": "Japan",
    "ap-east-1": "Hong Kong",
    "us-east-1": "USA",
    "us-east-2": "USA",
    "us-west-1": "USA ",
    "us-west-2": "USA",
    "us-west-3": "USA",
    "us-west-4": "USA",
    "us-gov-west-1": "USA",
    "us-gov-east-1": "USA",
    "eu-west-1": "Ireland",
    "eu-west-2": "United Kingdom",
    "eu-west-3": "France",
    "eu-west-4": "Netherlands",
    "eu-central-1": "Germany",
    "eu-central-2": "Hungary",
    "eu-north-1": "Sweden",
    "eu-south-1": "Italy",
    "ca-central-1": "Canada",
    "sa-east-1": "Brazil",
    "sa-east-2": "Brazil",
    "me-south-1": "Middle East (UAE)",
    "me-central-1": "Saudi Arabia",
    "af-south-1": "Africa",
}


def get_country_from_aws_region(region: str) -> str:
    return AWS_REGION_TO_COUNTRY.get(region, "Unknown")
