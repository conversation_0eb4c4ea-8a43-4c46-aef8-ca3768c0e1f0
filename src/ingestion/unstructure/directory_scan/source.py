import os
from uuid import uuid4
from typing import Dict, AsyncGenerator
from src.utils.logger import get_ingestion_logger
from src.common.config import (
    TEMP_DATA_DIR,
    PROFILE_MAX_SIZE_THRESHOLD,
    PROFILE_SUPPORTED_FILE_TYPES,
)
from src.common.constants import ServiceTypes, ServiceProviders, LocalSubservice
from src.utils.helpers import file_exists, create_folder, get_file_extension
from src.modules.repos import DatabaseManager
from src.ingestion.base_source import UnstructureSource
from src.ingestion.data_class import FileMetadata

logger = get_ingestion_logger()


class DirectorySource(UnstructureSource):
    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.DIRECTORY_SCAN.value, service_name)
        self.db = DatabaseManager()
        self.service_provider = ServiceProviders.LOCAL
        self.sub_service = LocalSubservice.DIRECTORY_SCAN
        self.directory_path = None
        self.service: Dict = {}
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"

    async def get_service_details(self) -> Dict:
        logger.info(f"Fetching service details for {self.service_name}")
        self.service = await self.db.get_service_by_service_name(
            self.service_name
        )
        self.directory_path = self.service.get("credentials", {}).get(
            "directory_path"
        )
        if not self.directory_path:
            raise ValueError("Directory path is missing in credentials")
        logger.info(f"Directory path set to {self.directory_path}")
        return self.service

    async def test_connection(self) -> Dict:
        logger.info(f"Testing connection for directory: {self.directory_path}")
        if self.directory_path and os.path.exists(self.directory_path):
            return {"status": True, "message": "Connection successful"}
        return {"status": False, "message": "Directory does not exist"}

    async def scan_directory_infra(self) -> dict:
        logger.info(
            f"Starting infra scan for directory: {self.directory_path}"
        )
        total_size = 0
        total_files = 0
        if not self.directory_path:
            raise ValueError("Directory path is not set")
        for root, _, files in os.walk(self.directory_path):
            for f in files:
                try:
                    fp = os.path.join(root, f)
                    total_size += os.path.getsize(fp)
                    total_files += 1
                except Exception as e:
                    logger.warning(
                        f"Error reading file size for {fp}: {str(e)}"
                    )
        logger.info(
            f"Infrastructure scan completed: {total_files} files, "
            f"{total_size} bytes"
        )
        return {"total_size": total_size, "total_files": total_files}

    async def fetch_file_from_directory(self, root: str, file: str) -> Dict:
        full_path = os.path.join(root, file)
        rel_path = os.path.relpath(full_path, self.directory_path)
        size = os.path.getsize(full_path)
        created = os.path.getctime(full_path)
        modified = os.path.getmtime(full_path)
        file_ext = get_file_extension(file)
        file_uri = f"{self.directory_path}/{rel_path}"

        # Skip logic
        if self.rule_manager.skip_file(file_uri):
            logger.info(f"Skipping file {file} based on skip rules")
            return None

        if size > PROFILE_MAX_SIZE_THRESHOLD:
            logger.info(
                f"Skipping {file}: Size {size} exceeds threshold "
                f"{PROFILE_MAX_SIZE_THRESHOLD}"
            )
            return None

        if file_ext not in PROFILE_SUPPORTED_FILE_TYPES:
            logger.info(f"Skipping {file}: Unsupported file type .{file_ext}")
            return None

        # Create temp directory with service name
        temp_dir = self.local_data_dir
        create_folder(temp_dir)
        safe_file_name = rel_path.replace("/", "_").replace("\\", "_")
        dest_path = f"{temp_dir}/{str(uuid4())}_{safe_file_name}"

        if not file_exists(dest_path):
            try:
                os.makedirs(os.path.dirname(dest_path), exist_ok=True)
                with open(full_path, "rb") as src, \
                        open(dest_path, "wb") as dst:
                    dst.write(src.read())
                logger.info(f"Copied file {file} to {dest_path}")
            except Exception as e:
                logger.error(f"Error copying {file}: {str(e)}")
                return None

        return {
            "name": file,
            "path": rel_path,
            "size": size,
            "createdDateTime": str(created),
            "lastModifiedDateTime": str(modified),
            "file_uri": file_uri,
            "filepath": dest_path,
            "file_type": file_ext,
            "success": True
        }

    async def scan_directory_deep(self) -> AsyncGenerator[FileMetadata, None]:
        logger.info(f"Starting deep scan for directory: {self.directory_path}")
        for root, _, files in os.walk(self.directory_path):
            for f in files:
                file_info = await self.fetch_file_from_directory(root, f)
                if file_info and file_info.get("success"):
                    yield FileMetadata(
                        service_name=self.service_name,
                        service_type=self.service_type,
                        service_provider=self.service_provider,
                        sub_service=self.sub_service,
                        file_key=file_info["name"],
                        file_size=file_info["size"],
                        file_type=file_info["file_type"],
                        file_uri=file_info["file_uri"],
                        local_filepath=file_info["filepath"],
                        details={
                            "directory": self.directory_path,
                            "region": "India",
                            "created": file_info["createdDateTime"],
                            "modified": file_info["lastModifiedDateTime"]
                        }
                    )
        logger.info("Deep scan complete")

    async def infra_scan(self):
        await self.get_service_details()
        return await self.scan_directory_infra()

    async def deep_scan(self) -> AsyncGenerator[FileMetadata, None]:
        await self.get_service_details()
        async for file_metadata in self.scan_directory_deep():
            yield file_metadata
