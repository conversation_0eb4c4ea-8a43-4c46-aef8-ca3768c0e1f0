import os
import logging
from src.utils.exceptions import ClientException, ServerException

logger = logging.getLogger("Unstructure")


async def test_directory_connection(data: dict) -> dict:
    creds = data.get("credentials", {})
    try:
        directory_path = creds.get("directory_path")
        if not directory_path:
            logger.warning("Directory path is missing in credentials")
            raise ClientException(
                "Directory path is required", code=400, excep=None
            )

        logger.info(f"Testing directory access: {directory_path}")

        # Check if directory exists
        if not os.path.exists(directory_path):
            logger.warning(f"Directory does not exist: {directory_path}")
            raise ClientException(
                f"Directory does not exist: {directory_path}", 
                code=404, 
                excep=None
            )

        # Check if it's actually a directory
        if not os.path.isdir(directory_path):
            logger.warning(f"Path exists but is not a directory: {directory_path}")
            raise ClientException(
                f"Path exists but is not a directory: {directory_path}", 
                code=400, 
                excep=None
            )

        # Check if directory is readable
        if not os.access(directory_path, os.R_OK):
            logger.warning(f"Directory is not readable: {directory_path}")
            raise ClientException(
                f"Directory is not readable: {directory_path}", 
                code=403, 
                excep=None
            )

        # Test listing directory contents
        try:
            files = os.listdir(directory_path)
            logger.info(f"Directory test successful. Found {len(files)} items")
        except PermissionError as e:
            logger.warning(f"Permission denied accessing directory: {directory_path}")
            raise ClientException(
                f"Permission denied accessing directory: {directory_path}", 
                code=403, 
                excep=e
            )

        success_message = f"Directory connection successful. Found {len(files)} items"
        return {
            "status": True,
            "message": success_message
        }

    except ClientException:
        # Re-raise client exceptions as they are already properly formatted
        raise

    except Exception as e:
        logger.exception("Unexpected error during directory connection test")
        raise ServerException("Directory test failed", excep=e) 