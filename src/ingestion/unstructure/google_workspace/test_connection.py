import logging
from typing import Dict
import asyncio

from google.oauth2 import service_account
from google.auth.transport.requests import Request
from googleapiclient.discovery import build

logger = logging.getLogger("Unstructure")

SCOPES = [
    "https://www.googleapis.com/auth/admin.directory.user.readonly",
    "https://www.googleapis.com/auth/gmail.readonly",
    "https://www.googleapis.com/auth/drive.readonly",
]

async def verify_google_workspace_credentials(data: Dict) -> Dict:
    def _sync_verification(credentials: Dict) -> tuple[bool, str]:
        try:
            admin_email = credentials.get("admin_email")
            if not admin_email:
                return False, "Missing 'admin_email' for domain-wide delegation"

            credentials_obj = service_account.Credentials.from_service_account_info(
                credentials
            )

            scoped_credentials = credentials_obj.with_scopes(SCOPES).with_subject(admin_email)
            scoped_credentials.refresh(Request())
            logger.info("Token refresh successful — credentials are valid")

            # Admin Directory API
            admin_service = build("admin", "directory_v1", credentials=scoped_credentials)
            admin_service.users().list(customer="my_customer", maxResults=1).execute()
            logger.info(" Admin Directory access verified")

            # Gmail API
            gmail_service = build("gmail", "v1", credentials=scoped_credentials)
            gmail_service.users().getProfile(userId="me").execute()
            logger.info(" Gmail access verified")

            # Google Drive API
            drive_service = build("drive", "v3", credentials=scoped_credentials)
            drive_service.files().list(pageSize=1).execute()
            logger.info(" Drive access verified")

            return True, " All services verified: Admin, Gmail, Drive"

        except Exception as e:
            logger.error(f" Google Workspace verification failed: {e}")
            return False, f"Verification failed: {str(e)}"

    credentials = data.get("credentials", {})
    success, message = await asyncio.to_thread(_sync_verification, credentials)

    return {
        "status": success,
        "message": message
    }
