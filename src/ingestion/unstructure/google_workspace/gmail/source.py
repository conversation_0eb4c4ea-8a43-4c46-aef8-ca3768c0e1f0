from multiprocessing import get_logger
import os
import re
import base64
from uuid import uuid4
from datetime import datetime
from typing import List, Dict, AsyncGenerator
from src.ingestion.data_class import AssetsDetails as AssetDetailsSchema
import asyncio
from fastapi import HTTPException
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from src.common.config import CONCURRENT_LIMIT, PROFILE_MAX_SIZE_THRESHOLD, PROFILE_SUPPORTED_FILE_TYPES, TEMP_DATA_DIR
from src.common.constants import GWSSubServices, ServiceProviders, ServiceTypes
from src.ingestion.base_source import UnstructureSource
from src.ingestion.data_class import FileMetadata
from src.ingestion.unstructure.google_workspace.connection import fetch_all_users_with_details
from src.modules.repos import DatabaseManager
from src.utils.helpers import create_folder
from src.utils.loaders import load_access_controls, load_asset_details
from src.utils.logger import get_ingestion_logger


logger = get_ingestion_logger()

class GmailSource(UnstructureSource):
    def __init__(self, service_name: str):
        super().__init__('gmail', service_name)
        self.db = DatabaseManager()
        self.service = None
        self.google_credentials = None
        self.admin_email = None
        self.user_email = None
        self.service_provider = None
        self.sub_service = None
        self.local_data_dir = os.path.join(TEMP_DATA_DIR, 'gmail')
        self.rule_manager = None  # Set externally if needed


    def _fix_private_key(self, private_key: str) -> str:
        """Fix private key formatting by handling \\n to \n conversion"""
        if not private_key:
            return private_key
            
        # Handle different variations of newline encoding
        fixed_key = private_key.replace('\\n', '\n')
        fixed_key = fixed_key.replace('//n', '\n')
        
        # Ensure proper formatting with header and footer
        if not fixed_key.startswith('-----BEGIN'):
            lines = fixed_key.strip().split('\n')
            if len(lines) > 1:
                key_content = ''.join(line.strip() for line in lines if not line.startswith('-----'))
                fixed_key = f"-----BEGIN PRIVATE KEY-----\n{key_content}\n-----END PRIVATE KEY-----"
        
        # Ensure consistent line breaks
        lines = fixed_key.split('\n')
        formatted_lines = []
        
        for line in lines:
            line = line.strip()
            if line:
                if line.startswith('-----BEGIN') or line.startswith('-----END'):
                    formatted_lines.append(line)
                else:
                    while len(line) > 64:
                        formatted_lines.append(line[:64])
                        line = line[64:]
                    if line:
                        formatted_lines.append(line)
        
        return '\n'.join(formatted_lines)

    def _validate_credentials(self, credentials: dict) -> dict:
        """Validate and fix credentials format"""
        fixed_credentials = credentials.copy()
        
        if 'private_key' in fixed_credentials:
            fixed_credentials['private_key'] = self._fix_private_key(
                fixed_credentials['private_key']
            )
            logger.info("Private key formatting has been corrected")
        
        required_fields = [
            'type', 'project_id', 'private_key_id', 'private_key',
            'client_email', 'client_id', 'auth_uri', 'token_uri'
        ]
        
        missing_fields = [field for field in required_fields if not fixed_credentials.get(field)]
        if missing_fields:
            raise ValueError(f"Missing required credential fields: {missing_fields}")
        
        return fixed_credentials

    async def get_service_details(self) -> Dict:
        """Get Gmail service configuration and credentials"""
        try:
            self.service = await self.db.get_service_by_service_name(self.service_name)
            credentials = self.service.get("credentials", {})

            # ✅ Fix credentials (including private_key)
            self.google_credentials = self._validate_credentials({
                "type": credentials.get("type", "service_account"),
                "project_id": credentials.get("project_id"),
                "private_key_id": credentials.get("private_key_id"),
                "private_key": credentials.get("private_key"),
                "client_email": credentials.get("client_email"),
                "client_id": credentials.get("client_id"),
                "auth_uri": credentials.get("auth_uri", "https://accounts.google.com/o/oauth2/auth"),
                "token_uri": credentials.get("token_uri", "https://oauth2.googleapis.com/token"),
                "auth_provider_x509_cert_url": credentials.get("auth_provider_x509_cert_url", "https://www.googleapis.com/oauth2/v1/certs"),
                "client_x509_cert_url": credentials.get("client_x509_cert_url"),
            })

            # ✅ Admin email is required
            self.admin_email = credentials.get("admin_email")
            if not self.admin_email:
                raise ValueError("admin_email is required in credentials")

            # ✅ Handle single or multiple user emails
            self.user_emails = []
            user_email = credentials.get("user_email")
            if user_email:
                if isinstance(user_email, str):
                    self.user_emails = [user_email]
                elif isinstance(user_email, list):
                    self.user_emails = user_email

            self.service_provider = ServiceProviders.GWS
            self.sub_service = GWSSubServices.GOOGLE_GMAIL
            self.service_steward = self.service.get("data_steward")
            self.rule_manager = getattr(self, 'rule_manager', None)


            if self.user_emails:
                logger.info(f"[Gmail] Specific user emails configured: {self.user_emails}")
            else:
                logger.info("[Gmail] No specific user email - will scan all organization users")

            return self.service

        except Exception as e:
            logger.error(f"[Gmail] Failed to get service details: {str(e)}")
            raise

    async def test_connection(self):
        try:
            SCOPES = [
                "https://www.googleapis.com/auth/admin.directory.user.readonly",
                "https://www.googleapis.com/auth/gmail.readonly",
            ]
            if not self.google_credentials or not all(self.google_credentials.values()):
                raise ValueError("[Gmail] Invalid or incomplete Google credentials")

            credentials = service_account.Credentials.from_service_account_info(
                self.google_credentials, scopes=SCOPES
            )
            delegated_credentials = credentials.with_subject(self.admin_email)
            service = build("gmail", "v1", credentials=delegated_credentials)
            results = service.users().labels().list(userId="me").execute()
            labels = results.get("labels", [])
            logger.info(f"[Gmail] Successfully fetched labels: {[l['name'] for l in labels]}")
            return True, "Authentication successful"
        except HttpError as e:
            logger.error(f"[Gmail] Authentication error: {e}")
            return False, f"Authentication failed: {e}"
        except Exception as e:
            logger.error(f"[Gmail] Unexpected error during test connection: {e}")
            return False, str(e)

    async def fetch_user_id_by_email(self, user_email: str) -> str:
        try:
            SCOPES = ["https://www.googleapis.com/auth/admin.directory.user.readonly"]
            credentials = service_account.Credentials.from_service_account_info(
                self.google_credentials, scopes=SCOPES
            )
            delegated_credentials = credentials.with_subject(self.admin_email)
            service = build("admin", "directory_v1", credentials=delegated_credentials)
            user_result = service.users().get(userKey=user_email).execute()
            logger.info(f"[Gmail] Successfully fetched user ID for {user_email}")
            return user_result.get("id")
        except Exception as e:
            logger.error(f"[Gmail] Error fetching user ID for {user_email}: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Error fetching user ID for {user_email}: {e}"
            )

    async def infra_scan(self):
        try:
            await self.get_service_details()
            if self.user_email:
                users = [{"primaryEmail": self.user_email}]
            else:
                users = await fetch_all_users_with_details(self)
            total_users = len(users)
            logger.info(f"[Gmail] Total users found: {total_users}")
            total_file_count = 0
            total_file_size = 0
            semaphore = asyncio.Semaphore(CONCURRENT_LIMIT)
            tasks = [self._count_user_gmail_attachments(user["primaryEmail"], semaphore) for user in users]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            for res in results:
                if isinstance(res, Exception):
                    continue
                file_count, file_size = res
                total_file_count += file_count
                total_file_size += file_size
            return {
                "total_users": total_users,
                "total_files": total_file_count,
                "total_size": total_file_size,
            }
        except Exception as e:
            logger.error(f"[Gmail] Error in infra_scan: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    async def _count_user_gmail_attachments(self, user_email: str, semaphore):
        async with semaphore:
            SCOPES = ["https://www.googleapis.com/auth/gmail.readonly"]
            credentials = service_account.Credentials.from_service_account_info(
                self.google_credentials, scopes=SCOPES
            )
            delegated_credentials = credentials.with_subject(user_email)
            service = build("gmail", "v1", credentials=delegated_credentials)

            response = service.users().messages().list(userId="me", maxResults=100).execute()
            messages = response.get("messages", [])

            file_count = 0
            file_size = 0

            for msg in messages:
                msg_id = msg["id"]
                msg_detail = service.users().messages().get(userId="me", id=msg_id).execute()
                payload = msg_detail.get("payload", {})
                parts = payload.get("parts", [])
                for part in parts:
                    filename = part.get("filename")
                    if not filename:
                        continue

                    file_ext = filename.split(".")[-1].lower()
                    if file_ext not in PROFILE_SUPPORTED_FILE_TYPES:
                        continue

                    attachment_id = part.get("body", {}).get("attachmentId")
                    if not attachment_id:
                        continue

                    attachment = service.users().messages().attachments().get(
                        userId="me", messageId=msg_id, id=attachment_id
                    ).execute()
                    data = attachment.get("data")
                    if not data:
                        continue

                    file_count += 1
                    file_size += len(base64.urlsafe_b64decode(data.encode("UTF-8")))

            return file_count, file_size

    async def deep_scan(self) -> AsyncGenerator[FileMetadata, None]:
        total_file_count = 0
        total_file_size = 0
        try:
            # await self.infra_scan()
            await self.get_service_details()
            if self.user_emails:
                users = [{"primaryEmail": email} for email in self.user_emails]
            else:
                users = await fetch_all_users_with_details(self)
            logger.info(f"[Gmail] Starting deep scan for {len(users)} users")
            for user in users:
                async for file_info in self._process_user_gmail(user["primaryEmail"]):
                    # --- File checks and skip logic for email text ---
                    file_uri = f"gmail/{file_info['user_email']}/{file_info['date']}/{file_info['subject']}/email.txt"
                    if self.rule_manager and self.rule_manager.skip_file(file_uri):
                        logger.info(f"Skipping email {file_info['subject']} based on skip rules")
                        continue
                    file_size = os.path.getsize(file_info["email_file"])
                    if file_size > PROFILE_MAX_SIZE_THRESHOLD:
                        logger.info(f"Skipping email {file_info['subject']}: Size {file_size} exceeds threshold")
                        continue
                    # --- FileMetadata for email text ---
                    total_file_count += 1
                    total_file_size += file_size
                    yield FileMetadata(
                        service_name=self.service_name,
                        service_type=self.service_type,
                        service_provider=self.service_provider,
                        sub_service=self.sub_service,
                        file_key=file_info["subject"],
                        file_size=file_size,
                        file_type="txt",
                        file_uri=file_uri,
                        local_filepath=file_info["email_file"],
                        details={
                            "created": file_info.get("created"),
                            "modified": file_info.get("modified"),
                            "region": "India",
                            "user_email": file_info["user_email"],
                            "user_name": file_info.get("user_name", "")
                        }
                    )
                    # --- File checks and skip logic for attachments ---
                    for fname in os.listdir(file_info["attachments_dir"]):
                        if fname.endswith(".txt"):
                            continue
                        fpath = os.path.join(file_info["attachments_dir"], fname)
                        file_uri = f"gmail/{file_info['user_email']}/{file_info['date']}/{file_info['subject']}/{fname}"
                        if self.rule_manager and self.rule_manager.skip_file(file_uri):
                            logger.info(f"Skipping attachment {fname} based on skip rules")
                            continue
                        file_size = os.path.getsize(fpath)
                        if file_size > PROFILE_MAX_SIZE_THRESHOLD:
                            logger.info(f"Skipping attachment {fname}: Size {file_size} exceeds threshold")
                            continue
                        file_ext = fname.split(".")[-1].lower()
                        if file_ext not in PROFILE_SUPPORTED_FILE_TYPES:
                            logger.info(f"Skipping attachment {fname}: Unsupported file type .{file_ext}")
                            continue
                        # --- FileMetadata for attachment ---
                        total_file_count += 1
                        total_file_size += file_size
                        yield FileMetadata(
                            service_name=self.service_name,
                            service_type=self.service_type,
                            service_provider=self.service_provider,
                            sub_service=self.sub_service,
                            file_key=fname,
                            file_size=file_size,
                            file_type=file_ext,
                            file_uri=file_uri,
                            local_filepath=fpath,
                            details={
                                "created": file_info.get("created"),
                                "modified": file_info.get("modified"),
                                "region": "India",
                                "user_email": file_info["user_email"],
                                "user_name": file_info.get("user_name", "")
                            }
                        )
        except Exception as e:
            logger.error(f"[Gmail] Error in deep_scan: {e}")
            raise HTTPException(status_code=500, detail=str(e))
        
        finally:
            asset = [
                AssetDetailsSchema(
                    asset_name=f"gmail_{self.service_name}",
                    service_provider=self.service_provider,
                    type="file_storage",
                    category="unstructured",
                    location="Global",
                    owner=self.admin_email,
                    security=True,
                    size=total_file_size,
                    count=total_file_count,
                    access_category="restricted",
                    service_name=str(self.service_name),
                    steward=str(self.service_steward),
                )
            ]
            load_asset_details(asset)
            logger.info(f"Asset created for GMail, loading to system")
            access = []
            # access = [
            #     {
            #         "asset_name": f"gmail_{self.service_name}",
            #         "user_or_role": self.admin_email,
            #         "role": "owner",
            #         "access": "full",
            #     }
            # ]
            for user_email in self.user_emails:
                if user_email != self.admin_email:
                    access.append({"asset_name": f"gmail_{self.service_name}", "user_or_role": user_email, "role": "user", "access": "full"})

            load_access_controls(access)
            logger.info(f"Access control created for GMail, loading to system")
        

    async def _process_user_gmail(self, user_email: str):
        SCOPES = ["https://www.googleapis.com/auth/gmail.readonly"]
        credentials = service_account.Credentials.from_service_account_info(
            self.google_credentials, scopes=SCOPES
        )
        delegated_credentials = credentials.with_subject(user_email)
        service = build("gmail", "v1", credentials=delegated_credentials)
        response = service.users().messages().list(userId="me", maxResults=100).execute()
        messages = response.get("messages", [])
        for msg in messages:
            msg_id = msg["id"]
            msg_detail = service.users().messages().get(userId="me", id=msg_id).execute()
            headers = msg_detail.get("payload", {}).get("headers", [])
            subject = next((h["value"] for h in headers if h["name"].lower() == "subject"), "No Subject")
            internal_date = msg_detail.get("internalDate")
            formatted_date = (
                datetime.utcfromtimestamp(int(internal_date) / 1000).strftime("%Y-%m-%d")
                if internal_date else "unknown-date"
            )
            created = datetime.utcfromtimestamp(int(msg_detail.get("internalDate")) / 1000).isoformat()
            modified = created
            user_name = ""
            # Save email text
            email_text = msg_detail.get("snippet", "")
            safe_subject = re.sub(r"[^\w\-_. ]", "_", subject).strip().replace(" ", "_")
            email_dir = os.path.join(self.local_data_dir, user_email, formatted_date, safe_subject)
            create_folder(email_dir)
            email_file = os.path.join(email_dir, f"{uuid4()}_email.txt")
            with open(email_file, "w", encoding="utf-8") as f:
                f.write(email_text)
            # Save attachments
            parts = msg_detail.get("payload", {}).get("parts", [])
            for part in parts:
                filename = part.get("filename")
                if not filename:
                    continue
                file_ext = filename.split(".")[-1].lower()
                if file_ext not in PROFILE_SUPPORTED_FILE_TYPES:
                    continue
                attachment_id = part.get("body", {}).get("attachmentId")
                if not attachment_id:
                    continue
                attachment = service.users().messages().attachments().get(userId="me", messageId=msg_id, id=attachment_id).execute()
                data = attachment.get("data")
                if not data:
                    continue
                decoded_data = base64.urlsafe_b64decode(data.encode("UTF-8"))
                if len(decoded_data) > PROFILE_MAX_SIZE_THRESHOLD:
                    continue
                file_path = os.path.join(email_dir, f"{uuid4()}_{filename}")
                with open(file_path, "wb") as f:
                    f.write(decoded_data)
            logger.info(f"[Gmail] Processed email and attachments for user: {user_email}")

            yield {
                "user_email": user_email,
                "subject": subject,
                "date": formatted_date,
                "email_file": email_file,
                "attachments_dir": email_dir,
                "created": created,
                "modified": modified,
                "user_name": user_name
            }