from uuid import uuid4
from typing import List, Dict
from fastapi import HTTPException
from google.oauth2 import service_account
from googleapiclient.discovery import build
from google.oauth2.service_account import Credentials
from src.utils.logger import get_ingestion_logger

logger = get_ingestion_logger()



async def fetch_all_users_with_details(self) -> List[Dict]:
        try:
            SCOPES = ["https://www.googleapis.com/auth/admin.directory.user.readonly"]
            credentials = service_account.Credentials.from_service_account_info(
                self.google_credentials, scopes=SCOPES
            )
            admin_credentials = credentials.with_subject(self.admin_email)
            admin_service = build("admin", "directory_v1", credentials=admin_credentials)
            users_result = admin_service.users().list(customer="my_customer", maxResults=500).execute()
            users = users_result.get("users", [])

            user_details = []
            for user in users:
                user_details.append({
                    "id": user.get("id"),
                    "primaryEmail": user.get("primaryEmail"),
                    "name": user.get("name", {}).get("fullName"),
                    "givenName": user.get("name", {}).get("givenName"),
                    "familyName": user.get("name", {}).get("familyName"),
                    "lastLoginTime": user.get("lastLoginTime"),
                })
            return user_details
        except Exception as e:
            logger.error(f"Error fetching users with details: {e}")
            raise HTTPException(status_code=500, detail=f"Error fetching users with details: {e}")


async def fetch_user_id_by_email(self, user_email: str) -> str:
    try:
        SCOPES = ["https://www.googleapis.com/auth/admin.directory.user.readonly"]
        credentials = Credentials.from_service_account_info(
            self.google_credentials, scopes=SCOPES
        )
        admin_credentials = credentials.with_subject(self.admin_email)
        admin_service = build("admin", "directory_v1", credentials=admin_credentials)
        user_result = admin_service.users().get(userKey=user_email).execute()
        return user_result.get("id")
    except Exception as e:
        logger.error(f"Error fetching user ID for {user_email}: {e}")
        raise HTTPException(status_code=500, detail=f"Error fetching user ID for {user_email}: {e}")