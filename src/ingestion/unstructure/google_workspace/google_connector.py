import base64
from datetime import datetime
from io import BytesIO
import json
import logging
from typing import Dict, List
import aiohttp
import asyncio
from google.oauth2 import service_account

from fastapi import HTTPException
from google.oauth2 import service_account
from googleapiclient.http import MediaIoBaseDownload
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from minio import Minio
from minio.error import *
import os

from src.utils.logger import get_ingestion_logger

logger = get_ingestion_logger()

async def fetch_user_id_by_email(user_email: str, google_credentials: dict, admin_email: str) -> str:
    """Fetch user ID by email address"""
    try:
        SCOPES = ["https://www.googleapis.com/auth/admin.directory.user.readonly"]
        
        credentials = service_account.Credentials.from_service_account_info(
            google_credentials, scopes=SCOPES
        )
        admin_credentials = credentials.with_subject(admin_email)
        admin_service = build("admin", "directory_v1", credentials=admin_credentials)
        
        user_result = admin_service.users().get(userKey=user_email).execute()
        user_id = user_result.get("id")
        
        logger.info(f"Successfully fetched user ID for {user_email}: {user_id}")
        return user_id
        
    except HttpError as e:
        if e.resp.status == 404:
            logger.error(f"User not found: {user_email}")
            raise HTTPException(status_code=404, detail=f"User not found: {user_email}")
        else:
            logger.error(f"HTTP error fetching user ID for {user_email}: {e}")
            raise HTTPException(status_code=e.resp.status, detail=str(e))
    except Exception as e:
        logger.error(f"Error fetching user ID for {user_email}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error fetching user ID for {user_email}: {e}",
        )

async def list_org_users(google_credentials: dict, admin_email: str) -> List[Dict]:
    """List all users in the Google Workspace organization"""
    try:
        SCOPES = ["https://www.googleapis.com/auth/admin.directory.user.readonly"]
        
        credentials = service_account.Credentials.from_service_account_info(
            google_credentials, scopes=SCOPES
        )
        admin_credentials = credentials.with_subject(admin_email)
        admin_service = build("admin", "directory_v1", credentials=admin_credentials)
        
        all_users = []
        page_token = None
        
        while True:
            if page_token:
                users_result = admin_service.users().list(
                    customer="my_customer", 
                    maxResults=500,
                    pageToken=page_token
                ).execute()
            else:
                users_result = admin_service.users().list(
                    customer="my_customer", 
                    maxResults=500
                ).execute()
            
            users = users_result.get("users", [])
            
            for user in users:
                user_info = {
                    "primaryEmail": user.get("primaryEmail"),
                    "id": user.get("id"),
                    "name": user.get("name", {}).get("fullName", ""),
                    "givenName": user.get("name", {}).get("givenName", ""),
                    "familyName": user.get("name", {}).get("familyName", ""),
                    "suspended": user.get("suspended", False),
                    "orgUnitPath": user.get("orgUnitPath", "/"),
                    "lastLoginTime": user.get("lastLoginTime")
                }
                all_users.append(user_info)
            
            page_token = users_result.get("nextPageToken")
            if not page_token:
                break
        
        # Filter out suspended users
        active_users = [user for user in all_users if not user.get("suspended", False)]
        
        logger.info(f"Successfully fetched {len(active_users)} active users from organization")
        return active_users
        
    except Exception as e:
        logger.error(f"Error listing org users: {e}")
        return []

async def fetch_all_users_with_details(google_credentials: dict, admin_email: str) -> List[Dict]:
    """Fetch all users with detailed information"""
    try:
        users = await list_org_users(google_credentials, admin_email)
        logger.info(f"Successfully fetched {len(users)} users with details.")
        return users
    except Exception as e:
        logger.error(f"Error fetching users with details: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error fetching users with details: {e}"
        )

async def check_user_drive_permission(google_credentials: dict, admin_email: str, user_email: str) -> bool:
    """Check if a user has Google Drive access permission"""
    try:
        SCOPES = ["https://www.googleapis.com/auth/drive.readonly"]
        
        credentials = service_account.Credentials.from_service_account_info(
            google_credentials, scopes=SCOPES
        )
        delegated_credentials = credentials.with_subject(user_email)
        drive_service = build("drive", "v3", credentials=delegated_credentials)
        
        # Try a simple operation to test access
        try:
            drive_service.files().list(pageSize=1).execute()
            logger.info(f"User {user_email} has Google Drive access")
            return True
        except HttpError as e:
            if e.resp.status in [403, 404]:
                logger.warning(f"User {user_email} does not have Gmail access: {e}")
                return False
            else:
                raise e
                
    except Exception as e:
        logger.error(f"Error checking Gmail permission for {user_email}: {e}")
        return False


async def check_user_gmail_permission(google_credentials: dict, admin_email: str, user_email: str) -> bool:
    """Check if a user has Gmail access permission"""
    try:
        SCOPES = ["https://www.googleapis.com/auth/gmail.readonly"]
        
        credentials = service_account.Credentials.from_service_account_info(
            google_credentials, scopes=SCOPES
        )
        delegated_credentials = credentials.with_subject(user_email)
        gmail_service = build("gmail", "v1", credentials=delegated_credentials)
        
        # Try a simple operation to test access
        try:
            gmail_service.users().getProfile(userId="me").execute()
            logger.info(f"User {user_email} has Gmail access")
            return True
        except HttpError as e:
            # Handle HttpError here
            logger.error(f"HttpError occurred: {e}")
            return False
    except Exception as e:
        logger.error(f"Error checking Gmail permission for {user_email}: {e}")
        return False