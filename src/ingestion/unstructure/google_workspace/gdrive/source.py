import os
import asyncio
import zipfile
from typing import List, Dict, AsyncGenerator
from io import By<PERSON><PERSON>
from uuid import uuid4
from datetime import datetime
import shutil
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.http import MediaIoBaseDownload
from googleapiclient.errors import HttpError
from src.modules.repos import DatabaseManager
from src.ingestion.unstructure.google_workspace.google_connector import (
    fetch_all_users_with_details, 
    fetch_user_id_by_email, 
    list_org_users, 
    check_user_drive_permission
)
from src.utils.loaders import load_access_controls,load_asset_details
from src.ingestion.data_class import AssetsDetails as AssetDetailsSchema
from src.common.constants import (ServiceProviders, GWSSubServices, ServiceTypes)
from src.ingestion.data_class import FileMetadata
from src.utils.helpers import file_exists, create_folder, get_file_extension
from src.common.config import (
    TEMP_DATA_DIR,
    CONCURRENT_LIMIT,
    PROFILE_MAX_SIZE_THRESHOLD,
    PROFILE_SUPPORTED_FILE_TYPES,
)
from src.utils.logger import get_ingestion_logger
from src.ingestion.base_source import UnstructureSource

logger = get_ingestion_logger()

class GoogleDriveSource(UnstructureSource):
    def __init__(self, service_name: str, user_emails=None):
        super().__init__(ServiceTypes.GOOGLE_DRIVE.value, service_name)
        self.db = DatabaseManager()
        self.service = None
        self.google_credentials = None
        self.admin_email = None
        # Accept user_emails as a list or single string
        if user_emails is None:
            self.user_emails = []
        elif isinstance(user_emails, str):
            self.user_emails = [user_emails]
        else:
            self.user_emails = user_emails
        self.service_provider = ServiceProviders.GWS
        self.sub_service = GWSSubServices.GOOGLE_DRIVE
        # User-specific directory structure: temp/googledrive/user_email/
        self.local_data_dir = os.path.join(TEMP_DATA_DIR, 'googledrive')
        self.rule_manager = None
        # Removed processed_files tracking

    def _fix_private_key(self, private_key: str) -> str:
        """Fix private key formatting by handling \\n to \n conversion"""
        if not private_key:
            return private_key
            
        # Handle different variations of newline encoding
        fixed_key = private_key.replace('\\n', '\n')
        fixed_key = fixed_key.replace('//n', '\n')
        
        # Ensure proper formatting with header and footer
        if not fixed_key.startswith('-----BEGIN'):
            lines = fixed_key.strip().split('\n')
            if len(lines) > 1:
                key_content = ''.join(line.strip() for line in lines if not line.startswith('-----'))
                fixed_key = f"-----BEGIN PRIVATE KEY-----\n{key_content}\n-----END PRIVATE KEY-----"
        
        # Ensure consistent line breaks
        lines = fixed_key.split('\n')
        formatted_lines = []
        
        for line in lines:
            line = line.strip()
            if line:
                if line.startswith('-----BEGIN') or line.startswith('-----END'):
                    formatted_lines.append(line)
                else:
                    while len(line) > 64:
                        formatted_lines.append(line[:64])
                        line = line[64:]
                    if line:
                        formatted_lines.append(line)
        
        return '\n'.join(formatted_lines)

    def _validate_credentials(self, credentials: dict) -> dict:
        """Validate and fix credentials format"""
        fixed_credentials = credentials.copy()
        
        if 'private_key' in fixed_credentials:
            fixed_credentials['private_key'] = self._fix_private_key(
                fixed_credentials['private_key']
            )
            logger.info("Private key formatting has been corrected")
        
        required_fields = [
            'type', 'project_id', 'private_key_id', 'private_key',
            'client_email', 'client_id', 'auth_uri', 'token_uri'
        ]
        
        missing_fields = [field for field in required_fields if not fixed_credentials.get(field)]
        if missing_fields:
            raise ValueError(f"Missing required credential fields: {missing_fields}")
        
        return fixed_credentials

    async def get_service_details(self) -> Dict:
        """Get service configuration and credentials"""
        try:
            self.service = await self.db.get_service_by_service_name(self.service_name)
            credentials = self.service.get("credentials", {})
            
            self.google_credentials = self._validate_credentials({
                "type": credentials.get("type", "service_account"),
                "project_id": credentials.get("project_id"),
                "private_key_id": credentials.get("private_key_id"),
                "private_key": credentials.get("private_key"),
                "client_email": credentials.get("client_email"),
                "client_id": credentials.get("client_id"),
                "auth_uri": credentials.get("auth_uri", "https://accounts.google.com/o/oauth2/auth"),
                "token_uri": credentials.get("token_uri", "https://oauth2.googleapis.com/token"),
                "auth_provider_x509_cert_url": credentials.get("auth_provider_x509_cert_url", "https://www.googleapis.com/oauth2/v1/certs"),
                "client_x509_cert_url": credentials.get("client_x509_cert_url"),
            })
            
            self.admin_email = credentials.get("admin_email")
            self.service_steward = self.service.get("data_steward")
            # Remove self.user_email, use self.user_emails
            user_email = credentials.get("user_email")
            if user_email:
                if isinstance(user_email, str):
                    self.user_emails = [user_email]
                elif isinstance(user_email, list):
                    self.user_emails = user_email
            
            if not self.admin_email:
                raise ValueError("admin_email is required in credentials")
            
            # Always ensure these are properly set
            self.service_provider = ServiceProviders.GWS
            self.sub_service = GWSSubServices.GOOGLE_DRIVE
            self.rule_manager = getattr(self, 'rule_manager', None)
            
            logger.info(f"Service details loaded for {self.service_name}")
            logger.info(f"Admin email: {self.admin_email}")
            logger.info(f"Service Provider: {self.service_provider}")
            logger.info(f"Sub Service: {self.sub_service}")
            
            # Logging
            if self.user_emails:
                logger.info(f"Specific user emails configured: {self.user_emails}")
            else:
                logger.info("No specific user email - will scan all organization users")
            
            return self.service
            
        except Exception as e:
            logger.error(f"Failed to get service details: {str(e)}")
            raise

    async def test_connection(self) -> Dict:
        """Test Google Workspace connection with comprehensive validation"""
        try:
            await self.get_service_details()
            
            scopes = [
                "https://www.googleapis.com/auth/admin.directory.user.readonly",
                "https://www.googleapis.com/auth/drive.readonly"
            ]
            
            # Test 1: Create credentials
            try:
                credentials = service_account.Credentials.from_service_account_info(
                    self.google_credentials, scopes=scopes
                )
                logger.info("Service account credentials created successfully")
            except Exception as cred_error:
                logger.error(f"Failed to create credentials: {str(cred_error)}")
                return {
                    "status": False, 
                    "message": f"Invalid credentials format: {str(cred_error)}",
                    "error_type": "CREDENTIAL_FORMAT_ERROR"
                }
            
            # Test 2: Test admin access
            admin_access_success = False
            admin_error = None
            
            try:
                delegated_credentials = credentials.with_subject(self.admin_email)
                admin_service = build("admin", "directory_v1", credentials=delegated_credentials)
                
                result = admin_service.users().list(customer="my_customer", maxResults=1).execute()
                admin_access_success = True
                logger.info("Admin directory access successful")
                
            except Exception as admin_err:
                admin_error = str(admin_err)
                logger.error(f"Admin access failed: {admin_error}")
                
                if "invalid_grant" in admin_error:
                    return {
                        "status": False,
                        "message": "Domain-wide delegation not configured properly",
                        "error_type": "DOMAIN_DELEGATION_ERROR",
                        "details": {
                            "error": admin_error,
                            "fixes": [
                                "Enable domain-wide delegation for the service account",
                                "Ensure admin email has proper Google Workspace admin privileges",
                                "Authorize required OAuth scopes in Google Admin Console",
                                "Verify the admin email is correct and active"
                            ]
                        }
                    }
            
            # Test 3: Test drive access
            drive_access_success = False
            
            if admin_access_success:
                try:
                    drive_service = build("drive", "v3", credentials=delegated_credentials)
                    drive_result = drive_service.files().list(pageSize=1).execute()
                    drive_access_success = True
                    logger.info("Drive access successful")
                    
                except Exception as drive_error:
                    logger.error(f"Drive access failed: {str(drive_error)}")
                    return {
                        "status": False,
                        "message": f"Drive access failed: {str(drive_error)}",
                        "error_type": "DRIVE_ACCESS_ERROR"
                    }
            
            if admin_access_success and drive_access_success:
                logger.info("All connection tests passed successfully")
                return {"status": True, "message": "Connection successful - ready for organization scanning"}
            else:
                return {
                    "status": False,
                    "message": "Connection test incomplete",
                    "error_type": "INCOMPLETE_TEST"
                }
            
        except Exception as e:
            logger.error(f"Connection test failed: {str(e)}")
            return {
                "status": False, 
                "message": f"Unexpected error: {str(e)}",
                "error_type": "UNEXPECTED_ERROR"
            }

    async def infra_scan(self) -> Dict:
        """Scan infrastructure to get user count"""
        try:
            await self.get_service_details()
            
            if self.user_emails:
                users = [{"primaryEmail": email} for email in self.user_emails]
                logger.info(f"Infra scan configured for specific users: {self.user_emails}")
            else:
                try:
                    users = await self._get_all_org_users()
                    logger.info(f"Infra scan found {len(users)} users in organization")
                except Exception as e:
                    logger.error(f"Failed to get organization users: {str(e)}")
                    users = []
            
            return {"user_count": len(users)}
            
        except Exception as e:
            logger.error(f"Infra scan failed: {str(e)}")
            return {"user_count": 0, "error": str(e)}

    async def _get_all_org_users(self) -> List[Dict]:
        """Get all organization users with proper error handling"""
        try:
            logger.info("Attempting to get users via Admin SDK...")
            scopes = [
                "https://www.googleapis.com/auth/admin.directory.user.readonly",
                "https://www.googleapis.com/auth/drive.readonly"
            ]
            
            credentials = service_account.Credentials.from_service_account_info(
                self.google_credentials, scopes=scopes
            )
            
            delegated_credentials = credentials.with_subject(self.admin_email)
            admin_service = build("admin", "directory_v1", credentials=delegated_credentials)
            
            users = []
            page_token = None
            
            while True:
                try:
                    if page_token:
                        result = admin_service.users().list(
                            customer="my_customer",
                            maxResults=500,
                            pageToken=page_token
                        ).execute()
                    else:
                        result = admin_service.users().list(
                            customer="my_customer",
                            maxResults=500
                        ).execute()
                    
                    users.extend(result.get('users', []))
                    page_token = result.get('nextPageToken')
                    
                    if not page_token:
                        break
                        
                except Exception as e:
                    error_msg = str(e)
                    if "invalid_grant" in error_msg:
                        logger.error("Domain-wide delegation not properly configured")
                    else:
                        logger.error(f"Error fetching users page: {error_msg}")
                    break
            
            if users:
                logger.info(f"Successfully retrieved {len(users)} organization users")
                return users
            else:
                logger.warning("Admin SDK returned 0 users")
                
        except Exception as e:
            logger.error(f"Admin SDK method failed: {str(e)}")
        
        # Fallback methods
        try:
            logger.info("Trying fallback method...")
            users = await fetch_all_users_with_details(self.google_credentials, self.admin_email)
            if users:
                logger.info(f"Successfully retrieved {len(users)} users via fallback method")
                return users
        except Exception as fallback_error:
            logger.error(f"Fallback method failed: {str(fallback_error)}")
        
        return []

    def _should_skip_file(self, filename: str, file_size: int, file_uri: str) -> bool:
        """Check if file should be skipped based on type, size and rules"""
        # Get file extension
        file_ext = get_file_extension(filename)

        # Check rule-based filtering
        if self.rule_manager and self.rule_manager.skip_file(file_uri):
            logger.info(f"Skipping file {filename} due to skip rules")
            return True
        
        # Check file type (allow zip for extraction, but filter others)
        if file_ext not in PROFILE_SUPPORTED_FILE_TYPES and file_ext != "zip":
            logger.info(f"Skipping {filename}: Unsupported file type .{file_ext}")
            return True
        
        # Check file size
        if file_size > PROFILE_MAX_SIZE_THRESHOLD:
            logger.info(f"Skipping {filename}: Size {file_size} exceeds threshold {PROFILE_MAX_SIZE_THRESHOLD}")
            return True
        
        
        return False

    async def _download_file_content(self, drive_service, file_id: str, filename: str, mime_type: str) -> tuple:
        """Download file content and return (content, actual_filename, actual_size)"""
        try:
            file_stream = BytesIO()
            
            # Handle Google Apps files by exporting them
            if mime_type.startswith("application/vnd.google-apps."):
                export_format, export_extension = self._get_export_format(mime_type)
                
                if export_format:
                    logger.info(f"Exporting Google Apps file: {filename} (type: {mime_type})")
                    request = drive_service.files().export_media(fileId=file_id, mimeType=export_format)
                    
                    # Update filename with export extension
                    if not filename.lower().endswith(export_extension.lower()):
                        actual_filename = filename + export_extension
                    else:
                        actual_filename = filename
                else:
                    logger.info(f"Unsupported Google Apps file type for export: {filename} (type: {mime_type})")
                    return None, None, 0
            else:
                # Regular file download
                request = drive_service.files().get_media(fileId=file_id)
                actual_filename = filename
            
            # Download the file
            downloader = MediaIoBaseDownload(file_stream, request)
            done = False
            while not done:
                status, done = downloader.next_chunk()
            
            file_stream.seek(0)
            content = file_stream.getvalue()
            actual_size = len(content)
            
            # For exported files, check if the exported file type is supported
            if mime_type.startswith("application/vnd.google-apps."):
                exported_ext = get_file_extension(actual_filename)
                if exported_ext not in PROFILE_SUPPORTED_FILE_TYPES:
                    logger.info(f"Skipping exported file {actual_filename}: Unsupported exported type .{exported_ext}")
                    return None, None, 0
                
                # Check exported file size
                if actual_size > PROFILE_MAX_SIZE_THRESHOLD:
                    logger.info(f"Skipping exported file {actual_filename}: Size {actual_size} exceeds threshold")
                    return None, None, 0
            
            return content, actual_filename, actual_size
            
        except HttpError as e:
            if e.resp.status == 403:
                logger.warning(f"Permission denied for file: {filename}")
            elif e.resp.status == 404:
                logger.warning(f"File not found: {filename}")
            else:
                logger.error(f"HTTP error downloading {filename}: {str(e)}")
            return None, None, 0
        except Exception as e:
            logger.error(f"Error downloading {filename}: {str(e)}")
            return None, None, 0

    def _get_export_format(self, mime_type: str) -> tuple:
        """Get export format and extension for Google Apps files"""
        export_formats = {
            "application/vnd.google-apps.document": (
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document", 
                ".docx"
            ),
            "application/vnd.google-apps.spreadsheet": (
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 
                ".xlsx"
            ),
            "application/vnd.google-apps.presentation": (
                "application/vnd.openxmlformats-officedocument.presentationml.presentation", 
                ".pptx"
            ),
            "application/vnd.google-apps.drawing": (
                "image/png", 
                ".png"
            )
        }
        return export_formats.get(mime_type, (None, None))

    # async def deep_scan(self) -> AsyncGenerator[FileMetadata, None]:
    #     """Deep scan all user drives with enhanced error handling and proper logging like OneDrive"""
    #     try:
    #         await self.get_service_details()
            
    #         # Determine users to scan
    #         if self.user_emails:
    #             users = [{"primaryEmail": email} for email in self.user_emails]
    #             logger.info(f"Deep scan configured for specific users: {self.user_emails}")
    #         else:
    #             users = await self._get_all_org_users()
    #             logger.info(f"Deep scan attempting to process {len(users)} organization users")
            
    #         if not users:
    #             logger.error("No users found to scan")
    #             return
            
    #         # Create service account credentials
    #         scopes = ["https://www.googleapis.com/auth/drive.readonly"]
    #         credentials = service_account.Credentials.from_service_account_info(
    #             self.google_credentials, scopes=scopes
    #         )
            
    #         successful_scans = 0
    #         failed_scans = 0
    #         total_files_processed = 0
            
    #         # Create main temp directory
    #         create_folder(self.local_data_dir)
            
    #         # Process each user's drive
    #         for user in users:
    #             user_email = user.get("primaryEmail")
    #             if not user_email:
    #                 logger.warning(f"Skipping user without email: {user}")
    #                 failed_scans += 1
    #                 continue
                
    #             try:
    #                 # Check drive access
    #                 has_drive_access = await self._check_user_drive_access(user_email)
    #                 if not has_drive_access:
    #                     logger.info(f"User {user_email} does not have Google Drive access. Skipping.")
    #                     continue
                    
    #                 # Create user-specific directory: temp/googledrive/user_email/
    #                 user_folder = os.path.join(self.local_data_dir, self._sanitize_filename(user_email))
    #                 create_folder(user_folder)
    #                 logger.info(f"Created user folder: {user_folder}")
                    
    #                 # Create delegated credentials for this user
    #                 delegated_credentials = credentials.with_subject(user_email)
    #                 drive_service = build("drive", "v3", credentials=delegated_credentials)
                    
    #                 logger.info(f"Starting Google Drive deep scan for user: {user_email}")
    #                 user_file_count = 0
                    
    #                 # Process all files in user's drive
    #                 async for file_metadata in self._scan_user_drive_recursive(
    #                     drive_service, user_email, user.get("name", {}).get("fullName", ""), user_folder
    #                 ):
    #                     user_file_count += 1
    #                     total_files_processed += 1
                        
    #                     # Log file processing similar to OneDrive
    #                     logger.info(f"Created file audit log for {file_metadata.file_uri}")
    #                     yield file_metadata
                    
    #                 if user_file_count > 0:
    #                     logger.info(f"Successfully processed {user_file_count} files for user {user_email}")
    #                     successful_scans += 1
    #                 else:
    #                     logger.info(f"No files found for user {user_email}")
                        
    #             except Exception as user_error:
    #                 failed_scans += 1
    #                 error_msg = str(user_error)
    #                 if "invalid_grant" in error_msg or "unauthorized" in error_msg.lower():
    #                     logger.error(f"Authorization failed for user {user_email}: {error_msg}")
    #                 else:
    #                     logger.error(f"Error processing user {user_email}: {error_msg}")
    #                 continue
            
    #         logger.info(f"Deep scan completed: {successful_scans} successful users, {failed_scans} failed users, {total_files_processed} total files processed")
            
    #     except Exception as e:
    #         logger.error(f"Deep scan failed: {str(e)}")
    #         raise

    async def deep_scan(self) -> AsyncGenerator[FileMetadata, None]:
        total_file_count = 0
        total_file_size = 0
        try:
            await self.get_service_details()
            if self.user_emails:
                users = [{"primaryEmail": email} for email in self.user_emails]
                logger.info(f"Deep scan configured for specific users: {self.user_emails}")
            else:
                users = await self._get_all_org_users()
                logger.info(f"[GDrive] Deep scan attempting to process {len(users)} organization users")

            if not users:
                logger.error("No users found to scan in infra_scan")
                return

            # Create service account credentials
            scopes = ["https://www.googleapis.com/auth/drive.readonly"]
            credentials = service_account.Credentials.from_service_account_info(
                self.google_credentials, scopes=scopes
            )

            successful_scans = 0
            failed_scans = 0
            # Create main temp directory
            create_folder(self.local_data_dir)

            # Process each user's drive
            for user in users:
                # Depending on how infra_scan returns, normalize
                user_email = user.get("primaryEmail")
                if not user_email:
                    logger.warning(f"Skipping user without email: {user}")
                    failed_scans += 1
                    continue

                try:
                    # Check drive access
                    has_drive_access = await self._check_user_drive_access(user_email)
                    if not has_drive_access:
                        logger.info(f"User {user_email} does not have Google Drive access. Skipping.")
                        continue

                    # Create user-specific directory: temp/googledrive/user_email/
                    user_folder = os.path.join(self.local_data_dir, self._sanitize_filename(user_email))
                    create_folder(user_folder)

                    # Create delegated credentials for this user
                    delegated_credentials = credentials.with_subject(user_email)
                    drive_service = build("drive", "v3", credentials=delegated_credentials)

                    logger.info(f"Starting Google Drive deep scan for user: {user_email}")
                    user_file_count = 0

                    # Process all files in user's drive
                    async for file_metadata in self._scan_user_drive_recursive(
                        drive_service, user_email, user.get("name", {}).get("fullName", ""), user_folder
                    ):
                        user_file_count += 1
                        total_file_count += 1
                        total_file_size += file_metadata.file_size or 0
                        yield file_metadata

                    if user_file_count > 0:
                        logger.info(f"Successfully processed {user_file_count} files for user {user_email}")
                        successful_scans += 1
                    else:
                        logger.info(f"No files found for user {user_email}")

                except Exception as user_error:
                    failed_scans += 1
                    error_msg = str(user_error)
                    if "invalid_grant" in error_msg or "unauthorized" in error_msg.lower():
                        logger.error(f"Authorization failed for user {user_email}: {error_msg}")
                    else:
                        logger.error(f"Error processing user {user_email}: {error_msg}")
                    continue

            logger.info(
                f"[GDrive] Deep scan completed: {successful_scans} successful users, "
                f"{failed_scans} failed users, {total_file_count} total files"
            )

        except Exception as e:
            logger.error(f"[GDrive] Deep scan failed: {str(e)}", exc_info=True)
            raise

        finally:
            asset = [
                AssetDetailsSchema(
                    asset_name=f"gdrive_{self.service_name}",
                    service_provider=self.service_provider,
                    type="file_storage",
                    category="unstructured",
                    location="Global",
                    owner=self.admin_email,
                    security=True,
                    size=total_file_size,
                    count=total_file_count,
                    access_category="restricted",
                    service_name=str(self.service_name),
                    steward=str(self.service_steward),
                )
            ]
            load_asset_details(asset)
            logger.info(f"Asset created for GDrive, loading to system")

            # --- Create AccessControl entry for GDrive ---
            # access = [{
            #     "asset_name": f"gdrive_{self.service_name}",
            #     "user_or_role": self.admin_email,
            #     "role": "owner",
            #     "access": "full"
            # }]
            access = []
            for user_email in self.user_emails:
                if user_email != self.admin_email:
                    access.append({"asset_name": f"gdrive_{self.service_name}", "user_or_role": user_email, "role": "user", "access": "full"})

            load_access_controls(access)
            logger.info(f"-Access control created for GDrive, loading to system")


    async def _scan_user_drive_recursive(self, drive_service, user_email: str, user_name: str = "", user_folder: str = "") -> AsyncGenerator[FileMetadata, None]:
        """Recursively scan user's Google Drive with proper file filtering and processing"""
        
        async def process_folder_async(folder_id: str, folder_path: str = ""):
            query = f"'{folder_id}' in parents and trashed=false"
            fields = "nextPageToken,files(id,name,mimeType,size,createdTime,modifiedTime,parents)"
            page_token = None
            while True:
                try:
                    if page_token:
                        response = drive_service.files().list(
                            q=query, 
                            fields=fields, 
                            pageToken=page_token,
                            pageSize=1000
                        ).execute()
                    else:
                        response = drive_service.files().list(
                            q=query, 
                            fields=fields,
                            pageSize=1000
                        ).execute()
                    files = response.get("files", [])
                    for file in files:
                        file_id = file["id"]
                        filename = file["name"]
                        mime_type = file["mimeType"]
                        file_size = int(file.get("size", 0)) if file.get("size") else 0
                        created = file.get("createdTime")
                        modified = file.get("modifiedTime")
                        
                        if mime_type == "application/vnd.google-apps.folder":
                            new_folder_path = f"{folder_path}/{filename}" if folder_path else filename
                            async for fm in process_folder_async(file_id, new_folder_path):
                                yield fm
                        else:
                            # Build file URI first
                            file_uri = self._build_file_uri(user_email, filename, folder_path)
                            
                            # Check if file should be skipped (type/size/rules)
                            if self._should_skip_file(filename, file_size, file_uri):
                                continue
                            
                            # CHECK IF ALREADY PROCESSED - BEFORE DOWNLOADING
                            if hasattr(self, 'is_file_processed') and await self.is_file_processed(file_uri):
                                logger.info(f"Skipping already processed file: {file_uri}")
                                continue
                            
                            # For Google Apps files, check exported format before downloading
                            actual_filename = filename
                            if mime_type.startswith("application/vnd.google-apps."):
                                export_format, export_extension = self._get_export_format(mime_type)
                                if export_format:
                                    # Update filename for exported file
                                    if not filename.lower().endswith(export_extension.lower()):
                                        actual_filename = filename + export_extension
                                    
                                    # Build URI for exported file and check if processed
                                    exported_file_uri = self._build_file_uri(user_email, actual_filename, folder_path)
                                    if hasattr(self, 'is_file_processed') and await self.is_file_processed(exported_file_uri):
                                        logger.info(f"Skipping already processed exported file: {exported_file_uri}")
                                        continue
                                else:
                                    logger.info(f"Unsupported Google Apps file type for export: {filename} (type: {mime_type})")
                                    continue
                            
                            # Now download the file (only if not already processed)
                            content, actual_filename, actual_size = await self._download_file_content(drive_service, file_id, filename, mime_type)
                            if content is None:
                                continue
                            
                            file_ext = get_file_extension(actual_filename)
                            
                            if file_ext == "zip":
                                extracted_files = self._extract_zip_content(
                                    content, actual_filename, folder_path, user_email, user_name, 
                                    created, modified, user_folder
                                )
                                for extracted_file_info in extracted_files:
                                    # Check if extracted file is already processed
                                    if hasattr(self, 'is_file_processed') and await self.is_file_processed(extracted_file_info["file_uri"]):
                                        logger.info(f"Skipping already processed extracted file: {extracted_file_info['file_uri']}")
                                        continue
                                    
                                    yield FileMetadata(
                                        service_name=self.service_name,
                                        service_type=self.service_type,
                                        service_provider=self.service_provider,
                                        sub_service=self.sub_service,
                                        file_key=extracted_file_info["filename"],
                                        file_size=extracted_file_info["size"],
                                        file_type=get_file_extension(extracted_file_info["filename"]),
                                        file_uri=extracted_file_info["file_uri"],
                                        local_filepath=extracted_file_info["file_path"],
                                        details={
                                            "created": extracted_file_info.get("created"),
                                            "modified": extracted_file_info.get("modified"),
                                            "region": "India",
                                            "user_email": extracted_file_info["user_email"],
                                            "user_name": extracted_file_info.get("user_name", ""),
                                            "drive_file_id": extracted_file_info.get("drive_file_id", ""),
                                            "mime_type": extracted_file_info.get("mime_type", ""),
                                            "folder_path": extracted_file_info.get("folder_path", ""),
                                            "full_path": extracted_file_info.get("full_path", ""),
                                            "extracted_from": extracted_file_info.get("extracted_from", "")
                                        }
                                    )
                            else:
                                safe_filename = self._sanitize_filename(actual_filename)
                                file_path = os.path.join(user_folder, f"{str(uuid4())}_{safe_filename}")
                                with open(file_path, "wb") as f:
                                    f.write(content)
                                
                                full_path = f"{folder_path}/{actual_filename}" if folder_path else actual_filename
                                # Update file_uri for exported files
                                final_file_uri = self._build_file_uri(user_email, actual_filename, folder_path)
                                
                                file_info = {
                                    "user_email": user_email,
                                    "user_name": user_name,
                                    "file_path": file_path,
                                    "filename": safe_filename,
                                    "size": actual_size,
                                    "created": self._format_timestamp(created),
                                    "modified": self._format_timestamp(modified),
                                    "drive_file_id": file_id,
                                    "mime_type": mime_type,
                                    "folder_path": folder_path,
                                    "full_path": full_path,
                                    "file_uri": final_file_uri
                                }
                                
                                yield FileMetadata(
                                    service_name=self.service_name,
                                    service_type=self.service_type,
                                    service_provider=self.service_provider,
                                    sub_service=self.sub_service,
                                    file_key=file_info["filename"],
                                    file_size=file_info["size"],
                                    file_type=get_file_extension(file_info["filename"]),
                                    file_uri=file_info["file_uri"],
                                    local_filepath=file_info["file_path"],
                                    details={
                                        "created": file_info.get("created"),
                                        "modified": file_info.get("modified"),
                                        "region": "India",
                                        "user_email": file_info["user_email"],
                                        "user_name": file_info.get("user_name", ""),
                                        "drive_file_id": file_info.get("drive_file_id", ""),
                                        "mime_type": file_info.get("mime_type", ""),
                                        "folder_path": file_info.get("folder_path", ""),
                                        "full_path": file_info.get("full_path", "")
                                    }
                                )
                                logger.info(f"Downloaded: {full_path} ({actual_size} bytes) for {user_email}")
                    
                    page_token = response.get('nextPageToken')
                    if not page_token:
                        break
                except Exception as e:
                    logger.error(f"Error fetching files from folder: {str(e)}")
                    break
        
        # No return needed for async generator
        try:
            async for file_metadata in process_folder_async('root', ''):
                yield file_metadata
        except Exception as e:
            logger.error(f"Error in recursive drive scan for {user_email}: {str(e)}")

    def _extract_zip_content(self, zip_content: bytes, zip_filename: str, folder_path: str, 
                           user_email: str, user_name: str, created: str, modified: str, 
                           user_folder: str) -> List[Dict]:
        """Extract ZIP content and return list of extracted file info (don't save ZIP itself)"""
        extracted_files = []
        
        try:
            # Create temporary directory for extraction within user folder
            temp_extract_dir = os.path.join(user_folder, f"temp_extract_{uuid4().hex[:8]}")
            os.makedirs(temp_extract_dir, exist_ok=True)
            
            # Write ZIP to temp location for extraction
            temp_zip_path = os.path.join(temp_extract_dir, "temp.zip")
            with open(temp_zip_path, "wb") as f:
                f.write(zip_content)
            
            # Extract ZIP contents
            with zipfile.ZipFile(temp_zip_path, 'r') as zip_ref:
                zip_ref.extractall(temp_extract_dir)
            
            # Process extracted files
            for root, dirs, files in os.walk(temp_extract_dir):
                for extracted_filename in files:
                    if extracted_filename == "temp.zip":  # Skip the temp ZIP file itself
                        continue
                    
                    extracted_path = os.path.join(root, extracted_filename)
                    
                    if not os.path.isfile(extracted_path):
                        continue
                    
                    extracted_size = os.path.getsize(extracted_path)
                    extracted_ext = get_file_extension(extracted_filename)
                    
                    # Check if extracted file meets criteria
                    if extracted_ext not in PROFILE_SUPPORTED_FILE_TYPES:
                        logger.info(f"Skipping extracted file {extracted_filename}: Unsupported type .{extracted_ext}")
                        continue
                    
                    if extracted_size > PROFILE_MAX_SIZE_THRESHOLD:
                        logger.info(f"Skipping extracted file {extracted_filename}: Size {extracted_size} exceeds threshold")
                        continue
                    
                    # Create final file path in user folder
                    safe_filename = self._sanitize_filename(extracted_filename)
                    final_file_path = os.path.join(user_folder, f"{str(uuid4())}_{safe_filename}")
                    
                    # Copy extracted file to final location
                    with open(extracted_path, "rb") as src, open(final_file_path, "wb") as dst:
                        dst.write(src.read())
                    
                    # Build proper URI for extracted file
                    rel_path = os.path.relpath(extracted_path, temp_extract_dir)
                    if rel_path.startswith("temp.zip"):
                        rel_path = rel_path.replace("temp.zip/", "", 1).replace("temp.zip\\", "", 1)
                    
                    # Build URI: user_email/folder_path/zip_file_name_extracted/relative_path
                    uri_parts = [user_email]
                    if folder_path:
                        uri_parts.append(folder_path)
                    uri_parts.append(f"{zip_filename}_extracted")
                    if "/" in rel_path or "\\" in rel_path:
                        rel_path_parts = rel_path.replace("\\", "/").split("/")
                        uri_parts.extend(rel_path_parts)
                    else:
                        uri_parts.append(rel_path)
                    
                    extracted_uri = "/".join(uri_parts)
                    
                    # Build full path for logging
                    full_path = f"{folder_path}/{zip_filename}_extracted/{rel_path}" if folder_path else f"{zip_filename}_extracted/{rel_path}"
                    
                    extracted_file_info = {
                        "user_email": user_email,
                        "user_name": user_name,
                        "file_path": final_file_path,
                        "filename": safe_filename,
                        "size": extracted_size,
                        "created": self._format_timestamp(created),
                        "modified": self._format_timestamp(modified),
                        "drive_file_id": "",
                        "mime_type": f"application/{extracted_ext}",
                        "folder_path": folder_path,
                        "full_path": full_path,
                        "file_uri": extracted_uri,
                        "extracted_from": zip_filename
                    }
                    
                    extracted_files.append(extracted_file_info)
                    logger.info(f"Extracted and processed: {full_path} ({extracted_size} bytes) from {zip_filename}")
            
            # Clean up temp extraction directory
            shutil.rmtree(temp_extract_dir, ignore_errors=True)
            
        except Exception as e:
            logger.error(f"Error extracting ZIP file {zip_filename}: {str(e)}")
        
        return extracted_files

    def _build_file_uri(self, user_email: str, filename: str, folder_path: str = "") -> str:
        """Build unique file URI: googledrive/user_email/[folder_path]/[filename]"""
        uri_parts = ["googledrive", user_email]
        if folder_path and folder_path.strip():
            folder_parts = [part.strip() for part in folder_path.split("/") if part.strip()]
            uri_parts.extend(folder_parts)
        uri_parts.append(filename)
        return "/".join(uri_parts)

    async def _check_user_drive_access(self, user_email: str) -> bool:
        """Check if user has Google Drive access"""
        try:
            return await check_user_drive_permission(self.google_credentials, self.admin_email, user_email)
        except Exception as e:
            logger.warning(f"Could not verify drive access for {user_email}: {str(e)}. Assuming access exists.")
            return True

    def _format_timestamp(self, timestamp_str):
        """Format Google API timestamp to standard format"""
        if not timestamp_str:
            return None
        
        try:
            # Parse Google's ISO format timestamp
            dt = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
            return dt.strftime("%Y-%m-%d %H:%M:%S UTC")
        except Exception:
            return timestamp_str

    def _sanitize_filename(self, filename):
        """Sanitize filename for safe filesystem storage"""
        import re
        # Remove or replace invalid characters
        sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # Remove any leading/trailing dots and spaces
        sanitized = sanitized.strip('. ')
        # Limit length
        if len(sanitized) > 255:
            name, ext = os.path.splitext(sanitized)
            sanitized = name[:255-len(ext)] + ext
        return sanitized