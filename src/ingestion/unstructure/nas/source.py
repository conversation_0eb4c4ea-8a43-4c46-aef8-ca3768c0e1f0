import os
import asyncio
import uuid
import zipfile
import tempfile
import shutil
from typing import AsyncGenerator, List, Dict, Any
import struct
from smbprotocol.connection import Connection
from smbprotocol.session import Session
from smbprotocol.tree import TreeConnect
from smbprotocol.open import (
    Open, CreateDisposition, CreateOptions, FileAttributes,
    ShareAccess, ImpersonationLevel
)
from smbprotocol.file_info import FileInformationClass
from collections import defaultdict
from src.utils.loaders import load_access_controls,load_asset_details
from src.ingestion.data_class import AssetsDetails as AssetDetailsSchema
from src.modules.repos import DatabaseManager
from src.common.constants import ServiceTypes, ServiceProviders, LocalSubservice
from src.ingestion.base_source import UnstructureSource
from src.ingestion.data_class import FileMetadata
from src.utils.logger import get_ingestion_logger
from src.utils.helpers import create_folder, get_file_extension
from src.common.config import (
    TEMP_DATA_DIR,
    CONCURRENT_LIMIT,
    PROFILE_SUPPORTED_FILE_TYPES,
    PROFILE_MAX_SIZE_THRESHOLD
)

logger = get_ingestion_logger()

FILE_READ_DATA = 0x00000001
FILE_LIST_DIRECTORY = 0x00000001
FILE_READ_ATTRIBUTES = 0x00000080


class NASSource(UnstructureSource):
    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.NAS.value, service_name)
        self.db = DatabaseManager()
        self.service_provider = ServiceProviders.LOCAL
        self.sub_service = LocalSubservice.NAS
        self.conn = None
        self.session = None
        self.tree = None
        self.root_path = ""
        self.share_name = ""
        self.username = None
        self.password = None
        self.hostname = None
        self.port = 445
        self.processed_files = set()  # Track processed files to avoid duplicates
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"
        self.max_read_size = 655798798736  # SMB default max read size
        self.scan_stats = {
            "total_files_processed": 0,
            "total_directories_scanned": 0,
            "zip_files_processed": 0,
            "files_extracted_from_zip": 0,
            "files_skipped": 0,
            "errors_encountered": 0
        }

    async def get_service_details(self):
        """Get service configuration from database"""
        self.service = await self.db.get_service_by_service_name(self.service_name)
        credentials = self.service.get("credentials", {})
        self.hostname = credentials.get("hostname")
        self.port = credentials.get("port", 445)
        self.share_name = credentials.get("share_name")
        self.root_path = credentials.get("root_path", "")
        self.username = credentials.get("username")
        self.password = credentials.get("password")
        self.service_steward = self.service.get("data_steward")

        if not all([self.hostname, self.share_name, self.username, self.password]):
            raise Exception("Missing required SMB credentials: hostname, share_name, username, password")

        self._connect_smb()
        return self.service

    def _connect_smb(self):
        """Establish SMB connection to the NAS"""
        try:
            self.conn = Connection(uuid.uuid4(), self.hostname, self.port)
            self.conn.connect()
            self.session = Session(self.conn, self.username, self.password)
            self.session.connect()
            self.tree = TreeConnect(self.session, fr"\\{self.hostname}\{self.share_name}")
            self.tree.connect()
            
            # Get the negotiated read size to avoid read errors
            if hasattr(self.session, 'max_read_size'):
                self.max_read_size = min(self.session.max_read_size, 65536)
            elif hasattr(self.conn, 'max_read_size'):
                self.max_read_size = min(self.conn.max_read_size, 65536)
            
            logger.info(f"Connected to SMB share: {self.share_name} on {self.hostname}")
            logger.info(f"Max read size: {self.max_read_size}")
        except Exception as e:
            logger.error(f"SMB connection failed: {e}")
            raise

    def _normalize_path(self, path: str) -> str:
        """Normalize path by removing leading/trailing slashes and backslashes"""
        if not path:
            return ""
        return path.strip("/\\").replace("\\", "/")

    def _construct_smb_path(self, relative_path: str) -> str:
        """Construct proper SMB path"""
        if not relative_path:
            return self._normalize_path(self.root_path) if self.root_path else ""
        
        # Normalize the path
        normalized = self._normalize_path(relative_path)
        
        # If we have a root_path, prepend it
        if self.root_path:
            root_normalized = self._normalize_path(self.root_path)
            if normalized:
                return f"{root_normalized}/{normalized}"
            else:
                return root_normalized
        
        return normalized

    def _generate_file_id(self, file_path: str) -> str:
        """Generate unique file ID to track processed files"""
        return f"{self.service_name}:{self.share_name}:{file_path}"

    def _construct_file_uri(self, relative_path: str, is_zip_content: bool = False, zip_path: str = "") -> str:
        """Construct proper file URI for tracking and identification"""
        base_uri = f"/{self.service_name}/{self.share_name}"
        
        if is_zip_content and zip_path:
            # For files inside ZIP: /service_name/share_name/path/to/file.zip/internal/path/file.ext
            return f"{base_uri}/{zip_path}/{relative_path}"
        else:
            # For regular files: /service_name/share_name/path/to/file.ext
            return f"{base_uri}/{relative_path}"

    def _smb_listdir(self, path: str):
        """List directory contents via SMB"""
        try:
            smb_path = self._normalize_path(path)
            logger.debug(f"Listing SMB directory: '{smb_path if smb_path else 'ROOT'}'")

            directory = Open(self.tree, smb_path)
            directory.create(
                desired_access=FILE_LIST_DIRECTORY | FILE_READ_ATTRIBUTES,
                file_attributes=FileAttributes.FILE_ATTRIBUTE_DIRECTORY,
                share_access=ShareAccess.FILE_SHARE_READ,
                create_disposition=CreateDisposition.FILE_OPEN,
                create_options=CreateOptions.FILE_DIRECTORY_FILE,
                impersonation_level=ImpersonationLevel.Impersonation
            )

            entries = []
            for entry in directory.query_directory("*", FileInformationClass.FILE_DIRECTORY_INFORMATION):
                try:
                    # Handle different smbprotocol versions
                    if hasattr(entry, 'filename'):
                        name = entry.filename
                    elif hasattr(entry, 'file_name_information'):
                        name = entry.file_name_information
                    elif hasattr(entry, 'get_value'):
                        name = entry.get_value()
                    else:
                        # Fallback: try to access raw data and parse manually
                        raw_data = entry.pack() if hasattr(entry, 'pack') else bytes(entry)
                        if len(raw_data) >= 64:
                            name_length = struct.unpack('<I', raw_data[60:64])[0]
                            if name_length > 0 and len(raw_data) >= 64 + name_length:
                                name_bytes = raw_data[64:64 + name_length]
                                name = name_bytes.decode('utf-16le', errors='ignore').rstrip('\x00')
                            else:
                                continue
                        else:
                            continue
                    
                    # Handle different types of name values
                    if isinstance(name, bytes):
                        name = name.decode('utf-16le', errors='ignore').rstrip('\x00')
                    elif isinstance(name, str):
                        name = name.rstrip('\x00')
                    else:
                        name = str(name).rstrip('\x00')
                    
                    # Skip current and parent directory entries
                    if name and name not in (".", ".."):
                        entries.append(name)
                        
                except Exception as decode_err:
                    logger.warning(f"Failed to parse entry in '{smb_path}': {decode_err}")
                    self.scan_stats["errors_encountered"] += 1
                    # Alternative parsing method
                    try:
                        entry_data = str(entry)
                        if "file_name" in entry_data:
                            import re
                            hex_pattern = r'file_name = ([0-9A-F ]+)'
                            match = re.search(hex_pattern, entry_data)
                            if match:
                                hex_string = match.group(1).replace(' ', '')
                                if len(hex_string) % 2 == 0:
                                    name_bytes = bytes.fromhex(hex_string)
                                    name = name_bytes.decode('utf-16le', errors='ignore').rstrip('\x00')
                                    if name and name not in (".", ".."):
                                        entries.append(name)
                    except:
                        pass
                    continue

            directory.close()
            logger.debug(f"Found {len(entries)} entries in: {smb_path if smb_path else 'ROOT'}")
            return entries

        except Exception as e:
            logger.warning(f"SMB listdir failed for '{path}': {e}")
            self.scan_stats["errors_encountered"] += 1
            return []

    def _smb_stat(self, path: str):
        """Get file/directory statistics via SMB"""
        try:
            smb_path = self._normalize_path(path)
            try:
                # Try to open as directory first
                fo = Open(self.tree, smb_path)
                fo.create(
                    desired_access=FILE_READ_ATTRIBUTES,
                    file_attributes=FileAttributes.FILE_ATTRIBUTE_DIRECTORY,
                    share_access=ShareAccess.FILE_SHARE_READ,
                    create_disposition=CreateDisposition.FILE_OPEN,
                    create_options=CreateOptions.FILE_DIRECTORY_FILE,
                    impersonation_level=ImpersonationLevel.Impersonation
                )
                is_dir = True
            except:
                # If directory open fails, try as file
                fo = Open(self.tree, smb_path)
                fo.create(
                    desired_access=FILE_READ_ATTRIBUTES,
                    file_attributes=FileAttributes.FILE_ATTRIBUTE_NORMAL,
                    share_access=ShareAccess.FILE_SHARE_READ,
                    create_disposition=CreateDisposition.FILE_OPEN,
                    create_options=CreateOptions.FILE_NON_DIRECTORY_FILE,
                    impersonation_level=ImpersonationLevel.Impersonation
                )
                is_dir = False

            size = fo.end_of_file
            fo.close()
            return {"is_dir": is_dir, "size": size}
        except Exception as e:
            logger.warning(f"SMB stat failed for '{path}': {e}")
            return None

    def is_zip_file(self, filename: str) -> bool:
        """Check if file is a ZIP file based on extension"""
        return filename.lower().endswith(('.zip', '.zipx', '.jar', '.war', '.ear'))

    def is_supported_file_type(self, filename: str) -> bool:
        """Check if file type is supported for processing"""
        file_ext = get_file_extension(filename)
        return file_ext in PROFILE_SUPPORTED_FILE_TYPES or self.is_zip_file(filename)

    def process_zip_file(self, zip_path: str, extract_to: str) -> list:
        """Extract ZIP file and return list of extracted files with metadata"""
        extracted_files = []
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                for member in zip_ref.namelist():
                    # Skip directory entries
                    if member.endswith('/'):
                        continue
                    
                    try:
                        zip_ref.extract(member, extract_to)
                        extracted_path = os.path.join(extract_to, member)
                        
                        if os.path.exists(extracted_path) and os.path.isfile(extracted_path):
                            extracted_files.append({
                                'local_path': extracted_path,
                                'archive_path': member,
                                'name': os.path.basename(member),
                                'size': os.path.getsize(extracted_path),
                                'directory': os.path.dirname(member)
                            })
                            logger.debug(f"Extracted: {member} -> {extracted_path}")
                    except Exception as e:
                        logger.warning(f"Failed to extract {member} from {zip_path}: {e}")
                        self.scan_stats["errors_encountered"] += 1
                        continue
                        
            logger.info(f"Successfully extracted {len(extracted_files)} files from {os.path.basename(zip_path)}")
            self.scan_stats["files_extracted_from_zip"] += len(extracted_files)
            return extracted_files
            
        except Exception as e:
            logger.error(f"Failed to process ZIP file {zip_path}: {e}")
            self.scan_stats["errors_encountered"] += 1
            return []

    async def process_extracted_files(self, extracted_files: list, semaphore, parent_zip_path: str) -> AsyncGenerator[FileMetadata, None]:
        """Process extracted files, including recursive ZIP processing"""
        for file_info in extracted_files:
            local_path = file_info['local_path']
            archive_path = file_info['archive_path']
            file_name = file_info['name']
            file_size = file_info['size']

            try:
                # Check if extracted file is also a ZIP (nested ZIP)
                if self.is_zip_file(file_name):
                    logger.info(f"Processing nested ZIP file: {archive_path}")
                    self.scan_stats["zip_files_processed"] += 1

                    nested_extract_dir = tempfile.mkdtemp(dir=os.path.dirname(local_path))
                    nested_files = self.process_zip_file(local_path, nested_extract_dir)

                    # Process nested ZIP contents recursively
                    nested_zip_path = f"{parent_zip_path}/{archive_path}"
                    async for nested_metadata in self.process_extracted_files(nested_files, semaphore, nested_zip_path):
                        yield nested_metadata

                    # Clean up nested extraction directory
                    shutil.rmtree(nested_extract_dir, ignore_errors=True)
                
                # Process the file regardless of type (ZIP or regular file)
                file_ext = get_file_extension(file_name)
                file_uri = self._construct_file_uri(archive_path, is_zip_content=True, zip_path=parent_zip_path)

                # Skip Logic 1: Rule-based skip check
                if hasattr(self, 'rule_manager') and self.rule_manager and self.rule_manager.skip_file(file_uri):
                    logger.info(f"Skipping file {file_name} based on skip rules")
                    continue

                # Skip Logic 2: File type check (allow ZIP for extraction, but filter others)
                if file_ext not in PROFILE_SUPPORTED_FILE_TYPES and not self.is_zip_file(file_name):
                    logger.info(f"Skipping {file_name}: Unsupported file type .{file_ext}")
                    continue

                # Skip Logic 3: File size check
                if file_size > PROFILE_MAX_SIZE_THRESHOLD:
                    logger.info(f"Skipping {file_name}: Size {file_size} exceeds threshold {PROFILE_MAX_SIZE_THRESHOLD}")
                    continue

                # Create unique local filepath
                temp_dir = os.path.join(self.local_data_dir, "extracted")
                create_folder(temp_dir)
                final_path = os.path.join(temp_dir, f"{uuid.uuid4()}_{file_name}")

                # Move file to final location
                shutil.move(local_path, final_path)

                # Update stats
                self.scan_stats["total_files_processed"] += 1

                # Yield metadata for the extracted file
                yield FileMetadata(
                    service_name=self.service_name,
                    service_type=self.service_type,
                    service_provider=self.service_provider,
                    sub_service=self.sub_service,
                    file_key=file_name,
                    file_size=file_size,
                    file_type=file_ext,
                    file_uri=file_uri,
                    local_filepath=final_path,
                    details={
                        "created": None,
                        "modified": None,
                        "region": "India",
                        "extracted_from_zip": True,
                        "parent_zip_path": parent_zip_path,
                        "archive_path": archive_path,
                        "is_nested_zip": self.is_zip_file(file_name)
                    }
                )

            except Exception as e:
                logger.error(f"Error processing extracted file {archive_path}: {e}")
                self.scan_stats["errors_encountered"] += 1
                continue

    async def download_file_with_chunks(self, file_open, local_path: str, file_size: int) -> bool:
        """Download file using proper chunk size to avoid SMB read size errors"""
        try:
            with open(local_path, "wb") as outfile:
                offset = 0
                chunk_size = min(self.max_read_size, 32768)  # Use smaller, safer chunk size
                
                while offset < file_size:
                    to_read = min(chunk_size, file_size - offset)
                    try:
                        data = file_open.read(offset, to_read)
                        if not data or not isinstance(data, bytes):
                            logger.warning(f"No data received at offset {offset}")
                            break
                        outfile.write(data)
                        offset += len(data)
                        
                        # Log progress for large files (every 1MB)
                        if file_size > 1024*1024 and offset % (1024*1024) == 0:
                            progress = (offset / file_size) * 100
                            logger.debug(f"Download progress: {progress:.1f}%")
                            
                    except Exception as read_err:
                        logger.error(f"Read error at offset {offset}: {read_err}")
                        # Try with smaller chunk size
                        if chunk_size > 8192:
                            chunk_size = chunk_size // 2
                            logger.info(f"Reducing chunk size to {chunk_size}")
                            continue
                        else:
                            raise read_err
                            
            return True
        except Exception as e:
            logger.error(f"Failed to download file to {local_path}: {e}")
            return False

    async def deep_walk(self, path: str, semaphore) -> AsyncGenerator[FileMetadata, None]:
        """Recursively walk through directory structure and process all files"""
        logger.debug(f"Walking into directory: {path if path else 'ROOT'}")
        
        smb_path = self._construct_smb_path(path) if path else ""
        entries = self._smb_listdir(smb_path)
        
        if not entries:
            logger.debug(f"No entries found in directory: {path if path else 'ROOT'}")
            return
        
        self.scan_stats["total_directories_scanned"] += 1
        logger.info(f"Scanning directory: {path if path else 'ROOT'} ({len(entries)} entries)")
            
        for entry in entries:
            # Construct full path
            if path:
                full_relative_path = f"{path.rstrip('/')}/{entry}" if path.strip() else entry
            else:
                full_relative_path = entry
                
            full_smb_path = self._construct_smb_path(full_relative_path)
            file_id = self._generate_file_id(full_smb_path)
            
            # Check if already processed (avoid duplicates)
            if file_id in self.processed_files:
                logger.debug(f"Skipping already processed: {full_smb_path}")
                continue
            
            # Get file/directory info
            stat = self._smb_stat(full_smb_path)
            if not stat:
                logger.warning(f"Could not stat: {full_smb_path}")
                self.scan_stats["errors_encountered"] += 1
                continue
                
            if stat['is_dir']:
                # Recursively process directory
                logger.debug(f"Entering subdirectory: {full_smb_path}")
                async for metadata in self.deep_walk(full_relative_path, semaphore):
                    yield metadata
            else:
                # Process file
                file_name = os.path.basename(full_smb_path)
                
                # Check if file should be processed
                logger.debug(f"Processing file: {full_smb_path}")
                
                # Mark as processed
                self.processed_files.add(file_id)
                
                # Generate file URI
                file_uri = self._construct_file_uri(full_relative_path)

                # Skip Logic 1: Rule-based skip check
                if hasattr(self, 'rule_manager') and self.rule_manager and self.rule_manager.skip_file(file_uri):
                    logger.info(f"Skipping file {file_name} based on skip rules")
                    
                    continue

                # Skip Logic 2: File type check (allow ZIP for extraction, but filter others)
                file_ext = get_file_extension(file_name)
                if file_ext not in PROFILE_SUPPORTED_FILE_TYPES and not self.is_zip_file(file_name):
                    logger.info(f"Skipping {file_name}: Unsupported file type .{file_ext}")
                    
                    continue

                # Skip Logic 3: File size check
                if stat['size'] > PROFILE_MAX_SIZE_THRESHOLD:
                    logger.info(f"Skipping {file_name}: Size {stat['size']} exceeds threshold {PROFILE_MAX_SIZE_THRESHOLD}")
                    
                    continue
                
                # Create local directory structure
                local_dir = os.path.join(self.local_data_dir, os.path.dirname(full_relative_path) if os.path.dirname(full_relative_path) else "")
                create_folder(local_dir)
                local_path = os.path.join(local_dir, f"{uuid.uuid4()}_{file_name}")
                
                # Download file with semaphore control
                download_success = False
                async with semaphore:
                    try:
                        file_open = Open(self.tree, full_smb_path)
                        file_open.create(
                            desired_access=FILE_READ_DATA,
                            file_attributes=FileAttributes.FILE_ATTRIBUTE_NORMAL,
                            share_access=ShareAccess.FILE_SHARE_READ,
                            create_disposition=CreateDisposition.FILE_OPEN,
                            create_options=CreateOptions.FILE_NON_DIRECTORY_FILE,
                            impersonation_level=ImpersonationLevel.Impersonation
                        )
                        
                        download_success = await self.download_file_with_chunks(file_open, local_path, stat['size'])
                        file_open.close()
                        
                        if download_success:
                            logger.debug(f"Downloaded: {full_smb_path} -> {local_path}")
                        
                    except Exception as e:
                        logger.error(f"Failed downloading {full_smb_path}: {e}")
                        self.scan_stats["errors_encountered"] += 1
                        download_success = False
                
                # Process downloaded file
                if download_success:
                    self.scan_stats["total_files_processed"] += 1
                    
                    if self.is_zip_file(file_name):
                        logger.info(f"Processing ZIP file: {full_smb_path}")
                        self.scan_stats["zip_files_processed"] += 1
                        
                        try:
                            # Extract ZIP file
                            extract_dir = tempfile.mkdtemp()
                            extracted_files = self.process_zip_file(local_path, extract_dir)
                            
                            # Process extracted files
                            if extracted_files:
                                async for zip_metadata in self.process_extracted_files(extracted_files, semaphore, full_relative_path):
                                    yield zip_metadata
                            
                            # Clean up extraction directory
                            shutil.rmtree(extract_dir, ignore_errors=True)
                            
                            # Remove the original ZIP file after processing to save space
                            if os.path.exists(local_path):
                                os.remove(local_path)
                            
                            # Yield metadata for the ZIP file itself (container)
                            file_ext = get_file_extension(file_name)
                            yield FileMetadata(
                                service_name=self.service_name,
                                service_type=self.service_type,
                                service_provider=self.service_provider,
                                sub_service=self.sub_service,
                                file_key=file_name,
                                file_size=stat["size"],
                                file_type=file_ext,
                                file_uri=file_uri,
                                local_filepath=local_path,  # This will be cleaned up
                                details={
                                    "created": None, 
                                    "modified": None, 
                                    "region": "India", 
                                    "is_zip_container": True,
                                    "extracted_files_count": len(extracted_files)
                                }
                            )
                            
                        except Exception as e:
                            logger.error(f"Failed to process ZIP file {full_smb_path}: {e}")
                            self.scan_stats["errors_encountered"] += 1
                            # Remove failed ZIP file
                            if os.path.exists(local_path):
                                os.remove(local_path)
                    else:
                        # Regular file - check if supported type
                        file_ext = get_file_extension(file_name)
                        
                        if self.is_supported_file_type(file_name):
                            # Supported file type
                            yield FileMetadata(
                                service_name=self.service_name,
                                service_type=self.service_type,
                                service_provider=self.service_provider,
                                sub_service=self.sub_service,
                                file_key=file_name,
                                file_size=stat["size"],
                                file_type=file_ext,
                                file_uri=file_uri,
                                local_filepath=local_path,
                                details={
                                    "created": None, 
                                    "modified": None, 
                                    "region": "India"
                                }
                            )
                        else:
                            # Unsupported file type - log and clean up
                            logger.debug(f"Skipping unsupported file type: {file_name}")
                            self.scan_stats["files_skipped"] += 1
                            if os.path.exists(local_path):
                                os.remove(local_path)
                else:
                    # Remove from processed files if download failed
                    self.processed_files.discard(file_id)

    async def test_connection(self):
        """Test connection to NAS and verify access"""
        try:
            await self.get_service_details()
            
            # Test basic connectivity
            start_path = self.root_path if self.root_path else ""
            test_entries = self._smb_listdir(start_path)
            
            logger.info(f"NAS connection test successful for {self.service_name}")
            logger.info(f"Share: {self.share_name}, Root path: '{start_path}'")
            logger.info(f"Found {len(test_entries)} entries in root directory")
            
            return True, f"Connection successful. Found {len(test_entries)} entries in root directory."
            
        except Exception as e:
            logger.error(f"NAS connection test failed: {e}")
            return False, f"Connection failed: {e}"

    async def infra_scan(self):
        """Perform infrastructure scan to get basic metrics"""
        try:
            await self.get_service_details()
            total_files, total_size, total_dirs = 0, 0, 0
            
            # Use BFS to count files and directories
            queue = [self.root_path if self.root_path else ""]
            visited = set()
            
            while queue:
                current_path = queue.pop(0)
                
                # Avoid infinite loops
                path_key = self._construct_smb_path(current_path)
                if path_key in visited:
                    continue
                visited.add(path_key)
                
                smb_path = self._construct_smb_path(current_path)
                entries = self._smb_listdir(smb_path)
                
                if not entries:
                    continue
                
                for entry in entries:
                    if current_path and current_path.strip():
                        full_path = f"{current_path.rstrip('/')}/{entry}"
                    else:
                        full_path = entry
                    
                    full_smb_path = self._construct_smb_path(full_path)
                    stat = self._smb_stat(full_smb_path)
                    
                    if not stat:
                        continue
                        
                    if stat['is_dir']:
                        total_dirs += 1
                        if full_smb_path not in visited:
                            queue.append(full_path)
                    else:
                        total_files += 1
                        total_size += stat['size']
                        
            logger.info(f"Infrastructure scan complete: {total_files} files, {total_dirs} directories, {total_size} bytes")
            
            return {
                "total_files": total_files, 
                "total_size": total_size, 
                "total_directories": total_dirs,
                "region": "India"
            }
            
        except Exception as e:
            logger.error(f"NAS infrastructure scan error: {e}")
            return {
                "total_files": 0, 
                "total_size": 0, 
                "total_directories": 0,
                "region": "India"
            }
        finally:
            await self._cleanup_connections()


    async def deep_scan(self) -> AsyncGenerator[FileMetadata, None]:
        """Perform comprehensive deep scan of all files and directories, grouped by share_name"""
        # Dictionary to hold per-share stats
        share_stats = defaultdict(lambda: {"count": 0, "size": 0})
        access = []

        try:
            await self.get_service_details()

            # Reset tracking variables
            self.processed_files = set()
            self.scan_stats = {
                "total_files_processed": 0,
                "total_directories_scanned": 0,
                "zip_files_processed": 0,
                "files_extracted_from_zip": 0,
                "files_skipped": 0,
                "errors_encountered": 0
            }

            semaphore = asyncio.Semaphore(CONCURRENT_LIMIT)

            logger.info("=" * 80)
            logger.info("STARTING COMPREHENSIVE NAS SCAN")
            logger.info("=" * 80)
            logger.info(f"Service: {self.service_name}")
            logger.info(f"Root path: '{self.root_path if self.root_path else 'ROOT'}'")
            logger.info(f"Concurrent limit: {CONCURRENT_LIMIT}")

            # Start comprehensive scan from root path
            start_path = self.root_path if self.root_path else ""
            async for metadata in self.deep_walk(start_path, semaphore):
                # Extract share_name from file URI or details
                share_name = metadata.details.get("share_name") or self.share_name or "default"

                # Update per-share stats
                share_stats[share_name]["count"] += 1
                share_stats[share_name]["size"] += metadata.file_size or 0

                # Update global stats
                self.scan_stats["total_files_processed"] += 1

                yield metadata

            logger.info("=" * 80)
            logger.info("COMPREHENSIVE SCAN COMPLETED")
            logger.info("=" * 80)
            logger.info(f"Total directories scanned: {self.scan_stats['total_directories_scanned']}")
            logger.info(f"Total files processed: {self.scan_stats['total_files_processed']}")
            logger.info(f"ZIP files processed: {self.scan_stats['zip_files_processed']}")
            logger.info(f"Files extracted from ZIPs: {self.scan_stats['files_extracted_from_zip']}")
            logger.info(f"Files skipped (unsupported): {self.scan_stats['files_skipped']}")
            logger.info(f"Errors encountered: {self.scan_stats['errors_encountered']}")
            logger.info(f"Unique files tracked: {len(self.processed_files)}")

        except Exception as e:
            logger.error(f"Deep scan error: {e}", exc_info=True)

        finally:
            try:
                # Create one AssetDetailsSchema per share
                assets = []
                for share_name, stats in share_stats.items():
                    assets.append(
                        AssetDetailsSchema(
                            asset_name=f"nas_{self.service_name}",
                            service_provider=self.service_provider,
                            type="file_storage",
                            category="unstructured",
                            location="Global",
                            owner="system",
                            security=True,
                            size=stats["size"],
                            count=stats["count"],
                            access_category="restricted",
                            service_name=str(self.service_name),
                            steward=str(self.service_steward),
                        )
                    )
                    # Access control (system as owner by default)
                    access.append({
                        "asset_name": f"nas_{self.service_name}",
                        "user_or_role": self.username,
                        "role": "owner",
                        "access": "full",
                    })

                if assets:
                    load_asset_details(assets)
                    logger.info(f"[NAS] --> Created {len(assets)} asset(s) in asset_details")

                if access:
                    load_access_controls(access)
                    logger.info(f"[NAS] --> Created {len(access)} access_control entries")

            except Exception as e:
                logger.error(f"Failed to persist NAS assets or access controls: {e}", exc_info=True)

    async def user_scan(self, search_files: List[Dict[str, Any]]):
        """Download only the specified files (and specific entries from zips) for DSR.
        Expected each item to contain at least: service_name, file_uri, file_name, file_type.
        file_uri format for NAS should be like:
          - /{service_name}/{share_name}/path/to/file.ext
          - /{service_name}/{share_name}/path/to/archive.zip!/internal/path/file.xlsx
        """
        try:
            await self.get_service_details()

            results: List[Dict[str, Any]] = []

            def parse_uri(uri: str):
                """Split outer path and optional zip inner path.
                Supports both explicit "!/" and implicit "/...zip/..." forms.
                """
                outer, inner = uri, None
                if "!/" in uri:
                    parts = uri.split("!/", 1)
                    outer, inner = parts[0], parts[1]
                    return outer, inner
                # Implicit: detect first .zip-like segment
                normalized = self._normalize_path(uri)
                for ext in ('.zip', '.zipx', '.jar', '.war', '.ear'):
                    marker = f"{ext}/"
                    if marker in normalized.lower():
                        idx = normalized.lower().index(marker) + len(ext)
                        outer = normalized[:idx]
                        # remove any leading slash from remainder
                        inner = normalized[idx:].lstrip('/')
                        return outer, inner
                return outer, inner

            def to_relative_path(outer_uri: str) -> str:
                # Remove leading /{service}/{share}
                prefix = f"/{self.service_name}/{self.share_name}"
                if outer_uri.startswith(prefix):
                    rel = outer_uri[len(prefix):]
                else:
                    # Fallback: strip first two segments
                    rel = outer_uri
                return self._normalize_path(rel)

            semaphore = asyncio.Semaphore(CONCURRENT_LIMIT)

            async def resolve_smb_path(rel: str) -> str:
                """Try to resolve a relative SMB path to a valid path on the share.
                Attempts:
                  1) as-is
                  2) prepend root_path if configured and not already present
                  3) bounded BFS search by filename starting at root_path (or "")
                Returns the first valid path or an empty string if not found.
                """
                candidate = self._construct_smb_path(rel)
                if self._smb_stat(candidate):
                    return candidate

                # Try with root_path if not already included
                if self.root_path:
                    root_norm = self._normalize_path(self.root_path)
                    if not rel.startswith(root_norm):
                        with_root = self._construct_smb_path(f"{root_norm}/{rel}")
                        if self._smb_stat(with_root):
                            return with_root

                # Bounded BFS filename search
                try:
                    target_name = os.path.basename(rel)
                    queue = [self._construct_smb_path(self.root_path)] if self.root_path else [""]
                    visited = set()
                    max_dirs = 500  # safety cap
                    processed = 0

                    while queue and processed < max_dirs:
                        current = queue.pop(0)
                        if current in visited:
                            continue
                        visited.add(current)
                        entries = self._smb_listdir(current)
                        processed += 1
                        for entry in entries:
                            sub_rel = f"{current}/{entry}" if current else entry
                            stat = self._smb_stat(sub_rel)
                            if not stat:
                                continue
                            if stat["is_dir"]:
                                queue.append(sub_rel)
                            else:
                                if os.path.basename(sub_rel).lower() == target_name.lower():
                                    return sub_rel
                except Exception:
                    pass

                return ""

            for file_info in search_files:
                file_uri = str(file_info.get("file_uri", ""))
                file_name = str(file_info.get("file_name", ""))
                if not file_uri or not file_name:
                    continue

                if self.rule_manager and self.rule_manager.skip_file(file_uri):
                    logger.info(f"Skipping by rule: {file_uri}")
                    continue

                outer_uri, inner_path = parse_uri(file_uri)
                rel_path = to_relative_path(outer_uri)
                smb_path = await resolve_smb_path(rel_path)
                if not smb_path:
                    logger.warning(f"NAS user_scan: could not resolve path for {file_uri}")
                    continue

                # Download outer file to local
                local_dir = self.local_data_dir
                create_folder(local_dir)
                local_path = os.path.join(local_dir, f"{uuid.uuid4()}_{os.path.basename(rel_path) or file_name}")

                try:
                    fo = Open(self.tree, smb_path)
                    fo.create(
                        desired_access=FILE_READ_DATA,
                        file_attributes=FileAttributes.FILE_ATTRIBUTE_NORMAL,
                        share_access=ShareAccess.FILE_SHARE_READ,
                        create_disposition=CreateDisposition.FILE_OPEN,
                        create_options=CreateOptions.FILE_NON_DIRECTORY_FILE,
                        impersonation_level=ImpersonationLevel.Impersonation
                    )
                    size = fo.end_of_file
                    async with semaphore:
                        ok = await self.download_file_with_chunks(fo, local_path, size)
                    fo.close()
                    if not ok:
                        logger.warning(f"Download failed for {smb_path}")
                        if os.path.exists(local_path):
                            os.remove(local_path)
                        continue

                    # If targeting a file inside a zip, extract only that entry (no metadata persistence)
                    if inner_path and self.is_zip_file(local_path):
                        try:
                            with zipfile.ZipFile(local_path, 'r') as zip_ref:
                                # Normalize both paths
                                norm_inner = self._normalize_path(inner_path)
                                # Exact match or fallback to basename match
                                names = zip_ref.namelist()
                                candidates = [m for m in names if self._normalize_path(m) == norm_inner]
                                if not candidates:
                                    base = os.path.basename(norm_inner)
                                    candidates = [m for m in names if os.path.basename(self._normalize_path(m)).lower() == base.lower()]
                                if not candidates:
                                    logger.warning(f"Inner member not found in zip: {inner_path}")
                                else:
                                    member = candidates[0]
                                    extract_dir = tempfile.mkdtemp(dir=os.path.dirname(local_path))
                                    zip_ref.extract(member, extract_dir)
                                    extracted_path = os.path.join(extract_dir, member)
                                    final_dir = os.path.join(self.local_data_dir, "extracted")
                                    create_folder(final_dir)
                                    final_path = os.path.join(final_dir, f"{uuid.uuid4()}_{os.path.basename(member)}")
                                    shutil.move(extracted_path, final_path)
                                    shutil.rmtree(extract_dir, ignore_errors=True)
                                    # Replace result to point to extracted member and remove outer zip
                                    try:
                                        if os.path.exists(local_path):
                                            os.remove(local_path)
                                    except Exception:
                                        pass
                                    local_path = final_path
                        except Exception as e:
                            logger.error(f"Zip member extraction failed: {e}")

                    results.append({
                        "service_name": self.service_name,
                        "service_provider": str(self.service_provider),
                        "sub_service": str(self.sub_service),
                        "file_name": file_name,
                        "file_uri": file_uri,
                        "file_type": get_file_extension(file_name),
                        "file_size": os.path.getsize(local_path) if os.path.exists(local_path) else 0,
                        "local_filepath": local_path,
                    })
                except Exception as e:
                    logger.error(f"Failed processing {file_uri}: {e}")
                    try:
                        if os.path.exists(local_path):
                            os.remove(local_path)
                    except Exception:
                        pass

            if results:
                yield results
        except Exception as e:
            logger.error(f"NAS user_scan failed: {e}", exc_info=True)