from azure.identity.aio import ClientSecretCredential
from azure.storage.blob.aio import BlobServiceClient
from azure.core.exceptions import AzureError
import logging
from src.utils.exceptions import ClientException, ServerException

logger = logging.getLogger("Unstructure")


async def test_adls_connection(data: dict) -> dict:
    creds = data.get("credentials", {})

    tenant_id = creds.get("tenant_id")
    client_id = creds.get("client_id")
    client_secret = creds.get("client_secret")
    storage_account_name = creds.get("storage_account_name")
    container_name = creds.get("container_name") or data.get("service_name")

    if not all([tenant_id, client_id, client_secret, storage_account_name, container_name]):
        raise ClientException("Missing required ADLS credentials", code=400)

    try:
        logger.info(f"Initializing ADLS BlobServiceClient for account: {storage_account_name}")

        credential = ClientSecretCredential(
            tenant_id=tenant_id,
            client_id=client_id,
            client_secret=client_secret,
        )

        account_url = f"https://{storage_account_name}.blob.core.windows.net"
        blob_service_client = BlobServiceClient(account_url, credential=credential)
        container_client = blob_service_client.get_container_client(container_name)

        # Try listing blobs to confirm read access
        async for _ in container_client.list_blobs(name_starts_with=None, results_per_page=1).by_page():
            logger.info(f"ADLS access validated for container: {container_name}")
            break

        return {
            "status": True,
            "message": f"ADLS connection successful for container '{container_name}'"
        }

    except AzureError as e:
        logger.error(f"Azure error during ADLS test for container '{container_name}': {str(e)}")
        raise ClientException(f"ADLS test failed: {str(e)}", code=400)

    except Exception as e:
        logger.exception(f"Unexpected error during ADLS connection test for '{container_name}'")
        raise ServerException("ADLS test connection failed", excep=e)
