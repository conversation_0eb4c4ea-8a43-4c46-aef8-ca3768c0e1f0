import socket
import aiohttp
from src.utils.exceptions import ServerException
from src.utils.logger import get_ingestion_logger

logger = get_ingestion_logger()


async def get_adls_region(storage_account_name: str, service_name: str) -> str:
    try:
        logger.info(f"Resolving region for storage account: {storage_account_name}")
        hostname = f"{storage_account_name}.blob.core.windows.net"
        ip = socket.gethostbyname(hostname)
        logger.info(f"Resolved IP for {hostname}: {ip}")

        url = f"http://ip-api.com/json/{ip}"
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    country = data.get("country", "")
                    if not country:
                        raise ServerException(
                            f"Unable to determine country for IP: {ip}"
                        )
                    logger.info(f"Country detected for {service_name}: {country}")
                    return country
                else:
                    raise ServerException(
                        f"GeoIP API returned status code {resp.status}"
                    )
    except socket.gaierror as e:
        logger.error(f"DNS resolution failed for {hostname}: {str(e)}", exc_info=True)
        raise ServerException(f"DNS resolution failed: {str(e)}")
    except Exception as e:
        logger.error(
            f"Error detecting region for {service_name}: {str(e)}", exc_info=True
        )
        raise ServerException(f"Region detection failed: {str(e)}")
