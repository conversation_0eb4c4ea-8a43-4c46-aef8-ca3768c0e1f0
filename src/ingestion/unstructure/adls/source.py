from azure.storage.blob.aio import BlobServiceClient, BlobClient
from azure.core.exceptions import ResourceNotFoundError, AzureError
from azure.identity import ClientSecretCredential
import aiohttp
import asyncio
from uuid import uuid4
from typing import AsyncGenerator, Dict
from src.utils.logger import get_ingestion_logger
from src.common.config import (
    TEMP_DATA_DIR,
    CONCURRENT_LIMIT,
    PROFILE_MAX_SIZE_THRESHOLD,
    PROFILE_SUPPORTED_FILE_TYPES,
)
from src.ingestion.unstructure.adls.connection import get_adls_region
from src.common.constants import ServiceTypes, ServiceProviders, AzureSubServices
from src.ingestion.base_source import UnstructureSource
from src.ingestion.data_class import FileMetadata
from src.modules.repos import DatabaseManager
from src.utils.helpers import file_exists, create_folder, get_file_extension
from src.utils.loaders import load_access_controls, load_asset_details
from src.ingestion.data_class import AssetsDetails as AssetDetailsSchema
import os

logger = get_ingestion_logger()


class ADLSSource(UnstructureSource):
    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.AZURE_DATA_LAKE.value, service_name)
        self.db = DatabaseManager()
        self.service_provider = ServiceProviders.AZURE
        self.sub_service = AzureSubServices.AZURE_DATA_LAKE
        self.blob_service_client = None
        self.container_name = None
        self.credential = None
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"
        self.service_steward = None

    async def get_service_details(self) -> Dict:
        """Gets service details"""
        logger.info(f"Fetching service details for {self.service_name}")
        self.service = await self.db.get_service_by_service_name(
            self.service_name
        )
        credentials = self.service.get("credentials", {})
        self.tenant_id = credentials.get("tenant_id")
        self.client_id = credentials.get("client_id")
        self.client_secret = credentials.get("client_secret")
        self.storage_account_name = credentials.get("storage_account_name")
        self.container_name = credentials.get(
            "container_name", self.service_name
        )
        self.service_steward = self.service.get("data_steward")

        self.credential = ClientSecretCredential(
            self.tenant_id, self.client_id, self.client_secret
        )
        account_url = (
            f"https://{self.storage_account_name}.blob.core.windows.net"
        )
        self.blob_service_client = BlobServiceClient(
            account_url, credential=self.credential
        )
        logger.info(
            f"Service details fetched and BlobServiceClient initialized for "
            f"{self.service_name}"
        )
        return self.service

    async def test_connection(self) -> Dict:
        logger.info(f"Testing connection for {self.service_name}")
        try:
            await self.blob_service_client.get_service_properties()
            logger.info(f"Connection test successful for {self.service_name}")
            return {"status": True, "message": "Authentication successful"}
        except AzureError as e:
            logger.error(
                f"Error verifying credentials for {self.service_name}: {str(e)}"
            )
            return {"status": False, "message": f"Authentication failed: {str(e)}"}

    async def get_adls_encryption_status(self) -> bool:
        """Determine encryption status for ADLS"""
        # Azure Data Lake Storage encrypts data at rest and in transit by default
        return True

    async def download_file_from_adls(
        self, blob_client: BlobClient, filepath: str
    ):
        if file_exists(filepath):
            logger.info(
                f"File already exists at {filepath}, skipping download"
            )
            return

        logger.info(f"Downloading file to {filepath}")
        try:
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            downloader = await blob_client.download_blob()
            content = await downloader.readall()
            with open(filepath, "wb") as f:
                f.write(content)
            logger.info(f"Successfully downloaded file to {filepath}")
        except Exception as e:
            logger.error(
                f"Error downloading file from ADLS to {filepath}: {str(e)}"
            )
            raise

    async def fetch_file_from_adls(self, blob, semaphore) -> Dict:
        file_name = blob.name
        file_ext = get_file_extension(file_name).lower()
        file_uri = f"{self.container_name}/{blob.name}"

        # Skip logic
        if self.rule_manager.skip_file(file_uri):
            logger.info(f"Skipping file {file_name} based on skip rules")
            return None

        if blob.size > PROFILE_MAX_SIZE_THRESHOLD:
            logger.info(
                f"Skipping {file_name}: Size {blob.size} exceeds threshold "
                f"{PROFILE_MAX_SIZE_THRESHOLD}"
            )
            return None

        if file_ext not in PROFILE_SUPPORTED_FILE_TYPES:
            logger.info(f"Skipping {file_name}: Unsupported file type .{file_ext}")
            return None

        # Create temp directory with service name
        temp_dir = self.local_data_dir
        create_folder(temp_dir)
        safe_blob_name = blob.name.replace("/", "_")
        filepath = f"{temp_dir}/{str(uuid4())}_{safe_blob_name}"

        if not self.blob_service_client:
            await self.get_service_details()

        blob_client = self.blob_service_client.get_blob_client(
            self.container_name, blob.name
        )
        logger.info(f"Fetching {blob.name} to {filepath}")
        async with semaphore:
            await self.download_file_from_adls(blob_client, filepath)
            logger.info(f"Completed fetching {blob.name}")

        return {
            "name": file_name,
            "size": blob.size,
            "file_type": file_ext,
            "file_uri": file_uri,
            "filepath": filepath,
            "created": blob.creation_time.isoformat() if blob.creation_time else None,
            "modified": blob.last_modified.isoformat(),
            "success": True
        }

    async def scan_adls_infra(self) -> Dict:
        logger.info(
            f"Starting infrastructure scan for container {self.container_name}"
        )
        total_files = 0
        total_size = 0
        try:
            container_client = self.blob_service_client.get_container_client(
                self.container_name
            )
            async for blob in container_client.list_blobs():
                total_size += blob.size
                total_files += 1
                logger.debug(f"Found file: {blob.name}, Size: {blob.size}")
            logger.info(
                f"Infrastructure scan completed: {total_files} files, "
                f"{total_size} bytes"
            )
            return {"total_size": total_size, "total_files": total_files}
        except Exception as e:
            logger.error(
                f"ADLS infra scan failed for {self.container_name}: {str(e)}"
            )
            return {"total_size": 0, "total_files": 0}

    async def scan_adls_deep(self) -> AsyncGenerator[FileMetadata, None]:
        logger.info(f"Starting deep scan for container {self.container_name}")
        try:
            container_client = self.blob_service_client.get_container_client(
                self.container_name
            )
            semaphore = asyncio.Semaphore(CONCURRENT_LIMIT)
            tasks = []

            async for blob in container_client.list_blobs():
                logger.debug(f"Queuing deep scan task for {blob.name}")
                tasks.append(self.fetch_file_from_adls(blob, semaphore))

            for future in asyncio.as_completed(tasks):
                file_info = await future
                if file_info and file_info.get("success"):
                    yield FileMetadata(
                        service_name=self.service_name,
                        service_type=self.service_type,
                        service_provider=self.service_provider,
                        sub_service=self.sub_service,
                        file_key=file_info["name"],
                        file_size=file_info["size"],
                        file_type=file_info["file_type"],
                        file_uri=file_info["file_uri"],
                        local_filepath=file_info["filepath"],
                        details={
                            "created": file_info["created"],
                            "modified": file_info["modified"],
                            "region": "India"
                        }
                    )
            logger.info(f"Deep scan completed for container {self.container_name}")
        except Exception as e:
            logger.error(
                f"ADLS deep scan failed for {self.container_name}: {str(e)}"
            )

    async def infra_scan(self) -> Dict:
        logger.info(f"Initiating infra scan for {self.service_name}")
        try:
            await self.get_service_details()
            country = await get_adls_region(
                self.storage_account_name, self.service_name
            )
            infra = await self.scan_adls_infra()
            infra["region"] = country
            logger.info(f"Infra scan finished for {self.service_name}: {infra}")
            return infra
        except Exception as e:
            logger.error(
                f"Error during ADLS infra scanning for {self.service_name}: "
                f"{str(e)}",
                exc_info=True,
            )
            return {"total_size": 0, "total_files": 0}

    async def deep_scan(self) -> AsyncGenerator[FileMetadata, None]:
        logger.info(f"Initiating deep scan for {self.service_name}")
        total_file_size = 0
        total_file_count = 0
        try:
            await self.get_service_details()
            country = await get_adls_region(
                self.storage_account_name, self.service_name
            )
            async for file_metadata in self.scan_adls_deep():
                total_file_size += file_metadata.file_size
                total_file_count += 1
                yield file_metadata
            logger.info(f"Deep scan finished for {self.service_name}")
        except Exception as e:
            logger.error(
                f"Error during ADLS deep scanning for {self.service_name}: "
                f"{str(e)}",
                exc_info=True,
            )
        finally:
            if self.blob_service_client:
                logger.info(
                    f"Closing BlobServiceClient for {self.service_name}"
                )
                await self.blob_service_client.close()

            # ---- Asset details ----
            try:
                encryption_enabled = await self.get_adls_encryption_status()
            except Exception as e:
                logger.error(f"Error checking ADLS encryption status: {str(e)}")
                encryption_enabled = False

            owner = self.service_steward if self.service_steward else "system"
            asset = [
                AssetDetailsSchema(
                    asset_name=f"adls_{self.service_name}",
                    service_provider=self.service_provider,
                    type="file_storage",
                    category="unstructured",
                    location=country if 'country' in locals() else "unknown",
                    owner=owner,
                    security=encryption_enabled,
                    size=total_file_size,
                    count=total_file_count,
                    access_category="restricted",
                    service_name=str(self.service_name),
                    steward=str(self.service_steward),
                )
            ]
            load_asset_details(asset)

            # ---- Access controls ----
            access = [
                {
                    "asset_name": f"adls_{self.service_name}",
                    "user_or_role": owner,
                    "role": "owner",
                    "access": "full",
                }
            ]
            load_access_controls(access)