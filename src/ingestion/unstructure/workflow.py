from uuid import uuid4
from typing import List, Optional
import json
from datetime import datetime
from src.utils.logger import get_ingestion_logger
from src.ingestion.unstructure.aws.s3.source import S3Source
from src.ingestion.unstructure.office_365.onedrive.source import OneDriveSource
from src.ingestion.unstructure.office_365.outlook.source import OutlookSource
from src.ingestion.unstructure.google_workspace.gmail.source import GmailSource
from src.ingestion.unstructure.google_workspace.gdrive.source import GoogleDriveSource
from src.ingestion.profile_client import Process, UnstructProfiler
from src.common.config import (
    PROFILE_MAX_SIZE_THRESHOLD,
    WAREHOUSE_FOLDER,
)
from src.utils.helpers import remove_file, create_file, create_folder
from src.modules.repos import DatabaseManager
from src.ingestion.unstructure.office_365.sharepoint.source import (
    SharePointSource,
)
import os
import asyncio
import aiohttp
from src.ingestion.unstructure.office_365.teams.source import TeamsSource
from src.ingestion.unstructure.adls.source import ADLSSource
from src.ingestion.unstructure.directory_scan.source import DirectorySource
from src.ingestion.unstructure.sftp_server.source import SFTPSource
from src.ingestion.base_workflow import UnstructIngestionFlow


logger = get_ingestion_logger()

"""
Implement Warehouse formatters, helper classes for every source

"""


def save_to_warhouse_folder(data: dict):
    create_folder(WAREHOUSE_FOLDER)
    file = datetime.now().strftime("%Y%m%d%H%M%s") + str(uuid4())
    create_file(f"{WAREHOUSE_FOLDER}/{file}.json", json.dumps(data))


class S3Workflow(UnstructIngestionFlow):
    def __init__(self, service_type: str, service_name: str, ingestion_id: str):
        super().__init__(service_type, service_name, ingestion_id)

    async def get_source(self):
        source = S3Source(self.service_name)
        verification = await source.verify_s3_iam_role_authentication()
        if not verification.get("success"):
            raise Exception(f"Authentication failed: {verification.get('message')}")
        return source

    async def execute_internal(self) -> None:
        """Workflow-specific logic to execute safely"""
        source = await self.get_source()
        profiler = UnstructProfiler()

        # self.steps = await self.create_ingestion_pipeline()

        buckets = await source.infra_scan()
        buckets = [
            {
                "bucket": b.get("bucket"),
                "bucket_size": b.get("size"),
                "no_of_objects": b.get("no_of_objects"),
                "region": b.get("region"),
            }
            for b in buckets
            # logger.info(f"Bucket: {b['bucket']} is in region: {b['region']}")
        ]
        infra_data = {
            "service_name": self.service_name,
            "service_type": self.service_type,
            "buckets": buckets,
            "scan_type": "infra",
        }
        save_to_warhouse_folder(infra_data)

        async for record in source.deep_scan():
            object_size = record.get("size", 0)
            filepath = record.get("filepath", "")
            piis = []
            token = None
            if object_size <= PROFILE_MAX_SIZE_THRESHOLD:
                token = await profiler.run(record)
            if filepath:
                remove_file(filepath)

            data = {
                "service_name": self.service_name,
                "service_type": self.service_type,
                "file_key": record.get("key"),
                "file_size": record.get("size", 0),
                "file_type": record.get("key", "").split(".")[-1],
                "file_uri": record.get("file_uri", ""),
                "piis": piis,
                "token": token,
                "scan_type": "file_metadata",
                "details": {
                    "bucket": record.get("bucket"),
                    **infra_data
                },
            }
            # logger.info("Prepared scan data: %s", json.dumps(data, indent=2))
            save_to_warhouse_folder(data)

    async def calculate_success(self) -> float:
        """Get the success % of the internal execution"""
        return 0.0

    async def get_failures(self):
        """Get the failures to flag whether if the workflow succeeded or not"""

    async def execute(self) -> None:
        """
        Main entrypoint:
        1. Start logging timer. It will be closed at `stop`
        2. Execute the workflow
        3. Validate the pipeline status
        4. Update the pipeline status at the end
        """
        try:
            await self.execute_internal()
        except Exception as err:
            logger.error(f"Ingestion failed for the service {self.service_name}, {err}")
        finally:
            await self.stop()

    async def stop(self) -> None:
        """
        Main stopping logic
        """

    async def create_ingestion_pipeline(self) -> List[Process]:
        """Creates Ingestion pipeline"""
        return [UnstructProfiler()]


class OnedriveWorkflow(UnstructIngestionFlow):
    def __init__(self, service_type: str, service_name: str, ingestion_id: str):
        super().__init__(service_type, service_name, ingestion_id)

    async def get_source(self):
        """Get source"""
        return OneDriveSource(self.service_name)

    async def execute_internal(self) -> None:
        """Workflow-specific logic to execute safely"""
        source = await self.get_source()
        profiler = UnstructProfiler()

        # self.steps = await self.create_ingestion_pipeline()

        infra = await source.infra_scan()
        logger.info(f"Onedrive Infra scan output: {infra}")
        users = []
        for user in infra.get("users", []):
            users.append(
                {
                    "user_email": user,
                    "storage_size": 0,
                    "region": infra.get("tenant_region", ""),
                }
            )

        infra_data = {
            "service_name": self.service_name,
            "service_type": self.service_type,
            "users": users,
            "scan_type": "infra",
        }
        save_to_warhouse_folder(infra_data)

        async for record in source.deep_scan():
            object_size = record.get("size", 0)
            filepath = record.get("filepath", "")
            piis = []
            token = None
            if object_size <= PROFILE_MAX_SIZE_THRESHOLD:
                token = await profiler.run(record)

            if filepath:
                remove_file(filepath)

            data = {
                "service_name": self.service_name,
                "service_type": self.service_type,
                "file_key": record.get("name"),
                "file_size": record.get("size", 0),
                "file_type": record.get("name", "").split(".")[-1],
                "file_uri": record.get("file_uri", ""),
                "piis": piis,
                "token": token if token is not None else "",
                "scan_type": "file_metadata",
                "details": {
                    "user_email": record.get("user_email"),
                    **infra_data
                },
            }
            # logger.info("Prepared scan data: %s", json.dumps(data, indent=2))
            save_to_warhouse_folder(data)

    async def calculate_success(self) -> float:
        """Get the success % of the internal execution"""
        return 0.0

    async def get_failures(self):
        """Get the failures to flag whether if the workflow succeeded or not"""

    async def execute(self) -> None:
        """
        Main entrypoint:
        1. Start logging timer. It will be closed at `stop`
        2. Execute the workflow
        3. Validate the pipeline status
        4. Update the pipeline status at the end
        """
        try:
            await self.execute_internal()
        except Exception as err:
            logger.error(f"Ingestion failed for the service {self.service_name}, {err}")
        finally:
            await self.stop()

    async def stop(self) -> None:
        """
        Main stopping logic
        """

    async def create_ingestion_pipeline(self) -> List[Process]:
        """Creates Ingestion pipeline"""
        return [UnstructProfiler()]


class OutlookWorkflow(UnstructIngestionFlow):
    def __init__(self, service_type: str, service_name: str, ingestion_id: str):
        super().__init__(service_type, service_name, ingestion_id)

    async def get_source(self):
        """Get source"""
        return OutlookSource(self.service_name)

    async def execute_internal(self) -> None:
        """Workflow-specific logic to execute safely"""
        source = await self.get_source()
        profiler = UnstructProfiler()

        # self.steps = await self.create_ingestion_pipeline()

        infra = await source.infra_scan()
        logger.info(f"outlook Infra scan output: {infra}")
        users = []
        for user in infra.get("users", []):
            users.append(
                {
                    "user_email": user,
                    "storage_size": 0,
                    "region": infra.get("tenant_region", ""),
                }
            )

        infra_data = {
            "service_name": self.service_name,
            "service_type": self.service_type,
            "users": users,
            "scan_type": "infra",
        }
        save_to_warhouse_folder(infra_data)

        async for record in source.deep_scan():
            object_size = record.get("size", 0)
            filepath = record.get("filepath", "")
            piis = []
            token = None
            if object_size <= PROFILE_MAX_SIZE_THRESHOLD:
                token = await profiler.run(record)

            if filepath:
                remove_file(filepath)

            data = {
                "service_name": self.service_name,
                "service_type": self.service_type,
                "file_key": record.get("name"),
                "file_size": record.get("size", 0),
                "file_type": record.get("name", "").split(".")[-1],
                "file_uri": record.get("file_uri", ""),
                "piis": piis,
                "token": token,
                "scan_type": "file_metadata",
                "details": {
                    "user_email": record.get("user_email"),
                    **infra_data
                },
            }
            # logger.info("Prepared scan data: %s", json.dumps(data, indent=2))
            save_to_warhouse_folder(data)

    async def calculate_success(self) -> float:
        """Get the success % of the internal execution"""
        return 0.0

    async def get_failures(self):
        """Get the failures to flag whether if the workflow succeeded or not"""

    async def execute(self) -> None:
        """
        Main entrypoint:
        1. Start logging timer. It will be closed at `stop`
        2. Execute the workflow
        3. Validate the pipeline status
        4. Update the pipeline status at the end
        """
        try:
            await self.execute_internal()
        except Exception as err:
            logger.error(f"Ingestion failed for the service {self.service_name}, {err}")
        finally:
            await self.stop()

    async def stop(self) -> None:
        """
        Main stopping logic
        """

    async def create_ingestion_pipeline(self) -> List[Process]:
        """Creates Ingestion pipeline"""
        return [UnstructProfiler()]


class SharePointWorkflow(UnstructIngestionFlow):
    def __init__(self, service_type: str, service_name: str, ingestion_id: str):
        super().__init__(service_type, service_name, ingestion_id)

    async def get_source(self):
        """Get SharePoint source"""
        return SharePointSource(self.service_name)

    async def execute_internal(self) -> None:
        """Workflow-specific logic to execute safely"""
        source = await self.get_source()
        profiler = UnstructProfiler()

        infra = await source.infra_scan()
        logger.info(f"sharepoint Infra scan output: {infra}")
        users = []
        for user in infra.get("users", []):
            users.append(
                {
                    "user_email": user,
                    "storage_size": 0,
                    "region": infra.get("tenant_region", ""),
                }
            )

        infra_data = {
            "service_name": self.service_name,
            "service_type": self.service_type,
            "users": users,
            "scan_type": "infra",
        }
        save_to_warhouse_folder(infra_data)

        async for record in source.deep_scan():
            object_size = record.get("size", 0)
            filepath = record.get("filepath", "")
            piis = []
            token = None
            if object_size <= PROFILE_MAX_SIZE_THRESHOLD:
                token = await profiler.run(record)

            if filepath:
                remove_file(filepath)

            data = {
                "service_name": self.service_name,
                "service_type": self.service_type,
                "file_key": record.get("name"),
                "file_size": record.get("size", 0),
                "file_type": record.get("name", "").split(".")[-1],
                "file_uri": record.get("file_uri", ""),
                "piis": piis,
                "token": token,
                "scan_type": "file_metadata",
                "details": {
                    "user_email": record.get("user_email"),
                    **infra_data
                },
            }
            # logger.info("Prepared scan data: %s", json.dumps(data, indent=2))
            save_to_warhouse_folder(data)

    async def calculate_success(self) -> float:
        """Get the success % of the internal execution"""
        return 0.0

    async def get_failures(self):
        """Get the failures to flag whether the workflow succeeded or not"""

    async def execute(self) -> None:
        """
        Main entrypoint:
        1. Start logging timer. It will be closed at `stop`
        2. Execute the workflow
        3. Validate the pipeline status
        4. Update the pipeline status at the end
        """
        try:
            await self.execute_internal()
        except Exception as err:
            logger.error(f"Ingestion failed for the service {self.service_name}, {err}")
        finally:
            await self.stop()

    async def stop(self) -> None:
        """Main stopping logic"""

    async def create_ingestion_pipeline(self) -> List[Process]:
        """Creates Ingestion pipeline"""
        return [UnstructProfiler()]


class TeamsWorkflow(UnstructIngestionFlow):
    def __init__(self, service_type: str, service_name: str, ingestion_id: str):
        super().__init__(service_type, service_name, ingestion_id)

    async def get_source(self):
        """Get Teams source"""
        return TeamsSource(self.service_name)

    async def execute_internal(self) -> None:
        source = await self.get_source()
        profiler = UnstructProfiler()

        # Fix: Use infra_scan() instead of scan_teams_infra()
        infra = await source.infra_scan()
        logger.info(f"teams] Infra scan output: {infra}")
        users = []
        for user in infra.get("users", []):
            users.append(
                {
                    "user_email": user,
                    "storage_size": 0,
                    "region": infra.get("tenant_region", ""),
                }
            )

        infra_data = {
            "service_name": self.service_name,
            "service_type": self.service_type,
            "users": users,
            "scan_type": "infra",
        }
        save_to_warhouse_folder(infra_data)

        async for record in source.deep_scan():
            object_size = record.get("size", 0)
            filepath = record.get("filepath", "")
            piis = []
            token = None
            if object_size <= PROFILE_MAX_SIZE_THRESHOLD:
                token = await profiler.run(record)

            if filepath:
                remove_file(filepath)

            data = {
                "service_name": self.service_name,
                "service_type": self.service_type,
                "file_key": record.get("name"),
                "file_size": record.get("size", 0.0),
                "file_type": record.get("name", "").split(".")[-1],
                "file_uri": record.get("file_uri"),
                "piis": piis,
                "token": token,
                "scan_type": "file_metadata",
                "details": {
                    "user_email": record.get("user_email"),
                    **infra_data
                },
            }
            # logger.info("Prepared scan data: %s", json.dumps(data, indent=2))
            save_to_warhouse_folder(data)

    async def calculate_success(self) -> float:
        """Calculate success percentage"""
        return 0.0

    async def get_failures(self):
        """Get failure logs"""

    async def execute(self) -> None:
        """Main entrypoint for the Teams ingestion workflow"""
        try:
            await self.execute_internal()
        except Exception as err:
            logger.error(f"Ingestion failed for Teams {self.service_name}, {err}")
        finally:
            await self.stop()

    async def stop(self) -> None:
        """Cleanup process"""

    async def create_ingestion_pipeline(self) -> List:
        """Creates Teams ingestion pipeline"""
        return [UnstructProfiler()]


class ADLSWorkflow:
    def __init__(self, service_type: str, service_name: str, ingestion_id: str):
        self.service_name = service_name
        self.service_type = service_type
        self.ingestion_id = ingestion_id
        self.db = DatabaseManager()
        self.session = None  # Will hold the aiohttp.ClientSession

    async def get_ingestion_details(self):
        """Single function to execute to create a Workflow instance"""
        ingestion = self.db.get_ingestion_by_id(self.ingestion_id)
        self.ingestion = ingestion
        return ingestion

    async def get_source(self):
        """Get ADLS source"""
        return ADLSSource(self.service_name)

    async def execute_internal(self) -> None:
        """Workflow-specific logic to execute safely"""
        source = await self.get_source()
        profiler = UnstructProfiler()

        # Create a single session for the workflow
        async with aiohttp.ClientSession() as session:
            self.session = session  # Store for use in profiler if needed
            logger.info(f"Starting workflow execution for {self.service_name}")

            infra = await source.infra_scan()
            logger.info(f"Infra scan output: {infra}")
            infra_data = {
                "service_name": self.service_name,
                "total_files": infra.get("total_files", 0),
                "total_size": infra.get("total_size", 0),
                "region": infra.get("region", "unknown"),
            }
            save_to_warhouse_folder(infra_data)  # Uses existing function

            async for record in source.deep_scan():
                object_size = record.get("size", 0)
                filepath = record.get("filepath")
                piis = []

                token = None
                if object_size <= PROFILE_MAX_SIZE_THRESHOLD:
                    token = await profiler.run(record)

                if filepath:
                    remove_file(filepath)

                data = {
                    "service_name": self.service_name,
                    "service_type": self.service_type,
                    "file_key": record.get("name"),
                    "file_size": record.get("size", 0),
                    "file_type": record.get("name", "").split(".")[-1],
                    "file_uri": record.get("file_uri"),
                    "piis": piis,
                    "token": token,
                    "scan_type": "file_metadata",
                    "details": {
                        **infra_data
                    },
                }
                # logger.info("Prepared scan data: %s", json.dumps(data, indent=2))
                save_to_warhouse_folder(data)  

            logger.info(f"Workflow execution completed for {self.service_name}")

    async def calculate_success(self) -> float:
        """Get the success % of the internal execution"""
        return 0.0

    async def get_failures(self):
        """Get the failures to flag whether if the workflow succeeded or not"""
        pass

    async def execute(self) -> None:
        """
        Main entrypoint:
        1. Start logging timer. It will be closed at `stop`
        2. Execute the workflow
        3. Validate the pipeline status
        4. Update the pipeline status at the end
        """
        try:
            await self.execute_internal()
        except Exception as err:
            logger.error(f"Ingestion failed for the service {self.service_name}, {err}")
        finally:
            await self.stop()

    async def stop(self) -> None:
        """Main stopping logic"""
        if self.session and not self.session.closed:
            logger.info(f"Closing aiohttp session for {self.service_name}")
            await self.session.close()
        logger.info(f"Workflow stopped for {self.service_name}")

    async def create_ingestion_pipeline(self) -> List[Process]:
        """Creates Ingestion pipeline"""
        return [UnstructProfiler()]


class DirectoryWorkflow:
    def __init__(self, service_type: str, service_name: str, ingestion_id: str):
        self.service_name = service_name
        self.service_type = service_type
        self.ingestion_id = ingestion_id
        self.db = DatabaseManager()
        self.session = None

    async def get_ingestion_details(self):
        ingestion = self.db.get_ingestion_by_id(self.ingestion_id)
        self.ingestion = ingestion
        return ingestion

    async def get_source(self):
        return DirectorySource(self.service_name)

    async def execute_internal(self) -> None:
        source = await self.get_source()
        profiler = UnstructProfiler()

        async with aiohttp.ClientSession() as session:
            self.session = session
            logger.info(
                f"Starting directory workflow execution for {self.service_name}"
            )

            infra = await source.infra_scan()
            infra_data = {
                "service_name": self.service_name,
                "total_files": infra.get("total_files", 0),
                "total_size": infra.get("total_size", 0),
                "region": "India"
            }
            save_to_warhouse_folder(infra_data)

            async for record in source.deep_scan():
                object_size = record.get("size", 0)
                filepath = record.get("filepath")
                piis = []

                token = None
                if object_size <= PROFILE_MAX_SIZE_THRESHOLD:
                    token = await profiler.run(record)

                if filepath:
                    remove_file(filepath)

                data = {
                    "service_name": self.service_name,
                    "service_type": self.service_type,
                    "file_key": record.get("name"),
                    "file_size": record.get("size", 0),
                    "file_type": record.get("name", "").split(".")[-1],
                    "file_uri": record.get("file_uri"),
                    "piis": piis,
                    "token": token,
                    "scan_type": "file_metadata",
                    "details": {
                        "createdDateTime": record.get("createdDateTime"),
                        **infra_data
                    },
                }
                logger.info(f"URI: {record.get('file_uri')}")
                # logger.info("Prepared scan data: %s", json.dumps(data, indent=2))
                save_to_warhouse_folder(data)

            logger.info(
                f"Directory workflow execution completed for {self.service_name}"
            )

    async def calculate_success(self) -> float:
        return 0.0

    async def get_failures(self):
        pass

    async def execute(self) -> None:
        try:
            await self.execute_internal()
        except Exception as err:
            logger.error(f"Directory ingestion failed for {self.service_name}, {err}")
        finally:
            await self.stop()

    async def stop(self) -> None:
        if self.session and not self.session.closed:
            logger.info(f"Closing aiohttp session for {self.service_name}")
            await self.session.close()
        logger.info(f"Workflow stopped for {self.service_name}")

    async def create_ingestion_pipeline(self) -> List:
        return [UnstructProfiler()]


class GmailWorkflow:
    def __init__(self, service_type: str, service_name: str, ingestion_id: str):
        self.service_name = service_name
        self.service_type = service_type
        self.ingestion_id = ingestion_id
        self.db = DatabaseManager()
        self.session: Optional[aiohttp.ClientSession] = None

    async def get_ingestion_details(self):
        """Fetch ingestion metadata"""
        self.ingestion = self.db.get_ingestion_by_id(self.ingestion_id)
        return self.ingestion

    async def get_source(self):
        """Get Gmail source"""
        return GmailSource(self.service_name)

    async def execute_internal(self) -> None:
        source = await self.get_source()
        profiler = UnstructProfiler()

        async with aiohttp.ClientSession() as session:
            self.session = session
            logger.info(f"Starting Gmail workflow execution for {self.service_name}")

            # Infra scan
            infra = await source.infra_scan()
            infra_data = {
                "service_name": self.service_name,
                "total_emails": infra.get("total_emails", 0),
                "total_size": infra.get("total_size", 0),
            }
            save_to_warhouse_folder(infra_data)

            # Deep scan (emails and attachments)
            async for record in source.deep_scan():
                object_size = record.get("size", 0)
                filepath = record.get("filepath")
                piis = []

                token = None
                if object_size <= PROFILE_MAX_SIZE_THRESHOLD:
                    token = await profiler.run(record)

                if filepath:
                    remove_file(filepath)

                data = {
                    "service_name": self.service_name,
                    "service_type": self.service_type,
                    "file_key": record.get("name"),
                    "file_size": record.get("size", 0),
                    "file_type": record.get("name", "").split(".")[-1],
                    "file_uri": record.get("file_uri"),
                    "piis": piis,
                    "token": token,
                    "scan_type": "email_metadata",
                    "details": {
                        "subject": record.get("subject"),
                        "from": record.get("from"),
                        "to": record.get("to"),
                        "date": record.get("date"),
                    },
                }
                # logger.info("Prepared scan data: %s", json.dumps(data, indent=2))
                save_to_warhouse_folder(data)

            logger.info(f"Gmail workflow execution completed for {self.service_name}")

    async def calculate_success(self) -> float:
        return 0.0

    async def get_failures(self):
        pass

    async def execute(self) -> None:
        try:
            await self.execute_internal()
        except Exception as err:
            logger.error(f"Gmail ingestion failed for {self.service_name}, {err}")
        finally:
            await self.stop()

    async def stop(self) -> None:
        if self.session and not self.session.closed:
            logger.info(f"Closing aiohttp session for {self.service_name}")


# class GoogleDriveWorkflow:
#     def __init__(self, service_type: str, service_name: str, ingestion_id: str):
#         self.service_name = service_name
#         self.service_type = service_type
#         self.ingestion_id = ingestion_id
#         self.db = DatabaseManager()
#         self.session: Optional[aiohttp.ClientSession] = None

#     async def get_ingestion_details(self):
#         """Fetch ingestion metadata from the database."""
#         ingestion = self.db.get_ingestion_by_id(self.ingestion_id)
#         self.ingestion = ingestion
#         return ingestion

#     async def get_source(self):
#         """Get Google Drive source"""
#         return GoogleDriveSource(self.service_name)

#     async def execute_internal(self) -> None:
#         """Workflow logic to execute Google Drive scan."""
#         source = await self.get_source()
#         profiler = UnstructProfiler()

#         async with aiohttp.ClientSession() as session:
#             self.session = session
#             logger.info(
#                 f"Starting Google Drive workflow execution for {self.service_name}"
#             )

#             # Infra Scan
#             infra = await source.infra_scan()
#             infra_data = {
#                 "service_name": self.service_name,
#                 "total_files": infra.get("total_files", 0),
#                 "total_size": infra.get("total_size", 0),
#             }
#             save_to_warhouse_folder(infra_data)

#             # Deep scan (iterate over files)
#             async for record in source.deep_scan():
#                 object_size = record.get("size", 0)
#                 piis = []
#                 token = None

#                 if object_size <= PROFILE_MAX_SIZE_THRESHOLD:
#                     token = await profiler.run(record)

#                 filepath = record.get("filepath")
#                 if filepath:
#                     remove_file(filepath)

#                 data = {
#                     "service_name": self.service_name,
#                     "service_type": self.service_type,
#                     "file_key": record.get("name"),
#                     "file_size": object_size,
#                     "file_type": record.get("name", "").split(".")[-1],
#                     "file_uri": record.get("file_uri"),
#                     "piis": piis,
#                     "token": token,
#                     "scan_type": "file_metadata",
#                     "details": {
#                         "createdDateTime": record.get("createdDateTime"),
#                         "lastModifiedDateTime": record.get("lastModifiedDateTime"),
#                         "owner": record.get("owner"),
#                     },
#                 }
#                 # logger.info("Prepared scan data: %s", json.dumps(data, indent=2))
#                 save_to_warhouse_folder(data)

#             logger.info(
#                 f"Google Drive workflow execution completed for {self.service_name}"
#             )

#     async def calculate_success(self) -> float:
#         """Placeholder for calculating success rate"""
#         return 0.0

#     async def get_failures(self):
#         """Placeholder for fetching failures"""
#         pass

#     async def execute(self) -> None:
#         try:
#             await self.execute_internal()
#         except Exception as err:
#             logger.error(
#                 f"Ingestion failed for Google Drive service {self.service_name}, {err}"
#             )
#         finally:
#             await self.stop()

#     async def stop(self) -> None:
#         if self.session and not self.session.closed:
#             logger.info(f"Closing aiohttp session for {self.service_name}")
#             await self.session.close()
#         logger.info(f"Workflow stopped for {self.service_name}")

#     async def create_ingestion_pipeline(self) -> List[Process]:
#         return [UnstructProfiler()]


# class SFTPWorkflow:
#     def __init__(self, service_type: str, service_name: str, ingestion_id: str):
#         self.service_type = service_type
#         self.service_name = service_name
#         self.ingestion_id = ingestion_id
#         self.db = DatabaseManager()
#         self.session = None

#     async def get_ingestion_details(self):
#         ingestion = self.db.get_ingestion_by_id(self.ingestion_id)
#         self.ingestion = ingestion
#         return ingestion

#     async def get_source(self):
#         return SFTPSource(self.service_name)

#     async def execute_internal(self):
#         source = await self.get_source()
#         profiler = UnstructProfiler()

#         logger.info(f"Starting SFTP workflow execution for {self.service_name}")

#         infra = await source.infra_scan()
#         logger.info(f"SFTP infra scan output: {infra}")
#         infra_data = {
#             "service_name": self.service_name,
#             "total_files": infra.get("total_files", 0),
#             "total_size": infra.get("total_size", 0),
#             "region": infra.get("region", "unknown"),
#         }

#         save_to_warhouse_folder(infra_data)

#         async for record in source.deep_scan():
#             object_size = record.get("size", 0)
#             filepath = record.get("filepath")
#             piis = []

#             token = None
#             if object_size <= PROFILE_MAX_SIZE_THRESHOLD:
#                 token = await profiler.run(record)

#             if filepath:
#                 remove_file(filepath)

#             data = {
#                 "service_name": self.service_name,
#                 "service_type": self.service_type,
#                 "file_key": record.get("name"),
#                 "file_size": object_size,
#                 "file_type": record.get("name", "").split(".")[-1],
#                 "file_uri": record.get("file_uri"),
#                 "piis": piis,
#                 "token": token,
#                 "scan_type": "file_metadata",
#                 "details": {**infra_data},
#             }

#             save_to_warhouse_folder(data)

#         logger.info(f"SFTP workflow execution completed for {self.service_name}")

#     async def calculate_success(self) -> float:
#         return 0.0

#     async def get_failures(self):
#         pass

#     async def execute(self) -> None:
#         try:
#             await self.execute_internal()
#         except Exception as err:
#             logger.error(f"Ingestion failed for service {self.service_name}, error: {err}")
#         finally:
#             await self.stop()

#     async def stop(self) -> None:
#         if self.session and not self.session.closed:
#             logger.info(f"Closing session for {self.service_name}")
#             await self.session.close()
#         logger.info(f"Workflow stopped for {self.service_name}")

#     async def create_ingestion_pipeline(self) -> List[Process]:
#         return [UnstructProfiler()]
