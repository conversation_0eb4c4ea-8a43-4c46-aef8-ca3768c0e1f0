import pycountry
import msal
import aiohttp
import time
import json
from datetime import datetime, timedelta

from src.utils.logger import get_ingestion_logger
from src.utils.exceptions import ServerException


logger = get_ingestion_logger()


def log_graph_api_request(method: str, url: str, headers: dict, body: dict = None, auth_type: str = "Application"):
    """Log complete Graph API request details"""
    # Mask sensitive information in headers
    safe_headers = headers.copy()
    if "Authorization" in safe_headers:
        safe_headers["Authorization"] = "Bearer [REDACTED]"
    
    log_data = {
        "method": method,
        "url": url,
        "headers": safe_headers,
        "authentication_type": auth_type,
        "timestamp": datetime.utcnow().isoformat(),
    }
    
    if body:
        log_data["body"] = body
    
    logger.info(f"Graph API Request: {json.dumps(log_data, indent=2)}")


def get_365_authenticators(tenant_id: str, client_id: str, client_secret: str) -> dict:
    """Get Microsoft 365 access token and return auth details"""
    access_token = None
    authority = f"https://login.microsoftonline.com/{tenant_id}"
    scope = ["https://graph.microsoft.com/.default"]
    
    try:
        app = msal.ConfidentialClientApplication(
            client_id,
            authority=authority,
            client_credential=client_secret,
        )
        
        # Try to get token from cache first
        result = app.acquire_token_silent(scope, account=None)
        if not result:
            logger.info("No token in cache, acquiring new token")
            result = app.acquire_token_for_client(scopes=scope)

        if "access_token" in result:
            access_token = result.get("access_token")
            expires_in = result.get("expires_in", 3600)  # Default 1 hour
            expires_at = time.time() + expires_in
            
            logger.info(f"Authentication successful - Token expires at: {datetime.fromtimestamp(expires_at)}")
            
            return {
                "access_token": access_token,
                "expires_at": expires_at,
                "auth_app": app
            }
        else:
            error_msg = result.get("error_description", "Unknown authentication error")
            logger.error(f"Authentication failed: {error_msg}")
            raise ServerException(f"Authentication failed: {error_msg}")
            
    except Exception as e:
        logger.error(f"Authentication failed: {str(e)}")
        raise ServerException(f"Authentication failed: {str(e)}", excep=e)


def refresh_token_if_needed(auth_app, tenant_id: str, client_id: str, client_secret: str) -> dict:
    """Refresh the access token if needed"""
    try:
        scope = ["https://graph.microsoft.com/.default"]
        
        # Try to acquire a new token
        result = auth_app.acquire_token_for_client(scopes=scope)
        
        if "access_token" in result:
            access_token = result.get("access_token")
            expires_in = result.get("expires_in", 3600)
            expires_at = time.time() + expires_in
            
            logger.info(f"Token refreshed successfully - New token expires at: {datetime.fromtimestamp(expires_at)}")
            
            return {
                "access_token": access_token,
                "expires_at": expires_at,
                "auth_app": auth_app
            }
        else:
            error_msg = result.get("error_description", "Unknown refresh error")
            logger.error(f"Token refresh failed: {error_msg}")
            return None
            
    except Exception as e:
        logger.error(f"Error refreshing token: {str(e)}")
        return None


async def check_user_permissions(access_token: str, user_id: str, user_email: str = None, log_details: bool = False) -> bool:
    """Check if we have permission to access a user's OneDrive"""
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
    }
    url = f"https://graph.microsoft.com/v1.0/users/{user_id}/drive/root"
    if log_details:
        log_graph_api_request("GET", url, headers, None, "Application")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers) as response:
                if response.status == 200:
                    if user_email:
                        logger.info(f"✓ OneDrive accessible for user: {user_email}")
                    return True
                elif response.status in [401, 403]:
                    return False
                elif response.status == 404:
                    return False
                else:
                    if log_details:
                        response_text = await response.text()
                        request_id = response.headers.get("request-id", "N/A")
                        timestamp = response.headers.get("date", "N/A")
                        logger.warning(f"User {user_id} - Unexpected status {response.status} - "
                                     f"Request ID: {request_id}, Timestamp: {timestamp}, Response: {response_text}")
                    return False
                    
    except Exception as e:
        if log_details:
            logger.error(f"Error checking permissions for user {user_id}: {str(e)}")
        return False


async def get_all_users(access_token: str, log_requests: bool = False):
    """Get all users from the tenant"""
    users = []
    endpoint = "https://graph.microsoft.com/v1.0/users"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            request_count = 0
            while endpoint:
                request_count += 1
                if log_requests or request_count == 1:
                    log_graph_api_request("GET", endpoint, headers, None, "Application")
                
                async with session.get(endpoint, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        batch_users = data.get("value", [])
                        users.extend(batch_users)
                        endpoint = data.get("@odata.nextLink")
                        
                        # Only log progress every 1000 users to reduce noise
                        if len(users) % 1000 == 0:
                            logger.info(f"Fetching users progress: {len(users)} users retrieved")
                    elif response.status == 401:
                        response_text = await response.text()
                        request_id = response.headers.get("request-id", "N/A")
                        timestamp = response.headers.get("date", "N/A")
                        logger.error(f"Token expired while fetching users - Status: {response.status}, "
                                   f"Request ID: {request_id}, Timestamp: {timestamp}, Response: {response_text}")
                        break
                    else:
                        response_text = await response.text()
                        request_id = response.headers.get("request-id", "N/A")
                        timestamp = response.headers.get("date", "N/A")
                        logger.error(f"Failed to fetch users - Status: {response.status}, "
                                   f"Request ID: {request_id}, Timestamp: {timestamp}, Response: {response_text}")
                        break
        except Exception as e:
            logger.error(f"Error fetching users: {str(e)}")

    logger.info(f"Total users fetched: {len(users)}")
    return users


async def get_tenant_info(access_token: str):
    """Get tenant information with enhanced error logging"""
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            # Try organization endpoint first
            org_endpoint = "https://graph.microsoft.com/v1.0/organization"
            log_graph_api_request("GET", org_endpoint, headers, None, "Application")
            
            async with session.get(org_endpoint, headers=headers) as response:
                if response.status == 200:
                    org_data = await response.json()
                    if "value" in org_data and org_data["value"]:
                        country_code = org_data["value"][0].get(
                            "countryLetterCode"
                        ) or org_data["value"][0].get("country")
                        if country_code:
                            try:
                                country_name = pycountry.countries.get(alpha_2=country_code).name
                                logger.info(f"Tenant location determined from organization endpoint: {country_name}")
                                return country_name
                            except AttributeError:
                                logger.warning(f"Invalid country code from organization: {country_code}")
                else:
                    response_text = await response.text()
                    request_id = response.headers.get("request-id", "N/A")
                    timestamp = response.headers.get("date", "N/A")
                    logger.warning(f"Organization endpoint failed - Status: {response.status}, "
                                 f"Request ID: {request_id}, Timestamp: {timestamp}")
            tenant_endpoint = "https://graph.microsoft.com/v1.0/tenantRelationships/managedTenants"
            log_graph_api_request("GET", tenant_endpoint, headers, None, "Application")
            
            async with session.get(tenant_endpoint, headers=headers) as response:
                if response.status == 200:
                    tenant_data = await response.json()
                    if "value" in tenant_data and tenant_data["value"]:
                        country_code = tenant_data["value"][0].get("defaultDomainCountry")
                        if country_code:
                            try:
                                country_name = pycountry.countries.get(alpha_2=country_code).name
                                logger.info(f"Tenant location determined from tenant relationships: {country_name}")
                                return country_name
                            except AttributeError:
                                logger.warning(f"Invalid country code from tenant relationships: {country_code}")
                else:
                    response_text = await response.text()
                    request_id = response.headers.get("request-id", "N/A")
                    timestamp = response.headers.get("date", "N/A")
                    logger.warning(f"Tenant relationships endpoint failed - Status: {response.status}, "
                                 f"Request ID: {request_id}, Timestamp: {timestamp}")
            users_endpoint = "https://graph.microsoft.com/v1.0/users?$select=usageLocation,country&$top=1"
            log_graph_api_request("GET", users_endpoint, headers, None, "Application")
            
            async with session.get(users_endpoint, headers=headers) as response:
                if response.status == 200:
                    users_data = await response.json()
                    if "value" in users_data and users_data["value"]:
                        country_code = users_data["value"][0].get(
                            "usageLocation"
                        ) or users_data["value"][0].get("country")
                        if country_code:
                            try:
                                country_name = pycountry.countries.get(alpha_2=country_code).name
                                logger.info(f"Tenant location determined from users endpoint: {country_name}")
                                return country_name
                            except AttributeError:
                                logger.warning(f"Invalid country code from users: {country_code}")
                else:
                    response_text = await response.text()
                    request_id = response.headers.get("request-id", "N/A")
                    timestamp = response.headers.get("date", "N/A")
                    logger.warning(f"Users endpoint failed - Status: {response.status}, "
                                 f"Request ID: {request_id}, Timestamp: {timestamp}")

            logger.warning("Could not determine tenant location from any endpoint")
            return "Unknown"

        except Exception as e:
            logger.error(f"Error fetching tenant info: {str(e)}")
            return "Unknown"


async def get_user_by_email(access_token: str, email: str) -> dict:
    """Get user details by email address"""
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
    }
    email = email.strip()
    if not email:
        return None
    endpoints = [
        f"https://graph.microsoft.com/v1.0/users/{email}",
        f"https://graph.microsoft.com/v1.0/users?$filter=mail eq '{email}'",
        f"https://graph.microsoft.com/v1.0/users?$filter=userPrincipalName eq '{email}'"
    ]
    
    async with aiohttp.ClientSession() as session:
        for i, endpoint in enumerate(endpoints):
            try:
                if i == 0:
                    logger.debug(f"Looking up user: {email}")
                
                async with session.get(endpoint, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        if "value" in data:
                            if data["value"]:
                                user = data["value"][0]
                                logger.debug(f"Found user {email} via filter query")
                                return user
                        else:  # Direct lookup
                            logger.debug(f"Found user {email} via direct lookup")
                            return data
                    elif response.status == 404:
                        continue
                    else:
                        if i == 0:
                            response_text = await response.text()
                            request_id = response.headers.get("request-id", "N/A")
                            timestamp = response.headers.get("date", "N/A")
                            logger.warning(f"Failed to get user {email} - Status: {response.status}, "
                                         f"Request ID: {request_id}, Timestamp: {timestamp}")
                        
            except Exception as e:
                if i == 0:
                    logger.error(f"Error getting user {email}: {str(e)}")
                continue
    return None


async def validate_include_emails(access_token: str, include_emails: list) -> list:
    """Validate and return valid users from include_emails list"""
    valid_users = []
    
    for email in include_emails:
        user = await get_user_by_email(access_token, email)
        if user:
            valid_users.append(user)
            logger.info(f"Validated include email: {email}")
        else:
            logger.warning(f"Include email not found: {email}")
    
    return valid_users