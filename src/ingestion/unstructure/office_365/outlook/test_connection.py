import aiohttp
import logging
from src.utils.exceptions import ClientException, ServerException

logger = logging.getLogger("Unstructure")


async def get_ms_graph_token(tenant_id: str, client_id: str, client_secret: str) -> str:
    token_url = f"https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/token"
    data = {
        "client_id": client_id,
        "client_secret": client_secret,
        "grant_type": "client_credentials",
        "scope": "https://graph.microsoft.com/.default"
    }

    async with aiohttp.ClientSession() as session:
        async with session.post(token_url, data=data) as resp:
            token_data = await resp.json()
            if resp.status != 200:
                logger.warning(f"Token fetch failed: {token_data}")
                raise ClientException("Microsoft token fetch failed", code=resp.status)
            return token_data["access_token"]


async def test_outlook_connection(data: dict) -> dict:
    creds = data.get("credentials", {})
    try:
        logger.info("Testing Microsoft Outlook connection...")

        token = await get_ms_graph_token(creds["tenant_id"], creds["client_id"], creds["client_secret"])
        headers = {"Authorization": f"Bearer {token}"}

        async with aiohttp.ClientSession() as session:
            # Validate token and credentials
            async with session.get("https://graph.microsoft.com/v1.0/organization", headers=headers) as org_resp:
                if org_resp.status != 200:
                    raise ClientException("Invalid Microsoft credentials", code=org_resp.status)

            # Fetch user list
            async with session.get("https://graph.microsoft.com/v1.0/users?$select=id", headers=headers) as users_resp:
                users_data = await users_resp.json()
                if users_resp.status != 200 or "value" not in users_data:
                    raise ClientException("Failed to retrieve Outlook user list", code=users_resp.status)

                user_id = users_data["value"][0]["id"]
                inbox_url = f"https://graph.microsoft.com/v1.0/users/{user_id}/mailFolders/inbox/messages?$top=1"
                async with session.get(inbox_url, headers=headers) as inbox_resp:
                    if inbox_resp.status == 200:
                        logger.info("Outlook inbox access validated successfully.")
                        return {"status": True, "message": "Outlook mail access validated"}
                    else:
                        msg = await inbox_resp.json()
                        raise ClientException(f"Outlook inbox access failed: {msg}", code=inbox_resp.status)

    except ClientException:
        raise
    except Exception as e:
        logger.exception("Unexpected error during Outlook connection test")
        raise ServerException("Outlook test failed", excep=e)
