import asyncio
import base64
import json
import re
import time
from datetime import datetime
from typing import As<PERSON><PERSON>enerator, Dict, List
from uuid import uuid4

import aiohttp

from src.common.config import (
    CONCURRENT_LIMIT,
    PROFILE_MAX_SIZE_THRESHOLD,
    PROFILE_SUPPORTED_FILE_TYPES,
    TEMP_DATA_DIR,
)
from src.common.constants import M365SubServices, ServiceProviders, ServiceTypes
from src.ingestion.base_source import UnstructureSource
from src.ingestion.data_class import FileMetadata
from src.ingestion.unstructure.office_365.connection import (
    get_365_authenticators,
    get_all_users,
    get_tenant_info,
    refresh_token_if_needed,
    log_graph_api_request,
    get_user_by_email,
)
from src.modules.repos import DatabaseManager
from src.utils.helpers import create_folder, file_exists, get_file_extension
from src.utils.logger import get_ingestion_logger
from src.utils.loaders import load_access_controls,load_asset_details
from src.ingestion.data_class import AssetsDetails as AssetDetailsSchema

logger = get_ingestion_logger()


class OutlookSource(UnstructureSource):
    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.MS_365_OUTLOOK.value, service_name)
        self.db = DatabaseManager()
        self.service_provider = ServiceProviders.M365
        self.sub_service = M365SubServices.MS_OUTLOOK
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"
        self.access_token = None
        self.token_expires_at = None
        self.auth_app = None

    def sanitize_filename(self, filename: str) -> str:
        """Sanitize filename to make it safe for file systems"""
        # Remove invalid characters
        sanitized = re.sub(r'[<>:"/\\|?*\x00-\x1f]', '', filename)
        # Replace spaces and special chars with underscore
        sanitized = re.sub(r'[\s]+', '_', sanitized)
        # Remove leading/trailing underscores and dots
        sanitized = sanitized.strip('_. ')
        # Limit length
        if len(sanitized) > 100:
            sanitized = sanitized[:100]
        # If empty, use default
        if not sanitized:
            sanitized = 'unnamed_email'
        return sanitized

    async def get_service_details(self) -> Dict:
        """Gets service details"""
        self.service = await self.db.get_service_by_service_name(self.service_name)
        credentials = self.service.get("credentials", {})
        self.tenant_id = credentials.get("tenant_id")
        self.client_id = credentials.get("client_id")
        self.client_secret = credentials.get("client_secret")
        include_emails = credentials.get("include_emails", "")
        exclude_emails = credentials.get("exclude_emails", "")
        self.service_steward = self.service.get("data_steward")
        
        # Parse include_emails - handle multiple formats
        if include_emails:
            if isinstance(include_emails, str):
                # Handle comma, semicolon, or space separated emails
                email_list = re.split(r'[,;\s]+', include_emails.strip())
                self.include_emails = [e.strip() for e in email_list if e.strip()]
            elif isinstance(include_emails, list):
                self.include_emails = [str(e).strip() for e in include_emails if str(e).strip()]
            else:
                self.include_emails = []
        else:
            self.include_emails = []
            
        # Parse exclude_emails
        if exclude_emails:
            if isinstance(exclude_emails, str):
                email_list = re.split(r'[,;\s]+', exclude_emails.strip())
                self.exclude_emails = [e.strip() for e in email_list if e.strip()]
            elif isinstance(exclude_emails, list):
                self.exclude_emails = [str(e).strip() for e in exclude_emails if str(e).strip()]
            else:
                self.exclude_emails = []
        else:
            self.exclude_emails = []
        
        # Get initial token and set up authentication
        auth_result = get_365_authenticators(
            self.tenant_id, self.client_id, self.client_secret
        )
        self.access_token = auth_result["access_token"]
        self.token_expires_at = auth_result.get("expires_at")
        self.auth_app = auth_result.get("auth_app")
        
        return self.service

    async def ensure_valid_token(self) -> bool:
        """Ensure we have a valid access token, refresh if needed"""
        try:
            if self.token_expires_at and time.time() >= self.token_expires_at - 300:  # Refresh 5 min before expiry
                logger.info("Access token expired or expiring soon, refreshing...")
                auth_result = refresh_token_if_needed(
                    self.auth_app, self.tenant_id, self.client_id, self.client_secret
                )
                if auth_result:
                    self.access_token = auth_result["access_token"]
                    self.token_expires_at = auth_result.get("expires_at")
                    logger.info("Access token refreshed successfully")
                    return True
                else:
                    logger.error("Failed to refresh access token")
                    return False
            return True
        except Exception as e:
            logger.error(f"Error ensuring valid token: {str(e)}")
            return False

    async def test_connection(self) -> Dict:
        try:
            if not await self.ensure_valid_token():
                return {"status": False, "message": "Token refresh failed"}
                
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json",
            }
            
            url = "https://graph.microsoft.com/v1.0/organization"
            log_graph_api_request("GET", url, headers, None, "Application")
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    response_text = await response.text()
                    
                    if response.status == 200:
                        logger.info(f"Graph API Response: {response.status} - Authentication successful")
                        return {"status": True, "message": "Authentication successful"}
                    else:
                        try:
                            error_data = json.loads(response_text)
                            error_msg = error_data.get("error", {}).get("message", "Unknown error")
                            error_code = error_data.get("error", {}).get("code", "Unknown")
                            request_id = response.headers.get("request-id", "N/A")
                            timestamp = response.headers.get("date", "N/A")
                        except:
                            error_msg = response_text
                            error_code = "Unknown"
                            request_id = "N/A"
                            timestamp = "N/A"
                            
                        logger.error(f"Graph API Error - Status: {response.status}, Code: {error_code}, "
                                   f"Message: {error_msg}, Request ID: {request_id}, Timestamp: {timestamp}")
                        return {
                            "status": False,
                            "message": f"Token validation failed: {error_msg}",
                        }
        except Exception as e:
            logger.error(f"Error verifying credentials: {str(e)}")
            return {"status": False, "message": str(e)}

    async def get_accessible_users(self) -> List[Dict]:
        """Get list of users we have permission to access"""
        accessible_users = []
        
        # If include_emails is specified, prioritize those users
        if self.include_emails:
            logger.info(f"Using specified include_emails: {self.include_emails}")
            
            for email in self.include_emails:
                user = await get_user_by_email(self.access_token, email.strip())
                if user:
                    accessible_users.append(user)
                    logger.info(f"✓ User {email} found and accessible")
                else:
                    logger.warning(f"✗ User with email {email} not found in tenant")
        else:
            # Get all users if no specific emails provided
            logger.info("No specific emails provided, fetching all users...")
            all_users = await get_all_users(self.access_token, log_requests=False)
            
            for user in all_users:
                user_mail = user.get("mail")
                user_principal = user.get("userPrincipalName")
                user_email = user_mail if user_mail else user_principal
                
                if not user_email:
                    continue
                
                # Skip excluded emails
                if self.exclude_emails:
                    user_email_lower = user_email.lower()
                    if any(user_email_lower == excluded.lower() for excluded in self.exclude_emails):
                        continue
                
                accessible_users.append(user)
        
        logger.info(f"✓ Found {len(accessible_users)} accessible users for Outlook scanning")
        return accessible_users

    async def fetch_user_outlook_messages(self, user_id: str, user_email: str) -> list:
        """Fetch all messages for a user, handling pagination."""
        if not await self.ensure_valid_token():
            logger.error(f"Cannot fetch messages for user {user_id} - token refresh failed")
            return []
            
        messages = []
        url = f"https://graph.microsoft.com/v1.0/users/{user_id}/messages?$expand=attachments"
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
        }

        async with aiohttp.ClientSession() as session:
            while url:
                log_graph_api_request("GET", url, headers, None, "Application")
                
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        batch_messages = data.get("value", [])
                        messages.extend(batch_messages)
                        url = data.get("@odata.nextLink")
                        
                        # Log progress for large message sets
                        if len(messages) % 1000 == 0:
                            logger.info(f"Message fetch progress for {user_email}: {len(messages)} messages retrieved")
                    elif response.status == 401:
                        response_text = await response.text()
                        request_id = response.headers.get("request-id", "N/A")
                        timestamp = response.headers.get("date", "N/A")
                        logger.error(f"Token expired while fetching messages for {user_email} - Status: {response.status}, "
                                   f"Request ID: {request_id}, Timestamp: {timestamp}")
                        break
                    else:
                        response_text = await response.text()
                        request_id = response.headers.get("request-id", "N/A")
                        timestamp = response.headers.get("date", "N/A")
                        logger.error(f"Failed to fetch messages for {user_email} - Status: {response.status}, "
                                   f"Request ID: {request_id}, Timestamp: {timestamp}, Response: {response_text}")
                        break
        
        logger.info(f"Total messages fetched for {user_email}: {len(messages)}")
        return messages

    async def download_outlook_attachment(
        self, attachment_id: str, message_id: str, user_id: str, filepath: str, user_email: str
    ):
        """Handle Outlook email attachments download and storage."""
        if file_exists(filepath):
            return True

        if not await self.ensure_valid_token():
            logger.error(f"Cannot download attachment for user {user_email} - token refresh failed")
            return False

        async with aiohttp.ClientSession() as session:
            try:
                url = f"https://graph.microsoft.com/v1.0/users/{user_id}/messages/{message_id}/attachments/{attachment_id}"
                headers = {"Authorization": f"Bearer {self.access_token}"}
                
                log_graph_api_request("GET", url, headers, None, "Application")
                
                async with session.get(url, headers=headers) as attachment_response:
                    if attachment_response.status == 200:
                        attachment_content = await attachment_response.json()
                        content_bytes = base64.b64decode(attachment_content["contentBytes"])
                        with open(filepath, "wb") as f:
                            f.write(content_bytes)
                        logger.debug(f"Successfully downloaded attachment to {filepath}")
                        return True
                    else:
                        response_text = await attachment_response.text()
                        request_id = attachment_response.headers.get("request-id", "N/A")
                        timestamp = attachment_response.headers.get("date", "N/A")
                        logger.error(f"Failed to download attachment for {user_email} - Status: {attachment_response.status}, "
                                   f"Request ID: {request_id}, Timestamp: {timestamp}, Response: {response_text}")
                        return False
            except Exception as e:
                logger.error(f"Error processing attachment for {user_email}: {str(e)}")
                return False

    async def save_email_body_as_txt(
        self, message: dict, user_email: str, formatted_date: str, safe_subject: str, semaphore
    ) -> Dict:
        """Save email body as txt file"""
        try:
            # Get email body content
            body = message.get("body", {})
            body_content = body.get("content", "")
            
            # Skip if no body content
            if not body_content or body_content.strip() == "":
                return None
            
            # Create filename from subject
            subject = message.get("subject", "No_Subject")
            sanitized_subject = self.sanitize_filename(subject)
            filename = f"{sanitized_subject}.txt"
            
            # Create file_uri
            file_uri = f"outlook/{user_email}/{formatted_date}/{safe_subject}/{filename}"
            
            # Check skip rules
            if self.rule_manager.skip_file(file_uri):
                logger.info(f"Skipping email body {filename} based on skip rules")
                return None
            
            # Calculate size
            body_size = len(body_content.encode('utf-8'))
            
            # Check size threshold
            if body_size > PROFILE_MAX_SIZE_THRESHOLD:
                logger.info(f"Skipping email body {filename}: Size {body_size} exceeds threshold")
                return None
            
            # Check file type (txt should be supported)
            file_ext = "txt"
            if file_ext not in PROFILE_SUPPORTED_FILE_TYPES:
                logger.info(f"Skipping email body {filename}: File type .{file_ext} not supported")
                return None
            
            # Create temp directory
            temp_dir = self.local_data_dir
            await asyncio.to_thread(create_folder, temp_dir)
            filepath = f"{temp_dir}/{str(uuid4())}_{filename}"
            
            # Save body content to txt file
            async with semaphore:
                await asyncio.to_thread(self._write_file, filepath, body_content)
            
            logger.debug(f"Successfully saved email body to {filepath}")
            
            # Get metadata
            received_datetime = message.get("receivedDateTime")
            sender_email = message.get("from", {}).get("emailAddress", {}).get("address", "unknown-sender")
            
            return {
                "name": filename,
                "size": body_size,
                "file_type": file_ext,
                "file_uri": file_uri,
                "filepath": filepath,
                "created": received_datetime,
                "modified": received_datetime,
                "success": True,
                "user_email": user_email,
                "sender_email": sender_email,
                "message_subject": subject,
            }
            
        except Exception as e:
            logger.error(f"Error saving email body as txt: {str(e)}")
            return None

    def _write_file(self, filepath: str, content: str):
        """Helper method to write file synchronously"""
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)

    async def fetch_outlook_attachment(
        self, attachment: dict, message: dict, user_id: str, user_email: str, semaphore
    ) -> Dict:
        filename = attachment.get("name", "")
        attachment_id = attachment.get("id")
        message_id = message.get("id", "")
        subject = message.get("subject", "No Subject")
        received_datetime = message.get("receivedDateTime")
        sender_email = message.get("from", {}).get("emailAddress", {}).get("address", "unknown-sender")
        
        safe_subject = re.sub(r"[^\w\-_. ]", "_", subject).strip().replace(" ", "_")
        if received_datetime:
            try:
                parsed_date = datetime.fromisoformat(received_datetime.replace("Z", "+00:00"))
                formatted_date = parsed_date.strftime("%Y-%m-%d")
            except Exception:
                formatted_date = "unknown-date"
        else:
            formatted_date = "unknown-date"
            
        file_uri = f"outlook/{user_email}/{formatted_date}/{safe_subject}/{filename}"

        # Skip logic
        if self.rule_manager.skip_file(file_uri):
            logger.info(f"Skipping file {filename} based on skip rules")
            return None

        object_size = attachment.get("size", 0)
        if object_size > PROFILE_MAX_SIZE_THRESHOLD:
            logger.info(f"Skipping {filename}: Size {object_size} exceeds threshold")
            return None

        file_ext = get_file_extension(filename)
        if file_ext not in PROFILE_SUPPORTED_FILE_TYPES:
            logger.info(f"Skipping {filename}: Unsupported file type .{file_ext}")
            return None

        # Create temp directory
        temp_dir = self.local_data_dir
        create_folder(temp_dir)
        filepath = f"{temp_dir}/{str(uuid4())}_{filename}"

        async with semaphore:
            success = await self.download_outlook_attachment(
                attachment_id, message_id, user_id, filepath, user_email
            )

        return {
            "name": filename,
            "size": object_size,
            "file_type": file_ext,
            "file_uri": file_uri,
            "filepath": filepath,
            "created": received_datetime,
            "modified": received_datetime,
            "success": success,
            "user_email": user_email,
            "sender_email": sender_email,
            "message_subject": subject,
        }

    async def scan_user_outlook_infra(self, user_id: str, user_email: str) -> Dict:
        """Scan user's Outlook for infrastructure information"""
        if not await self.ensure_valid_token():
            return {"total_messages": 0, "total_attachments": 0}
            
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
        }
        
        total_messages = 0
        total_attachments = 0
        
        url = f"https://graph.microsoft.com/v1.0/users/{user_id}/messages?$select=id,hasAttachments&$top=100"
        log_graph_api_request("GET", url, headers, None, "Application")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        messages = data.get("value", [])
                        total_messages = len(messages)
                        total_attachments = sum(1 for msg in messages if msg.get("hasAttachments"))
                        logger.info(f"Outlook infra scan for {user_email}: {total_messages} messages, {total_attachments} with attachments")
                    else:
                        response_text = await response.text()
                        request_id = response.headers.get("request-id", "N/A")
                        timestamp = response.headers.get("date", "N/A")
                        logger.error(f"Outlook infra scan failed for {user_email} - Status: {response.status}, "
                                   f"Request ID: {request_id}, Timestamp: {timestamp}")
        except Exception as e:
            logger.error(f"Outlook infra scan failed for {user_email}: {str(e)}")
            
        return {"total_messages": total_messages, "total_attachments": total_attachments}

    async def scan_user_outlook_deep(self, user_id: str, user_email: str) -> AsyncGenerator[FileMetadata, None]:
        """Perform deep scan of user's Outlook messages and attachments"""
        messages = await self.fetch_user_outlook_messages(user_id, user_email)
        if not messages:
            logger.info(f"No messages found for user {user_email}")
            return
            
        semaphore = asyncio.Semaphore(CONCURRENT_LIMIT)
        body_tasks = []
        attachment_tasks = []
        
        logger.info(f"Processing {len(messages)} messages for user {user_email}")
        
        for message in messages:
            # Prepare common data for this message
            subject = message.get("subject", "No Subject")
            received_datetime = message.get("receivedDateTime")
            safe_subject = re.sub(r"[^\w\-_. ]", "_", subject).strip().replace(" ", "_")
            
            if received_datetime:
                try:
                    parsed_date = datetime.fromisoformat(received_datetime.replace("Z", "+00:00"))
                    formatted_date = parsed_date.strftime("%Y-%m-%d")
                except Exception:
                    formatted_date = "unknown-date"
            else:
                formatted_date = "unknown-date"
            
            # Process email body as txt file
            body_tasks.append(
                self.save_email_body_as_txt(
                    message, user_email, formatted_date, safe_subject, semaphore
                )
            )
            
            # Process attachments
            attachments = message.get("attachments", [])
            for attachment in attachments:
                # Only process file attachments, skip inline/embedded attachments
                if attachment.get("@odata.type") == "#microsoft.graph.fileAttachment":
                    attachment_tasks.append(
                        self.fetch_outlook_attachment(
                            attachment, message, user_id, user_email, semaphore
                        )
                    )

        # Process email bodies first
        email_body_count = 0
        logger.info(f"Processing {len(body_tasks)} email bodies for user {user_email}")
        for future in asyncio.as_completed(body_tasks):
            file_info = await future
            if file_info and file_info.get("success"):
                email_body_count += 1
                yield FileMetadata(
                    service_name=self.service_name,
                    service_type=self.service_type,
                    service_provider=self.service_provider,
                    sub_service=self.sub_service,
                    file_key=file_info["name"],
                    file_size=file_info["size"],
                    file_type=file_info["file_type"],
                    file_uri=file_info["file_uri"],
                    local_filepath=file_info["filepath"],
                    details={
                        "created": file_info["created"],
                        "modified": file_info["modified"],
                        "user_email": file_info["user_email"],
                        "sender_email": file_info["sender_email"],
                        "message_subject": file_info["message_subject"],
                        "region": "India",
                        "content_type": "email_body",
                    },
                )
        
        logger.info(f"Processed {email_body_count} email bodies for user {user_email}")
        
        # Process attachments
        attachment_count = 0
        logger.info(f"Processing {len(attachment_tasks)} attachments for user {user_email}")
        for future in asyncio.as_completed(attachment_tasks):
            file_info = await future
            if file_info and file_info.get("success"):
                attachment_count += 1
                yield FileMetadata(
                    service_name=self.service_name,
                    service_type=self.service_type,
                    service_provider=self.service_provider,
                    sub_service=self.sub_service,
                    file_key=file_info["name"],
                    file_size=file_info["size"],
                    file_type=file_info["file_type"],
                    file_uri=file_info["file_uri"],
                    local_filepath=file_info["filepath"],
                    details={
                        "created": file_info["created"],
                        "modified": file_info["modified"],
                        "user_email": file_info["user_email"],
                        "sender_email": file_info["sender_email"],
                        "message_subject": file_info["message_subject"],
                        "region": "India",
                        "content_type": "attachment",
                    },
                )
        
        logger.info(f"Processed {attachment_count} attachments for user {user_email}")
        logger.info(f"Total processed for user {user_email}: {email_body_count} email bodies + {attachment_count} attachments = {email_body_count + attachment_count} items")

    async def infra_scan(self) -> Dict:
        try:
            await self.get_service_details()
            tenant_region = await get_tenant_info(self.access_token)
            accessible_users = await self.get_accessible_users()
            
            infra = {
                "tenant_region": tenant_region,
                "users": accessible_users,
                "accessible_users_count": len(accessible_users),
            }
            logger.info(f"Outlook infrastructure scan completed - {len(accessible_users)} accessible users found")
            return infra
        except Exception as e:
            logger.error(f"Error during outlook infra scanning: {str(e)}", exc_info=True)
            return {"tenant_region": "", "users": [], "accessible_users_count": 0}

    async def deep_scan(self) -> AsyncGenerator[FileMetadata, None]:
        logger.info("Starting Outlook deep scan")

        total_file_count = 0
        total_file_size = 0
        access = []

        try:
            await self.get_service_details()
            accessible_users = await self.get_accessible_users()
            
            if not accessible_users:
                logger.warning("No accessible users found for Outlook scanning")
                return

            logger.info(f"Starting Outlook deep scan for {len(accessible_users)} accessible users")
            
            for user in accessible_users:
                user_email = user.get("mail") or user.get("userPrincipalName")
                user_id = user.get("id")
                
                if not user_email or not user_id:
                    logger.warning(f"Skipping user with missing email or ID: {user}")
                    continue

                logger.info(f"Scanning Outlook for user: {user_email} ({user_id})")
                
                async for file_metadata in self.scan_user_outlook_deep(user_id, user_email):
                    # Track totals
                    total_file_count += 1
                    total_file_size += file_metadata.file_size or 0

                    # Enrich file metadata
                    file_metadata.details.update({
                        "user_id": user_id,
                        "user_name": user.get("displayName", ""),
                    })
                    yield file_metadata

                # Add access control entry for this user
                access.append({
                    "asset_name": f"outlook_{self.service_name}",
                    "user_or_role": user_email,
                    "role": "user",
                    "access": "full",
                })
                logger.info(f"--> Access control added for {user_email} successfully.")

            # After scanning all users, store asset details
            asset = [
                AssetDetailsSchema(
                    asset_name=f"outlook_{self.service_name}",
                    service_provider=self.service_provider,
                    type="file_storage",
                    category="unstructured",
                    location="Global",
                    owner="system",
                    security=True,
                    size=total_file_size,
                    count=total_file_count,
                    access_category="restricted",
                    service_name=str(self.service_name),
                    steward=str(self.service_steward) if self.service_steward else ""
                )
            ]

            load_asset_details(asset)
            logger.info(f"[Outlook] Asset created with size={total_file_size}, count={total_file_count}")

            if access:
                load_access_controls(access)
                logger.info(f"[Outlook] ------------------> Access control created with {len(access)} users")

            logger.info("Outlook deep scan completed for all accessible users")

        except Exception as e:
            logger.error(f"Error during outlook deep scanning: {str(e)}", exc_info=True)
            raise