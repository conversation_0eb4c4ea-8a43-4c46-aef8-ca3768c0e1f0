import aiohttp
import logging
from src.utils.exceptions import ClientException, ServerException

logger = logging.getLogger("Unstructure")


async def get_ms_graph_token(tenant_id: str, client_id: str, client_secret: str) -> str:
    try:
        token_url = f"https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/token"
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        data = {
            "client_id": client_id,
            "client_secret": client_secret,
            "grant_type": "client_credentials",
            "scope": "https://graph.microsoft.com/.default",
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(token_url, headers=headers, data=data) as resp:
                token_data = await resp.json()
                if resp.status != 200:
                    logger.warning(f"Token fetch failed: {token_data}")
                    raise ClientException("Microsoft token fetch failed", code=resp.status)
                return token_data["access_token"]
    except ClientException:
        raise
    except Exception as e:
        logger.exception("Error while fetching Microsoft Graph token")
        raise ServerException("Token fetch failed", excep=e)


async def test_onedrive_connection(data: dict) -> dict:
    creds = data.get("credentials", {})
    try:
        logger.info("Testing Microsoft OneDrive connection...")

        tenant_id = creds.get("tenant_id")
        client_id = creds.get("client_id")
        client_secret = creds.get("client_secret")

        if not tenant_id or not client_id or not client_secret:
            raise ClientException("Missing Microsoft credentials", code=400)

        access_token = await get_ms_graph_token(tenant_id, client_id, client_secret)

        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        async with aiohttp.ClientSession() as session:
            # Step 1: Validate token
            async with session.get("https://graph.microsoft.com/v1.0/organization", headers=headers) as org_resp:
                if org_resp.status != 200:
                    msg = await org_resp.json()
                    raise ClientException(f"Token validation failed: {msg}", code=org_resp.status)

            # Step 2: Test OneDrive root read access
            drive_url = "https://graph.microsoft.com/v1.0/drive/root/children?$top=1"
            async with session.get(drive_url, headers=headers) as drive_resp:
                if drive_resp.status == 200:
                    logger.info("OneDrive root folder read access validated successfully.")
                    return {
                        "status": True,
                        "message": "OneDrive connection and read permission validated"
                    }
                else:
                    msg = await drive_resp.json()
                    raise ClientException(f"OneDrive read failed: {msg}", code=drive_resp.status)

    except ClientException:
        raise
    except Exception as e:
        logger.exception("Unexpected error during OneDrive test connection")
        raise ServerException("OneDrive test failed", excep=e)
