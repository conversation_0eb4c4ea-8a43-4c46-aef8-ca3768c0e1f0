import asyncio
import json
from dataclasses import asdict
from typing import AsyncGenerator, Dict, List, Optional
from uuid import uuid4
import time
import zipfile
import aiohttp
import io
import os 
from src.common.config import (CONCURRENT_LIMIT, PROFILE_MAX_SIZE_THRESHOLD, PROFILE_SUPPORTED_FILE_TYPES, TEMP_DATA_DIR)
from src.common.constants import (M365SubServices, ServiceProviders, ServiceTypes)
from src.ingestion.base_source import UnstructureSource
from src.ingestion.data_class import FileMetadata
from src.ingestion.unstructure.office_365.connection import (
    get_365_authenticators, get_all_users, get_tenant_info,
    check_user_permissions, refresh_token_if_needed, log_graph_api_request, get_user_by_email)
from src.modules.repos import DatabaseManager
from src.utils.helpers import create_folder, file_exists, get_file_extension
from src.utils.logger import get_ingestion_logger
from src.utils.loaders import load_access_controls,load_asset_details
from src.ingestion.data_class import AssetsDetails as AssetDetailsSchema
 
logger = get_ingestion_logger()

 
class OneDriveSource(UnstructureSource):
    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.MS_365_ONEDRIVE.value, service_name)
        self.db = DatabaseManager()
        self.service_provider = ServiceProviders.M365
        self.sub_service = M365SubServices.MS_ONEDRIVE
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"
        self.access_token = None
        self.token_expires_at = None
        self.auth_app = None
        self.minio_client = None  # Initialize MinIO client if needed
 
    async def get_service_details(self) -> Dict:
        """Gets service details"""
        self.service = await self.db.get_service_by_service_name(self.service_name)
        credentials = self.service.get("credentials", {})
        self.tenant_id = credentials.get("tenant_id")
        self.client_id = credentials.get("client_id")
        self.client_secret = credentials.get("client_secret")
        include_emails = credentials.get("include_emails", "")
        exclude_emails = credentials.get("exclude_emails", "")
        self.service_steward = self.service.get("data_steward")
        
        # Parse include_emails - handle multiple formats
        if include_emails:
            if isinstance(include_emails, str):
                import re
                email_list = re.split(r'[,;\s]+', include_emails.strip())
                self.include_emails = [e.strip() for e in email_list if e.strip()]
            elif isinstance(include_emails, list):
                self.include_emails = [str(e).strip() for e in include_emails if str(e).strip()]
            else:
                self.include_emails = []
        else:
            self.include_emails = []
            
        # Parse exclude_emails
        if exclude_emails:
            if isinstance(exclude_emails, str):
                import re
                email_list = re.split(r'[,;\s]+', exclude_emails.strip())
                self.exclude_emails = [e.strip() for e in email_list if e.strip()]
            elif isinstance(exclude_emails, list):
                self.exclude_emails = [str(e).strip() for e in exclude_emails if str(e).strip()]
            else:
                self.exclude_emails = []
        else:
            self.exclude_emails = []
        
        # Get initial token and set up authentication
        auth_result = get_365_authenticators(
            self.tenant_id, self.client_id, self.client_secret
        )
        self.access_token = auth_result["access_token"]
        self.token_expires_at = auth_result.get("expires_at")
        self.auth_app = auth_result.get("auth_app")
        
        return self.service
 
    async def ensure_valid_token(self) -> bool:
        """Ensure we have a valid access token, refresh if needed"""
        try:
            if self.token_expires_at and time.time() >= self.token_expires_at - 300:
                logger.info("Access token expired or expiring soon, refreshing...")
                auth_result = refresh_token_if_needed(
                    self.auth_app, self.tenant_id, self.client_id, self.client_secret
                )
                if auth_result:
                    self.access_token = auth_result["access_token"]
                    self.token_expires_at = auth_result.get("expires_at")
                    logger.info("Access token refreshed successfully")
                    return True
                else:
                    logger.error("Failed to refresh access token")
                    return False
            return True
        except Exception as e:
            logger.error(f"Error ensuring valid token: {str(e)}")
            return False
 
    async def test_connection(self) -> Dict:
        try:
            if not await self.ensure_valid_token():
                return {"status": False, "message": "Token refresh failed"}
                
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json",
            }
            
            url = "https://graph.microsoft.com/v1.0/organization"
            log_graph_api_request("GET", url, headers, None, "Application")
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    response_text = await response.text()
                    
                    if response.status == 200:
                        logger.info(f"Graph API Response: {response.status} - Authentication successful")
                        return {"status": True, "message": "Authentication successful"}
                    else:
                        try:
                            error_data = json.loads(response_text)
                            error_msg = error_data.get("error", {}).get("message", "Unknown error")
                            error_code = error_data.get("error", {}).get("code", "Unknown")
                            request_id = response.headers.get("request-id", "N/A")
                            timestamp = response.headers.get("date", "N/A")
                        except:
                            error_msg = response_text
                            error_code = "Unknown"
                            request_id = "N/A"
                            timestamp = "N/A"
                            
                        logger.error(f"Graph API Error - Status: {response.status}, Code: {error_code}, "
                                   f"Message: {error_msg}, Request ID: {request_id}, Timestamp: {timestamp}")
                        return {
                            "status": False,
                            "message": f"Token validation failed: {error_msg}",
                        }
        except Exception as e:
            logger.error(f"Error verifying credentials: {str(e)}")
            return {"status": False, "message": str(e)}
 
    async def get_accessible_users(self) -> List[Dict]:
        """Get list of users we have permission to access"""
        accessible_users = []
        
        if self.include_emails:
            logger.info(f"Using specified include_emails: {self.include_emails}")
            
            for email in self.include_emails:
                user = await get_user_by_email(self.access_token, email.strip())
                if user:
                    has_permission = await check_user_permissions(self.access_token, user["id"], email, log_details=True)
                    if has_permission:
                        accessible_users.append(user)
                    else:
                        logger.warning(f"User {email} found but no OneDrive permission")
                else:
                    logger.warning(f"User with email {email} not found in tenant")
        else:
            logger.info("No specific emails provided, checking all users for OneDrive permissions...")
            all_users = await get_all_users(self.access_token, log_requests=False)
            
            logger.info(f"Checking OneDrive permissions for {len(all_users)} users...")
            
            batch_size = 100
            processed = 0
            
            for i in range(0, len(all_users), batch_size):
                batch_users = all_users[i:i+batch_size]
                
                for user in batch_users:
                    user_mail = user.get("mail")
                    user_principal = user.get("userPrincipalName")
                    user_email = user_mail if user_mail else user_principal
                    
                    if not user_email:
                        logger.debug(f"Skipping user {user.get('id')} - no email found")
                        continue
                    
                    if self.exclude_emails:
                        user_email_lower = user_email.lower()
                        if any(user_email_lower == excluded.lower() for excluded in self.exclude_emails):
                            continue
                    
                    has_permission = await check_user_permissions(self.access_token, user["id"], user_email, log_details=False)
                    if has_permission:
                        accessible_users.append(user)
                
                processed += len(batch_users)
                if processed % 1000 == 0:
                    logger.info(f"Permission check progress: {processed}/{len(all_users)} users processed, {len(accessible_users)} accessible")
        
        logger.info(f"Found {len(accessible_users)} accessible users for OneDrive scanning")
        
        if accessible_users:
            logger.info("Accessible users for OneDrive scanning:")
            for user in accessible_users[:10]:
                user_email = user.get("mail") or user.get("userPrincipalName") or "No email"
                logger.info(f"  - {user_email}")
            if len(accessible_users) > 10:
                logger.info(f"  ... and {len(accessible_users) - 10} more users")
        
        return accessible_users

    async def extract_zip_recursive(self, zip_content: bytes, base_path: str, depth: int = 0) -> List[Dict]:
        """Recursively extract zip files and return list of extracted files"""
        MAX_DEPTH = 5  # Prevent infinite recursion
        extracted_files = []
        
        if depth > MAX_DEPTH:
            logger.warning(f"Max zip extraction depth {MAX_DEPTH} reached")
            return extracted_files
        
        try:
            with zipfile.ZipFile(io.BytesIO(zip_content)) as zip_ref:
                for zip_info in zip_ref.namelist():
                    if zip_info.endswith('/'):
                        continue
                    
                    file_data = zip_ref.read(zip_info)
                    file_ext = get_file_extension(zip_info)
                    
                    # Create unique file path
                    extracted_path = f"{base_path}/{zip_info}"

                    # Skip Logic 1: Rule-based skip check
                    if hasattr(self, 'rule_manager') and self.rule_manager and self.rule_manager.skip_file(extracted_path):
                        logger.info(f"Skipping extracted file {zip_info} based on skip rules")
                        continue

                    # Skip Logic 2: File type check
                    if file_ext not in PROFILE_SUPPORTED_FILE_TYPES and file_ext != 'zip':
                        logger.info(f"Skipping extracted file {zip_info}: Unsupported file type .{file_ext}")
                        continue

                    # Skip Logic 3: File size check
                    if len(file_data) > PROFILE_MAX_SIZE_THRESHOLD:
                        logger.info(f"Skipping extracted file {zip_info}: Size {len(file_data)} exceeds threshold {PROFILE_MAX_SIZE_THRESHOLD}")
                        continue
                    
                    # Check if it's another zip file
                    if file_ext == 'zip':
                        logger.info(f"Found nested zip at depth {depth}: {zip_info}")
                        nested_files = await self.extract_zip_recursive(file_data, extracted_path, depth + 1)
                        extracted_files.extend(nested_files)
                    else:
                        # Save extracted file temporarily
                        temp_file_path = f"{self.local_data_dir}/{uuid4()}_{os.path.basename(zip_info)}"
                        with open(temp_file_path, 'wb') as f:
                            f.write(file_data)
                        
                        extracted_files.append({
                            'original_path': extracted_path,
                            'temp_path': temp_file_path,
                            'file_name': os.path.basename(zip_info),
                            'file_type': file_ext,
                            'size': len(file_data),
                            'data': file_data  # Store data for DSR usage
                        })
                        
                        logger.debug(f"Extracted: {extracted_path}")
        
        except zipfile.BadZipFile:
            logger.error(f"Bad zip file at {base_path}")
        except Exception as e:
            logger.error(f"Error extracting zip {base_path}: {str(e)}")
        
        return extracted_files

    async def download_file_from_onedrive(self, web_url: str, filepath: str):
        """Download file from OneDrive"""
        if file_exists(filepath):
            return
 
        if not await self.ensure_valid_token():
            logger.error("Cannot download file - token refresh failed")
            return
 
        async with aiohttp.ClientSession() as session:
            try:
                log_graph_api_request("GET", web_url, {"Authorization": f"Bearer {self.access_token}"}, None, "Application")
                
                async with session.get(web_url) as response:
                    if response.status == 200:
                        content = await response.read()
                        with open(filepath, "wb") as f:
                            f.write(content)
                        logger.debug(f"Successfully downloaded file to {filepath}")
                    else:
                        response_text = await response.text()
                        logger.error(f"Failed to download file - Status: {response.status}, Response: {response_text}")
            except Exception as e:
                logger.error(f"Error downloading file from {web_url}: {str(e)}")

    async def search_file_in_user_onedrive(self, user_id: str, user_email: str, file_name: str, file_path: str = None) -> Optional[Dict]:
        """Search for a specific file in user's OneDrive by name and optional path"""
        if not await self.ensure_valid_token():
            logger.error(f"Cannot search file for user {user_id} - token refresh failed")
            return None
        
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
        }
        
        try:
            # Normalize incoming file_path (collapse duplicate slashes, trim spaces)
            def normalize_path(s: str) -> str:
                if not s:
                    return ""
                s = s.replace("\\", "/").strip()
                while '//' in s:
                    s = s.replace('//', '/')
                return s.strip('/')

            normalized_target_path = normalize_path(file_path) if file_path else None
            normalized_file_name = file_name.strip()

            # Search by file name
            search_url = f"https://graph.microsoft.com/v1.0/users/{user_id}/drive/root/search(q='{file_name}')"
            log_graph_api_request("GET", search_url, headers, None, "Application")
            
            async with aiohttp.ClientSession() as session:
                async with session.get(search_url, headers=headers) as response:
                    if response.status == 200:
                        search_results = await response.json()
                        items = search_results.get("value", [])
                        
                        # If file_path is provided, match the path (normalize both sides)
                        if normalized_target_path:
                            for item in items:
                                parent_path = item.get("parentReference", {}).get("path", "")
                                item_name = item.get("name", "")
                                
                                # Normalize paths for comparison
                                if parent_path.startswith("/drive/root:"):
                                    parent_path = parent_path.replace("/drive/root:", "")
                                
                                full_path = f"{user_email}{parent_path}/{item_name}".strip("/")
                                full_path = normalize_path(full_path)
                                target_path = normalize_path(f"{user_email}/{normalized_target_path}")

                                # Case-insensitive comparison for reliability
                                if target_path.lower() == full_path.lower():
                                    logger.info(f"Found file: {file_name} for user {user_email} at {full_path}")
                                    return item
                        else:
                            # Return first match if no path specified
                            if items:
                                logger.info(f"Found file: {file_name} for user {user_email}")
                                return items[0]
                        
                        logger.warning(f"File {file_name} not found in search for user {user_email}, trying folder-based lookup...")
                        # Fallback: try folder-based lookup if path provided
                        if normalized_target_path:
                            try:
                                # Split folder path and name
                                folder_path = normalized_target_path
                                if folder_path.endswith(f"/{normalized_file_name}"):
                                    folder_path = folder_path[: -len(f"/{normalized_file_name}")]

                                # Query the target folder children and match by name
                                # API requires prefix '/drive/root:' for path addressing
                                encoded_folder_path = folder_path if folder_path.startswith('/') else f"/{folder_path}"
                                children_url = f"https://graph.microsoft.com/v1.0/users/{user_id}/drive/root:{encoded_folder_path}:/children"
                                log_graph_api_request("GET", children_url, headers, None, "Application")
                                async with aiohttp.ClientSession() as session2:
                                    async with session2.get(children_url, headers=headers) as child_resp:
                                        if child_resp.status == 200:
                                            child_data = await child_resp.json()
                                            for item in child_data.get("value", []):
                                                if str(item.get("name", "")).strip().lower() == normalized_file_name.lower():
                                                    logger.info(f"Found file via folder lookup: {file_name} for user {user_email}")
                                                    return item
                            except Exception as e2:
                                logger.debug(f"Folder-based lookup failed: {str(e2)}")

                        return None
                    else:
                        response_text = await response.text()
                        logger.error(f"Failed to search file for user {user_id} - Status: {response.status}, Response: {response_text}")
                        return None
        except Exception as e:
            logger.error(f"Error searching file for user {user_id}: {str(e)}")
            return None

    async def get_file_download_url(self, user_id: str, file_id: str) -> Optional[str]:
        """Get download URL for a file by its ID"""
        if not await self.ensure_valid_token():
            logger.error("Cannot get download URL - token refresh failed")
            return None
        
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
        }
        
        url = f"https://graph.microsoft.com/v1.0/users/{user_id}/drive/items/{file_id}"
        log_graph_api_request("GET", url, headers, None, "Application")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        file_data = await response.json()
                        return file_data.get("@microsoft.graph.downloadUrl")
                    else:
                        response_text = await response.text()
                        logger.error(f"Failed to get download URL - Status: {response.status}, Response: {response_text}")
                        return None
        except Exception as e:
            logger.error(f"Error getting download URL: {str(e)}")
            return None

    async def download_zip_and_extract_file(self, download_url: str, target_file_name: str, user_email: str, zip_file_name: str) -> Optional[Dict]:
        """
        Download zip file and extract only the target file in memory.
        Does not save the entire zip to disk.
        """
        if not await self.ensure_valid_token():
            logger.error("Cannot download zip - token refresh failed")
            return None
        
        try:
            logger.info(f"Downloading and extracting {target_file_name} from {zip_file_name}")
            
            async with aiohttp.ClientSession() as session:
                log_graph_api_request("GET", download_url, {"Authorization": f"Bearer {self.access_token}"}, None, "Application")
                
                async with session.get(download_url) as response:
                    if response.status != 200:
                        response_text = await response.text()
                        logger.error(f"Failed to download zip - Status: {response.status}, Response: {response_text}")
                        return None
                    
                    # Read zip content into memory
                    zip_content = await response.read()
                    logger.info(f"Downloaded zip file {zip_file_name} ({len(zip_content)} bytes)")
            
            # Extract the target file from zip in memory
            try:
                with zipfile.ZipFile(io.BytesIO(zip_content)) as zip_ref:
                    # List all files in zip
                    zip_files = zip_ref.namelist()
                    logger.info(f"Zip contains {len(zip_files)} files")
                    
                    # Search for target file (case-insensitive, handle nested paths)
                    target_file_found = None
                    for zip_file_path in zip_files:
                        if zip_file_path.endswith('/'):
                            continue
                        
                        # Get just the filename from the path
                        file_name_in_zip = os.path.basename(zip_file_path)
                        
                        # Case-insensitive comparison
                        if file_name_in_zip.lower() == target_file_name.lower():
                            target_file_found = zip_file_path
                            logger.info(f"Found target file at: {zip_file_path}")
                            break
                    
                    if not target_file_found:
                        logger.warning(f"Target file {target_file_name} not found in zip. Available files: {zip_files[:10]}")
                        return None
                    
                    # Extract only the target file
                    file_data = zip_ref.read(target_file_found)
                    file_ext = get_file_extension(target_file_name)
                    
                    # Check file size
                    if len(file_data) > PROFILE_MAX_SIZE_THRESHOLD:
                        logger.warning(f"Extracted file {target_file_name} size {len(file_data)} exceeds threshold")
                        return None
                    
                    # Save extracted file to temp directory
                    temp_dir = f"{self.local_data_dir}/dsr"
                    create_folder(temp_dir)
                    local_filepath = f"{temp_dir}/{uuid4()}_{target_file_name}"
                    
                    with open(local_filepath, 'wb') as f:
                        f.write(file_data)
                    
                    logger.info(f"Extracted {target_file_name} to {local_filepath}")
                    
                    return {
                        'file_name': target_file_name,
                        'file_type': file_ext,
                        'file_size': len(file_data),
                        'local_filepath': local_filepath,
                        'original_path': f"{user_email}/{zip_file_name}/{target_file_found}",
                        'extracted_from_zip': True,
                        'parent_zip': zip_file_name,
                    }
                    
            except zipfile.BadZipFile:
                logger.error(f"Bad zip file: {zip_file_name}")
                return None
            except Exception as e:
                logger.error(f"Error extracting from zip: {str(e)}")
                return None
                
        except Exception as e:
            logger.error(f"Error downloading and extracting zip: {str(e)}", exc_info=True)
            return None

    async def search_and_extract_from_zip(self, user_id: str, user_email: str, zip_file_name: str, target_file_name: str, zip_file_path: str = None) -> Optional[Dict]:
        """
        Search for a zip file in OneDrive, download it, and extract only the target file.
        Returns the extracted file information if found.
        """
        try:
            # Search for the zip file in user's OneDrive
            logger.info(f"Searching for zip file: {zip_file_name} for user {user_email}")
            zip_file = await self.search_file_in_user_onedrive(user_id, user_email, zip_file_name, zip_file_path)
            
            if not zip_file:
                logger.warning(f"Zip file {zip_file_name} not found for user {user_email}")
                return None
            
            # Get file ID
            file_id = zip_file.get("id")
            if not file_id:
                logger.error(f"No file ID for zip file {zip_file_name}")
                return None
            
            # Get download URL (fresh one with proper permissions)
            download_url = await self.get_file_download_url(user_id, file_id)
            
            if not download_url:
                logger.error(f"Could not get download URL for zip file {zip_file_name}")
                return None
            
            logger.info(f"Got download URL for {zip_file_name}")
            
            # Download zip and extract only the target file
            extracted_file = await self.download_zip_and_extract_file(
                download_url, target_file_name, user_email, zip_file_name
            )
            
            if not extracted_file:
                logger.warning(f"Could not extract {target_file_name} from {zip_file_name}")
                return None
            
            return extracted_file
            
        except Exception as e:
            logger.error(f"Error searching and extracting from zip: {str(e)}", exc_info=True)
            return None

    async def fetch_file_from_one_drive_dir(
        self, file, semaphore, user_email: str, user_name: str
    ) -> Dict:
        """Fetch file from OneDrive directory"""
        file_name = file.get("name", "")
        parent_path = file.get("parentReference", {}).get("path", "")
 
        if parent_path.startswith("/drive/root:"):
            logical_path = parent_path.replace("/drive/root:", "")
        else:
            logical_path = parent_path
 
        file_uri = f"{user_email}/{logical_path}/{file_name}".strip("/")
 
        if self.rule_manager.skip_file(file_uri):
            logger.info(f"Skipping file {file_name} based on skip rules")
            return None
 
        downloadurl = file.get("@microsoft.graph.downloadUrl")
        object_size = file.get("size", 0)
        if object_size > PROFILE_MAX_SIZE_THRESHOLD:
            logger.info(f"Skipping {file_name}: Size {object_size} exceeds threshold")
            return None
 
        file_ext = get_file_extension(file_name)
        if file_ext not in PROFILE_SUPPORTED_FILE_TYPES:
            logger.info(f"Skipping {file_name}: Unsupported file type .{file_ext}")
            return None
 
        temp_dir = self.local_data_dir
        create_folder(temp_dir)
        filepath = f"{temp_dir}/{str(uuid4())}_{file_name}"
 
        async with semaphore:
            await self.download_file_from_onedrive(downloadurl, filepath)
        
        # Handle zip files - extract recursively
        extracted_files = []
        if file_ext == 'zip':
            logger.info(f"Processing zip file: {file_name}")
            try:
                with open(filepath, 'rb') as f:
                    zip_content = f.read()
                extracted_files = await self.extract_zip_recursive(zip_content, file_uri)
                logger.info(f"Extracted {len(extracted_files)} files from {file_name}")
            except Exception as e:
                logger.error(f"Error processing zip file {file_name}: {str(e)}")
 
        return {
            "name": file_name,
            "size": object_size,
            "file_type": file_ext,
            "file_uri": file_uri,
            "filepath": filepath,
            "created": file.get("createdDateTime"),
            "modified": file.get("lastModifiedDateTime"),
            "success": True,
            "user_email": user_email,
            "user_name": user_name,
            "extracted_files": extracted_files
        }
 
    async def scan_user_onedrive_infra(self, user_id: str) -> Dict:
        """Scan user OneDrive infrastructure"""
        if not await self.ensure_valid_token():
            return {"total_size": 0, "total_files": 0}
            
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
        }
        total_files = 0
        total_size = 0
        
        url = f"https://graph.microsoft.com/v1.0/users/{user_id}/drive/root/children"
        log_graph_api_request("GET", url, headers, None, "Application")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as drive_response:
                    if drive_response.status == 200:
                        drive_files = await drive_response.json()
                        files = drive_files.get("value", [])
                        total_size = sum(file.get("size", 0) for file in files)
                        total_files = len(files)
                        logger.info(f"OneDrive infra scan completed for user {user_id}: {total_files} files, {total_size} bytes")
                    else:
                        response_text = await drive_response.text()
                        request_id = drive_response.headers.get("request-id", "N/A")
                        timestamp = drive_response.headers.get("date", "N/A")
                        logger.error(f"OneDrive infra scan failed for user {user_id} - Status: {drive_response.status}, "
                                   f"Request ID: {request_id}, Timestamp: {timestamp}, Response: {response_text}")
        except Exception as e:
            logger.error(f"OneDrive scan failed for user {user_id}: {str(e)}")
        return {"total_size": total_size, "total_files": total_files}
 
    async def scan_folder_recursive(
        self,
        user_id: str,
        folder_id: str,
        semaphore,
        session,
        user_email: str,
        user_name: str,
    ) -> AsyncGenerator[FileMetadata, None]:
        """Recursively scan a folder and its subfolders."""
        if not await self.ensure_valid_token():
            logger.error("Cannot scan folder - token refresh failed")
            return
            
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
        }
        url = f"https://graph.microsoft.com/v1.0/users/{user_id}/drive/items/{folder_id}/children"
        
        log_graph_api_request("GET", url, headers, None, "Application")
 
        try:
            async with session.get(url, headers=headers) as response:
                if response.status != 200:
                    response_text = await response.text()
                    request_id = response.headers.get("request-id", "N/A")
                    timestamp = response.headers.get("date", "N/A")
                    logger.error(f"Failed to fetch folder {folder_id} for user {user_id} - Status: {response.status}, "
                               f"Request ID: {request_id}, Timestamp: {timestamp}, Response: {response_text}")
                    return
 
                folder_data = await response.json()
                items = folder_data.get("value", [])
                tasks = []
 
                for item in items:
                    if "folder" in item:
                        subfolder_id = item["id"]
                        async for file_metadata in self.scan_folder_recursive(
                            user_id,
                            subfolder_id,
                            semaphore,
                            session,
                            user_email,
                            user_name,
                        ):
                            yield file_metadata
                    elif "file" in item:
                        tasks.append(
                            self.fetch_file_from_one_drive_dir(
                                item, semaphore, user_email, user_name
                            )
                        )
 
                for future in asyncio.as_completed(tasks):
                    file_info = await future
                    if file_info and file_info.get("success"):
                        # Yield main file
                        yield FileMetadata(
                            service_name=self.service_name,
                            service_type=self.service_type,
                            service_provider=self.service_provider,
                            sub_service=self.sub_service,
                            file_key=file_info["name"],
                            file_size=file_info["size"],
                            file_type=file_info["file_type"],
                            file_uri=file_info["file_uri"],
                            local_filepath=file_info["filepath"],
                            details={
                                "created": file_info["created"],
                                "modified": file_info["modified"],
                                "region": "India",
                                "user_email": file_info["user_email"],
                                "user_name": file_info["user_name"],
                            },
                        )
                        
                        # Yield extracted files from zip
                        for extracted in file_info.get("extracted_files", []):
                            yield FileMetadata(
                                service_name=self.service_name,
                                service_type=self.service_type,
                                service_provider=self.service_provider,
                                sub_service=self.sub_service,
                                file_key=extracted["file_name"],
                                file_size=extracted["size"],
                                file_type=extracted["file_type"],
                                file_uri=extracted["original_path"],
                                local_filepath=extracted["temp_path"],
                                details={
                                    "created": file_info["created"],
                                    "modified": file_info["modified"],
                                    "region": "India",
                                    "user_email": file_info["user_email"],
                                    "user_name": file_info["user_name"],
                                    "extracted_from_zip": True,
                                    "parent_file": file_info["file_uri"]
                                },
                            )
 
                logger.debug(f"Scanned folder {folder_id} for user {user_id}, found {len(items)} items.")
        except Exception as e:
            logger.error(f"Error scanning folder {folder_id} for user {user_id}: {str(e)}")
 
    async def scan_user_onedrive_deep(
        self, user_id: str, user_email: str, user_name: str
    ) -> AsyncGenerator[FileMetadata, None]:
        """Performs a deep scan of a user's OneDrive, recursively processing folders and files."""
        if not await self.ensure_valid_token():
            logger.error(f"Cannot scan OneDrive for user {user_id} - token refresh failed")
            return
            
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
        }
        
        try:
            semaphore = asyncio.Semaphore(CONCURRENT_LIMIT)
            async with aiohttp.ClientSession() as session:
                root_url = f"https://graph.microsoft.com/v1.0/users/{user_id}/drive/root"
                log_graph_api_request("GET", root_url, headers, None, "Application")
                
                async with session.get(root_url, headers=headers) as drive_response:
                    if drive_response.status == 200:
                        root_folder = await drive_response.json()
                        root_folder_id = root_folder.get("id")
                        if root_folder_id:
                            logger.info(f"Starting OneDrive deep scan for user {user_email} ({user_id})")
                            async for file_metadata in self.scan_folder_recursive(
                                user_id,
                                root_folder_id,
                                semaphore,
                                session,
                                user_email,
                                user_name,
                            ):
                                yield file_metadata
                            logger.info(f"Completed OneDrive deep scan for user {user_email}")
                        else:
                            logger.error(f"No root folder ID found for user {user_id}")
                    else:
                        response_text = await drive_response.text()
                        request_id = drive_response.headers.get("request-id", "N/A")
                        timestamp = drive_response.headers.get("date", "N/A")
                        logger.error(f"Failed to fetch root folder for user {user_id} - Status: {drive_response.status}, "
                                   f"Request ID: {request_id}, Timestamp: {timestamp}, Response: {response_text}")
 
        except Exception as e:
            logger.error(f"OneDrive deep scan failed for user {user_id}: {str(e)}")

    # ===================== USER SCAN FOR DSR =====================
    async def user_scan(self, search_files: List[Dict]) -> AsyncGenerator[Dict, None]:
        """
        Scan OneDrive for specific files based on DSR request.
        Now supports files inside zip archives.
        
        search_files format:
        [
            {
                "service_name": "onedrive_service",
                "service_provider": "M365",
                "sub_service": "MS_ONEDRIVE",
                "file_uri": "<EMAIL>/data-discovery.zip/dsr.xlsx",
                "file_name": "dsr.xlsx",
                "file_type": "xlsx"
            }
        ]
        """
        try:
            await self.get_service_details()
            accessible_users = await self.get_accessible_users()
            
            if not accessible_users:
                logger.warning("No accessible users found for OneDrive DSR scan")
                return
            
            # Group files by user email (extract from file_uri)
            files_by_user = {}
            for file_info in search_files:
                file_uri = file_info.get("file_uri", "")
                file_name = file_info.get("file_name", "")
                
                # Extract user email from file_uri
                parts = file_uri.strip("/").split("/")
                if len(parts) > 0:
                    user_email = parts[0]
                    
                    # Check if file is inside a zip (contains .zip in path)
                    is_inside_zip = '.zip/' in file_uri.lower()
                    
                    if is_inside_zip:
                        # Parse zip file structure: <EMAIL>/path/archive.zip/inner/file.xlsx
                        zip_file_name = None
                        zip_file_path = None
                        target_file_name = file_name
                        
                        # Find the zip file in the path
                        for i, part in enumerate(parts):
                            if part.lower().endswith('.zip'):
                                zip_file_name = part
                                # Path to zip file (excluding user email and zip filename)
                                if i > 1:
                                    zip_file_path = "/".join(parts[1:i])
                                else:
                                    zip_file_path = ""
                                break
                        
                        if not zip_file_name:
                            logger.warning(f"Could not extract zip filename from path: {file_uri}")
                            continue
                        
                        if user_email not in files_by_user:
                            files_by_user[user_email] = []
                        
                        files_by_user[user_email].append({
                            "file_name": file_name,
                            "file_uri": file_uri,
                            "file_info": file_info,
                            "is_inside_zip": True,
                            "zip_file_name": zip_file_name,
                            "zip_file_path": zip_file_path,
                            "target_file_name": target_file_name
                        })
                    else:
                        # Regular file (not inside zip)
                        file_path = "/".join(parts[1:]) if len(parts) > 1 else ""
                        
                        if user_email not in files_by_user:
                            files_by_user[user_email] = []
                        
                        files_by_user[user_email].append({
                            "file_name": file_name,
                            "file_path": file_path,
                            "file_uri": file_uri,
                            "file_info": file_info,
                            "is_inside_zip": False
                        })
            
            logger.info(f"DSR scan: Found {len(files_by_user)} users with files to download")
            
            results = []
            
            # Process each user
            for user_email, files in files_by_user.items():
                # Find user in accessible users
                user_info = None
                for user in accessible_users:
                    user_mail = user.get("mail") or user.get("userPrincipalName")
                    if user_mail and user_mail.lower() == user_email.lower():
                        user_info = user
                        break
                
                if not user_info:
                    logger.warning(f"User {user_email} not found in accessible users, skipping")
                    continue
                
                user_id = user_info["id"]
                user_name = user_info.get("displayName", "")
                
                logger.info(f"DSR scan: Processing {len(files)} files for user {user_email}")
                
                # Process each file
                for file_data in files:
                    file_name = file_data["file_name"]
                    file_uri = file_data["file_uri"]
                    is_inside_zip = file_data.get("is_inside_zip", False)
                    
                    if is_inside_zip:
                        # Handle file inside zip
                        zip_file_name = file_data["zip_file_name"]
                        zip_file_path = file_data.get("zip_file_path", "")
                        target_file_name = file_data["target_file_name"]
                        
                        logger.info(f"DSR: Processing file inside zip - {target_file_name} from {zip_file_name}")
                        
                        # Search and extract from zip
                        extracted_file = await self.search_and_extract_from_zip(
                            user_id, user_email, zip_file_name, target_file_name, zip_file_path
                        )
                        
                        if not extracted_file:
                            logger.warning(f"File {target_file_name} not found inside zip {zip_file_name}")
                            continue
                        
                        # Verify extracted file exists
                        if not file_exists(extracted_file['local_filepath']):
                            logger.error(f"Extracted file {target_file_name} does not exist at {extracted_file['local_filepath']}")
                            continue
                        
                        # Prepare result for extracted file
                        results.append({
                            "service_name": self.service_name,
                            "service_provider": str(self.service_provider),
                            "sub_service": str(self.sub_service),
                            "user_email": user_email,
                            "user_name": user_name,
                            "file_name": extracted_file['file_name'],
                            "file_uri": file_uri,
                            "file_type": extracted_file['file_type'],
                            "file_size": extracted_file['file_size'],
                            "local_filepath": extracted_file['local_filepath'],
                            "extracted_from_zip": True,
                            "parent_zip": zip_file_name,
                        })
                        logger.info(f"DSR: Extracted and processed {target_file_name} from {zip_file_name}")
                        
                    else:
                        # Handle regular file (not inside zip)
                        file_path = file_data.get("file_path", "")
                        
                        # Search for file in user's OneDrive
                        found_file = await self.search_file_in_user_onedrive(
                            user_id, user_email, file_name, file_path
                        )
                        
                        if not found_file:
                            logger.warning(f"File {file_name} not found for user {user_email}")
                            continue
                        
                        # Download the file
                        download_url = found_file.get("@microsoft.graph.downloadUrl")
                        if not download_url:
                            logger.error(f"No download URL for file {file_name}")
                            continue
                        
                        # Create temp file path
                        temp_dir = f"{self.local_data_dir}/dsr"
                        create_folder(temp_dir)
                        local_filepath = f"{temp_dir}/{uuid4()}_{file_name}"
                        
                        # Download file
                        await self.download_file_from_onedrive(download_url, local_filepath)
                        
                        if not file_exists(local_filepath):
                            logger.error(f"Failed to download file {file_name}")
                            continue
                        
                        # Prepare result
                        file_ext = get_file_extension(file_name)
                        results.append({
                            "service_name": self.service_name,
                            "service_provider": str(self.service_provider),
                            "sub_service": str(self.sub_service),
                            "user_email": user_email,
                            "user_name": user_name,
                            "file_name": file_name,
                            "file_uri": file_uri,
                            "file_type": file_ext,
                            "file_size": found_file.get("size", 0),
                            "local_filepath": local_filepath,
                            "extracted_from_zip": False,
                        })
                        logger.info(f"DSR: Downloaded file {file_name} for user {user_email}")
            
            # Yield all results
            if results:
                yield results
                logger.info(f"DSR scan completed: {len(results)} files processed")
            else:
                logger.warning("DSR scan completed: No files were processed")
                
        except Exception as e:
            logger.error(f"Error during OneDrive DSR user scan: {str(e)}", exc_info=True)
 
    async def get_user_encryption_status(self, user_id: str) -> str:
        """Fetch encryption status of a user's OneDrive root folder."""
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
        }
        url = f"https://graph.microsoft.com/v1.0/users/{user_id}/drive/root?$select=encryptionInfo"
 
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        enc_info = data.get("encryptionInfo")
                        if enc_info and "encryptionType" in enc_info:
                            return enc_info["encryptionType"]
                        else:
                            return "encrypted"
                    else:
                        return "unknown"
        except Exception as e:
            logger.error(f"Error fetching encryption status for {user_id}: {str(e)}")
            return "unknown"
 
    async def infra_scan(self) -> Dict:
        try:
            await self.get_service_details()
            tenant_region = await get_tenant_info(self.access_token)
            accessible_users = await self.get_accessible_users()
            
            infra = {
                "tenant_region": tenant_region,
                "users": accessible_users,
                "accessible_users_count": len(accessible_users)
            }
            logger.info(f"Infrastructure scan completed - {len(accessible_users)} accessible users found")
            return infra
        except Exception as e:
            logger.error(f"Error during onedrive infra scanning: {str(e)}", exc_info=True)
            return {"tenant_region": "", "users": [], "accessible_users_count": 0}
 
    async def deep_scan(self) -> AsyncGenerator[FileMetadata, None]:
        """Performs a deep scan on accessible users' OneDrive, processing files inside folders and subfolders."""
        total_file_count = 0
        total_file_size = 0    
        try:
            await self.get_service_details()
            tenant_region = await get_tenant_info(self.access_token)
            accessible_users = await self.get_accessible_users()
            
            if not accessible_users:
                logger.warning("No accessible users found for OneDrive scanning")
                return
 
            logger.info(f"Starting OneDrive deep scan for {len(accessible_users)} accessible users")
            
            for user in accessible_users:
                user_email = user.get("mail") or user.get("userPrincipalName")
                user_id = user["id"]
                user_name = user.get("displayName", "")
 
                logger.info(f"Scanning OneDrive for user: {user_email} ({user_id})")
                
                async for file_metadata in self.scan_user_onedrive_deep(
                    user_id, user_email, user_name
                ):
                    file_metadata.details.update(
                        {
                            "user_email": user_email,
                            "user_id": user_id,
                            "user_name": user_name,
                        }
                    )
                    total_file_count += 1
                    total_file_size += file_metadata.file_size
                    yield file_metadata
 
            logger.info("OneDrive deep scan completed for all accessible users")
 
        except Exception as e:
            logger.error(f"Error during scanning: {str(e)}", exc_info=True)
 
        finally:
            # ---- Asset details ----
            try:
                encryption_enabled = False
                for user in accessible_users:
                    user_id = user["id"]
                    status = await self.get_user_encryption_status(user_id)
                    if status:   
                        encryption_enabled = True
                        break
            except Exception as e:
                encryption_enabled = False
        
            try:
                asset = [
                    AssetDetailsSchema(
                        asset_name=f"onedrive_{self.service_name}",
                        service_provider=self.service_provider,
                        type="file_storage",
                        category="unstructured",
                        location=tenant_region,
                        owner="system",
                        security=encryption_enabled,
                        size=total_file_size,
                        count=total_file_count,
                        access_category="restricted",
                        service_name=str(self.service_name),
                        steward=str(self.service_steward),
                    )
                ]
                load_asset_details(asset)
 
                access = []
                for user in accessible_users:
                    user_email = user.get("mail") or user.get("userPrincipalName")
                    user_id = user["id"]
 
                    if not user_email:
                        continue
 
                    access.append({
                        "asset_name": f"onedrive_{self.service_name}",
                        "user_or_role": user_email,
                        "role": "user",
                        "access": "full",
                    })
 
                if not access:
                    access.append({
                        "asset_name": f"onedrive_{self.service_name}",
                        "user_or_role": "system",
                        "role": "owner",
                        "access": "full",
                    })
 
                load_access_controls(access)
            except Exception as e:
                logger.error(f"Error loading asset details and access controls: {str(e)}")