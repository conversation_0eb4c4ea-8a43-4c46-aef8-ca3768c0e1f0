import asyncio
import json
import re
import time
from typing import As<PERSON><PERSON>enerator, Dict, List
from uuid import uuid4

import aiohttp

from src.common.config import (
    CONCURRENT_LIMIT,
    PROFILE_MAX_SIZE_THRESHOLD,
    PROFILE_SUPPORTED_FILE_TYPES,
    TEMP_DATA_DIR,
)
from src.common.constants import M365SubServices, ServiceProviders, ServiceTypes
from src.ingestion.base_source import UnstructureSource
from src.ingestion.data_class import FileMetadata
from src.ingestion.unstructure.office_365.connection import (
    get_365_authenticators,
    get_all_users,
    get_tenant_info,
    refresh_token_if_needed,
    log_graph_api_request,
    get_user_by_email,
)
from src.modules.repos import DatabaseManager
from src.utils.helpers import create_folder, file_exists, get_file_extension
from src.utils.logger import get_ingestion_logger
from src.utils.loaders import load_access_controls, load_asset_details
from src.ingestion.data_class import AssetsDetails as AssetDetailsSchema

logger = get_ingestion_logger()


class SharePointSource(UnstructureSource):
    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.MS_365_SHAREPOINT.value, service_name)
        self.db = DatabaseManager()
        self.service_provider = ServiceProviders.M365
        self.sub_service = M365SubServices.MS_SHAREPOINT
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"
        self.access_token = None
        self.token_expires_at = None
        self.auth_app = None
        self.service_steward = None

    async def get_service_details(self) -> Dict:
        """Gets service details"""
        self.service = await self.db.get_service_by_service_name(self.service_name)
        credentials = self.service.get("credentials", {})
        self.tenant_id = credentials.get("tenant_id")
        self.client_id = credentials.get("client_id")
        self.client_secret = credentials.get("client_secret")
        self.service_steward = self.service.get("data_steward")
        include_emails = credentials.get("include_emails", "")
        exclude_emails = credentials.get("exclude_emails", "")
        
        # Parse include_emails - handle multiple formats
        if include_emails:
            if isinstance(include_emails, str):
                # Handle comma, semicolon, or space separated emails
                email_list = re.split(r'[,;\s]+', include_emails.strip())
                self.include_emails = [e.strip() for e in email_list if e.strip()]
            elif isinstance(include_emails, list):
                self.include_emails = [str(e).strip() for e in include_emails if str(e).strip()]
            else:
                self.include_emails = []
        else:
            self.include_emails = []
            
        # Parse exclude_emails
        if exclude_emails:
            if isinstance(exclude_emails, str):
                email_list = re.split(r'[,;\s]+', exclude_emails.strip())
                self.exclude_emails = [e.strip() for e in email_list if e.strip()]
            elif isinstance(exclude_emails, list):
                self.exclude_emails = [str(e).strip() for e in exclude_emails if str(e).strip()]
            else:
                self.exclude_emails = []
        else:
            self.exclude_emails = []
        
        # Get initial token and set up authentication
        auth_result = get_365_authenticators(
            self.tenant_id, self.client_id, self.client_secret
        )
        self.access_token = auth_result["access_token"]
        self.token_expires_at = auth_result.get("expires_at")
        self.auth_app = auth_result.get("auth_app")
        
        return self.service

    async def ensure_valid_token(self) -> bool:
        """Ensure we have a valid access token, refresh if needed"""
        try:
            if self.token_expires_at and time.time() >= self.token_expires_at - 300:  # Refresh 5 min before expiry
                logger.info("Access token expired or expiring soon, refreshing...")
                auth_result = refresh_token_if_needed(
                    self.auth_app, self.tenant_id, self.client_id, self.client_secret
                )
                if auth_result:
                    self.access_token = auth_result["access_token"]
                    self.token_expires_at = auth_result.get("expires_at")
                    logger.info("Access token refreshed successfully")
                    return True
                else:
                    logger.error("Failed to refresh access token")
                    return False
            return True
        except Exception as e:
            logger.error(f"Error ensuring valid token: {str(e)}")
            return False

    async def test_connection(self) -> Dict:
        try:
            if not await self.ensure_valid_token():
                return {"status": False, "message": "Token refresh failed"}
                
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json",
            }
            
            url = "https://graph.microsoft.com/v1.0/organization"
            log_graph_api_request("GET", url, headers, None, "Application")
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    response_text = await response.text()
                    
                    if response.status == 200:
                        logger.info(f"Graph API Response: {response.status} - Authentication successful")
                        return {"status": True, "message": "Authentication successful"}
                    else:
                        try:
                            error_data = json.loads(response_text)
                            error_msg = error_data.get("error", {}).get("message", "Unknown error")
                            error_code = error_data.get("error", {}).get("code", "Unknown")
                            request_id = response.headers.get("request-id", "N/A")
                            timestamp = response.headers.get("date", "N/A")
                        except:
                            error_msg = response_text
                            error_code = "Unknown"
                            request_id = "N/A"
                            timestamp = "N/A"
                            
                        logger.error(f"Graph API Error - Status: {response.status}, Code: {error_code}, "
                                   f"Message: {error_msg}, Request ID: {request_id}, Timestamp: {timestamp}")
                        return {
                            "status": False,
                            "message": f"Token validation failed: {error_msg}",
                        }
        except Exception as e:
            logger.error(f"Error verifying credentials: {str(e)}")
            return {"status": False, "message": str(e)}

    async def get_accessible_users(self) -> List[Dict]:
        """Get list of users we have permission to access"""
        accessible_users = []
        
        # If include_emails is specified, prioritize those users
        if self.include_emails:
            logger.info(f"Using specified include_emails: {self.include_emails}")
            
            for email in self.include_emails:
                user = await get_user_by_email(self.access_token, email.strip())
                if user:
                    accessible_users.append(user)
                    logger.info(f"User {email} found and accessible")
                else:
                    logger.warning(f"✗ User with email {email} not found in tenant")
        else:
            # Get all users if no specific emails provided
            logger.info("No specific emails provided, fetching all users...")
            all_users = await get_all_users(self.access_token, log_requests=False)
            
            for user in all_users:
                user_mail = user.get("mail")
                user_principal = user.get("userPrincipalName")
                user_email = user_mail if user_mail else user_principal
                
                if not user_email:
                    continue
                
                # Skip excluded emails
                if self.exclude_emails:
                    user_email_lower = user_email.lower()
                    if any(user_email_lower == excluded.lower() for excluded in self.exclude_emails):
                        continue
                
                accessible_users.append(user)
        
        logger.info(f"Found {len(accessible_users)} accessible users for SharePoint scanning")
        return accessible_users

    async def get_sharepoint_encryption_status(self) -> bool:
        """Determine encryption status for SharePoint"""
        # SharePoint encrypts data at rest and in transit by default
        return True

    async def download_file_from_sharepoint(self, web_url: str, filepath: str, user_email: str = None) -> bool:
        """Downloads a file from SharePoint with proper error handling"""
        if file_exists(filepath):
            return True

        if not await self.ensure_valid_token():
            logger.error(f"Cannot download file for user {user_email} - token refresh failed")
            return False

        async with aiohttp.ClientSession() as session:
            try:
                log_graph_api_request("GET", web_url, {"Authorization": f"Bearer {self.access_token}"}, None, "Application")
                
                async with session.get(web_url) as response:
                    if response.status == 200:
                        content = await response.read()
                        with open(filepath, "wb") as f:
                            f.write(content)
                        logger.debug(f"Successfully downloaded file to {filepath}")
                        return True
                    else:
                        response_text = await response.text()
                        request_id = response.headers.get("request-id", "N/A")
                        timestamp = response.headers.get("date", "N/A")
                        logger.error(f"Failed to download file for {user_email} - Status: {response.status}, "
                                   f"Request ID: {request_id}, Timestamp: {timestamp}, Response: {response_text}")
                        return False
            except Exception as e:
                logger.error(f"Error downloading file for {user_email}: {str(e)}")
                return False

    async def construct_file_uri(self, file: dict, user_email: str, site_name: str = None, drive_name: str = None) -> str:
        """Construct file URI with better fallback handling"""
        try:
            parent_path = file.get("parentReference", {}).get("path", "")
            file_name = file.get("name", "")
            
            # Extract path from Graph API format
            if parent_path.startswith("/drive/root:"):
                logical_path = parent_path.replace("/drive/root:", "").strip("/")
            elif parent_path.startswith("/drive/root"):
                logical_path = parent_path.replace("/drive/root", "").strip("/")
            else:
                logical_path = parent_path.strip("/")
            
            # Use provided site and drive names or fallback to user email
            site_part = site_name if site_name else user_email.split("@")[0]
            drive_part = drive_name if drive_name else "Documents"
            
            if logical_path:
                file_uri = f"sharepoint/{site_part}/{drive_part}/{logical_path}/{file_name}"
            else:
                file_uri = f"sharepoint/{site_part}/{drive_part}/{file_name}"
                
            logger.debug(f"Constructed file URI: {file_uri}")
            return file_uri
        except Exception as e:
            logger.warning(f"Error constructing file URI: {str(e)}")
            return f"sharepoint/{user_email}/{file.get('name', 'unknown_file')}"

    async def fetch_file_from_sharepoint_dir(self, file: dict, semaphore, user_id: str, user_email: str) -> Dict:
        """Fetch file metadata and download if applicable"""
        file_name = file.get("name", "")
        download_url = file.get("@microsoft.graph.downloadUrl")
        object_size = file.get("size", 0)
        
        file_uri = await self.construct_file_uri(file, user_email)

        # Skip logic
        if self.rule_manager.skip_file(file_uri):
            logger.info(f"Skipping file {file_name} based on skip rules")
            return None

        if object_size > PROFILE_MAX_SIZE_THRESHOLD:
            logger.info(f"Skipping {file_name}: Size {object_size} exceeds threshold")
            return None

        file_ext = get_file_extension(file_name)
        if file_ext not in PROFILE_SUPPORTED_FILE_TYPES:
            logger.info(f"Skipping {file_name}: Unsupported file type .{file_ext}")
            return None

        # Create temp directory
        temp_dir = self.local_data_dir
        create_folder(temp_dir)
        filepath = f"{temp_dir}/{str(uuid4())}_{file_name}"

        async with semaphore:
            success = await self.download_file_from_sharepoint(download_url, filepath, user_email)

        return {
            "name": file_name,
            "size": object_size,
            "file_type": file_ext,
            "file_uri": file_uri,
            "filepath": filepath,
            "created": file.get("createdDateTime"),
            "modified": file.get("lastModifiedDateTime"),
            "success": success,
            "user_email": user_email,
            "user_id": user_id,
        }

    async def scan_folder_recursive(
        self,
        user_id: str,
        folder_id: str,
        semaphore,
        session,
        user_email: str,
    ) -> AsyncGenerator[FileMetadata, None]:
        """Recursively scan SharePoint folders and subfolders"""
        if not await self.ensure_valid_token():
            logger.error("Cannot scan folder - token refresh failed")
            return
            
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
        }
        
        url = f"https://graph.microsoft.com/v1.0/users/{user_id}/drive/items/{folder_id}/children"
        log_graph_api_request("GET", url, headers, None, "Application")

        try:
            async with session.get(url, headers=headers) as response:
                if response.status != 200:
                    response_text = await response.text()
                    request_id = response.headers.get("request-id", "N/A")
                    timestamp = response.headers.get("date", "N/A")
                    logger.error(f"Failed to fetch folder {folder_id} for user {user_email} - Status: {response.status}, "
                               f"Request ID: {request_id}, Timestamp: {timestamp}, Response: {response_text}")
                    return

                folder_data = await response.json()
                items = folder_data.get("value", [])
                tasks = []

                for item in items:
                    if "folder" in item:  # If the item is a folder
                        subfolder_id = item["id"]
                        async for file_metadata in self.scan_folder_recursive(
                            user_id, subfolder_id, semaphore, session, user_email
                        ):
                            yield file_metadata
                    elif "file" in item:  # If the item is a file
                        tasks.append(
                            self.fetch_file_from_sharepoint_dir(
                                item, semaphore, user_id, user_email
                            )
                        )

                for future in asyncio.as_completed(tasks):
                    file_info = await future
                    if file_info and file_info.get("success"):
                        yield FileMetadata(
                            service_name=self.service_name,
                            service_type=self.service_type,
                            service_provider=self.service_provider,
                            sub_service=self.sub_service,
                            file_key=file_info["name"],
                            file_size=file_info["size"],
                            file_type=file_info["file_type"],
                            file_uri=file_info["file_uri"],
                            local_filepath=file_info["filepath"],
                            details={
                                "created": file_info["created"],
                                "modified": file_info["modified"],
                                "user_email": file_info["user_email"],
                                "user_id": file_info["user_id"],
                                "region": "India",
                            },
                        )

                logger.debug(f"Scanned folder {folder_id} for user {user_email}, found {len(items)} items")
        except Exception as e:
            logger.error(f"Error scanning folder {folder_id} for user {user_email}: {str(e)}")

    async def scan_user_sharepoint_infra(self, user_id: str, user_email: str) -> Dict:
        """Scan SharePoint infrastructure for a specific user"""
        if not await self.ensure_valid_token():
            return {"total_files": 0, "total_size": 0}
            
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
        }
        
        total_files = 0
        total_size = 0
        
        url = f"https://graph.microsoft.com/v1.0/users/{user_id}/drive/root/children"
        log_graph_api_request("GET", url, headers, None, "Application")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        drive_files = await response.json()
                        files = drive_files.get("value", [])
                        total_size = sum(file.get("size", 0) for file in files)
                        total_files = len(files)
                        logger.info(f"SharePoint infra scan for {user_email}: {total_files} files, {total_size} bytes")
                    else:
                        response_text = await response.text()
                        request_id = response.headers.get("request-id", "N/A")
                        timestamp = response.headers.get("date", "N/A")
                        logger.error(f"SharePoint infra scan failed for {user_email} - Status: {response.status}, "
                                   f"Request ID: {request_id}, Timestamp: {timestamp}")
        except Exception as e:
            logger.error(f"SharePoint infra scan failed for {user_email}: {str(e)}")

        return {"total_files": total_files, "total_size": total_size}

    async def scan_user_sharepoint_deep(self, user_id: str, user_email: str) -> AsyncGenerator[FileMetadata, None]:
        """Perform deep scan of user's SharePoint drive"""
        if not await self.ensure_valid_token():
            logger.error(f"Cannot scan SharePoint for user {user_id} - token refresh failed")
            return
            
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
        }
        
        try:
            semaphore = asyncio.Semaphore(CONCURRENT_LIMIT)
            async with aiohttp.ClientSession() as session:
                root_url = f"https://graph.microsoft.com/v1.0/users/{user_id}/drive/root"
                log_graph_api_request("GET", root_url, headers, None, "Application")
                
                async with session.get(root_url, headers=headers) as drive_response:
                    if drive_response.status == 200:
                        root_folder = await drive_response.json()
                        root_folder_id = root_folder.get("id")
                        if root_folder_id:
                            logger.info(f"Starting SharePoint deep scan for user {user_email} ({user_id})")
                            async for file_metadata in self.scan_folder_recursive(
                                user_id, root_folder_id, semaphore, session, user_email
                            ):
                                yield file_metadata
                            logger.info(f"Completed SharePoint deep scan for user {user_email}")
                        else:
                            logger.error(f"No root folder ID found for user {user_id}")
                    else:
                        response_text = await drive_response.text()
                        request_id = drive_response.headers.get("request-id", "N/A")
                        timestamp = drive_response.headers.get("date", "N/A")
                        logger.error(f"Failed to fetch root folder for user {user_id} - Status: {drive_response.status}, "
                                   f"Request ID: {request_id}, Timestamp: {timestamp}, Response: {response_text}")

        except Exception as e:
            logger.error(f"SharePoint deep scan failed for user {user_id}: {str(e)}")

    async def infra_scan(self) -> Dict:
        try:
            await self.get_service_details()
            tenant_region = await get_tenant_info(self.access_token)
            accessible_users = await self.get_accessible_users()
            
            total_files = 0
            total_size = 0
            
            # Scan infrastructure for each accessible user
            for user in accessible_users:
                user_id = user.get("id")
                user_email = user.get("mail") or user.get("userPrincipalName")
                
                if user_id and user_email:
                    user_stats = await self.scan_user_sharepoint_infra(user_id, user_email)
                    total_files += user_stats["total_files"]
                    total_size += user_stats["total_size"]
            
            infra = {
                "tenant_region": tenant_region,
                "users": accessible_users,
                "accessible_users_count": len(accessible_users),
                "total_files": total_files,
                "total_size": total_size,
            }
            
            logger.info(f"SharePoint infrastructure scan completed - {len(accessible_users)} accessible users, {total_files} total files")
            return infra
        except Exception as e:
            logger.error(f"Error during SharePoint infra scanning: {str(e)}", exc_info=True)
            return {
                "tenant_region": "",
                "users": [],
                "accessible_users_count": 0,
                "total_files": 0,
                "total_size": 0,
            }

    async def user_scan(self, search_files: List[Dict]) -> AsyncGenerator[Dict, None]:
        """
        Scan SharePoint for specific files based on DSR request.
        
        search_files format:
        [
            {
                "file_uri": "sharepoint/<EMAIL>/documents/path/file.xlsx",
                "file_name": "file.xlsx",
                "file_type": "xlsx"
            }
        ]
        """
        try:
            await self.get_service_details()
            accessible_users = await self.get_accessible_users()
            
            if not accessible_users:
                logger.warning("No accessible users found for SharePoint DSR scan")
                return
            
            # Group files by user email (extract from file_uri)
            files_by_user = {}
            for file_info in search_files:
                file_uri = file_info.get("file_uri", "")
                file_name = file_info.get("file_name", "")
                
                # Extract user email from file_uri
                # Format: sharepoint/<EMAIL>/site/drive/path/file.xlsx
                parts = file_uri.strip("/").split("/")
                if len(parts) > 1:
                    # Skip "sharepoint" prefix, get email
                    user_email = parts[1]
                    
                    # Get file path (everything after user email)
                    file_path = "/".join(parts[2:]) if len(parts) > 2 else ""
                    
                    if user_email not in files_by_user:
                        files_by_user[user_email] = []
                    
                    files_by_user[user_email].append({
                        "file_name": file_name,
                        "file_path": file_path,
                        "file_uri": file_uri,
                        "file_info": file_info,
                    })
            
            logger.info(f"DSR scan: Found {len(files_by_user)} users with files to download")
            
            results = []
            
            # Process each user
            for user_email, files in files_by_user.items():
                # Find user in accessible users
                user_info = None
                for user in accessible_users:
                    user_mail = user.get("mail") or user.get("userPrincipalName")
                    if user_mail and user_mail.lower() == user_email.lower():
                        user_info = user
                        break
                
                if not user_info:
                    logger.warning(f"User {user_email} not found in accessible users, skipping")
                    continue
                
                user_id = user_info["id"]
                user_name = user_info.get("displayName", "")
                
                logger.info(f"DSR scan: Processing {len(files)} files for user {user_email}")
                
                # Process each file
                for file_data in files:
                    file_name = file_data["file_name"]
                    file_uri = file_data["file_uri"]
                    file_path = file_data.get("file_path", "")
                    
                    # Search for file in user's SharePoint drive
                    logger.info(f"DSR: Searching for file '{file_name}' in user {user_email} SharePoint")
                    
                    # Normalize file path for search
                    def normalize_path(s: str) -> str:
                        if not s:
                            return ""
                        s = s.replace("\\", "/").strip()
                        while '//' in s:
                            s = s.replace('//', '/')
                        return s.strip('/')
                    
                    normalized_path = normalize_path(file_path)
                    
                    # Search in user's drive
                    if not await self.ensure_valid_token():
                        logger.error(f"Cannot search file for user {user_id} - token refresh failed")
                        continue
                    
                    headers = {
                        "Authorization": f"Bearer {self.access_token}",
                        "Content-Type": "application/json",
                    }
                    
                    try:
                        # Search by file name
                        search_url = f"https://graph.microsoft.com/v1.0/users/{user_id}/drive/root/search(q='{file_name}')"
                        log_graph_api_request("GET", search_url, headers, None, "Application")
                        
                        async with aiohttp.ClientSession() as session:
                            async with session.get(search_url, headers=headers) as response:
                                if response.status == 200:
                                    search_results = await response.json()
                                    items = search_results.get("value", [])
                                    
                                    found_file = None
                                    for item in items:
                                        item_name = item.get("name", "")
                                        
                                        # Match by name
                                        if item_name.lower() == file_name.lower():
                                            # If path specified, also match path
                                            if normalized_path:
                                                parent_path = item.get("parentReference", {}).get("path", "")
                                                if parent_path.startswith("/drive/root:"):
                                                    parent_path = parent_path.replace("/drive/root:", "")
                                                elif parent_path.startswith("/drive/root"):
                                                    parent_path = parent_path.replace("/drive/root", "")
                                                
                                                full_path = f"{user_email}{parent_path}/{item_name}".strip("/")
                                                full_path = normalize_path(full_path)
                                                target_path = normalize_path(f"{user_email}/{normalized_path}")
                                                
                                                if target_path.lower() != full_path.lower():
                                                    continue
                                            
                                            found_file = item
                                            logger.info(f"Found file: {file_name} for user {user_email}")
                                            break
                                    
                                    if not found_file:
                                        logger.warning(f"File {file_name} not found for user {user_email}")
                                        continue
                                    
                                    # Download the file
                                    download_url = found_file.get("@microsoft.graph.downloadUrl")
                                    if not download_url:
                                        logger.error(f"No download URL for file {file_name}")
                                        continue
                                    
                                    # Create temp file path
                                    temp_dir = f"{self.local_data_dir}/dsr"
                                    create_folder(temp_dir)
                                    local_filepath = f"{temp_dir}/{uuid4()}_{file_name}"
                                    
                                    # Download file
                                    success = await self.download_file_from_sharepoint(
                                        download_url, local_filepath, user_email
                                    )
                                    
                                    if not success or not file_exists(local_filepath):
                                        logger.error(f"Failed to download file {file_name}")
                                        continue
                                    
                                    # Prepare result
                                    file_ext = get_file_extension(file_name)
                                    results.append({
                                        "service_name": self.service_name,
                                        "service_provider": str(self.service_provider),
                                        "sub_service": str(self.sub_service),
                                        "user_email": user_email,
                                        "user_name": user_name,
                                        "file_name": file_name,
                                        "file_uri": file_uri,
                                        "file_type": file_ext,
                                        "file_size": found_file.get("size", 0),
                                        "local_filepath": local_filepath,
                                    })
                                    logger.info(f"DSR: Downloaded file {file_name} for user {user_email}")
                                else:
                                    response_text = await response.text()
                                    logger.error(f"Failed to search file for user {user_id} - Status: {response.status}, Response: {response_text}")
                    except Exception as e:
                        logger.error(f"Error searching file for user {user_email}: {str(e)}")
            
            # Yield all results
            if results:
                yield results
                logger.info(f"DSR scan completed: {len(results)} files processed")
            else:
                logger.warning("DSR scan completed: No files were processed")
                
        except Exception as e:
            logger.error(f"Error during SharePoint DSR user scan: {str(e)}", exc_info=True)

    async def deep_scan(self) -> AsyncGenerator[FileMetadata, None]:
        """Deep scan SharePoint data for accessible users"""
        logger.info("Starting SharePoint deep scan")
        total_file_size = 0
        total_file_count = 0
        try:
            await self.get_service_details()
            accessible_users = await self.get_accessible_users()
            
            if not accessible_users:
                logger.warning("No accessible users found for SharePoint scanning")
                return

            logger.info(f"Starting SharePoint deep scan for {len(accessible_users)} accessible users")
            
            for user in accessible_users:
                user_email = user.get("mail") or user.get("userPrincipalName")
                user_id = user.get("id")
                user_name = user.get("displayName", "")
                
                if not user_email or not user_id:
                    logger.warning(f"Skipping user with missing email or ID: {user}")
                    continue

                logger.info(f"Scanning SharePoint for user: {user_email} ({user_id})")
                
                async for file_metadata in self.scan_user_sharepoint_deep(user_id, user_email):
                    file_metadata.details.update({
                        "user_name": user_name,
                    })
                    total_file_size += file_metadata.file_size
                    total_file_count += 1
                    yield file_metadata

            logger.info("SharePoint deep scan completed for all accessible users")

        except Exception as e:
            logger.error(f"Error during SharePoint deep scan: {str(e)}", exc_info=True)
        finally:
            # ---- Asset details ----
            try:
                tenant_region = await get_tenant_info(self.access_token)
                encryption_enabled = await self.get_sharepoint_encryption_status()
            except Exception as e:
                tenant_region = "unknown"
                encryption_enabled = False
            
            try:
                asset = [
                    AssetDetailsSchema(
                        asset_name=f"sharepoint_{self.service_name}",
                        service_provider=self.service_provider,
                        type="file_storage",
                        category="unstructured",
                        location=tenant_region,
                        owner="system",
                        security=encryption_enabled,
                        size=total_file_size,
                        count=total_file_count,
                        access_category="restricted",
                        service_name=str(self.service_name),
                        steward=str(self.service_steward),
                    )
                ]
                load_asset_details(asset)
    
                access = []
                for user in accessible_users:
                    user_email = user.get("mail") or user.get("userPrincipalName")
                    user_id = user["id"]
    
                    if not user_email:
                        continue
    
                    access.append({
                        "asset_name": f"sharepoint_{self.service_name}",
                        "user_or_role": user_email,
                        "role": "user",
                        "access": "full",
                    })
    
                if not access:
                    access.append({
                        "asset_name": f"sharepoint_{self.service_name}",
                        "user_or_role": "system",
                        "role": "owner",
                        "access": "full",
                    })
    
                load_access_controls(access)
            except Exception as e:
                logger.error(f"Error loading asset details and access controls: {str(e)}")