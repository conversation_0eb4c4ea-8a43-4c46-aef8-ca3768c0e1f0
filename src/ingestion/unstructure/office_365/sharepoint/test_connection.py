import aiohttp
import logging
from src.utils.exceptions import ClientException, ServerException

logger = logging.getLogger("Unstructure")

async def get_ms_graph_token(tenant_id: str, client_id: str, client_secret: str) -> str:
    token_url = f"https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/token"
    data = {
        "client_id": client_id,
        "client_secret": client_secret,
        "grant_type": "client_credentials",
        "scope": "https://graph.microsoft.com/.default"
    }

    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(token_url, data=data) as resp:
                token_data = await resp.json()
                if resp.status != 200:
                    raise ClientException(f"Token fetch failed: {token_data}", code=resp.status)
                return token_data["access_token"]
    except Exception as e:
        raise ServerException("Error while fetching Microsoft Graph token", excep=e)


async def test_sharepoint_connection(data: dict) -> dict:
    creds = data.get("credentials", {})
    try:
        token = await get_ms_graph_token(
            creds["tenant_id"],
            creds["client_id"],
            creds["client_secret"]
        )
        headers = {"Authorization": f"Bearer {token}"}

        async with aiohttp.ClientSession() as session:
            # Step 1: Validate organization access
            async with session.get("https://graph.microsoft.com/v1.0/organization", headers=headers) as org_resp:
                if org_resp.status != 200:
                    body = await org_resp.text()
                    raise ClientException(f"Invalid Microsoft credentials: {body}", code=org_resp.status)

            # Step 2: Validate SharePoint root site access
            async with session.get("https://graph.microsoft.com/v1.0/sites/root", headers=headers) as sp_resp:
                if sp_resp.status == 200:
                    return {
                        "status": True,
                        "message": "SharePoint connection verified successfully"
                    }
                else:
                    msg = await sp_resp.text()
                    raise ClientException(f"SharePoint access failed: {msg}", code=sp_resp.status)

    except ClientException as ce:
        raise ce
    except Exception as e:
        raise ServerException("Unexpected error in SharePoint connection test", excep=e)
