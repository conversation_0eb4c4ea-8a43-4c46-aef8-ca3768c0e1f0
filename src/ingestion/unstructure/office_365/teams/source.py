import asyncio
import json
import re
import time
import os
from datetime import datetime
from typing import As<PERSON><PERSON>enerator, Dict, List
from uuid import uuid4

import aiohttp

from src.common.config import (
    CONCURRENT_LIMIT,
    PROFILE_MAX_SIZE_THRESHOLD,
    PROFILE_SUPPORTED_FILE_TYPES,
    TEMP_DATA_DIR,
)
from src.common.constants import M365SubServices, ServiceProviders, ServiceTypes
from src.ingestion.base_source import UnstructureSource
from src.ingestion.data_class import FileMetadata
from src.ingestion.unstructure.office_365.connection import (
    get_365_authenticators,
    get_all_users,
    get_tenant_info,
    refresh_token_if_needed,
    log_graph_api_request,
    get_user_by_email,
)
from src.modules.repos import DatabaseManager
from src.utils.helpers import create_folder, file_exists, get_file_extension
from src.utils.logger import get_ingestion_logger
from src.utils.loaders import load_access_controls, load_asset_details
from src.ingestion.data_class import AssetsDetails as AssetDetailsSchema

logger = get_ingestion_logger()


class TeamsSource(UnstructureSource):
    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.MS_365_TEAMS.value, service_name)
        self.db = DatabaseManager()
        self.service_provider = ServiceProviders.M365
        self.sub_service = M365SubServices.MS_TEAMS
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"
        self.access_token = None
        self.token_expires_at = None
        self.auth_app = None
        self.service_steward = None
        # Rate limiting control
        self.api_calls_count = 0
        self.last_api_call_time = 0
        self.rate_limit_delay = 0.5  # Start with 500ms delay between calls

    async def get_service_details(self) -> Dict:
        """Gets service details and initializes authentication"""
        self.service = await self.db.get_service_by_service_name(self.service_name)
        credentials = self.service.get("credentials", {})
        self.tenant_id = credentials.get("tenant_id")
        self.client_id = credentials.get("client_id")
        self.client_secret = credentials.get("client_secret")
        self.service_steward = self.service.get("data_steward")

        include_emails = credentials.get("include_emails", "")
        exclude_emails = credentials.get("exclude_emails", "")

        # Parse include_emails - handle multiple formats
        if include_emails:
            if isinstance(include_emails, str):
                # Handle comma, semicolon, or space separated emails
                email_list = re.split(r'[,;\s]+', include_emails.strip())
                self.include_emails = [e.strip() for e in email_list if e.strip()]
            elif isinstance(include_emails, list):
                self.include_emails = [str(e).strip() for e in include_emails if str(e).strip()]
            else:
                self.include_emails = []
        else:
            self.include_emails = []

        # Parse exclude_emails
        if exclude_emails:
            if isinstance(exclude_emails, str):
                email_list = re.split(r'[,;\s]+', exclude_emails.strip())
                self.exclude_emails = [e.strip() for e in email_list if e.strip()]
            elif isinstance(exclude_emails, list):
                self.exclude_emails = [str(e).strip() for e in exclude_emails if str(e).strip()]
            else:
                self.exclude_emails = []
        else:
            self.exclude_emails = []

        # Get initial token and set up authentication
        auth_result = get_365_authenticators(
            self.tenant_id, self.client_id, self.client_secret
        )
        self.access_token = auth_result["access_token"]
        self.token_expires_at = auth_result.get("expires_at")
        self.auth_app = auth_result.get("auth_app")

        return self.service

    async def ensure_valid_token(self) -> bool:
        """Ensure we have a valid access token, refresh if needed"""
        try:
            if self.token_expires_at and time.time() >= self.token_expires_at - 300:  # Refresh 5 min before expiry
                logger.info("Access token expired or expiring soon, refreshing...")
                auth_result = refresh_token_if_needed(
                    self.auth_app, self.tenant_id, self.client_id, self.client_secret
                )
                if auth_result:
                    self.access_token = auth_result["access_token"]
                    self.token_expires_at = auth_result.get("expires_at")
                    logger.info("Access token refreshed successfully")
                    return True
                else:
                    logger.error("Failed to refresh Teams access token")
                    return False
            return True
        except Exception as e:
            logger.error(f"Error ensuring valid token: {str(e)}")
            return False

    async def handle_rate_limiting(self):
        """Handle rate limiting with exponential backoff"""
        current_time = time.time()
        time_since_last_call = current_time - self.last_api_call_time
        
        if time_since_last_call < self.rate_limit_delay:
            sleep_time = self.rate_limit_delay - time_since_last_call
            await asyncio.sleep(sleep_time)
        
        self.last_api_call_time = time.time()
        self.api_calls_count += 1
        
        # Increase delay if we're making too many calls
        if self.api_calls_count % 50 == 0:
            self.rate_limit_delay = min(self.rate_limit_delay * 1.2, 2.0)
            logger.info(f"Adjusted rate limit delay to {self.rate_limit_delay}s after {self.api_calls_count} calls")

    async def handle_429_error(self, response_headers):
        """Handle 429 rate limit errors with proper retry logic"""
        retry_after = response_headers.get('Retry-After')
        if retry_after:
            try:
                delay = int(retry_after)
                logger.warning(f"Rate limited, waiting {delay} seconds as specified in Retry-After header")
                await asyncio.sleep(delay)
                self.rate_limit_delay = max(delay / 2, 1.0)  # Adjust our rate limiting
                return True
            except (ValueError, TypeError):
                pass
        
        # Exponential backoff if no Retry-After header
        delay = min(self.rate_limit_delay * 2, 60)
        logger.warning(f"Rate limited, using exponential backoff: {delay} seconds")
        await asyncio.sleep(delay)
        self.rate_limit_delay = delay
        return True

    async def test_connection(self) -> Dict:
        """Test Teams authentication"""
        try:
            if not await self.ensure_valid_token():
                return {"status": False, "message": "Token refresh failed"}

            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json",
            }
            
            url = "https://graph.microsoft.com/v1.0/organization"
            log_graph_api_request("GET", url, headers, None, "Application")

            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    response_text = await response.text()
                    
                    if response.status == 200:
                        logger.info(f"Graph API Response: {response.status} - Authentication successful")
                        return {"status": True, "message": "Authentication successful"}
                    else:
                        try:
                            error_data = json.loads(response_text)
                            error_msg = error_data.get("error", {}).get("message", "Unknown error")
                            error_code = error_data.get("error", {}).get("code", "Unknown")
                            request_id = response.headers.get("request-id", "N/A")
                            timestamp = response.headers.get("date", "N/A")
                        except:
                            error_msg = response_text
                            error_code = "Unknown"
                            request_id = "N/A"
                            timestamp = "N/A"
                            
                        logger.error(f"Graph API Error - Status: {response.status}, Code: {error_code}, "
                                   f"Message: {error_msg}, Request ID: {request_id}, Timestamp: {timestamp}")
                        return {
                            "status": False,
                            "message": f"Token validation failed: {error_msg}",
                        }
        except Exception as e:
            logger.error(f"Teams connection error: {str(e)}")
            return {"status": False, "message": str(e)}

    async def get_accessible_users(self) -> List[Dict]:
        """Get list of users we have permission to access"""
        accessible_users = []
        
        # If include_emails is specified, prioritize those users
        if self.include_emails:
            logger.info(f"Using specified include_emails: {self.include_emails}")
            
            for email in self.include_emails:
                try:
                    user = await get_user_by_email(self.access_token, email.strip())
                    if user:
                        accessible_users.append(user)
                        logger.info(f"✓ User {email} found and accessible")
                    else:
                        logger.warning(f"✗ User with email {email} not found in tenant")
                except Exception as e:
                    logger.error(f"Error fetching user {email}: {str(e)}")
        else:
            # Get all users if no specific emails provided
            logger.info("No specific emails provided, fetching all users...")
            try:
                all_users = await get_all_users(self.access_token, log_requests=False)
                
                for user in all_users:
                    user_mail = user.get("mail")
                    user_principal = user.get("userPrincipalName")
                    user_email = user_mail if user_mail else user_principal
                    
                    if not user_email:
                        continue
                    
                    # Skip excluded emails
                    if self.exclude_emails:
                        user_email_lower = user_email.lower()
                        if any(user_email_lower == excluded.lower() for excluded in self.exclude_emails):
                            continue
                    
                    accessible_users.append(user)
            except Exception as e:
                logger.error(f"Error fetching all users: {str(e)}")
        
        logger.info(f"✓ Found {len(accessible_users)} accessible users for Teams scanning")
        return accessible_users

    async def construct_teams_file_uri(self, user_email: str, filename: str) -> str:
        """Construct Teams file URI"""
        try:
            file_uri = f"teams/{user_email}/exported_chats/{filename}"
            logger.debug(f"Generated file URI: {file_uri}")
            return file_uri
        except Exception as e:
            logger.error(f"Error constructing file URI for user {user_email}: {str(e)}")
            return f"teams/unknown/exported_chats/{filename}"

    async def download_attachment(self, attachment_url: str, filepath: str, user_email: str = None, max_retries: int = 3) -> bool:
        """Download Teams message attachment with proper error handling"""
        if not attachment_url or not filepath:
            logger.warning(f"Invalid attachment URL or filepath for user {user_email}")
            return False

        if file_exists(filepath):
            return True

        if not await self.ensure_valid_token():
            logger.error(f"Cannot download attachment for user {user_email} - token refresh failed")
            return False

        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
        }

        for attempt in range(max_retries):
            try:
                await self.handle_rate_limiting()
                
                async with aiohttp.ClientSession() as session:
                    log_graph_api_request("GET", attachment_url, headers, None, "Application")
                    
                    async with session.get(attachment_url, headers=headers) as response:
                        if response.status == 200:
                            content = await response.read()
                            # Ensure directory exists
                            os.makedirs(os.path.dirname(filepath), exist_ok=True)
                            with open(filepath, "wb") as f:
                                f.write(content)
                            logger.debug(f"Successfully downloaded attachment to {filepath}")
                            return True
                        elif response.status == 429:
                            if await self.handle_429_error(response.headers) and attempt < max_retries - 1:
                                continue
                        elif response.status in [401, 403]:
                            response_text = await response.text()
                            request_id = response.headers.get("request-id", "N/A")
                            timestamp = response.headers.get("date", "N/A")
                            logger.warning(f"Access denied for attachment download for {user_email} - "
                                         f"Status: {response.status}, Request ID: {request_id}, Timestamp: {timestamp}")
                            return False
                        elif response.status == 404:
                            logger.warning(f"Attachment not found for user {user_email}: {attachment_url}")
                            return False
                        else:
                            response_text = await response.text()
                            request_id = response.headers.get("request-id", "N/A")
                            timestamp = response.headers.get("date", "N/A")
                            logger.error(f"Failed to download attachment for {user_email} - Status: {response.status}, "
                                       f"Request ID: {request_id}, Timestamp: {timestamp}")
                            return False
            except Exception as e:
                logger.error(f"Error downloading attachment for {user_email} (attempt {attempt + 1}): {str(e)}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(2 ** attempt)  # Exponential backoff
                
        return False

    async def process_message_attachments(self, message: dict, user_email: str, semaphore) -> List[Dict]:
        """Process and download message attachments with better error handling"""
        attachments_info = []
        attachments = message.get("attachments", [])
        
        if not attachments:
            return attachments_info

        async with semaphore:
            for attachment in attachments:
                try:
                    attachment_name = attachment.get("name")
                    if not attachment_name:
                        logger.debug(f"Skipping attachment with no name for user {user_email}")
                        continue
                        
                    attachment_size = attachment.get("contentBytes", 0)
                    content_url = attachment.get("contentUrl")
                    
                    # Skip if no content URL
                    if not content_url:
                        logger.debug(f"Skipping attachment {attachment_name} - no content URL")
                        continue
                    
                    # Skip if too large
                    if attachment_size > PROFILE_MAX_SIZE_THRESHOLD:
                        logger.info(f"Skipping attachment {attachment_name}: Size {attachment_size} exceeds threshold")
                        continue
                    
                    # Check file type
                    file_ext = get_file_extension(attachment_name)
                    if file_ext not in PROFILE_SUPPORTED_FILE_TYPES:
                        logger.info(f"Skipping attachment {attachment_name}: Unsupported file type .{file_ext}")
                        continue
                    
                    # Create temp directory for attachments
                    temp_dir = f"{self.local_data_dir}/attachments"
                    create_folder(temp_dir)
                    filepath = f"{temp_dir}/{str(uuid4())}_{attachment_name}"
                    
                    success = await self.download_attachment(content_url, filepath, user_email)
                    if success:
                        attachments_info.append({
                            "name": attachment_name,
                            "size": attachment_size,
                            "file_type": file_ext,
                            "filepath": filepath,
                            "content_url": content_url,
                            "success": True
                        })
                    else:
                        logger.debug(f"Failed to download attachment {attachment_name} for user {user_email}")
                        
                except Exception as e:
                    logger.error(f"Error processing attachment for user {user_email}: {str(e)}")
        
        return attachments_info

    async def save_user_chats_to_file(self, user_id: str, chats_data: dict, filepath: str) -> tuple:
        """Saves all user's Teams chats to a JSON file"""
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(chats_data, f, ensure_ascii=False, indent=2)
            logger.info(f"Successfully saved chat data for user {user_id} to {filepath}")
            
            user_email = chats_data.get("user_email") or chats_data.get("userPrincipalName") or "unknown"
            filename = os.path.basename(filepath)
            file_uri = await self.construct_teams_file_uri(user_email, filename)
            return True, file_uri
        except Exception as e:
            logger.error(f"Error saving chat data for user {user_id}: {str(e)}")
            return False, None

    async def fetch_chat_messages_with_retry(self, session, url: str, headers: dict, user_email: str, max_retries: int = 3) -> dict:
        """Fetch messages with proper retry logic"""
        for attempt in range(max_retries):
            try:
                await self.handle_rate_limiting()
                log_graph_api_request("GET", url, headers, None, "Application")
                
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        return await response.json()
                    elif response.status == 429:
                        if await self.handle_429_error(response.headers) and attempt < max_retries - 1:
                            continue
                        else:
                            logger.error(f"Rate limit exceeded after retries for {user_email}")
                            return None
                    elif response.status in [403, 401]:
                        logger.warning(f"Access denied for user {user_email} - Status: {response.status}")
                        return None
                    else:
                        response_text = await response.text()
                        request_id = response.headers.get("request-id", "N/A")
                        timestamp = response.headers.get("date", "N/A")
                        logger.error(f"Failed to fetch messages for user {user_email} - "
                                   f"Status: {response.status}, Request ID: {request_id}, Timestamp: {timestamp}")
                        return None
            except Exception as e:
                logger.error(f"Error fetching messages for user {user_email} (attempt {attempt + 1}): {str(e)}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(2 ** attempt)
                    
        return None

    async def fetch_chat_messages(self, session, chat_id: str, headers: dict, user_email: str) -> list:
        """Fetches all messages for a specific chat with pagination and proper error handling"""
        all_messages = []
        messages_url = f"https://graph.microsoft.com/v1.0/chats/{chat_id}/messages"
        
        try:
            while messages_url:
                messages_data = await self.fetch_chat_messages_with_retry(session, messages_url, headers, user_email)
                if not messages_data:
                    break
                    
                messages = messages_data.get("value", [])
                all_messages.extend(messages)
                
                # Check for pagination
                messages_url = messages_data.get("@odata.nextLink")
                
                logger.debug(f"Retrieved {len(messages)} messages from chat {chat_id} for user {user_email}")
                
                # Safety check - prevent infinite loops
                if len(all_messages) > 10000:  # Reasonable limit
                    logger.warning(f"Chat {chat_id} has excessive messages ({len(all_messages)}), stopping pagination")
                    break
                        
        except Exception as e:
            logger.error(f"Error fetching messages for chat {chat_id} and user {user_email}: {str(e)}")
        
        logger.info(f"Total messages retrieved for chat {chat_id}: {len(all_messages)}")
        return all_messages

    async def fetch_chat_members(self, session, chat_id: str, headers: dict, user_email: str) -> list:
        """Fetches all members of a specific chat with proper error handling"""
        members = []
        try:
            members_url = f"https://graph.microsoft.com/v1.0/chats/{chat_id}/members"
            
            members_data = await self.fetch_chat_messages_with_retry(session, members_url, headers, user_email)
            if members_data:
                members = members_data.get("value", [])
                logger.debug(f"Retrieved {len(members)} members from chat {chat_id} for user {user_email}")
            
        except Exception as e:
            logger.error(f"Error fetching members for chat {chat_id} and user {user_email}: {str(e)}")
        
        return members

    async def fetch_user_teams_data(self, user_id: str, user_email: str, semaphore) -> Dict:
        """Fetches all Teams data for a user and saves to file"""
        if not await self.ensure_valid_token():
            logger.error(f"Cannot fetch Teams data for user {user_id} - token refresh failed")
            return {"success": False, "total_chats": 0, "total_messages": 0}

        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
        }

        teams_temp_dir = self.local_data_dir
        create_folder(teams_temp_dir)

        chat_info = {
            "name": f"teams_chat_{user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            "createdDateTime": datetime.now().isoformat(),
            "user_id": user_id,
            "user_email": user_email,
            "total_chats": 0,
            "total_messages": 0,
            "total_attachments": 0,
            "success": False
        }

        filepath = f"{teams_temp_dir}/{str(uuid4())}_{chat_info['name']}"
        chat_info["filepath"] = filepath

        async with semaphore:
            try:
                async with aiohttp.ClientSession() as session:
                    # Fetch all chats for the user
                    chat_url = f"https://graph.microsoft.com/v1.0/users/{user_id}/chats"
                    
                    chat_data = await self.fetch_chat_messages_with_retry(session, chat_url, headers, user_email)
                    if not chat_data:
                        logger.error(f"Failed to fetch chats for user {user_email}")
                        return chat_info
                    
                    chats = chat_data.get("value", [])
                    chat_info["total_chats"] = len(chats)

                    # Prepare complete data structure
                    complete_data = {
                        "user_id": user_id,
                        "user_email": user_email,
                        "extraction_time": datetime.now().isoformat(),
                        "total_chats": len(chats),
                        "chats": [],
                    }

                    # Process each chat
                    for i, chat in enumerate(chats):
                        try:
                            chat_id = chat["id"]
                            logger.debug(f"Processing chat {i+1}/{len(chats)} for user {user_email}: {chat_id}")
                            
                            # Fetch messages and members for each chat
                            messages = await self.fetch_chat_messages(session, chat_id, headers, user_email)
                            members = await self.fetch_chat_members(session, chat_id, headers, user_email)

                            # Process attachments in messages
                            total_chat_attachments = 0
                            for message in messages:
                                try:
                                    attachments_info = await self.process_message_attachments(
                                        message, user_email, semaphore
                                    )
                                    if attachments_info:
                                        message["processed_attachments"] = attachments_info
                                        total_chat_attachments += len(attachments_info)
                                except Exception as e:
                                    logger.error(f"Error processing attachments for message in chat {chat_id}: {str(e)}")

                            chat_data_item = {
                                "chat_id": chat_id,
                                "chat_type": chat.get("chatType"),
                                "created_datetime": chat.get("createdDateTime"),
                                "topic": chat.get("topic"),
                                "web_url": chat.get("webUrl"),
                                "members": members,
                                "messages": messages,
                                "message_count": len(messages),
                                "member_count": len(members),
                                "attachment_count": total_chat_attachments
                            }
                            
                            complete_data["chats"].append(chat_data_item)
                            chat_info["total_messages"] += len(messages)
                            chat_info["total_attachments"] += total_chat_attachments
                            
                        except Exception as e:
                            logger.error(f"Error processing chat {chat.get('id', 'unknown')} for user {user_email}: {str(e)}")

                    # Update complete data with totals
                    complete_data["total_messages"] = chat_info["total_messages"]
                    complete_data["total_attachments"] = chat_info["total_attachments"]

                    # Save all data to file
                    success, file_uri = await self.save_user_chats_to_file(user_id, complete_data, filepath)
                    if success:
                        chat_info["success"] = True
                        chat_info["file_uri"] = file_uri
                        logger.info(f"Saved {chat_info['total_messages']} messages from "
                                  f"{len(chats)} chats with {chat_info['total_attachments']} attachments for user {user_email}")
                    else:
                        logger.error(f"Failed to save chat data for user {user_email}")
                            
            except Exception as e:
                logger.error(f"Error processing Teams data for user {user_email}: {str(e)}")

        return chat_info

    async def scan_user_teams_infra(self, user_id: str, user_email: str) -> dict:
        """Scans Teams infrastructure for a specific user with lightweight approach"""
        if not await self.ensure_valid_token():
            return {"total_chats": 0, "total_messages": 0}
            
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
        }

        stats = {"total_chats": 0, "total_messages": 0}

        try:
            async with aiohttp.ClientSession() as session:
                # Get chats count
                chat_url = f"https://graph.microsoft.com/v1.0/users/{user_id}/chats"
                
                chat_data = await self.fetch_chat_messages_with_retry(session, chat_url, headers, user_email)
                if chat_data:
                    chats = chat_data.get("value", [])
                    stats["total_chats"] = len(chats)

                    # Get message count for limited number of chats (infra scan should be lightweight)
                    sample_chats = chats[:5]  # Only sample first 5 chats for infra scan
                    for chat in sample_chats:
                        try:
                            messages = await self.fetch_chat_messages(session, chat["id"], headers, user_email)
                            stats["total_messages"] += len(messages)
                        except Exception as e:
                            logger.error(f"Error counting messages for chat {chat['id']}: {str(e)}")

                    # Estimate total messages based on sample
                    if sample_chats and stats["total_messages"] > 0:
                        avg_messages = stats["total_messages"] / len(sample_chats)
                        stats["total_messages"] = int(avg_messages * len(chats))

                    logger.info(f"Teams infra scan for {user_email}: {stats['total_chats']} chats, ~{stats['total_messages']} estimated messages")

        except Exception as e:
            logger.error(f"Teams infra scan failed for user {user_email}: {str(e)}")

        return stats

    async def infra_scan(self) -> Dict:
        """Infrastructure scan for Teams"""
        try:
            await self.get_service_details()
            tenant_region = await get_tenant_info(self.access_token)
            accessible_users = await self.get_accessible_users()

            total_stats = {
                "total_chats": 0,
                "total_messages": 0,
                "total_users": len(accessible_users),
                "accessible_users_count": len(accessible_users),
            }

            for user in accessible_users:
                user_id = user.get("id")
                user_email = user.get("mail") or user.get("userPrincipalName")
                
                if user_id and user_email:
                    user_stats = await self.scan_user_teams_infra(user_id, user_email)
                    total_stats["total_chats"] += user_stats["total_chats"]
                    total_stats["total_messages"] += user_stats["total_messages"]

            infra = {
                "tenant_region": tenant_region,
                "users": accessible_users,
                **total_stats,
            }

            logger.info(f"Teams infrastructure scan completed: {total_stats['total_chats']} chats, "
                       f"{total_stats['total_messages']} messages found across {total_stats['total_users']} users")
            return infra

        except Exception as e:
            logger.error(f"Error during Teams infra scanning: {str(e)}", exc_info=True)
            return {
                "tenant_region": tenant_region if 'tenant_region' in locals() else "",
                "users": accessible_users if 'accessible_users' in locals() else [],
                "total_chats": 0,
                "total_messages": 0,
                "total_users": 0,
                "accessible_users_count": 0,
            }

    async def get_teams_encryption_status(self) -> bool:
        """Determine encryption status for Teams"""
        # Microsoft Teams encrypts data at rest and in transit by default
        return True

    async def deep_scan(self) -> AsyncGenerator[FileMetadata, None]:
        """Deep scan Teams messages for users"""
        logger.info("Starting Teams deep scan")
        total_file_size = 0
        total_file_count = 0
        try:
            await self.get_service_details()
            accessible_users = await self.get_accessible_users()
            
            if not accessible_users:
                logger.warning("No accessible users found for Teams scanning")
                return

            logger.info(f"Starting Teams deep scan for {len(accessible_users)} accessible users")

            for user in accessible_users:
                user_email = user.get("mail") or user.get("userPrincipalName")
                user_id = user.get("id")
                user_name = user.get("displayName", "")

                if not user_email or not user_id:
                    logger.warning(f"Skipping user with missing email or ID: {user}")
                    continue

                logger.info(f"Scanning Teams for user: {user_email} ({user_id})")

                try:
                    semaphore = asyncio.Semaphore(CONCURRENT_LIMIT)
                    chat_info = await self.fetch_user_teams_data(user_id, user_email, semaphore)

                    if chat_info and chat_info.get("success"):
                        # Get file size
                        file_size = 0
                        try:
                            if file_exists(chat_info["filepath"]):
                                file_size = os.path.getsize(chat_info["filepath"])
                        except Exception as e:
                            logger.warning(f"Could not get file size for {chat_info['filepath']}: {str(e)}")

                        yield FileMetadata(
                            service_name=self.service_name,
                            service_type=self.service_type,
                            service_provider=self.service_provider,
                            sub_service=self.sub_service,
                            file_key=chat_info["name"],
                            file_size=file_size,
                            file_type="json",
                            file_uri=chat_info["file_uri"],
                            local_filepath=chat_info["filepath"],
                            details={
                                "created": chat_info["createdDateTime"],
                                "modified": chat_info["createdDateTime"],
                                "user_email": user_email,
                                "user_id": user_id,
                                "user_name": user_name,
                                "total_chats": chat_info["total_chats"],
                                "total_messages": chat_info["total_messages"],
                                "total_attachments": chat_info.get("total_attachments", 0),
                                "region": "India"
                            }
                        )
                        total_file_size += file_size
                        total_file_count += 1
                    else:
                        logger.warning(f"No Teams data retrieved for user {user_email}")
                        
                except Exception as e:
                    logger.error(f"Error processing user {user_email}: {str(e)}")
                    continue

            logger.info("Teams deep scan completed for all accessible users")

        except Exception as e:
            logger.error(f"Error during Teams deep scan: {str(e)}", exc_info=True)
        finally:
            # ---- Asset details ----
            try:
                tenant_region = await get_tenant_info(self.access_token)
                encryption_enabled = await self.get_teams_encryption_status()
            except Exception as e:
                tenant_region = "unknown"
                encryption_enabled = False
            
            try:
                asset = [
                    AssetDetailsSchema(
                        asset_name=f"teams_{self.service_name}",
                        service_provider=self.service_provider,
                        type="collaboration",
                        category="unstructured",
                        location=tenant_region,
                        owner="system",
                        security=encryption_enabled,
                        size=total_file_size,
                        count=total_file_count,
                        access_category="restricted",
                        service_name=str(self.service_name),
                        steward=str(self.service_steward),
                    )
                ]
                load_asset_details(asset)
    
                access = []
                for user in accessible_users:
                    user_email = user.get("mail") or user.get("userPrincipalName")
                    user_id = user["id"]
    
                    if not user_email:
                        continue
    
                    access.append({
                        "asset_name": f"teams_{self.service_name}",
                        "user_or_role": user_email,
                        "role": "user",
                        "access": "full",
                    })
    
                if not access:
                    access.append({
                        "asset_name": f"teams_{self.service_name}",
                        "user_or_role": "system",
                        "role": "owner",
                        "access": "full",
                    })
    
                load_access_controls(access)
            except Exception as e:
                logger.error(f"Error loading asset details and access controls: {str(e)}")