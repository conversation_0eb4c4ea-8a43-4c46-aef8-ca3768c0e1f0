import aiohttp
import logging
from src.utils.exceptions import ClientException, ServerException

logger = logging.getLogger("Unstructure")

async def get_ms_graph_token(tenant_id: str, client_id: str, client_secret: str) -> str:
    token_url = f"https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/token"
    data = {
        "client_id": client_id,
        "client_secret": client_secret,
        "grant_type": "client_credentials",
        "scope": "https://graph.microsoft.com/.default"
    }

    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(token_url, data=data) as resp:
                token_data = await resp.json()
                if resp.status != 200:
                    raise ClientException(f"Token fetch failed: {token_data}", code=resp.status)
                return token_data["access_token"]
    except Exception as e:
        raise ServerException("Error while fetching Microsoft Graph token", excep=e)


async def test_teams_connection(data: dict) -> dict:
    creds = data.get("credentials", {})
    try:
        token = await get_ms_graph_token(
            creds["tenant_id"],
            creds["client_id"],
            creds["client_secret"]
        )
        headers = {"Authorization": f"Bearer {token}"}

        async with aiohttp.ClientSession() as session:
            # Verify token is valid
            async with session.get("https://graph.microsoft.com/v1.0/organization", headers=headers) as resp:
                if resp.status != 200:
                    body = await resp.text()
                    raise ClientException(f"Invalid Microsoft credentials: {body}", code=resp.status)

            # Verify Teams API access
            async with session.get("https://graph.microsoft.com/v1.0/teams", headers=headers) as teams_resp:
                if teams_resp.status == 200:
                    return {
                        "status": True,
                        "message": "Microsoft Teams connection verified successfully"
                    }
                else:
                    msg = await teams_resp.text()
                    raise ClientException(f"Teams access failed: {msg}", code=teams_resp.status)

    except ClientException as ce:
        raise ce
    except Exception as e:
        raise ServerException("Unexpected error in Teams connection test", excep=e)
