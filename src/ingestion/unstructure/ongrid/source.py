import aiohttp
import asyncio
from uuid import uuid4
from typing import As<PERSON><PERSON>enerator, Dict, List, Optional
from datetime import datetime
import os
import json
from urllib.parse import urljoin

from src.utils.logger import get_ingestion_logger
from src.common.config import (
    TEMP_DATA_DIR,
    CONCURRENT_LIMIT,
    PROFILE_MAX_SIZE_THRESHOLD,
    PROFILE_SUPPORTED_FILE_TYPES,
)
from src.common.constants import ServiceTypes, ServiceProviders
from src.ingestion.base_source import UnstructureSource
from src.ingestion.data_class import FileMetadata
from src.modules.repos import DatabaseManager
from src.utils.helpers import (
    file_exists,
    create_folder,
    get_file_extension,
)

logger = get_ingestion_logger()


class OnGridSource(UnstructureSource):
    """
    OnGrid Background Verification connector for fetching verification data and documents.
    Supports both API Key and OAuth 2.0 authentication.
    """

    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.ONGRID.value, service_name)
        self.db = DatabaseManager()
        self.service_provider = ServiceProviders.ONGRID
        self.sub_service = "background_verification"
        self.session: Optional[aiohttp.ClientSession] = None
        self.access_token: Optional[str] = None
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"
        
        # API Configuration
        self.base_url = None
        self.api_key = None
        self.auth_type = None  # 'api_key' or 'oauth'
        
        # OAuth specific
        self.client_id = None
        self.client_secret = None
        self.token_url = None
        
        # Pagination
        self.page_size = 100
        self.max_retries = 3
        self.retry_delay = 2

    async def get_service_details(self) -> Dict:
        """Fetch service credentials and configuration from database."""
        logger.info(f"Fetching service details for {self.service_name}")
        
        self.service = await self.db.get_service_by_service_name(self.service_name)
        credentials = self.service.get("credentials", {})
        
        # Base configuration
        self.base_url = credentials.get("base_url", "https://api.ongrid.in/v2").rstrip("/")
        self.auth_type = credentials.get("auth_type", "api_key")
        self.organization_id = credentials.get("organization_id")
        
        # API Key authentication
        if self.auth_type == "api_key":
            self.api_key = credentials.get("api_key")
            if not self.api_key:
                raise ValueError("API key is required for api_key authentication")
        
        # OAuth 2.0 authentication
        elif self.auth_type == "oauth":
            self.client_id = credentials.get("client_id")
            self.client_secret = credentials.get("client_secret")
            self.token_url = credentials.get("token_url", f"{self.base_url}/oauth/token")
            
            if not self.client_id or not self.client_secret:
                raise ValueError("client_id and client_secret are required for OAuth")
        else:
            raise ValueError(f"Unsupported auth_type: {self.auth_type}")
        
        # Optional configurations
        self.page_size = credentials.get("page_size", 100)
        
        logger.info(f"Service details loaded: auth_type={self.auth_type}, org_id={self.organization_id}")
        return self.service

    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create aiohttp session."""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=300, connect=60)
            self.session = aiohttp.ClientSession(timeout=timeout)
        return self.session

    async def _get_oauth_token(self) -> str:
        """Obtain OAuth 2.0 access token."""
        logger.info("Requesting OAuth access token")
        
        session = await self._get_session()
        payload = {
            "grant_type": "client_credentials",
            "client_id": self.client_id,
            "client_secret": self.client_secret,
        }
        
        try:
            async with session.post(self.token_url, json=payload) as response:
                response.raise_for_status()
                data = await response.json()
                access_token = data.get("access_token")
                
                if not access_token:
                    raise ValueError("No access_token in OAuth response")
                
                logger.info("OAuth access token obtained successfully")
                return access_token
        except Exception as e:
            logger.error(f"Failed to obtain OAuth token: {str(e)}")
            raise

    async def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers based on auth type."""
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
        }
        
        if self.auth_type == "api_key":
            headers["Authorization"] = f"Bearer {self.api_key}"
        elif self.auth_type == "oauth":
            if not self.access_token:
                self.access_token = await self._get_oauth_token()
            headers["Authorization"] = f"Bearer {self.access_token}"
        
        return headers

    async def _make_request(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict] = None,
        json_data: Optional[Dict] = None,
        retry_count: int = 0,
    ) -> Dict:
        """Make HTTP request with retry logic."""
        url = urljoin(self.base_url + "/", endpoint.lstrip("/"))
        session = await self._get_session()
        headers = await self._get_auth_headers()
        
        try:
            async with session.request(
                method, url, headers=headers, params=params, json=json_data
            ) as response:
                # Handle token expiration for OAuth
                if response.status == 401 and self.auth_type == "oauth":
                    logger.warning("Access token expired, refreshing...")
                    self.access_token = await self._get_oauth_token()
                    headers = await self._get_auth_headers()
                    
                    async with session.request(
                        method, url, headers=headers, params=params, json=json_data
                    ) as retry_response:
                        retry_response.raise_for_status()
                        return await retry_response.json()
                
                response.raise_for_status()
                return await response.json()
                
        except aiohttp.ClientError as e:
            if retry_count < self.max_retries:
                wait_time = self.retry_delay * (2 ** retry_count)
                logger.warning(
                    f"Request failed, retrying in {wait_time}s (attempt {retry_count + 1}/{self.max_retries}): {str(e)}"
                )
                await asyncio.sleep(wait_time)
                return await self._make_request(method, endpoint, params, json_data, retry_count + 1)
            
            logger.error(f"Request failed after {self.max_retries} retries: {str(e)}")
            raise

    async def _download_file(
        self, url: str, filepath: str, headers: Optional[Dict] = None
    ) -> bool:
        """Download file from URL to local filepath."""
        if file_exists(filepath):
            logger.info(f"File already exists at {filepath}, skipping download")
            return True
        
        logger.info(f"Downloading file from {url} to {filepath}")
        
        try:
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            session = await self._get_session()
            
            if headers is None:
                headers = await self._get_auth_headers()
            
            async with session.get(url, headers=headers) as response:
                response.raise_for_status()
                
                with open(filepath, "wb") as f:
                    async for chunk in response.content.iter_chunked(8192):
                        f.write(chunk)
                
                logger.info(f"Successfully downloaded file to {filepath}")
                return True
                
        except Exception as e:
            logger.error(f"Error downloading file from {url}: {str(e)}")
            if os.path.exists(filepath):
                os.remove(filepath)
            return False

    async def test_connection(self) -> Dict:
        """Test API connection and authentication."""
        logger.info(f"Testing connection for {self.service_name}")
        
        try:
            # Try to fetch service properties or make a simple API call
            response = await self._make_request("GET", "/verifications", params={"limit": 1})
            
            logger.info(f"Connection test successful for {self.service_name}")
            return {"status": True, "message": "Authentication successful"}
            
        except Exception as e:
            logger.error(f"Connection test failed for {self.service_name}: {str(e)}")
            return {"status": False, "message": f"Authentication failed: {str(e)}"}

    async def _fetch_all_verifications(self) -> List[Dict]:
        """Fetch all verification records with pagination."""
        logger.info(f"Fetching all verifications for organization {self.organization_id}")
        
        all_verifications = []
        page = 1
        has_more = True
        
        while has_more:
            params = {
                "limit": self.page_size,
                "page": page,
            }
            
            if self.organization_id:
                params["organization_id"] = self.organization_id
            
            try:
                response = await self._make_request("GET", "/verifications", params=params)
                
                verifications = response.get("data", [])
                if not verifications:
                    has_more = False
                else:
                    all_verifications.extend(verifications)
                    logger.info(f"Fetched page {page}: {len(verifications)} verifications")
                    
                    # Check if there are more pages
                    total = response.get("total", 0)
                    if len(all_verifications) >= total:
                        has_more = False
                    else:
                        page += 1
                        
            except Exception as e:
                logger.error(f"Error fetching verifications page {page}: {str(e)}")
                has_more = False
        
        logger.info(f"Total verifications fetched: {len(all_verifications)}")
        return all_verifications

    async def _fetch_verification_details(self, verification_id: str) -> Optional[Dict]:
        """Fetch detailed information for a specific verification."""
        logger.info(f"Fetching details for verification {verification_id}")
        
        try:
            response = await self._make_request("GET", f"/verifications/{verification_id}")
            return response
        except Exception as e:
            logger.error(f"Error fetching verification {verification_id}: {str(e)}")
            return None

    async def _fetch_verification_documents(self, verification_id: str) -> List[Dict]:
        """Fetch list of documents for a verification."""
        logger.info(f"Fetching documents for verification {verification_id}")
        
        try:
            response = await self._make_request(
                "GET", f"/verifications/{verification_id}/documents"
            )
            return response.get("data", [])
        except Exception as e:
            logger.error(f"Error fetching documents for {verification_id}: {str(e)}")
            return []

    async def _process_verification_json(
        self, verification: Dict, semaphore: asyncio.Semaphore
    ) -> Optional[Dict]:
        """Process verification JSON data and save to file."""
        verification_id = verification.get("id")
        if not verification_id:
            logger.warning("Verification missing ID, skipping")
            return None
        
        file_uri = f"verifications/{verification_id}/data.json"
        
        # Skip logic
        if self.rule_manager.skip_file(file_uri):
            logger.info(f"Skipping verification {verification_id} based on skip rules")
            return None
        
        # Fetch detailed verification data
        async with semaphore:
            details = await self._fetch_verification_details(verification_id)
            
            if not details:
                logger.warning(f"Could not fetch details for {verification_id}")
                return None
        
        # Create temp directory
        temp_dir = self.local_data_dir
        create_folder(temp_dir)
        
        filename = f"{verification_id}_data.json"
        filepath = f"{temp_dir}/{uuid4()}_{filename}"
        
        # Save JSON to file
        try:
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(details, f, indent=2, ensure_ascii=False)
            
            file_size = os.path.getsize(filepath)
            
            logger.info(f"Saved verification JSON: {filename} ({file_size} bytes)")
            
            return {
                "name": filename,
                "size": file_size,
                "file_type": "json",
                "file_uri": file_uri,
                "filepath": filepath,
                "created": details.get("created_at"),
                "modified": details.get("updated_at"),
                "verification_id": verification_id,
                "verification_type": details.get("type"),
                "status": details.get("status"),
                "success": True,
            }
            
        except Exception as e:
            logger.error(f"Error saving JSON for {verification_id}: {str(e)}")
            return None

    async def _process_document(
        self,
        verification_id: str,
        document: Dict,
        semaphore: asyncio.Semaphore,
    ) -> Optional[Dict]:
        """Process and download a single document."""
        doc_id = document.get("id")
        doc_name = document.get("name", f"document_{doc_id}")
        doc_url = document.get("url") or document.get("download_url")
        
        if not doc_url:
            logger.warning(f"No download URL for document {doc_id}")
            return None
        
        file_ext = get_file_extension(doc_name).lower() or "pdf"
        file_uri = f"verifications/{verification_id}/documents/{doc_id}/{doc_name}"
        
        # Skip logic
        if self.rule_manager.skip_file(file_uri):
            logger.info(f"Skipping document {doc_name} based on skip rules")
            return None
        
        doc_size = document.get("size", 0)
        
        if doc_size > PROFILE_MAX_SIZE_THRESHOLD:
            logger.info(
                f"Skipping {doc_name}: Size {doc_size} exceeds threshold {PROFILE_MAX_SIZE_THRESHOLD}"
            )
            return None
        
        if file_ext not in PROFILE_SUPPORTED_FILE_TYPES and file_ext != "json":
            logger.info(f"Skipping {doc_name}: Unsupported file type .{file_ext}")
            return None
        
        # Create temp directory
        temp_dir = self.local_data_dir
        create_folder(temp_dir)
        
        safe_name = doc_name.replace("/", "_").replace("\\", "_")
        filepath = f"{temp_dir}/{uuid4()}_{safe_name}"
        
        # Download document
        async with semaphore:
            success = await self._download_file(doc_url, filepath)
            
            if not success:
                return None
        
        # Get actual file size
        actual_size = os.path.getsize(filepath) if os.path.exists(filepath) else 0
        
        return {
            "name": doc_name,
            "size": actual_size,
            "file_type": file_ext,
            "file_uri": file_uri,
            "filepath": filepath,
            "created": document.get("created_at"),
            "modified": document.get("updated_at"),
            "verification_id": verification_id,
            "document_id": doc_id,
            "document_type": document.get("type"),
            "success": True,
        }

    async def _process_verification_report(
        self,
        verification_id: str,
        verification_data: Dict,
        semaphore: asyncio.Semaphore,
    ) -> Optional[Dict]:
        """Download verification report (PDF or JSON)."""
        logger.info(f"Fetching report for verification {verification_id}")
        
        try:
            async with semaphore:
                response = await self._make_request(
                    "GET", f"/verifications/{verification_id}/report"
                )
            
            # If response is JSON, save as JSON
            if isinstance(response, dict):
                temp_dir = self.local_data_dir
                create_folder(temp_dir)
                
                filename = f"{verification_id}_report.json"
                filepath = f"{temp_dir}/{uuid4()}_{filename}"
                file_uri = f"verifications/{verification_id}/report.json"
                
                with open(filepath, "w", encoding="utf-8") as f:
                    json.dump(response, f, indent=2, ensure_ascii=False)
                
                file_size = os.path.getsize(filepath)
                
                return {
                    "name": filename,
                    "size": file_size,
                    "file_type": "json",
                    "file_uri": file_uri,
                    "filepath": filepath,
                    "created": verification_data.get("created_at"),
                    "modified": verification_data.get("updated_at"),
                    "verification_id": verification_id,
                    "is_report": True,
                    "success": True,
                }
            
        except Exception as e:
            logger.error(f"Error fetching report for {verification_id}: {str(e)}")
        
        return None

    async def scan_ongrid_infra(self) -> Dict:
        """Scan OnGrid infrastructure to get total counts."""
        logger.info(f"Starting infrastructure scan for {self.service_name}")
        
        try:
            verifications = await self._fetch_all_verifications()
            total_files = len(verifications)
            total_size = 0
            
            # Estimate size (actual size will be calculated during deep scan)
            for verification in verifications[:10]:  # Sample first 10
                details = await self._fetch_verification_details(verification.get("id"))
                if details:
                    total_size += len(json.dumps(details).encode("utf-8"))
            
            # Extrapolate
            if total_files > 10:
                avg_size = total_size / min(10, total_files)
                total_size = int(avg_size * total_files)
            
            logger.info(
                f"Infrastructure scan completed: {total_files} verifications, "
                f"~{total_size} bytes (estimated)"
            )
            
            return {"total_size": total_size, "total_files": total_files}
            
        except Exception as e:
            logger.error(f"OnGrid infra scan failed: {str(e)}")
            return {"total_size": 0, "total_files": 0}

    async def scan_ongrid_deep(self) -> AsyncGenerator[FileMetadata, None]:
        """Deep scan to fetch all verification data and documents."""
        logger.info(f"Starting deep scan for {self.service_name}")
        
        try:
            verifications = await self._fetch_all_verifications()
            semaphore = asyncio.Semaphore(CONCURRENT_LIMIT)
            
            for verification in verifications:
                verification_id = verification.get("id")
                if not verification_id:
                    continue
                
                logger.info(f"Processing verification {verification_id}")
                
                # Process verification JSON data
                json_task = self._process_verification_json(verification, semaphore)
                json_result = await json_task
                
                if json_result and json_result.get("success"):
                    yield FileMetadata(
                        service_name=self.service_name,
                        service_type=self.service_type,
                        service_provider=self.service_provider,
                        sub_service=self.sub_service,
                        file_key=json_result["name"],
                        file_size=json_result["size"],
                        file_type=json_result["file_type"],
                        file_uri=json_result["file_uri"],
                        local_filepath=json_result["filepath"],
                        details={
                            "created": json_result.get("created"),
                            "modified": json_result.get("modified"),
                            "verification_id": json_result.get("verification_id"),
                            "verification_type": json_result.get("verification_type"),
                            "status": json_result.get("status"),
                            "data_type": "verification_data",
                        },
                    )
                
                # Fetch and process documents
                documents = await self._fetch_verification_documents(verification_id)
                
                if documents:
                    doc_tasks = [
                        self._process_document(verification_id, doc, semaphore)
                        for doc in documents
                    ]
                    
                    for doc_task in asyncio.as_completed(doc_tasks):
                        doc_result = await doc_task
                        
                        if doc_result and doc_result.get("success"):
                            yield FileMetadata(
                                service_name=self.service_name,
                                service_type=self.service_type,
                                service_provider=self.service_provider,
                                sub_service=self.sub_service,
                                file_key=doc_result["name"],
                                file_size=doc_result["size"],
                                file_type=doc_result["file_type"],
                                file_uri=doc_result["file_uri"],
                                local_filepath=doc_result["filepath"],
                                details={
                                    "created": doc_result.get("created"),
                                    "modified": doc_result.get("modified"),
                                    "verification_id": doc_result.get("verification_id"),
                                    "document_id": doc_result.get("document_id"),
                                    "document_type": doc_result.get("document_type"),
                                    "data_type": "document",
                                },
                            )
                
                # Try to fetch verification report
                report_result = await self._process_verification_report(
                    verification_id, verification, semaphore
                )
                
                if report_result and report_result.get("success"):
                    yield FileMetadata(
                        service_name=self.service_name,
                        service_type=self.service_type,
                        service_provider=self.service_provider,
                        sub_service=self.sub_service,
                        file_key=report_result["name"],
                        file_size=report_result["size"],
                        file_type=report_result["file_type"],
                        file_uri=report_result["file_uri"],
                        local_filepath=report_result["filepath"],
                        details={
                            "created": report_result.get("created"),
                            "modified": report_result.get("modified"),
                            "verification_id": report_result.get("verification_id"),
                            "data_type": "report",
                            "is_report": True,
                        },
                    )
            
            logger.info(f"Deep scan completed for {self.service_name}")
            
        except Exception as e:
            logger.error(f"OnGrid deep scan failed: {str(e)}", exc_info=True)

    async def infra_scan(self) -> Dict:
        """Infrastructure scan entry point."""
        logger.info(f"Initiating infra scan for {self.service_name}")
        
        try:
            await self.get_service_details()
            infra = await self.scan_ongrid_infra()
            infra["region"] = "India"  # OnGrid is India-based
            logger.info(f"Infra scan finished for {self.service_name}: {infra}")
            return infra
            
        except Exception as e:
            logger.error(
                f"Error during OnGrid infra scanning for {self.service_name}: {str(e)}",
                exc_info=True,
            )
            return {"total_size": 0, "total_files": 0}

    async def deep_scan(self) -> AsyncGenerator[FileMetadata, None]:
        """Deep scan entry point."""
        logger.info(f"Initiating deep scan for {self.service_name}")
        
        try:
            await self.get_service_details()
            async for file_metadata in self.scan_ongrid_deep():
                yield file_metadata
            logger.info(f"Deep scan finished for {self.service_name}")
            
        except Exception as e:
            logger.error(
                f"Error during OnGrid deep scanning for {self.service_name}: {str(e)}",
                exc_info=True,
            )
            
        finally:
            if self.session and not self.session.closed:
                logger.info(f"Closing aiohttp session for {self.service_name}")
                await self.session.close()