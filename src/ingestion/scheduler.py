from typing import Optional, <PERSON><PERSON>
from uuid import uuid4
from datetime import datetime, timedelta
from src.utils.logger import get_scheduler_logger
from src.utils.helpers import (
    create_file,
    create_folder,
    list_files,
    remove_file,
)
from src.common.constants import IngestionJobStatus
from src.common.config import WORKFLOW_SCHEDULE_FOLDER
from src.modules.repos import DatabaseManager

logger = get_scheduler_logger()


async def schedule_ingestion_task(
    ingestion_id: str, delay_in_hours: Optional[int] = None
) -> dict:
    db = DatabaseManager()

    # Get pending jobs (Queued + Running)
    pending_jobs = []
    pending_jobs += await db.get_ingestion_jobs_by_status(
        ingestion_id, IngestionJobStatus.Queued.value
    )
    pending_jobs += await db.get_ingestion_jobs_by_status(
        ingestion_id, IngestionJobStatus.Running.value
    )

    if pending_jobs:
        latest_job = pending_jobs[-1]
        if latest_job.get("status") in [
            IngestionJobStatus.Queued.value,
            IngestionJobStatus.Running.value,
        ]:
            return {"job_id": latest_job.get("job_id")}

    job_id = str(uuid4())
    timestamp = datetime.now()
    if delay_in_hours:
        timestamp = datetime.now() + timedelta(hours=delay_in_hours)

    job = {
        "job_id": job_id,
        "ingestion_id": ingestion_id,
        "status": IngestionJobStatus.Queued.value,
        "start_at": timestamp,
    }

    await db.create_ingestion_job(job)
    return {"job_id": job_id}

async def get_next_ingestion_tasks() -> list:
    db = DatabaseManager()
    tasks = await db.get_all_ingestion_jobs_by_status(IngestionJobStatus.Queued.value)
    return tasks


async def schedule_dsr_task(dsr_id: str) -> dict:
    timestamp = datetime.now().strftime("%Y%m%d%H%m%s")
    task = f"{timestamp}_{dsr_id}"
    # db = DatabaseManager()
    job = {"dsr_id": dsr_id}
    # await db.create_ingestion_job(job)
    create_folder(f"{WORKFLOW_SCHEDULE_FOLDER}/dsr")
    create_file(f"{WORKFLOW_SCHEDULE_FOLDER}/dsr/{task}")
    return {"dsr_id": dsr_id}


def get_next_dsr_task() -> Tuple[Optional[str], Optional[str]]:
    tasks = list_files(f"{WORKFLOW_SCHEDULE_FOLDER}/dsr")
    tasks = list(sorted(tasks, reverse=True))
    if len(tasks) <= 0:
        return None

    task = tasks[0]
    _, dsr_id = task.split("_")
    remove_file(f"{WORKFLOW_SCHEDULE_FOLDER}/dsr/{task}")
    return dsr_id
