from typing import Optional
from abc import ABC, abstractmethod
from src.utils.minio_client import MinioClient
from src.utils.exceptions import CustomBaseException, ServerException
from src.utils.logger import get_ingestion_logger
from src.utils.helpers import send_request
from src.common.config import (
    PROFILER_BASE_URL,
    PROFILER_REQUEST_QUEUE,
    PROFILER_RESPONSE_QUEUE,
)
from src.utils.message_publisher import publish_message

logger = get_ingestion_logger()


class Process(ABC):
    @abstractmethod
    async def run(self, record: dict) -> list:
        """Start the process"""

    @abstractmethod
    async def stop(self):
        """Stop the process"""


class PIIProfiler(Process):
    def __init__(self):
        self.minio = MinioClient()

    async def run(
        self, correlation_id: str, local_filepath: str, minio_filepath: str
    ) -> None:
        """Start the process"""
        try:

            self.minio.upload_file(local_filepath, minio_filepath)

            profile_request = {
                "correlation_id": correlation_id,
                "input_type": "file",
                "input_data": "",
                "input_file": minio_filepath,
                "file_storage": "minio",
                "profile_type": "pii",
                "data_separator": "string",
            }
            publish_message(
                correlation_id=correlation_id,
                queue_name=PROFILER_REQUEST_QUEUE,
                message_type="profile",
                payload=profile_request,
                reply_to=PROFILER_RESPONSE_QUEUE,
            )
            return None
        except CustomBaseException as e:
            raise e
        except Exception as e:
            raise ServerException(f"Profile message queue failed. Error: {e}", excep=e)

    async def stop(self):
        """Stop the process"""


class DSRProfiler(Process):
    def __init__(self):
        self.minio = MinioClient()

    async def run(self, localfilepath: str) -> Optional[str]:
        """Start the process"""
        try:
            # add logic to send file for DSR profiling
            return ""
        except CustomBaseException:
            pass
        except Exception as e:
            logger.error("Unstructure profiler failed.", exc_info=e)

        return None

    async def stop(self):
        """Stop the process"""

    async def send_to_profiler(self, data: dict):
        """send data to profiler"""
        url = f"{PROFILER_BASE_URL}/async/create"

        status_code, headers, resp = send_request(url=url, method="POST", data=data)
        if status_code != 202:
            raise ServerException(
                f"Sending file to profiler failed. Status code: {status_code}, resp: {resp}"
            )

        return resp
