from typing import List, Optional
from dataclasses import dataclass, field
from src.common.constants import ScanTypes,ShareMethod,ShareTarget


@dataclass
class PII:
    pii_type: str
    value: str
    score: float


@dataclass
class TableColumn:
    column_name: str
    data_type: str
    index: bool = False
    primary_key: bool = False
    pii_type: str = None


@dataclass
class FileMetadata:
    service_name: str
    service_type: str
    service_provider: str
    sub_service: str
    file_key: str
    file_size: float
    file_type: str
    file_uri: str
    local_filepath: str = None
    piis: List[PII] = field(default_factory=list)
    scan_type: str = ScanTypes.FileMetadata.value
    details: dict = field(default_factory=dict)


@dataclass
class TableMetadata:
    service_name: str
    service_type: str
    service_provider: str
    sub_service: str
    db_type: str
    db_name: str
    table_name: str
    table_size: float
    no_of_rows: int
    table_uri: str
    columns: List[TableColumn]
    local_filepath: str = None
    piis: List[PII] = field(default_factory=list)
    scan_type: str = ScanTypes.TableMetadata.value
    details: dict = field(default_factory=dict)

@dataclass
class AssetsDetails:
    asset_name: str
    service_provider: str
    type: str
    category: str
    location: str
    owner: str
    security: str
    size: float
    count: int
    # sub_service: str
    service_name: str
    # provider_key: Optional[str] = None
    access_category: Optional[str] = None
    custodian: str = None
    description: str = None
    steward: str = None
    dept_tags: Optional[List[str]] = None
    custom_name: Optional[str] = None
    sharing_type: Optional[ShareTarget] = None
    sharing_method: Optional[ShareMethod] = None

@dataclass
class AccessControls:
    user_or_role: str
    role: str
    access: str  