from abc import ABC, abstractmethod
from typing import AsyncGenerator, List
from src.ingestion.data_class import FileMetadata, TableMetadata
from src.ingestion.rule_manager import UnstructreRuleManager, StructreRuleManager
from src.common.config import TEMP_DATA_DIR
from src.utils.exceptions import ServerException


class BaseSource(ABC):
    def __init__(self, service_type: str, service_name: str):
        self.service_name = service_name
        self.service_type = service_type
        self.local_data_dir = TEMP_DATA_DIR

    @abstractmethod
    async def get_service_details(self) -> dict:
        """Gets service details"""

    @abstractmethod
    async def test_connection(self) -> dict:
        """Test connection details"""

    @abstractmethod
    async def infra_scan(self):
        """Intra scan: collects the metrics"""

    @abstractmethod
    async def deep_scan(self):
        """Deep scan: collects the internal data"""


class StructuredSource(BaseSource):
    def __init__(self, service_type: str, service_name: str):
        super().__init__(service_type, service_name)
        self.rule_manager: StructreRuleManager = None

    @abstractmethod
    async def deep_scan(self) -> AsyncGenerator[TableMetadata, None]:
        """Deep scan: collects the internal data"""

    async def user_scan(
        self, inital_tables: List[dict]
    ) -> AsyncGenerator[TableMetadata, None]:
        """Deep scan: collects the internal data"""
        raise ServerException(
            f"User scan not supported for source type: {self.service_type}"
        )


class UnstructureSource(BaseSource):
    def __init__(self, service_type: str, service_name: str):
        super().__init__(service_type, service_name)
        self.rule_manager: UnstructreRuleManager = None

    @abstractmethod
    async def deep_scan(self) -> AsyncGenerator[FileMetadata, None]:
        """Deep scan: collects the internal data"""
    async def user_scan(
        self, inital_files: List[dict]
    ) -> AsyncGenerator[FileMetadata, None]:
        """Deep scan: collects the internal data"""
        raise ServerException(
            f"User scan not supported for source type: {self.service_type}"
        )