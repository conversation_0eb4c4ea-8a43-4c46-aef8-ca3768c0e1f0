import json
from dataclasses import asdict
from datetime import datetime
from uuid import uuid4
from typing import Optional
from src.utils.logger import get_ingestion_logger
from src.ingestion.base_source import UnstructureSource, StructuredSource
from src.ingestion.profile_client import <PERSON><PERSON>P<PERSON>filer
from src.common.config import (
    PROFILE_MAX_SIZE_THRESHOLD,
    TEMP_DATA_DIR,
    WAREHOUSE_BASE_URL,
    UNSTRUCTURED_STORAGE_LIMIT_BYTES,
    PROFILE_PENDING,
    WAREHOUSE_REQUEST_QUEUE,
    MINIMUM_REQ_STORAGE_FOR_INGESTION,
    INGESTION_RESTART_DURATION_IN_HOUR,
)
from src.utils.helpers import remove_file
from src.utils.exceptions import Custom<PERSON><PERSON><PERSON>x<PERSON>, FatalException, ServerException
from src.ingestion.base_workflow import IngestionFlow
from src.ingestion.service_config import ServiceConfigFactory, ServiceConfig
from src.ingestion.rule_manager import (
    UntructureIngestionRuleManager,
    StructureIngestionRuleManager,
)
from src.utils.minio_client import MinioClient
from src.modules.repos import DatabaseManager
from src.common.constants import StatusEnum, MessageTyeps, SourceName, AuditEventNames
from src.modules.audit_log import AuditLogger
from src.utils.message_publisher import publish_message
from src.common.constants import StatusEnum
from src.modules.audit_log import AuditLogger
from src.utils.minio_client import MinioClient
logger = get_ingestion_logger()


def publish_filemetadate_to_warehouse(correlation_id: str, data):
    try:
        publish_message(
            correlation_id=correlation_id,
            queue_name=WAREHOUSE_REQUEST_QUEUE,
            message_type=MessageTyeps.FILEMETADATA.value,
            payload=data,
            reply_to=None,
        )
        return None
    except Exception as e:
        logger.error(f"Error sending file metadata to warehouse: {str(e)}")
        return None


def publish_tablemetadate_to_warehouse(correlation_id: str, data):
    try:
        publish_message(
            correlation_id=correlation_id,
            queue_name=WAREHOUSE_REQUEST_QUEUE,
            message_type=MessageTyeps.TABLEMETADATA.value,
            payload=data,
            reply_to=None,
        )
        return None
    except Exception as e:
        logger.error(f"Error sending file metadata to warehouse: {str(e)}")
        return None


def generate_correlation_id(prefix="FILE"):
    return prefix + "_" + datetime.now().strftime("%Y%m%d%H%M%s") + str(uuid4())


"""
Deep scan will return either FileMetadata or TableMetadata
pass file skip strategy function to (Source will call this function befor downloading the file.)
"""


class UnstructIngestionFlow(IngestionFlow):
    def __init__(self, service_type: str, service_name: str, ingestion_id: str, job_id: str):
        super().__init__(service_type, service_name, ingestion_id, job_id)
        self.service_config: ServiceConfig = ServiceConfigFactory.get_service_config(
            service_type
        )
        self.db = DatabaseManager()
        self.audit_logger = AuditLogger()
        self.minio_client = MinioClient()
        self.minio_free_space = self.minio_client.get_minio_free_space()
        self.current_storage_used = 0

    async def get_ingestion_details(self):
        """Single function to execute to create a Workflow instance"""
        ingestion = await self.db.get_ingestion_by_id(self.ingestion_id)
        return ingestion
    
    async def get_job_details(self):
        """Fetch job details (overrides base if needed)"""
        job = await self.db.get_ingestion_job_by_id(self.ingestion_id, self.job_id)
        return job

    async def minio_storage_limit_reached(self, required_space: int) -> bool:
        """Check if adding file exceeds global MinIO limit"""
        if self.minio_free_space <= required_space:
            logger.warning(
                f"[Storage Limit] Exceeded: current={self.minio_free_space/(1024**3):.2f}GB available. "
            )
            return True
        return False

    async def update_storage_usage(self, used_size: int):
        """Update the storage usage after processing file"""
        self.minio_free_space -= used_size
        logger.info(
            f"[Storage Usage] [Free]: {self.minio_free_space/(1024**3):.2f}GB / "
        )

    async def reset_storage_counter(self):
        """Reset usage counter"""
        self.current_storage_used = 0
        logger.info("[Storage] Usage counter reset to 0")

    async def get_source(self):
        source_class = self.service_config.source_class
        source: UnstructureSource = source_class(self.service_name)
        source.rule_manager = UntructureIngestionRuleManager()
        source.local_data_dir = (
            f"{TEMP_DATA_DIR}/ingestion/{self.ingestion_id}/{self.service_type}"
        )
        return source
    
    async def execute_internal(self) -> None:
        """Workflow-specific logic to execute safely"""
        self.source: UnstructureSource = await self.get_source()
        profiler = PIIProfiler()

        ingestion_failed = False
        error = ""

        if await self.minio_storage_limit_reached(MINIMUM_REQ_STORAGE_FOR_INGESTION):
            raise FatalException(
                f"[Storage] Not enough space in minio. Ingestion restarted after {INGESTION_RESTART_DURATION_IN_HOUR} hours"
            )

        async for record in self.source.deep_scan():
            try:
                if await self.minio_storage_limit_reached(record.file_size):
                    error = f"[Storage] Not enough space in minio. Ingestion restarted after {INGESTION_RESTART_DURATION_IN_HOUR} hours"
                    ingestion_failed = True
                    break

                await self.update_storage_usage(record.file_size)

                correlation_id = generate_correlation_id("FILE")
                local_filepath = record.local_filepath
                file_uri = record.file_uri
                data = asdict(record)
                profile_status = StatusEnum.INITIATED.value
                warehouse_status = StatusEnum.INITIATED.value

                # Check if file audit exists
                existing_log = self.audit_logger.get_file_audit_log_by_uri(file_uri)
                if existing_log:
                    correlation_id = existing_log.get("correlation_id")
                    profile_status = existing_log.get("profile_status")
                    warehouse_status = existing_log.get("warehouse_status")
                else:
                    # Create new file log
                    self.audit_logger.create_file_audit_log_util(
                        correlation_id=correlation_id,
                        file_uri=file_uri,
                        metadata=data,
                        ingestion_id=self.ingestion_id,
                        job_id=self.job_id,
                    )
                    self.audit_logger.update_file_audit_log_by_correlation_id(
                        correlation_id,
                        file_log={
                            "ingestion_status": StatusEnum.FINISHED.value,
                            "ingestion_end_time": datetime.now(),
                        },
                    )
                    self.audit_logger.create_audit_event(
                        correlation_id, AuditEventNames.IngestionEnd.value
                    )

                data["correlation_id"] = correlation_id

                # Skip Profile processing
                if (
                    profile_status == StatusEnum.FINISHED.value
                    or profile_status == StatusEnum.RUNNING.value
                ):
                    logger.info(
                        f"[Profile]. Skipping already processed file: {file_uri}"
                    )
                    remove_file(local_filepath)
                else:
                    if (
                        local_filepath
                        and record.file_size <= PROFILE_MAX_SIZE_THRESHOLD
                    ):
                        try:
                            filename = local_filepath.split("/")[-1]
                            minio_filepath = f"ingestion/{self.ingestion_id}/{filename}"
                            await profiler.run(
                                correlation_id, local_filepath, minio_filepath
                            )
                            self.audit_logger.update_file_audit_log_by_correlation_id(
                                correlation_id,
                                file_log={
                                    "minio_file": minio_filepath,
                                },
                            )
                            self.audit_logger.create_audit_event(
                                correlation_id, AuditEventNames.ProfileQueue.value
                            )
                        except CustomBaseException as e:
                            self.audit_logger.create_audit_event(
                                correlation_id,
                                AuditEventNames.ProfileFail.value,
                                msg=str(e),
                            )

                        finally:
                            remove_file(local_filepath)

                # Skip Warehouse processing
                if (
                    warehouse_status == StatusEnum.FINISHED.value
                    or warehouse_status == StatusEnum.RUNNING.value
                ):
                    logger.info(
                        f"[Warehouse]. Skipping already processed file: {file_uri}"
                    )
                else:
                    publish_filemetadate_to_warehouse(correlation_id, data)
                    self.audit_logger.create_audit_event(
                        correlation_id, AuditEventNames.WarehouseMetadataQueue.value
                    )

            except Exception as e:
                logger.error(f"Error processing file {file_uri}: {str(e)}")

        if ingestion_failed:
            raise FatalException(error)

    async def execute(self) -> None:
        """
        Main entrypoint
        """
        try:
            await self.execute_internal()
        except CustomBaseException as e:
            raise e
        except Exception as e:
            raise ServerException(
                f"Ingestion failed for the service {self.service_name}", excep=e
            )


class StructIngestionFlow(IngestionFlow):
    def __init__(self, service_type: str, service_name: str, ingestion_id: str, job_id: str):
        super().__init__(service_type, service_name, ingestion_id, job_id)
        self.service_config: ServiceConfig = ServiceConfigFactory.get_service_config(
            service_type
        )
        self.db = DatabaseManager()
        self.audit_logger = AuditLogger()

    async def get_ingestion_details(self):
        """Single function to execute to create a Workflow instance"""
        ingestion = await self.db.get_ingestion_by_id(self.ingestion_id)
        return ingestion
    
    async def get_job_details(self):
        """Get job details"""
        job = await self.db.get_ingestion_job_by_id(self.ingestion_id, self.job_id)
        return job

    async def get_source(self):
        source_class = self.service_config.source_class
        source: StructuredSource = source_class(self.service_name)
        source.rule_manager = StructureIngestionRuleManager()
        source.local_data_dir = (
            f"{TEMP_DATA_DIR}/ingestion/{self.ingestion_id}/{self.service_type}"
        )
        return source

    async def execute_internal(self) -> None:
        """Workflow-specific logic to execute safely"""
        self.source: StructuredSource = await self.get_source()
        profiler = PIIProfiler()

        async for record in self.source.deep_scan():
            try:
                correlation_id = generate_correlation_id("TABLE")
                local_filepath = record.local_filepath
                table_uri = record.table_uri
                data = asdict(record)
                profile_status = StatusEnum.INITIATED.value
                warehouse_status = StatusEnum.INITIATED.value

                # Check if file audit log exists
                existing_log = self.audit_logger.get_table_audit_log_by_uri(table_uri)
                if existing_log:
                    correlation_id = existing_log.get("correlation_id")
                    profile_status = existing_log.get("profile_status")
                    warehouse_status = existing_log.get("warehouse_status")
                else:
                    # Create new table log
                    self.audit_logger.create_table_audit_log_util(
                        correlation_id=correlation_id,
                        table_uri=table_uri,
                        metadata=data,
                        ingestion_id=self.ingestion_id,
                        job_id=self.job_id,
                    )
                    self.audit_logger.update_table_audit_log_by_correlation_id(
                        correlation_id,
                        table_log={
                            "ingestion_status": StatusEnum.FINISHED.value,
                            "ingestion_end_time": datetime.now(),
                        },
                    )
                    self.audit_logger.create_audit_event(
                        correlation_id, AuditEventNames.IngestionEnd.value
                    )

                data["correlation_id"] = correlation_id

                # Skip Profile processing
                if (
                    profile_status == StatusEnum.FINISHED.value
                    or profile_status == StatusEnum.RUNNING.value
                ):
                    logger.info(
                        f"[Profile]. Skipping already processed file: {table_uri}"
                    )
                    remove_file(local_filepath)
                else:
                    if local_filepath:
                        try:
                            filename = local_filepath.split("/")[-1]
                            minio_filepath = f"ingestion/{self.ingestion_id}/{filename}"
                            await profiler.run(
                                correlation_id, local_filepath, minio_filepath
                            )
                            self.audit_logger.update_table_audit_log_by_correlation_id(
                                correlation_id,
                                table_log={
                                    "minio_file": minio_filepath,
                                },
                            )
                            self.audit_logger.create_audit_event(
                                correlation_id, AuditEventNames.ProfileQueue.value
                            )
                        except CustomBaseException as e:
                            self.audit_logger.create_audit_event(
                                correlation_id,
                                AuditEventNames.ProfileFail.value,
                                msg=str(e),
                            )

                        finally:
                            remove_file(local_filepath)

                # Skip Warehouse processing
                if (
                    warehouse_status == StatusEnum.FINISHED.value
                    or warehouse_status == StatusEnum.RUNNING.value
                ):
                    logger.info(
                        f"[Warehouse]. Skipping already processed file: {table_uri}"
                    )
                else:
                    publish_tablemetadate_to_warehouse(correlation_id, data)
                    self.audit_logger.create_audit_event(
                        correlation_id, AuditEventNames.WarehouseMetadataQueue.value
                    )

            except Exception as e:
                logger.error(f"Error processing table {table_uri}: {str(e)}")
                # self.audit_logger.log_table_processing_error(table_uri, str(e))

    async def execute(self) -> None:
        """
        Main entrypoint
        """
        try:
            await self.execute_internal()
        except CustomBaseException as e:
            raise e
        except Exception as e:
            raise ServerException(
                f"Ingestion failed for the service {self.service_name}", excep=e
            )


"""
Pending:
1. Improve log addition
2. Implement file skip logic
3. Profile webhook changes are required.
4. Add Force ingestion logic
5. Add extented scan logic
6. Add DSR launch script
"""
