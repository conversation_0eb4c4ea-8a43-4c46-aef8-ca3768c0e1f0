import sys
import os
import asyncio

# required during process launch time
sys.path.insert(0, os.getcwd())

from src.utils.logger import get_ingestion_logger


logger = get_ingestion_logger()


async def ingestion_engine():
    try:
        from src.ingestion.ingestion import perform_ingestion

        logger.info("Started Ingestion")
        ingestion_id = os.getenv("INGESTION_ID")
        job_id = os.getenv("JOB_ID")
        logger.info(f"JobID: {job_id}, IngestionID: {ingestion_id}")
        if ingestion_id is None or job_id is None:
            return None

        # make a DB entry for the new task created
        await perform_ingestion(ingestion_id, job_id)
    except Exception as e:
        logger.error(f"Ingestion failed. {e}")


try:
    asyncio.run(ingestion_engine())
except Exception as e:
    logger.error(e)
