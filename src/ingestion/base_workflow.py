from abc import ABC, abstractmethod
from typing import List
from src.utils.logger import get_ingestion_logger
from src.ingestion.profile_client import Process


logger = get_ingestion_logger()


class IngestionFlow(ABC):
    def __init__(self, service_type: str, service_name: str, ingestion_id: str, job_id: str):
        self.service_name = service_name
        self.service_type = service_type
        self.ingestion_id = ingestion_id
        self.job_id = job_id

    @abstractmethod
    async def get_ingestion_details(self):
        """Single function to execute to create a Workflow instance"""
    @abstractmethod
    async def get_job_details(self):
        """Get job details"""
        

    @abstractmethod
    async def get_source(self):
        """Get source"""

    @abstractmethod
    async def execute_internal(self) -> None:
        """Workflow-specific logic to execute safely"""

    async def calculate_success(self) -> float:
        """Get the success % of the internal execution"""

    async def get_failures(self):
        """Get the failures to flag whether if the workflow succeeded or not"""

    @abstractmethod
    async def execute(self) -> None:
        """
        Main entrypoint:
        1. Start logging timer. It will be closed at `stop`
        2. Execute the workflow
        3. Validate the pipeline status
        4. Update the pipeline status at the end
        """
        try:
            await self.execute_internal()
        except Exception as err:
            logger.error(f"Ingestion failed for the service {self.service_name}, {err}")
        finally:
            await self.stop()

    async def stop(self) -> None:
        """
        Main stopping logic
        """

    async def create_ingestion_pipeline(self) -> List[Process]:
        """Creates Ingestion pipeline"""
