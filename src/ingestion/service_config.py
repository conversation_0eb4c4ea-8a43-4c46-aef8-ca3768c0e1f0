from dataclasses import dataclass
from typing import Optional
from src.common.constants import (
    ServiceProviders,
    AWSSubServices,
    M365SubServices,
    LocalSubservice,
    AzureSubServices,
    GWSSubServices,
)
from src.common.constants import ServiceTypes
from src.ingestion.base_source import BaseSource
from src.ingestion.unstructure.aws.s3.source import S3Source
from src.ingestion.unstructure.office_365.onedrive.source import OneDriveSource
from src.ingestion.unstructure.office_365.outlook.source import OutlookSource
from src.ingestion.unstructure.google_workspace.gmail.source import GmailSource
from src.ingestion.unstructure.google_workspace.gdrive.source import GoogleDriveSource
from src.ingestion.unstructure.office_365.teams.source import TeamsSource
from src.ingestion.unstructure.adls.source import ADLSSource
from src.ingestion.unstructure.directory_scan.source import DirectorySource
from src.ingestion.unstructure.sftp_server.source import SFTPSource
from src.ingestion.structure.mongodb.source import MongoDBSource
from src.ingestion.structure.tcs_chroma.source import TCSChromaSource
from src.ingestion.structure.onex.source import ONEXSource
from src.ingestion.unstructure.Happay.source import HappaySource
from src.ingestion.unstructure.office_365.sharepoint.source import (
    SharePointSource,
)
from src.ingestion.structure.postgres.source import PostgresSource
from src.ingestion.structure.mysql.source import MySQLSource
from src.ingestion.structure.oracle.source import OracleSource
from src.ingestion.structure.mssql.source import MSSQLSource
from src.ingestion.structure.sqlite.source import SQLiteSource
from src.ingestion.structure.snowflake.source import SnowflakeSource
from src.ingestion.structure.salesforce.source import SalesforceSource
from src.ingestion.unstructure.nas.source import NASSource
from src.ingestion.unstructure.human_firewall.source import HumanFirewallSource
from src.ingestion.structure.oracle11g.source import Oracle11gSource
from src.ingestion.structure.deltalake.source import DeltaLakeSource
from src.ingestion.structure.keka.source import KekaSource
from src.ingestion.unstructure.slack.source import SlackSource
from src.ingestion.unstructure.cloudfare.source import CloudflareSource
from src.ingestion.structure.cassandra.source import CassandraSource

@dataclass
class ServiceConfig:
    service_type: ServiceTypes
    service_provider: str
    sub_service: str
    source_class: Optional[BaseSource] = None
    warehouse_url: Optional[str] = None


service_config_map = {
    ServiceTypes.MS_365_ONEDRIVE.value: {
        "warehouse_url": "/ms_onedrive",
        "service_provider": ServiceProviders.M365.value,
        "sub_service": M365SubServices.MS_ONEDRIVE.value,
        "source_class": OneDriveSource,
    },
    ServiceTypes.MS_365_TEAMS.value: {
        "warehouse_url": "/ms_teams",
        "service_provider": ServiceProviders.M365.value,
        "sub_service": M365SubServices.MS_TEAMS.value,
        "source_class": TeamsSource,
    },
    ServiceTypes.MS_365_OUTLOOK.value: {
        "warehouse_url": "/ms_outlook",
        "service_provider": ServiceProviders.M365.value,
        "sub_service": M365SubServices.MS_OUTLOOK.value,
        "source_class": OutlookSource,
    },
    ServiceTypes.MS_365_SHAREPOINT.value: {
        "warehouse_url": "/ms_sharepoint",
        "service_provider": ServiceProviders.M365.value,
        "sub_service": M365SubServices.MS_SHAREPOINT.value,
        "source_class": SharePointSource,
    },
    ServiceTypes.AWS_S3.value: {
        "warehouse_url": "/aws_s3",
        "service_provider": ServiceProviders.AWS.value,
        "sub_service": AWSSubServices.AWS_S3.value,
        "source_class": S3Source,
    },
    ServiceTypes.GOOGLE_DRIVE.value: {
        "warehouse_url": "/google_drive",
        "service_provider": ServiceProviders.GWS.value,
        "sub_service": GWSSubServices.GOOGLE_DRIVE.value,
        "source_class": GoogleDriveSource,
    },
    ServiceTypes.GOOGLE_GMAIL.value: {
        "warehouse_url": "/google_gmail",
        "service_provider": ServiceProviders.GWS.value,
        "sub_service": GWSSubServices.GOOGLE_GMAIL.value,
        "source_class": GmailSource,
    },
    ServiceTypes.AZURE_DATA_LAKE.value: {
        "warehouse_url": "/azure",
        "service_provider": ServiceProviders.AZURE.value,
        "sub_service": AzureSubServices.AZURE_BLOB_STORAGE.value,
        "source_class": ADLSSource,
    },
    ServiceTypes.DIRECTORY_SCAN.value: {
        "warehouse_url": "/directory_scan",
        "service_provider": ServiceProviders.LOCAL.value,
        "sub_service": LocalSubservice.DIRECTORY_SCAN.value,
        "source_class": DirectorySource,
    },
    # structured
    ServiceTypes.SFTP.value: {
        "warehouse_url": "/sftp",
        "service_provider": ServiceProviders.LOCAL.value,
        "sub_service": LocalSubservice.SFTP.value,
        "source_class": SFTPSource,
    },
    ServiceTypes.ORACLE.value: {
        "warehouse_url": "/oracle",
        "service_provider": ServiceProviders.LOCAL.value,
        "sub_service": LocalSubservice.ORACLE.value,
        "source_class": OracleSource,
    },
    ServiceTypes.POSTGRES.value: {
        "warehouse_url": "/postgres",
        "service_provider": ServiceProviders.LOCAL.value,
        "sub_service": LocalSubservice.POSTGRES.value,
        "source_class": PostgresSource,
    },
    ServiceTypes.MYSQL.value: {
        "warehouse_url": "/mysql",
        "service_provider": ServiceProviders.LOCAL.value,
        "sub_service": LocalSubservice.MYSQL.value,
        "source_class": MySQLSource,
    },
    ServiceTypes.MSSQL.value: {
        "warehouse_url": "/mssql",
        "service_provider": ServiceProviders.LOCAL.value,
        "sub_service": LocalSubservice.MSSQL.value,
        "source_class": MSSQLSource,
    },
    ServiceTypes.SQLITE.value: {
        "warehouse_url": "/sqlite",
        "service_provider": ServiceProviders.LOCAL.value,
        "sub_service": LocalSubservice.SQLITE.value,
        "source_class": SQLiteSource,
    },
    ServiceTypes.SNOWFLAKE.value: {
        "warehouse_url": "/snowflake",
        "service_provider": LocalSubservice.SNOWFLAKE.value,
        "source_class": SnowflakeSource,
    },
    ServiceTypes.SALESFORCE.value: {
        "warehouse_url": "/salesforce",
        "service_provider": LocalSubservice.SALESFORCE.value,
        "source_class": SalesforceSource
    },
    ServiceTypes.NAS.value: {
        "warehouse_url": "/nas",
        "service_provider": LocalSubservice.NAS.value,
        "source_class": NASSource
    },
    ServiceTypes.ORACLE11G.value: {
        "warehouse_url": "/dsr",
        "service_provider": ServiceProviders.LOCAL.value,
        "sub_service": LocalSubservice.ORACLE11G.value,
        "source_class": Oracle11gSource
    },
    ServiceTypes.MONGODB.value: {
        "warehouse_url": "/mongodb",
        "service_provider": ServiceProviders.LOCAL.value,
        "sub_service": LocalSubservice.MONGODB.value,
        "source_class": MongoDBSource,
    },
    ServiceTypes.TCS_CHROMA.value: {
        "warehouse_url": "/tcs_chroma",
        "service_provider": ServiceProviders.LOCAL.value,
        "sub_service": LocalSubservice.TCS_CHROMA.value,
        "source_class": TCSChromaSource,
    },
    ServiceTypes.ONEX.value: {
        "warehouse_url": "/onex",
        "service_provider": ServiceProviders.LOCAL.value,
        "sub_service": LocalSubservice.ONEX.value,
        "source_class": ONEXSource,
    },
    ServiceTypes.HAPPAY.value: {
        "warehouse_url": "/happay",
        "service_provider": ServiceProviders.LOCAL.value,
        "sub_service": LocalSubservice.HAPPAY.value,
        "source_class": HappaySource,
    },
    ServiceTypes.DELTALAKE.value: {
        "warehouse_url": "/deltalake",
        "service_provider": ServiceProviders.LOCAL.value,
        "sub_service": LocalSubservice.DELTALAKE.value,
        "source_class": DeltaLakeSource,
    },
    ServiceTypes.HUMAN_FIREWALL.value: {
        "warehouse_url": "/human_firewall",
        "service_provider": ServiceProviders.HUMAN_FIREWALL.value,
        "sub_service": LocalSubservice.HUMAN_FIREWALL.value,
        "source_class": HumanFirewallSource,
    },
    ServiceTypes.KEKA.value: {
        "warehouse_url": "/keka",
        "service_provider": ServiceProviders.LOCAL.value,
        "sub_service": LocalSubservice.KEKA.value,
        "source_class": KekaSource,
    },
    ServiceTypes.SLACK.value: {
        "warehouse_url": "/slack",
        "service_provider": ServiceProviders.LOCAL.value,
        "sub_service": LocalSubservice.SLACK.value,
        "source_class": SlackSource,
    },
    ServiceTypes.CLOUDFARE.value: {
        "warehouse_url": "/cloudfare",
        "service_provider": ServiceProviders.LOCAL.value,
        "sub_service": LocalSubservice.CLOUDFARE.value,
        "source_class": CloudflareSource,
    },
    ServiceTypes.CASSANDRA.value: {
        "warehouse_url": "/cassandra",
        "service_provider": ServiceProviders.LOCAL.value,
        "sub_service": LocalSubservice.CASSANDRA.value,
        "source_class": CassandraSource,
    },
}


class ServiceConfigFactory:
    @staticmethod
    def get_service_config(service_type: str) -> Optional[ServiceConfig]:
        service = service_config_map.get(service_type)
        if service is None:
            return None

        return ServiceConfig(
            service_type=service_type,
            service_provider=service.get("service_provider"),
            sub_service=service.get("sub_service"),
            warehouse_url=service.get("warehouse_url"),
            source_class=service.get("source_class"),
        )
