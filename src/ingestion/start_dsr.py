import sys
import os
import asyncio

# required during process launch time
sys.path.insert(0, os.getcwd())

from src.utils.logger import get_dsr_logger


logger = get_dsr_logger()


async def dsr_engine():
    try:
        from src.ingestion.dsr import perform_dsr

        logger.info("Started DSR")
        dsr_id = os.getenv("DSR_ID")
        logger.info(f"DSR_ID: {dsr_id}")
        if dsr_id is None:
            return None

        # make a DB entry for the new task created
        await perform_dsr(dsr_id)
    except Exception as e:
        logger.error(f"Ingestion failed. {e}")


try:
    asyncio.run(dsr_engine())
except Exception as e:
    logger.error(e)
