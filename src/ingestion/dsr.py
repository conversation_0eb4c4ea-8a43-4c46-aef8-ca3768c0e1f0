from datetime import datetime
from typing import List, Dict
import logging
import os
from src.utils.logger import get_dsr_logger
from src.modules.repos import DatabaseManager
from src.common.constants import DSRJobStatus
from src.utils.exceptions import CustomBaseException
from src.ingestion.dsr_workflow import StructDS<PERSON><PERSON><PERSON>, UnstructDSR<PERSON>low
from src.utils.helpers import save_to_json_file, send_request, zip_folder, remove_file
from src.common.config import TEMP_DATA_DIR, WAREHOUSE_BASE_URL, LOG_DIRNAME
from src.utils.minio_client import MinioClient

logger = get_dsr_logger()

ALLOWED_PII_TYPES_FOR_FILE_MATCHING = [
    "EMAIL_ADDRESS",
    "PHONE_NUMBER",
    "UPI_ID",
    "PASSPORT",
    "CREDIT_DEBIT_CARD_NUMBER",
    "PAN",
    "AADHAAR"
]

def get_pii_matching_tables_from_warehouse(pii_type: str) -> List[dict]:
    """response from warehouse for provided pii types and pii values"""
    url = f"{WAREHOUSE_BASE_URL}/pii_matching_tables"
    # url = "https://dev.gotrust.tech/warehouse-1/warehouse/pii_matching_tables"
    try:
        status_code, _, resp = send_request(url, "GET", params={"pii_types": pii_type})
        records = resp.get("data", [])
        message = resp.get("message", [])
        if status_code != 200:
            logger.error(
                f"Fetching PII Matching tables from warehouse failed. pii-type: {pii_type}, error: {message}"
            )
            return []

        results = []
        for record in records:
            results.append(
                {
                    "service_name": record.get("service_name"),
                    "db_name": record.get("db_name"),
                    "table_schema": record.get("table_schema", "public"),
                    "table_name": record.get("table_name"),
                    "column_name": record.get("column_name"),
                }
            )
        return results
    except Exception as e:
        logger.error(f"Error sending file metadata to warehouse: {str(e)}")

    return []


def get_table_pii_columns_from_warehouse(
    service_provider: str,
    sub_service: str,
    db_name: str,
    table_schema: str,
    table_name: str,
) -> dict:
    url = f"{WAREHOUSE_BASE_URL}/table_pii_columns"
    # url = "https://dev.gotrust.tech/warehouse-1/warehouse/table_pii_columns"
    try:
        if sub_service.lower() == "mssql" and table_schema:
            table_name_param = f"{table_schema}.{table_name}"
        else:
            table_name_param = table_name

        status_code, _, resp = send_request(
            url,
            "GET",
            params={
                "service_provider": service_provider,
                "sub_service": sub_service,
                "db_name": db_name,
                "table_name": table_name_param,
            },
        )
        data = resp.get("data", [])
        message = resp.get("message", [])
        if status_code != 200:
            logger.error(
                f"Fetching PII columns from table {table_name} failed. error: {message}"
            )
            return []

        pii_columns = {c.get("column_name"): c.get("pii_type") for c in data}

        return pii_columns
    except Exception as e:
        logger.error(f"Error sending file metadata to warehouse: {str(e)}")

    return []
def get_pii_matching_files_from_warehouse(pii_data: Dict[str, List[str]]) -> List[Dict]:
    """
    Fetch only for allowed PII types:
    EMAIL_ADDRESS, PHONE_NUMBER, UPI_ID, PASSPORT, CREDIT_DEBIT_CARD_NUMBER
    """
    url = f"{WAREHOUSE_BASE_URL}/pii_matching_files"
    # url = "https://dev.gotrust.tech/warehouse-1/warehouse/pii_matching_files"
    matching_files = []

    for pii_type, values in pii_data.items():
        # Skip disallowed PII types
        if pii_type not in ALLOWED_PII_TYPES_FOR_FILE_MATCHING:
            continue

        # Skip if no values found
        if not values:
            continue

        try:
            status_code, _, resp = send_request(
                url,
                "GET",
                params={
                    "pii_type": pii_type,
                    "values": ",".join(map(str, values)),
                },
            )

            if status_code != 200:
                logger.error(
                    f"Fetching PII files failed for {pii_type}. Error: {resp.get('message')}"
                )
                continue

            data = resp.get("data", [])
            if data:
                logger.info(f"Found {len(data)} matching files for {pii_type}")
            matching_files.extend(data)

        except Exception as e:
            logger.error(f"Error fetching PII files for {pii_type}: {str(e)}")

    return matching_files


def extract_personal_data_from_all_tables(results: list) -> dict:
    personal_data = {}
    for table in results:
        service_provider = table.get("service_provider")
        sub_service = table.get("sub_service")
        db_name = table.get("db_name")
        table_schema = table.get("table_schema")
        # table_schema = "public"
        table_name = table.get("table_name")
        matching_rows = table.get("matching_rows")
        pii_columns = get_table_pii_columns_from_warehouse(
            service_provider, sub_service, db_name, table_schema, table_name
        )
        for pii_column, pii_type in pii_columns.items():
            values = [v.get(pii_column) for v in matching_rows]
            if pii_type not in personal_data:
                personal_data[pii_type] = []
            personal_data[pii_type].extend(values)
            
    cleaned_data = {}
    for pii_type, values in personal_data.items():
        unique_values = {v for v in values if v not in (None, "", "null")}
        cleaned_data[pii_type] = list(unique_values)
    return cleaned_data

async def perform_dsr(dsr_id: str):
    """
    Full DSR execution flow:
    1. Fetch and validate request
    2. Run structured data flow
    3. Run unstructured data flow
    4. Extract and save personal data
    5. Zip all results and upload to MinIO
    """

    db = DatabaseManager()

    try:
        # Step 1: Fetch DSR Request
        dsr_request = await db.get_dsr_request(dsr_id)
        await db.update_dsr_request(
            dsr_id=dsr_id,
            data={"status": DSRJobStatus.Running, "start_at": datetime.now()},
        )

        logger.info(f"Starting DSR Processing for {dsr_id}")
        logger.debug(f"DSR details: {dsr_request}")

        search_data = dsr_request.get("search_data", {}) or {}
        PII_TYPES = "EMAIL_ADDRESS,PHONE_NUMBER"

        # Step 2: Fetch matching structured tables
        pii_tables = get_pii_matching_tables_from_warehouse(pii_type=PII_TYPES)
        logger.info(f"PII matching tables found: {len(pii_tables)}")
        await db.update_dsr_pii_matching_tables(dsr_id, pii_tables)

        # Step 3: Group tables per service
        service_tables = {}
        for table in pii_tables:
            service_name = table.get("service_name")
            if not service_name:
                continue
            service_tables.setdefault(service_name, [])

            column_name = (table.get("column_name") or "").upper()
            if "EMAIL" in column_name:
                table["values"] = search_data.get("emails", [])
            elif "PHONE" in column_name:
                table["values"] = search_data.get("phones", [])
            else:
                table["values"] = []

            service_tables[service_name].append(table)

        logger.info(f"Grouped service tables for structured flow: {list(service_tables.keys())}")

        total_structured_results = []

        # Step 4: Run structured flow for each service
        for service_name, inital_tables in service_tables.items():
            if not inital_tables:
                continue

            service_details = await db.get_service_by_service_name(service_name)
            service_type = service_details.get("service_type")

            logger.info(f"Running Structured DSR Flow for {service_name}")
            struct_flow = StructDSRFlow(dsr_id, service_type, service_name, inital_tables)
            await struct_flow.execute()
            total_structured_results.extend(struct_flow.search_results)

        # Step 5: Extract personal data from structured results
        personal_data = extract_personal_data_from_all_tables(total_structured_results)

        # Merge original search data
        if "emails" in search_data:
            personal_data.setdefault("EMAIL_ADDRESS", []).extend(search_data.get("emails", []))
        if "phones" in search_data:
            personal_data.setdefault("PHONE_NUMBER", []).extend(search_data.get("phones", []))

        # Deduplicate
        for key, value in personal_data.items():
            personal_data[key] = list(set(value))

        # Save personal data
        dsr_folder = f"{TEMP_DATA_DIR}/dsr/{dsr_id}"
        os.makedirs(dsr_folder, exist_ok=True)
        personal_data_path = os.path.join(dsr_folder, "personal_data.json")
        save_to_json_file(personal_data, personal_data_path)
        await db.update_dsr_personal_data(dsr_id, personal_data)

        # Step 6: Fetch matching unstructured files from warehouse
        matching_files = get_pii_matching_files_from_warehouse(pii_data=personal_data)
        logger.info(f"Matching unstructured files found (raw): {len(matching_files)}")
        logger.debug(f"Matching unstructured files (raw): {matching_files}")

        # Deduplicate matching files by (service_name, file_uri, file_name)
        unique_files = {}
        for f in matching_files:
            key = (f.get("service_name"), f.get("file_uri"), f.get("file_name"))
            if key not in unique_files:
                unique_files[key] = f
        matching_files = list(unique_files.values())
        logger.info(f"Matching unstructured files found (unique): {len(matching_files)}")
        logger.debug(f"Matching unstructured files (unique): {matching_files}")
        # Persist raw list for debugging/tracking inside the DSR folder
        try:
            save_to_json_file(matching_files, os.path.join(dsr_folder, "matching_unstructured_files.json"))
        except Exception:
            pass

        # Step 7: Group files per service
        service_files = {}
        for file in matching_files:
            service_name = file.get("service_name")
            if not service_name:
                continue
            service_files.setdefault(service_name, []).append(file)

        # Step 8: Run Unstructured DSR Flow for each service
        for service_name, files in service_files.items():
            if not files:
                continue

            service_details = await db.get_service_by_service_name(service_name)
            service_type = service_details.get("service_type")

            logger.info(f"Running Unstructured DSR Flow for {service_name}")
            unstruct_flow = UnstructDSRFlow(dsr_id, service_type, service_name, files)
            await unstruct_flow.execute()

        # Step 9: Zip folder and upload results to MinIO
        zip_file_path = f"{TEMP_DATA_DIR}/dsr/{dsr_id}.zip"

        if not os.path.exists(dsr_folder) or not any(os.scandir(dsr_folder)):
            logger.warning(f"No result files found for DSR {dsr_id}. Skipping zip/upload.")
            await db.update_dsr_request(
                dsr_id=dsr_id,
                data={
                    "status": DSRJobStatus.Completed,
                    "end_at": datetime.now(),
                    "result_file": None,
                    "result_url": None,
                },
            )
        else:
            logger.info(f"Creating zip for {dsr_id} → {zip_file_path}")
            zip_folder(dsr_folder, zip_file_path)

            logger.info(f"Uploading DSR zip to MinIO")
            minio_client = MinioClient()
            file_name = f"dsr/{dsr_id}.zip"
            minio_client.upload_file(zip_file_path, file_name)
            file_url = minio_client.get_download_url(file_name)

            remove_file(zip_file_path)

            await db.update_dsr_request(
                dsr_id=dsr_id,
                data={
                    "status": DSRJobStatus.Completed,
                    "end_at": datetime.now(),
                    "result_file": file_name,
                    "result_url": file_url,
                },
            )

            logger.info(f"DSR {dsr_id} completed successfully. File uploaded to {file_url}")

    except CustomBaseException as e:
        logger.error(f"Custom exception in DSR {dsr_id}: {e}")
        await db.update_dsr_request(
            dsr_id=dsr_id,
            data={
                "status": DSRJobStatus.Failed,
                "end_at": datetime.now(),
                "error": str(e),
            },
        )

    except Exception as e:
        logger.exception(f"Unexpected error in DSR {dsr_id}: {e}")
        await db.update_dsr_request(
            dsr_id=dsr_id,
            data={
                "status": DSRJobStatus.Failed,
                "end_at": datetime.now(),
                "error": str(e),
            },
        )

    finally:
        logger.info(f"Finished DSR processing for {dsr_id}")