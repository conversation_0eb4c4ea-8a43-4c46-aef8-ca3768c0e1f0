import sqlite3
import os
import sys
import traceback

def create_multi_schema_database(base_dir, db_name="MultiSchemaDB"):
    """
    Create a single SQLite database file with multiple schemas
    
    Args:
        base_dir: Directory where the database will be created
        db_name: Name of the database file
    """
    try:
        db_path = os.path.join(base_dir, f"{db_name}.db")
        print(f"Creating {db_name} at {db_path}")
        
        # Remove existing database if it exists
        if os.path.exists(db_path):
            os.remove(db_path)
        
        # Connect to the database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create schemas (in SQLite, these are actually separate tables with different prefixes)
        schemas = [
            "customer", "employee", "healthcare", "finance", 
            "education", "insurance", "retail", "telecom", 
            "transport", "government"
        ]
        
        # Create tables for each schema
        for schema in schemas:
            # Create main table for this schema
            cursor.execute(f'''
            CREATE TABLE {schema}_main (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                email TEXT,
                phone TEXT,
                identifier TEXT
            )
            ''')
            
            # Create related table for this schema
            cursor.execute(f'''
            CREATE TABLE {schema}_related (
                id INTEGER PRIMARY KEY,
                main_id INTEGER,
                detail_text TEXT,
                detail_code TEXT,
                FOREIGN KEY (main_id) REFERENCES {schema}_main (id)
            )
            ''')
            
            # Insert sample data into main table
            main_data = [
                (1, f'{schema.capitalize()} User 1', f'user1@{schema}.com', '9876543210', f'{schema}123'),
                (2, f'{schema.capitalize()} User 2', f'user2@{schema}.com', '8765432109', f'{schema}456'),
                (3, f'{schema.capitalize()} User 3', f'user3@{schema}.com', '7654321098', f'{schema}789')
            ]
            cursor.executemany(f'INSERT INTO {schema}_main VALUES (?, ?, ?, ?, ?)', main_data)
            
            # Insert sample data into related table
            related_data = [
                (1, 1, f'{schema} Detail 1', f'CODE-{schema}-1'),
                (2, 2, f'{schema} Detail 2', f'CODE-{schema}-2'),
                (3, 3, f'{schema} Detail 3', f'CODE-{schema}-3')
            ]
            cursor.executemany(f'INSERT INTO {schema}_related VALUES (?, ?, ?, ?)', related_data)
        
        conn.commit()
        conn.close()
        
        print(f"{db_name} created successfully at {db_path}")
        return True
    except Exception as e:
        print(f"Error creating {db_name}: {str(e)}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Starting SQLite multi-schema database creation...")
    
    # Get the output directory
    if len(sys.argv) > 1:
        output_dir = sys.argv[1]
    else:
        output_dir = os.getcwd()
    
    print(f"Creating database in: {output_dir}")
    
    # Create directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Create the multi-schema database
    if create_multi_schema_database(output_dir):
        print("Multi-schema database created successfully!")
    else:
        print("Failed to create multi-schema database.")
    
    print("Database creation complete!")