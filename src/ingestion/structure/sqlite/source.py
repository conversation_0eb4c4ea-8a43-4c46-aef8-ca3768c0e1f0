import sqlite3
from uuid import uuid4
import csv
import os
import urllib.parse
import boto3
from botocore.config import Config
from src.common.constants import ServiceTypes, AuthTypes
from src.utils.logger import get_ingestion_logger
from src.modules.repos import DatabaseManager
from src.ingestion.base_source import StructuredSource
from src.common.config import TEMP_DATA_DIR
from src.utils.helpers import create_folder
from src.utils.exceptions import ServerException
from src.ingestion.structure.sqlite.connection import get_region_from_host

logger = get_ingestion_logger()


class SQLiteSource(StructuredSource):
    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.SQLITE.value, service_name)
        self.db = DatabaseManager()
        self.connection = None
        self.cursor = None
        self.db_mode = None

    async def get_service_details(self):
        """Gets service details"""
        self.service = await self.db.get_service_by_service_name(self.service_name)
        credentials = self.service.get("credentials", {})
        self.host = credentials.get("host", "localhost")
        self.port = credentials.get("port", 0)
        self.dbname = credentials.get("dbname", "")
        self.user = credentials.get("user", "")
        self.auth_type = credentials.get("auth_type", AuthTypes.BASIC.value)
        self.password = credentials.get("password", "")
        self.sample_count = credentials.get("sample_count", 10)
        self.databases = credentials.get("databases", [])
        self.db_mode = credentials.get("db_mode", "file")
        
    
        if self.dbname == ":memory:" or self.db_mode == ":memory:":
            self.dbname = ":memory:"
            self.db_mode = ":memory:"
        
        self.readonly = credentials.get("readonly", False)
        self.region = credentials.get("region", None)
        
        if not self.region and "rds.amazonaws.com" in str(self.host):
            try:
                self.region = get_region_from_host(self.host)
            except Exception as e:
                logger.warning(f"Failed to extract region from host: {str(e)}")
        
        return self.service

    def generate_iam_token(self):
        """Generate IAM token for RDS authentication"""
        if not self.region or not self.host or not self.user:
            raise ServerException("Missing required fields for IAM authentication.")

        try:
            logger.info("Generating RDS IAM token using EC2 IAM role...")

            boto_config = Config(
                retries={"max_attempts": 3, "mode": "standard"},
                connect_timeout=2,
                read_timeout=2,
            )

            rds_client = boto3.client(
                "rds", region_name=self.region, config=boto_config
            )
            token = rds_client.generate_db_auth_token(
                DBHostname=self.host,
                Port=self.port,
                DBUsername=self.user,
            )
            logger.info("IAM token successfully generated.")
            return token
        except Exception as e:
            if "Unable to locate credentials" in str(e):
                raise ServerException(
                    "IAM role is not attached to the EC2 instance.", excep=e
                )
            raise ServerException("Failed to generate IAM token.", excep=e)

    def connect_using_iam(self):
        """Connect to RDS SQLite using IAM token"""
        try:
            token = self.generate_iam_token()
            

            if "rds.amazonaws.com" in self.host:
                # Build connection string with IAM token
                if os.path.isabs(self.dbname):
                    db_path = self.dbname
                else:
                    db_path = f"//{self.host}"
                    if self.port:
                        db_path += f":{self.port}"
                    db_path += f"/{self.dbname}"
                
                params = ["iam_auth=true"]
                if self.readonly:
                    params.append("mode=ro")
                
                conn_string = f"file:{db_path}?{'&'.join(params)}"
                
                # Connect with URI mode enabled
                self.connection = sqlite3.connect(conn_string, uri=True)
                self.cursor = self.connection.cursor()
                logger.info(f"Connected to SQLite database using IAM authentication: {conn_string}")
            else:
                # If not an RDS instance, fall back to basic auth
                logger.warning("IAM authentication requested but host is not an RDS instance. Using basic auth.")
                self.connect_using_creds()
        except Exception as e:
            raise ServerException(f"IAM-based SQLite connection failed: {str(e)}", excep=e)

    def build_connection_string(self):
        """Build SQLite connection string based on provided parameters"""
        if self.db_mode == ":memory:":
            return ":memory:"
        
        if self.host and self.host != "localhost" and "rds.amazonaws.com" in self.host:
            # This is an RDS instance
            if os.path.isabs(self.dbname):
                db_path = self.dbname
            else:
                db_path = f"//{self.host}"
                if self.port:
                    db_path += f":{self.port}"
                db_path += f"/{self.dbname}"
            

            params = []
            if self.readonly:
                params.append("mode=ro")
            
            if params:
                return f"file:{db_path}?{'&'.join(params)}"
            else:
                return db_path
        
        if self.dbname:
            if os.path.isabs(self.dbname):
                db_path = self.dbname
            else:
                if self.host and self.host != "localhost":
                    db_path = f"//{self.host}"
                    if self.port:
                        db_path += f":{self.port}"
                    db_path += f"/{self.dbname}"
                else:
                    db_path = self.dbname
            
            params = []
            if self.readonly:
                params.append("mode=ro")
            
            if params:
                return f"file:{db_path}?{'&'.join(params)}"
            else:
                return db_path
        
        return ":memory:"

    def connect_using_creds(self):
        """Connect to SQLite database"""
        try:
            conn_string = self.build_connection_string()
            
            if "?" in conn_string or conn_string.startswith("file:"):
                self.connection = sqlite3.connect(conn_string, uri=True)
            else:
                self.connection = sqlite3.connect(conn_string)
                
            self.cursor = self.connection.cursor()
            logger.info(f"Connected to SQLite database: {conn_string}")
        except Exception as e:
            raise ServerException(f"SQLite connection failed: {str(e)}", excep=e)
            
    async def connect(self):
        """Creates SQLite connection"""
        if self.connection is not None and self.cursor is not None:
            return
        
        self.connection = None
        self.cursor = None
        
        if self.auth_type and self.auth_type.lower() == AuthTypes.BASIC.value:
            self.connect_using_creds()
        elif self.auth_type and self.auth_type.lower() == AuthTypes.IAM.value:
            if "rds.amazonaws.com" in str(self.host):
                self.connect_using_iam()
            else:
                logger.warning("IAM authentication not directly supported for non-RDS SQLite. Using basic auth.")
                self.connect_using_creds()
        else:
            raise ServerException("Only basic and IAM authentication are supported for SQLite.")
        
        if not self.cursor:
            raise ServerException("Failed to create database cursor")
        
    async def test_connection(self):
        """Test the database connection"""
        try:
            await self.connect()
            self.cursor.execute("SELECT 1")
            logger.info(f"Successfully connected to SQLite database")
            self.connection.close()
            return True
        except Exception as e:
            logger.error(f"SQLite connection test failed: {str(e)}")
            return False
            
    def close_connection(self):
        """Close the database connection without logging every time"""
        try:
            if self.cursor:
                self.cursor.close()
                self.cursor = None
            if self.connection:
                self.connection.close()
                self.connection = None
        except Exception as e:
            pass
            
    async def get_tables(self):
        """Get all tables in the database"""
        await self.connect()
        try:
            self.cursor.execute(
                "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%';"
            )
            tables = [row[0] for row in self.cursor.fetchall()]
            return tables
        finally:
            self.close_connection()
            
    async def get_table_columns(self, table):
        """Get columns for a specific table"""
        await self.connect()
        try:
            self.cursor.execute(f"PRAGMA table_info({table});")
            columns = []
            for col in self.cursor.fetchall():
                columns.append({
                    "column_name": col[1],
                    "data_type": col[2],
                    "is_nullable": "YES" if not col[3] else "NO",
                    "column_default": col[4]
                })
            return columns
        finally:
            self.close_connection()
            
    async def extract_table_data(self, table):
        """Extract data from a table"""
        try:
            await self.connect()
            
            if not self.cursor:
                raise ServerException("Failed to create database cursor")
                
            self.cursor.execute(f"PRAGMA table_info({table});")
            column_info = self.cursor.fetchall()
            columns = []
            
            for col in column_info:
                col_id, col_name, data_type, not_null, default_val, pk = col
                columns.append({
                    "column_name": col_name,
                    "data_type": data_type,
                    "is_nullable": "NO" if not_null else "YES",
                    "column_default": default_val,
                    "primary_key": pk > 0
                })
    
            self.cursor.execute(f"SELECT COUNT(*) FROM {table};")
            row_count = self.cursor.fetchone()[0]

            self.cursor.execute(f"SELECT * FROM {table} LIMIT {self.sample_count};")
            rows = self.cursor.fetchall()
            
            if self.db_mode == ":memory:":
                table_uri = f"sqlite://memory/{table}"
            elif self.host and self.host != "localhost" and self.port:
                table_uri = f"sqlite://{self.host}:{self.port}/{self.dbname}/{table}"
            elif self.host and self.host != "localhost":
                table_uri = f"sqlite://{self.host}/{self.dbname}/{table}"
            else:
                table_uri = f"sqlite:///{self.dbname}/{table}"
                
            logger.info(f"Constructed table_uri for {table}: {table_uri}")


            temp_folder = f"{TEMP_DATA_DIR}/sqlite"
            create_folder(temp_folder)
            output_file = os.path.join(temp_folder, f"{table}_{str(uuid4())}.csv")
            with open(output_file, mode="w", newline="", encoding="utf-8") as file:
                writer = csv.writer(file)
                writer.writerow([col["column_name"] for col in columns])
                writer.writerows(rows)
            
            return {
                "table": table,
                "columns": columns,
                "row_count": row_count,
                "sample_rows": rows,
                "csv_path": output_file
            }
        except Exception as e:
            logger.error(f"Error extracting data from table {table}: {str(e)}")
            raise
        finally:
            self.close_connection()
            
    async def extract_data(self):
        """Extract data from all tables"""
        try:
            await self.connect()
            
            self.cursor.execute(
                "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%';"
            )
            tables = [row[0] for row in self.cursor.fetchall()]
            self.close_connection()
            
            results = []
            
            for table in tables:
                try:
                    table_data = await self.extract_table_data(table)
                    results.append(table_data)
                except Exception as e:
                    logger.error(f"Error extracting data from table {table}: {str(e)}")
                    
            return results
        except Exception as e:
            logger.error(f"Error extracting data: {str(e)}")
            return []
        finally:
            self.close_connection()

    async def deep_scan(self):
        """Perform deep scan to extract table schema and sample data."""
        try:
            await self.get_service_details()
            
            if self.connection is not None:
                self.close_connection()
            
            await self.connect()


            self.cursor.execute(
                "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%';"
            )
            tables = [row[0] for row in self.cursor.fetchall()]
            
    
            for table in tables:
                self.close_connection()
                await self.connect()
                
                table_res = await self.scan_table(table)
                yield table_res
            
        except Exception as e:
            logger.error(f"Deep scan failed. Service: {self.service_name}", exc_info=e)
        finally:
            if self.connection is not None:
                self.close_connection()
                logger.info("SQLite connection closed")

    async def scan_table(self, table):
        """Scan a single table for schema and sample data"""
        try:
            self.cursor.execute(f"PRAGMA table_info({table});")
            columns = []
            primary_keys = []
            
            for col in self.cursor.fetchall():
                col_id, col_name, data_type, not_null, default_val, pk = col
                is_primary = pk > 0
                if is_primary:
                    primary_keys.append(col_name)
                    
                columns.append({
                    "column_name": col_name,
                    "data_type": data_type,
                    "is_nullable": "NO" if not_null else "YES",
                    "column_default": default_val,
                    "primary_key": is_primary,
                    "index": is_primary  
                })
            

            self.cursor.execute(f"SELECT COUNT(*) FROM {table};")
            row_count = self.cursor.fetchone()[0]
            
    
            self.cursor.execute(f"SELECT * FROM {table} LIMIT {self.sample_count};")
            rows = self.cursor.fetchall()
            

            if self.db_mode == ":memory:":
                table_uri = f"sqlite://memory/{table}"
            elif self.host and self.host != "localhost" and self.port:
                table_uri = f"sqlite://{self.host}:{self.port}/{self.dbname}/{table}"
            elif self.host and self.host != "localhost":
                table_uri = f"sqlite://{self.host}/{self.dbname}/{table}"
            else:
                table_uri = f"sqlite:///{self.dbname}/{table}"
                
            logger.info(f"Constructed table_uri for {table}: {table_uri}")

            temp_folder = f"{TEMP_DATA_DIR}/sqlite"
            create_folder(temp_folder)
            output_file = os.path.join(temp_folder, f"{table}_{str(uuid4())}.csv")
            with open(output_file, mode="w", newline="", encoding="utf-8") as file:
                writer = csv.writer(file)
                writer.writerow([col["column_name"] for col in columns])
                writer.writerows(rows)
            
            return {
                "db_name": self.dbname or "memory",
                "db_type": ServiceTypes.SQLITE.value,
                "table_name": table,
                "table_uri": table_uri,
                "no_of_rows": row_count,
                "columns": columns,
                "primary_keys": primary_keys,
                "sample_data_file": output_file
            }
        except Exception as e:
            logger.error(f"Table scan failed. Table: {table}", exc_info=e)
            return {
                "db_name": self.dbname or "memory",
                "db_type": ServiceTypes.SQLITE.value,
                "table_name": table,
                "error": str(e)
            }

    async def infra_scan(self):
        """Perform infrastructure scan to get database metadata."""
        try:
            logger.info(f"Starting infra scan for service: {self.service_name}")
            await self.get_service_details()
            await self.connect()

            self.cursor.execute(
                "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%';"
            )
            tables = [row[0] for row in self.cursor.fetchall()]
            

            total_rows = 0
            for table in tables:
                try:
                    self.cursor.execute(f"SELECT COUNT(*) FROM {table};")
                    count = self.cursor.fetchone()[0]
                    total_rows += count
                except Exception as e:
                    logger.warning(f"Could not count rows in table {table}: {str(e)}")
            
            db_size = 0
            if self.db_mode != ":memory:" and self.dbname != ":memory:" and os.path.exists(self.dbname):
                try:
                    db_size = os.path.getsize(self.dbname)
                except Exception as e:
                    logger.warning(f"Could not determine database file size: {str(e)}")
            

            region = "Unknown"
            if self.region:
                region = self.region
            elif self.host:
                region = get_region_from_host(self.host)
            
            logger.info(
                f"Infra scan result - Region: {region}, Tables: {len(tables)}, Total Rows: {total_rows}"
            )
            
            return {
                "tables": tables,
                "total_rows": total_rows,
                "db_size": db_size,
                "region": region,
                "host": self.host or "local"
            }

        except Exception as e:
            logger.error(f"Infrastructure scan failed: {e}")
            return {"error": str(e)}
        finally:
            self.close_connection()
            logger.info("SQLite connection closed")
