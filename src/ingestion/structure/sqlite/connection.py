import re
import os
import socket
from src.utils.logger import get_ingestion_logger

logger = get_ingestion_logger()

def get_region_from_host(host: str) -> str:
    """
    Determine region from host for SQLite database
    For SQLite, this is typically local, but we'll try to determine
    the region based on the host if it's not localhost
    """
    try:
        # If host is localhost or a local path, return "local"
        if host in ["localhost", "127.0.0.1", "::1"] or os.path.exists(host):
            return "local"
            
        # If it's a remote host, try to determine the region
        if "amazonaws.com" in host:
            match = re.search(r"\.(?P<region>[a-z]{2}-[a-z]+-\d)\.rds\.amazonaws\.com", str(host))
            if match:
                region = match.group("region")
                if region:
                    return region
                    
        # For Azure
        if "database.windows.net" in host:
            return "azure"
            
        # For GCP
        if "cloudsql.googleapis.com" in host:
            return "gcp"
            
    except Exception:
        pass
    
    return "Unknown"
