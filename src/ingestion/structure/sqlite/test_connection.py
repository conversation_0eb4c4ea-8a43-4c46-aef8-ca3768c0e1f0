import sqlite3
import os
import boto3
from botocore.config import Config
from botocore.exceptions import NoCredentialsError

from src.common.constants import AuthTypes
from src.utils.logger import get_ingestion_logger
from src.utils.exceptions import ClientException, ServerException
from src.ingestion.structure.sqlite.connection import get_region_from_host

logger = get_ingestion_logger()

async def test_sqlite_connection(data: dict) -> dict:
    """Test connection to SQLite database"""
    creds = data.get("credentials", {})
    
    host = creds.get("host", "localhost")
    port = creds.get("port", 0)
    dbname = creds.get("dbname", "")
    user = creds.get("user", "")
    password = creds.get("password", "")
    auth_type = creds.get("auth_type", AuthTypes.BASIC.value)
    db_mode = creds.get("db_mode", "file")
    readonly = creds.get("readonly", False)
    region = creds.get("region")
    
    is_rds = "rds.amazonaws.com" in str(host)
    
    if not region and is_rds:
        try:
            region = get_region_from_host(host)
        except Exception as e:
            logger.warning(f"Failed to extract region from host: {str(e)}")
    
    try:
        if db_mode == ":memory:" or dbname == ":memory:":
            conn_string = ":memory:"
            uri_mode = False
        else:
            if os.path.isabs(dbname):
                db_path = dbname
            else:
                if host and host != "localhost":
                    db_path = f"//{host}"
                    if port:
                        db_path += f":{port}"
                    db_path += f"/{dbname}"
                else:
                    db_path = dbname
            
            params = []
            if readonly:
                params.append("mode=ro")
            
            if params:
                conn_string = f"file:{db_path}?{'&'.join(params)}"
                uri_mode = True
            else:
                conn_string = db_path
                uri_mode = False
        
        # Handle IAM authentication for RDS
        if auth_type and auth_type.lower() == AuthTypes.IAM.value and is_rds:
            logger.info(f"Testing SQLite connection with IAM authentication to {host}")
            
            if not region:
                raise ClientException("Region is required for IAM authentication with RDS", code=400)
                
            try:
                # Generate IAM token
                boto_config = Config(
                    retries={"max_attempts": 3, "mode": "standard"},
                    connect_timeout=2,
                    read_timeout=2,
                )
                
                rds_client = boto3.client("rds", region_name=region, config=boto_config)
                token = rds_client.generate_db_auth_token(
                    DBHostname=host,
                    Port=port,
                    DBUsername=user,
                )
                
                # Add IAM auth parameter
                if uri_mode:
                    conn_string = conn_string.replace("?", "?iam_auth=true&")
                else:
                    conn_string = f"file:{db_path}?iam_auth=true"
                    uri_mode = True
                
                # Connect with IAM token
                conn = sqlite3.connect(conn_string, uri=True)
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                cursor.close()
                conn.close()
                
                logger.info("SQLite IAM authentication successful")
                return {
                    "status": True,
                    "auth_mechanism": "iam",
                    "message": "SQLite IAM authentication successful"
                }
                
            except NoCredentialsError:
                logger.error("No AWS credentials found or accessible")
                raise ClientException("No valid AWS credentials found", code=401)
                
            except Exception as e:
                logger.error(f"SQLite IAM authentication failed: {str(e)}")
                raise ClientException(f"SQLite IAM authentication failed: {str(e)}", code=401)
        
        # Basic authentication
        else:
            logger.info(f"Testing SQLite connection with basic authentication to {conn_string}")
            
            if uri_mode:
                conn = sqlite3.connect(conn_string, uri=True)
            else:
                conn = sqlite3.connect(conn_string)
                
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            cursor.close()
            conn.close()
            
            logger.info("SQLite connection test successful")
            return {
                "status": True,
                "auth_mechanism": "basic",
                "message": "SQLite connection successful"
            }
            
    except sqlite3.Error as e:
        logger.error(f"SQLite connection error: {str(e)}")
        raise ClientException(f"SQLite connection failed: {str(e)}", code=400)
        
    except Exception as e:
        logger.error(f"Unexpected error testing SQLite connection: {str(e)}")
        raise ServerException(f"SQLite connection test failed: {str(e)}")
