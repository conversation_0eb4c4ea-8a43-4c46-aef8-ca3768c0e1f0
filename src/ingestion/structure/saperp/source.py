import os
import csv
from uuid import uuid4
import requests
from typing import AsyncGenerator, List, Dict, Any
from collections import defaultdict

try:
    import pyodata
    from pyodata.exceptions import PyODataException, HttpError
    PYODATA_AVAILABLE = True
except ImportError:
    PYODATA_AVAILABLE = False

from src.common.constants import ServiceTypes, DBTypes, AuthTypes, ServiceProviders, SAPSubServices
from src.utils.logger import get_ingestion_logger
from src.modules.repos import DatabaseManager
from src.ingestion.base_source import StructuredSource
from src.ingestion.data_class import TableMetadata, TableColumn, AssetsDetails
from src.common.config import TEMP_DATA_DIR
from src.utils.helpers import create_folder
from src.utils.loaders import load_access_controls, load_asset_details
from src.utils.exceptions import ServerException, CustomBaseException

logger = get_ingestion_logger()


class SAPERPSource(StructuredSource):
    
    def __init__(self, service_name: str):
        if not PYODATA_AVAILABLE:
            raise ImportError(
                "pyodata is required for SAP ERP connector.\n"
                "Install with: pip install pyodata requests\n"
                "Documentation: https://github.com/SAP/python-pyodata"
            )
        
        super().__init__(ServiceTypes.SAP_ERP.value, service_name)
        self.db = DatabaseManager()
        self.session = None
        self.client = None
        self.service_provider = ServiceProviders.SAP
        self.sub_service = DBTypes.SAP_ERP.value
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"
        
        # SAP-specific attributes
        self.base_url = None
        self.client_number = None
        self.language = "EN"
        self.entity_sets_to_scan = []
        
    async def get_service_details(self):
        """Gets SAP ERP service details from database"""
        self.service = await self.db.get_service_by_service_name(self.service_name)
        credentials = self.service.get("credentials", {})
        
        # Validate required credentials
        self._validate_required_credentials(credentials)
        
        # SAP OData connection parameters
        self.base_url = credentials.get("base_url")  # REQUIRED
        self.service_path = credentials.get("service_path", "")  # Optional: specific service
        self.user = credentials.get("user")  # REQUIRED (unless using cert auth)
        self.password = credentials.get("password")  # REQUIRED (unless using cert auth)
        self.client_number = credentials.get("client", "800")  # REQUIRED (defaults to 800)
        self.language = credentials.get("language", "EN")  # Optional
        
        # Optional: Certificate authentication
        self.cert_path = credentials.get("cert_path")
        self.key_path = credentials.get("key_path")
        
        # Scan configuration
        self.sample_count = credentials.get("sample_count", 10)
        self.entity_sets_to_scan = credentials.get("entity_sets", [])  # Specific entity sets
        self.verify_ssl = credentials.get("verify_ssl", True)
        
        # Service metadata
        self.service_steward = self.service.get("data_steward")
        self.service_owner = self.service.get("data_owner")
        
        return self.service
    
    def _validate_required_credentials(self, credentials: dict):
        """Validate that all required credentials are provided"""
        required_fields = ["base_url"]
        missing_fields = []
        
        # Check basic required fields
        for field in required_fields:
            if not credentials.get(field):
                missing_fields.append(field)
        
        # Check authentication - must have either user/password OR cert auth
        has_user_auth = credentials.get("user") and credentials.get("password")
        has_cert_auth = credentials.get("cert_path") and credentials.get("key_path")
        
        if not has_user_auth and not has_cert_auth:
            missing_fields.append("authentication (user+password OR cert_path+key_path)")
        
        # Validate base_url format
        base_url = credentials.get("base_url", "")
        if base_url and not (base_url.startswith("http://") or base_url.startswith("https://")):
            raise ServerException("base_url must start with http:// or https://")
        
        # Validate client number
        client = credentials.get("client")
        if client:
            try:
                int(client)  # Must be numeric
            except (ValueError, TypeError):
                raise ServerException("client must be a valid number (e.g., '100', '800')")
        
        if missing_fields:
            raise ServerException(f"Missing required credentials: {', '.join(missing_fields)}")
    
    async def connect(self):
        """Creates connection to SAP ERP system via OData"""
        try:
            # Create requests session
            self.session = requests.Session()
            
            # Set authentication
            if self.user and self.password:
                self.session.auth = (self.user, self.password)
            elif self.cert_path and self.key_path:
                self.session.cert = (self.cert_path, self.key_path)
            else:
                raise ServerException("Either username/password or certificate must be provided")
            
            # Set SSL verification
            self.session.verify = self.verify_ssl
            
            # Add SAP-specific headers
            self.session.headers.update({
                'sap-client': str(self.client_number),
                'sap-language': self.language,
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            })
            
            logger.info(f"Successfully connected to SAP ERP system: {self.base_url}")
            
        except Exception as e:
            raise ServerException(f"SAP OData connection failed: {str(e)}", excep=e)
    
    async def test_connection(self) -> dict:
        """Test SAP connection by accessing the service root"""
        try:
            await self.get_service_details()
            await self.connect()
            
            # Test connection by accessing service root
            response = self.session.get(self.base_url)
            response.raise_for_status()
            
            return {
                "status": "success",
                "message": "SAP ERP OData connection successful",
                "url": self.base_url,
                "client": self.client_number
            }
        except requests.exceptions.HTTPError as e:
            return {
                "status": "failed",
                "message": f"HTTP Error: {e.response.status_code} - {e.response.text}"
            }
        except Exception as e:
            return {
                "status": "failed",
                "message": str(e)
            }
        finally:
            self.close_connection()
    
    def get_odata_client(self, service_name: str):
        """
        Create PyOData client for a specific service
        
        Args:
            service_name: OData service name (e.g., 'API_BUSINESS_PARTNER')
        
        Returns:
            PyOData Client instance
        """
        try:
            service_url = f"{self.base_url}/{service_name}"
            
            # Get metadata to initialize client
            response = self.session.get(service_url)
            response.raise_for_status()
            
            # Create PyOData client
            client = pyodata.Client(service_url, self.session)
            
            return client
            
        except HttpError as e:
            logger.error(f"HTTP error accessing service {service_name}: {e}")
            raise ServerException(f"Failed to access OData service: {service_name}", excep=e)
        except Exception as e:
            logger.error(f"Error creating OData client for {service_name}: {e}")
            raise ServerException(f"Failed to create OData client: {service_name}", excep=e)
    
    async def get_available_services(self) -> List[str]:
        """Get list of available OData services"""
        try:
            # Try to get service catalog
            response = self.session.get(f"{self.base_url}/?$format=json")
            response.raise_for_status()
            
            data = response.json()
            services = []
            
            # Parse service document
            if 'd' in data and 'EntitySets' in data['d']:
                services = data['d']['EntitySets']
            
            logger.info(f"Found {len(services)} OData services")
            return services
            
        except Exception as e:
            logger.warning(f"Could not retrieve service catalog: {e}")
            return []
    
    async def get_entity_sets(self, service_name: str) -> List[Dict[str, Any]]:
        """
        Get entity sets from an OData service
        
        Args:
            service_name: OData service name
            
        Returns:
            List of entity set information
        """
        try:
            client = self.get_odata_client(service_name)
            entity_sets = []
            
            for es in client.schema.entity_sets:
                entity_sets.append({
                    "name": es.name,
                    "entity_type": es.entity_type.name,
                    "service": service_name
                })
            
            logger.info(f"Found {len(entity_sets)} entity sets in service {service_name}")
            return entity_sets
            
        except Exception as e:
            logger.error(f"Error getting entity sets from {service_name}: {e}")
            return []
    
    async def get_entity_metadata(self, service_name: str, entity_set_name: str) -> Dict[str, Any]:
        """Get metadata for a specific entity set"""
        try:
            client = self.get_odata_client(service_name)
            
            # Find the entity set
            entity_set = None
            for es in client.schema.entity_sets:
                if es.name == entity_set_name:
                    entity_set = es
                    break
            
            if not entity_set:
                logger.warning(f"Entity set {entity_set_name} not found in service {service_name}")
                return None
            
            # Get entity type
            entity_type = entity_set.entity_type
            
            metadata = {
                "entity_set_name": entity_set_name,
                "entity_type_name": entity_type.name,
                "properties": [],
                "keys": [],
                "navigation_properties": []
            }
            
            # Get properties - PyOData uses proprties attribute (typo in their library)
            # Try multiple possible attribute names
            props = None
            for attr_name in ['proprties', 'properties', '_properties']:
                props = getattr(entity_type, attr_name, None)
                if props is not None:
                    logger.debug(f"[SAP ERP] Found properties via attribute: {attr_name}")
                    break
            
            if props is None:
                logger.error(f"[SAP ERP] Could not find properties attribute. Available attributes: {dir(entity_type)}")
                return None
            
            # Handle if it's a method
            if callable(props):
                props = props()
            
            for prop in props:
                prop_info = {
                    "name": prop.name,
                    "type": str(prop.typ),
                    "nullable": getattr(prop, 'nullable', True),
                    "max_length": getattr(prop, 'max_length', None),
                    "precision": getattr(prop, 'precision', None),
                    "scale": getattr(prop, 'scale', None)
                }
                metadata["properties"].append(prop_info)
            
            # Get key properties
            key_props = getattr(entity_type, 'key_proprties', None) or getattr(entity_type, 'key_properties', [])
            for key_prop in key_props:
                metadata["keys"].append(key_prop.name)
            
            # Get navigation properties
            try:
                nav_props = getattr(entity_type, 'nav_proprties', None) or getattr(entity_type, 'nav_properties', [])
                for nav_prop in nav_props:
                    metadata["navigation_properties"].append({
                        "name": nav_prop.name,
                        "to_role": nav_prop.to_role.name if hasattr(nav_prop, 'to_role') else None
                    })
            except Exception as nav_error:
                logger.debug(f"Could not get navigation properties: {nav_error}")
                pass  # Navigation properties might not be available
            
            return metadata
            
        except Exception as e:
            logger.error(f"Error getting metadata for {entity_set_name}: {e}")
            return None
    
    async def get_entity_data(
        self, 
        service_name: str, 
        entity_set_name: str,
        limit: int = None,
        filter_expr: str = None,
        select_fields: List[str] = None
    ) -> List[Dict]:
        """Extract data from SAP entity set"""
        try:
            if limit is None:
                limit = self.sample_count
            
            client = self.get_odata_client(service_name)
            
            # Build query
            request = getattr(client.entity_sets, entity_set_name).get_entities()
            
            # Apply filters
            if filter_expr:
                request = request.filter(filter_expr)
            
            # Apply select
            if select_fields:
                request = request.select(*select_fields)
            
            # Apply limit
            request = request.top(limit)
            
            # Execute query
            rows = []
            for entity in request.execute():
                row = {}
                # Get all properties
                for prop_name in dir(entity):
                    if not prop_name.startswith('_'):
                        try:
                            value = getattr(entity, prop_name)
                            # Skip methods and navigation properties
                            if not callable(value):
                                row[prop_name] = value
                        except:
                            pass
                
                if row:  # Only add non-empty rows
                    rows.append(row)
            
            logger.info(f"Retrieved {len(rows)} rows from {entity_set_name}")
            return rows
            
        except HttpError as e:
            logger.error(f"HTTP error reading entity set {entity_set_name}: {e}")
            return []
        except Exception as e:
            logger.error(f"Error reading data from {entity_set_name}: {e}")
            return []
    
    async def get_entity_count(self, service_name: str, entity_set_name: str) -> int:
        """Get count of entities in an entity set"""
        try:
            client = self.get_odata_client(service_name)
            
            request = getattr(client.entity_sets, entity_set_name).get_entities()
            request = request.count(inline=True)
            
            # Execute with count
            result = request.execute()
            
            # Try to get count from response
            if hasattr(result, '__count__'):
                return result.__count__
            
            # Fallback: count items
            return len(list(result))
            
        except Exception as e:
            logger.warning(f"Could not get count for {entity_set_name}: {e}")
            return 0
    
    async def get_access_controls(self) -> List[Dict[str, Any]]:
        """
        Get SAP authorization data via OData
        
        Note: This requires specific OData services to be exposed for user/role data
        """
        try:
            access_controls = []
            
            # Common SAP user/role OData services
            user_services = [
                'API_USER_SRV',
                'API_CV_USERMANAGEMENT_SRV',
                'UserManagementService'
            ]
            
            for service in user_services:
                try:
                    client = self.get_odata_client(service)
                    
                    # Try to get users
                    for es in client.schema.entity_sets:
                        if 'user' in es.name.lower() or 'role' in es.name.lower():
                            users = await self.get_entity_data(service, es.name, limit=100)
                            
                            for user in users:
                                # Extract user/role information
                                username = user.get('UserName') or user.get('UserId') or user.get('User')
                                if username and not username.startswith('SAP'):
                                    access_controls.append({
                                        "user_or_role": username,
                                        "role": "user",
                                        "access": "restricted"  # Default access level
                                    })
                    
                    if access_controls:
                        break  # Found users, no need to try other services
                        
                except:
                    continue  # Service might not be available
            
            return access_controls
            
        except Exception as e:
            logger.error(f"Error fetching SAP access controls: {e}")
            return []
    
    async def get_encryption_status(self) -> bool:
        """Check if SAP connection uses encryption (HTTPS)"""
        return self.base_url.startswith('https://')
    
    async def infra_scan(self):
        """Perform infrastructure scan for SAP ERP system"""
        try:
            logger.info(f"Starting infra scan for SAP service: {self.service_name}")
            await self.get_service_details()
            await self.connect()
            
            # Get available services
            services = await self.get_available_services()
            
            metadata = {
                "base_url": self.base_url,
                "client": self.client_number,
                "language": self.language,
                "connection_type": "OData",
                "available_services": len(services)
            }
            
            # Count total entity sets across all services
            total_entity_sets = 0
            if self.entity_sets_to_scan:
                # Use specified entity sets
                total_entity_sets = len(self.entity_sets_to_scan)
            else:
                # Count from available services (sample first few)
                for service in services[:5]:  # Check first 5 services
                    try:
                        entity_sets = await self.get_entity_sets(service)
                        total_entity_sets += len(entity_sets)
                    except:
                        continue
            
            # Get access controls
            access_controls = await self.get_access_controls()
            access_objs = [{**perm, "asset_name": "SAP_ODATA_SYSTEM"} for perm in access_controls]
            
            try:
                load_access_controls(access_objs)
                logger.info("Access controls loaded successfully")
            except Exception as e:
                logger.error(f"Failed to load access controls: {e}")
            
            # Get encryption status
            encryption_status = await self.get_encryption_status()
            
            # Create asset record
            try:
                asset_obj = AssetsDetails(
                    asset_name=f"SAP_ODATA_{self.client_number}",
                    service_provider=self.service_provider,
                    type="erp_system",
                    category="structured",
                    location=self.base_url,
                    owner=self.service_owner,
                    security=encryption_status,
                    size=0,
                    count=total_entity_sets,
                    access_category="restricted" if access_controls else "none",
                    service_name=str(self.service_name),
                    steward=str(self.service_steward),
                )
                load_asset_details([asset_obj])
                logger.info("Asset details loaded successfully")
            except Exception as e:
                logger.error(f"Failed to load asset details: {e}")
            
            logger.info(f"Infra scan completed. Found {total_entity_sets} entity sets")
            return {
                "metadata": metadata,
                "entity_set_count": total_entity_sets,
                "encryption_enabled": encryption_status,
                "services": services[:10]  # Return first 10 services
            }
            
        except Exception as e:
            logger.error(f"Infrastructure scan failed: {e}", exc_info=True)
            return {"error": str(e)}
        finally:
            self.close_connection()
    
    async def deep_scan(self) -> AsyncGenerator[TableMetadata, None]:
        """Perform deep scan to extract entity set schema and sample data"""
        try:
            logger.info(f"[SAP ERP] ========== STARTING DEEP SCAN ==========")
            logger.info(f"[SAP ERP] Service: {self.service_name}")
            
            await self.get_service_details()
            await self.connect()
            
            logger.info(f"[SAP ERP] Connection established to: {self.base_url}")
            logger.info(f"[SAP ERP] Client Number: {self.client_number}")
            logger.info(f"[SAP ERP] Sample Count: {self.sample_count}")
            
            # Determine what to scan
            if self.entity_sets_to_scan:
                # Use specified entity sets
                scan_list = self.entity_sets_to_scan
                logger.info(f"[SAP ERP] Scanning {len(scan_list)} specified entity sets")
            else:
                # Get all available services and their entity sets
                logger.info(f"[SAP ERP] Discovering available OData services...")
                services = await self.get_available_services()
                logger.info(f"[SAP ERP] Found {len(services)} OData services")
                
                scan_list = []
                
                for service in services[:5]:  # Limit to first 5 services
                    try:
                        entity_sets = await self.get_entity_sets(service)
                        logger.info(f"[SAP ERP] Service '{service}' has {len(entity_sets)} entity sets")
                        for es in entity_sets:
                            scan_list.append({
                                "service": service,
                                "entity_set": es["name"]
                            })
                    except:
                        continue
                
                logger.info(f"[SAP ERP] Total entity sets to scan: {len(scan_list)}")
            
            # Scan each entity set
            scanned_count = 0
            for item in scan_list:
                if isinstance(item, dict):
                    service = item.get("service")
                    entity_set = item.get("entity_set")
                else:
                    # Assume format: "service/entity_set"
                    parts = item.split('/')
                    service = parts[0] if len(parts) > 1 else self.service_path
                    entity_set = parts[-1]
                
                try:
                    logger.info(f"[SAP ERP] {'='*60}")
                    logger.info(f"[SAP ERP] Scanning entity set {scanned_count + 1}/{len(scan_list)}: {service}/{entity_set}")
                    
                    table_metadata = await self.scan_entity_set(service, entity_set)
                    if table_metadata:
                        logger.info(f"[SAP ERP] ✅ Successfully scanned {entity_set}")
                        logger.info(f"[SAP ERP] 📤 Yielding TableMetadata to workflow...")
                        scanned_count += 1
                        yield table_metadata
                    else:
                        logger.warning(f"[SAP ERP] ⏭️  Skipped {entity_set} (no data or already processed)")
                except Exception as e:
                    logger.error(f"[SAP ERP] ❌ Failed to scan entity set {entity_set}: {e}", exc_info=True)
            
            logger.info(f"[SAP ERP] ========== DEEP SCAN COMPLETE ==========")
            logger.info(f"[SAP ERP] Successfully scanned: {scanned_count}/{len(scan_list)} entity sets")
                    
        except Exception as e:
            logger.error(f"[SAP ERP] Deep scan failed: {e}", exc_info=True)
        finally:
            self.close_connection()
            logger.info(f"[SAP ERP] Connection closed")
    
    async def scan_entity_set(self, service_name: str, entity_set_name: str) -> TableMetadata:
        """Scan a specific SAP entity set"""
        try:
            logger.info(f"[SAP ERP] Starting scan for entity set: {service_name}/{entity_set_name}")
            
            # Get entity metadata
            metadata = await self.get_entity_metadata(service_name, entity_set_name)
            if not metadata or not metadata["properties"]:
                logger.warning(f"[SAP ERP] No metadata for entity set {entity_set_name}")
                return None
            
            logger.info(f"[SAP ERP] Retrieved metadata for {entity_set_name}:")
            logger.info(f"[SAP ERP]   - Entity Type: {metadata['entity_type_name']}")
            logger.info(f"[SAP ERP]   - Properties Count: {len(metadata['properties'])}")
            logger.info(f"[SAP ERP]   - Key Fields: {metadata['keys']}")
            
            # Build columns list
            columns = []
            for prop in metadata["properties"]:
                is_key = prop["name"] in metadata["keys"]
                columns.append(
                    TableColumn(
                        column_name=prop["name"],
                        data_type=prop["type"],
                        primary_key=is_key,
                        index=is_key
                    )
                )
            
            logger.info(f"[SAP ERP] Created {len(columns)} TableColumn objects:")
            for i, col in enumerate(columns[:5], 1):  # Log first 5 columns
                logger.info(f"[SAP ERP]   {i}. {col.column_name} ({col.data_type}) - PK:{col.primary_key}, Index:{col.index}")
            if len(columns) > 5:
                logger.info(f"[SAP ERP]   ... and {len(columns) - 5} more columns")
            
            # Get row count
            row_count = await self.get_entity_count(service_name, entity_set_name)
            logger.info(f"[SAP ERP] Total rows in {entity_set_name}: {row_count}")
            
            # Get sample data
            sample_data = await self.get_entity_data(
                service_name, 
                entity_set_name, 
                self.sample_count
            )
            logger.info(f"[SAP ERP] Retrieved {len(sample_data)} sample records (requested: {self.sample_count})")
            
            # Create CSV file
            create_folder(self.local_data_dir)
            output_file = os.path.join(
                self.local_data_dir,
                f"{entity_set_name}_{str(uuid4())}.csv"
            )
            
            if sample_data and len(sample_data) > 0:
                with open(output_file, mode="w", newline="", encoding="utf-8") as file:
                    writer = csv.DictWriter(file, fieldnames=sample_data[0].keys())
                    writer.writeheader()
                    writer.writerows(sample_data)
                logger.info(f"[SAP ERP] CSV file created: {output_file}")
                logger.info(f"[SAP ERP] CSV contains {len(sample_data)} rows with {len(sample_data[0].keys())} columns")
            else:
                # Create empty CSV with column headers even if no data
                logger.warning(f"[SAP ERP] No sample data available, creating empty CSV with headers")
                column_names = [col.column_name for col in columns]
                with open(output_file, mode="w", newline="", encoding="utf-8") as file:
                    writer = csv.DictWriter(file, fieldnames=column_names)
                    writer.writeheader()
                logger.info(f"[SAP ERP] Empty CSV file created: {output_file} (headers only)")
            
            # Create table URI
            table_uri = f"sap://{self.client_number}/{service_name}/{entity_set_name}"
            logger.info(f"[SAP ERP] Table URI: {table_uri}")
            
            # Create table URI
            table_uri = f"sap://{self.client_number}/{service_name}/{entity_set_name}"
            logger.info(f"[SAP ERP] Table URI: {table_uri}")
            
            # Check if already processed
            if self.db.skip_table(table_uri):
                logger.info(f"[SAP ERP] ⏭️  Skipping entity set {entity_set_name} - Already processed")
                return None
            
            # Build details
            details = {
                "service_name": service_name,
                "client": self.client_number,
                "base_url": self.base_url,
                "entity_type": metadata["entity_type_name"],
                "key_fields": metadata["keys"],
                "property_count": len(metadata["properties"]),
                "navigation_properties": metadata["navigation_properties"]
            }
            
            logger.info(f"[SAP ERP] Creating TableMetadata object:")
            logger.info(f"[SAP ERP]   - Service: {self.service_name}")
            logger.info(f"[SAP ERP]   - DB Name: {service_name}")
            logger.info(f"[SAP ERP]   - Table Name: {entity_set_name}")
            logger.info(f"[SAP ERP]   - Table Size: 0 (not calculated)")
            logger.info(f"[SAP ERP]   - Row Count: {row_count}")
            logger.info(f"[SAP ERP]   - Columns: {len(columns)}")
            logger.info(f"[SAP ERP]   - CSV File: {output_file}")
            
            table_metadata = TableMetadata(
                service_name=self.service_name,
                service_type=self.service_type,
                service_provider=self.service_provider,
                sub_service=self.sub_service,
                db_type=DBTypes.SAP_ERP.value,
                db_name=service_name,
                table_name=entity_set_name,
                table_size=0,
                no_of_rows=row_count,
                table_uri=table_uri,
                columns=columns,
                local_filepath=output_file,
                details=details
            )
            
            logger.info(f"[SAP ERP] ✅ TableMetadata created successfully for {entity_set_name}")
            logger.info(f"[SAP ERP] TableMetadata will be yielded to workflow for processing")
            
            return table_metadata
            
        except Exception as e:
            logger.error(f"Entity set scan failed for {entity_set_name}: {e}", exc_info=True)
            return None
    
    async def user_scan(self, initial_entity_sets: List[dict]):
        """
        Perform user scan to find related data across SAP entity sets
        """
        try:
            await self.get_service_details()
            await self.connect()
            
            results = []
            visited_entity_sets = set()
            
            for entity_info in initial_entity_sets:
                service_name = entity_info.get("service_name") or entity_info.get("db_name")
                entity_set_name = entity_info.get("entity_set") or entity_info.get("table_name")
                property_name = entity_info.get("property") or entity_info.get("column_name")
                values = entity_info.get("values", [])
                
                entity_key = f"{service_name}/{entity_set_name}"
                if entity_key in visited_entity_sets:
                    continue
                
                # Get matching rows
                matching_rows = await self.find_matching_rows(
                    service_name, entity_set_name, property_name, values
                )
                
                if matching_rows:
                    results.append({
                        "service_name": self.service_name,
                        "service_provider": self.service_provider,
                        "sub_service": self.sub_service,
                        "db_name": service_name,
                        "table_name": entity_set_name,
                        "column_name": property_name,
                        "matching_rows": matching_rows
                    })
                    visited_entity_sets.add(entity_key)
            
            if results:
                yield results
                
        except Exception as e:
            logger.error(f"User scan failed: {e}", exc_info=True)
        finally:
            self.close_connection()
    
    async def find_matching_rows(
        self, 
        service_name: str, 
        entity_set_name: str, 
        property_name: str, 
        values: List[str]
    ) -> List[Dict]:
        """Find rows matching specific values in a property"""
        try:
            if not values:
                return []
            
            # Build OData filter expression
            filter_conditions = []
            for value in values[:10]:  # Limit to 10 values
                # Escape single quotes in value
                escaped_value = str(value).replace("'", "''")
                filter_conditions.append(f"{property_name} eq '{escaped_value}'")
            
            filter_expr = " or ".join(filter_conditions)
            
            # Get matching data
            rows = await self.get_entity_data(
                service_name,
                entity_set_name,
                limit=20,
                filter_expr=filter_expr
            )
            
            return rows
            
        except Exception as e:
            logger.error(f"Error finding matching rows in {entity_set_name}: {e}")
            return []
    
    def close_connection(self):
        """Close SAP connection"""
        if self.session:
            try:
                self.session.close()
                logger.info("SAP OData connection closed")
            except Exception as e:
                logger.warning(f"Error closing SAP connection: {e}")
            finally:
                self.session = None
                self.client = None