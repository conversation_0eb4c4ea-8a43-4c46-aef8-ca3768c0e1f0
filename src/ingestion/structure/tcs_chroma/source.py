import os
import csv
import json
import requests
from uuid import uuid4
from typing import AsyncGenerator, List, Dict, Any, Optional
from collections import defaultdict
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from src.common.constants import ServiceTypes, DBTypes, AuthTypes, ServiceProviders
from src.utils.logger import get_ingestion_logger
from src.utils.loaders import load_access_controls, load_asset_details
from src.modules.repos import DatabaseManager
from src.ingestion.base_source import StructuredSource
from src.ingestion.data_class import TableMetadata, TableColumn, AssetsDetails
from src.common.config import TEMP_DATA_DIR
from src.utils.helpers import create_folder
from src.utils.exceptions import ServerException

logger = get_ingestion_logger()


class TCSChromaSource(StructuredSource):
    """TCS Chroma HRMS connector with comprehensive error handling and edge case management"""
    
    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.TCS_CHROMA.value, service_name)
        self.db = DatabaseManager()
        self.session = None
        self.access_token = None
        self.service_provider = ServiceProviders.LOCAL
        self.sub_service = DBTypes.TCS_CHROMA.value
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"
        self.base_url = None
        self.headers = {}
        self.available_modules = []
        self.request_timeout = 60
        
    def _setup_session_with_retry(self):
        """Setup requests session with retry logic"""
        self.session = requests.Session()
        
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "POST", "PUT", "DELETE", "OPTIONS", "TRACE"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
    async def get_service_details(self):
        """Get service details from database with validation"""
        try:
            self.service = await self.db.get_service_by_service_name(self.service_name)
            
            if not self.service:
                raise ServerException(f"Service '{self.service_name}' not found in database")
            
            credentials = self.service.get("credentials", {})
            
            # Required fields validation
            self.base_url = credentials.get("base_url")
            if not self.base_url:
                raise ServerException("Missing required field: base_url")
            
            # Remove trailing slash from base_url
            self.base_url = self.base_url.rstrip('/')
            
            self.api_version = credentials.get("api_version", "v1")
            self.tenant_id = credentials.get("tenant_id")
            
            # Authentication
            self.auth_type = credentials.get("auth_type", AuthTypes.OAUTH.value)
            
            if self.auth_type.lower() == AuthTypes.OAUTH.value:
                self.client_id = credentials.get("client_id")
                self.client_secret = credentials.get("client_secret")
                
                if not self.client_id or not self.client_secret:
                    raise ServerException("OAuth authentication requires client_id and client_secret")
                    
            elif self.auth_type.lower() == AuthTypes.BASIC.value :
                self.username = credentials.get("username")
                self.password = credentials.get("password")
                
                if not self.username or not self.password:
                    raise ServerException("Basic authentication requires username and password")
            else:
                raise ServerException(f"Unsupported auth_type: {self.auth_type}")
            
            # Scanning configuration
            self.sample_count = credentials.get("sample_count", 10)
            self.modules = credentials.get("modules", [])
            self.region = credentials.get("region", "IN")
            self.service_steward = self.service.get("data_steward", "TCS Chroma Admin")
            self.service_owner = self.service.get("data_owner", "TCS Chroma Admin")
            
            logger.info(f"Service details loaded successfully for {self.service_name}")
            return self.service
            
        except Exception as e:
            logger.error(f"Failed to get service details: {str(e)}", exc_info=True)
            raise ServerException(f"Failed to get service details: {str(e)}", excep=e)
    
    async def authenticate_oauth(self):
        """Authenticate using OAuth 2.0 Client Credentials flow"""
        try:
            logger.info(f"Attempting OAuth authentication for tenant: {self.tenant_id}")
            
            auth_url = f"{self.base_url}/oauth/token"
            
            payload = {
                "grant_type": "client_credentials",
                "client_id": self.client_id,
                "client_secret": self.client_secret,
                "scope": "read"
            }
            
            if self.tenant_id:
                payload["tenant_id"] = self.tenant_id
            
            response = self.session.post(
                auth_url, 
                data=payload, 
                timeout=self.request_timeout,
                verify=True
            )
            
            if response.status_code != 200:
                error_msg = f"OAuth authentication failed with status {response.status_code}"
                try:
                    error_detail = response.json()
                    error_msg += f": {error_detail}"
                except:
                    error_msg += f": {response.text}"
                raise ServerException(error_msg)
            
            token_data = response.json()
            self.access_token = token_data.get("access_token")
            
            if not self.access_token:
                raise ServerException("No access token received from OAuth endpoint")
            
            self.headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json",
                "Accept": "application/json"
            }
            
            if self.tenant_id:
                self.headers["X-Tenant-ID"] = self.tenant_id
            
            logger.info("Successfully authenticated with TCS Chroma using OAuth")
            return True
            
        except requests.exceptions.RequestException as e:
            raise ServerException(f"OAuth authentication network error: {str(e)}", excep=e)
        except Exception as e:
            raise ServerException(f"OAuth authentication failed: {str(e)}", excep=e)
    
    async def authenticate_basic(self):
        """Authenticate using Basic Auth (username/password)"""
        try:
            logger.info(f"Attempting Basic authentication for user: {self.username}")
            
            auth_url = f"{self.base_url}/api/{self.api_version}/auth/login"
            
            payload = {
                "username": self.username,
                "password": self.password
            }
            
            if self.tenant_id:
                payload["tenant_id"] = self.tenant_id
            
            response = self.session.post(
                auth_url, 
                json=payload, 
                timeout=self.request_timeout,
                verify=True
            )
            
            if response.status_code != 200:
                error_msg = f"Basic authentication failed with status {response.status_code}"
                try:
                    error_detail = response.json()
                    error_msg += f": {error_detail}"
                except:
                    error_msg += f": {response.text}"
                raise ServerException(error_msg)
            
            auth_data = response.json()
            self.access_token = auth_data.get("token") or auth_data.get("access_token")
            
            if not self.access_token:
                raise ServerException("No access token received from login endpoint")
            
            self.headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json",
                "Accept": "application/json"
            }
            
            if self.tenant_id:
                self.headers["X-Tenant-ID"] = self.tenant_id
            
            logger.info("Successfully authenticated with TCS Chroma using Basic Auth")
            return True
            
        except requests.exceptions.RequestException as e:
            raise ServerException(f"Basic authentication network error: {str(e)}", excep=e)
        except Exception as e:
            raise ServerException(f"Basic authentication failed: {str(e)}", excep=e)
    
    async def connect(self):
        """Establish connection to TCS Chroma with proper error handling"""
        try:
            self._setup_session_with_retry()
            
            if self.auth_type.lower() == AuthTypes.OAUTH.value:
                await self.authenticate_oauth()
            elif self.auth_type.lower() == AuthTypes.BASIC.value:
                await self.authenticate_basic()
            else:
                raise ServerException(f"Unsupported authentication type: {self.auth_type}")
            
            logger.info("TCS Chroma connection established successfully")
            
        except Exception as e:
            logger.error(f"Failed to connect to TCS Chroma: {str(e)}", exc_info=True)
            raise ServerException(f"Failed to connect to TCS Chroma: {str(e)}", excep=e)
    
    async def test_connection(self):
        """Test the API connection with comprehensive validation"""
        try:
            logger.info(f"Testing connection for service: {self.service_name}")
            await self.get_service_details()
            await self.connect()
            
            # Test health endpoint
            test_url = f"{self.base_url}/api/{self.api_version}/health"
            
            try:
                response = self.session.get(
                    test_url, 
                    headers=self.headers, 
                    timeout=10
                )
                
                if response.status_code == 200:
                    logger.info("TCS Chroma health check passed")
                    return {"status": "success", "message": "Connection successful"}
                else:
                    logger.warning(f"Health check returned status {response.status_code}")
            except:
                logger.info("Health endpoint not available, testing with modules endpoint")
            
            # Fallback: Test modules endpoint
            modules_url = f"{self.base_url}/api/{self.api_version}/metadata/modules"
            response = self.session.get(
                modules_url, 
                headers=self.headers, 
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info("TCS Chroma connection test successful")
                return {"status": "success", "message": "Connection successful"}
            else:
                return {
                    "status": "failed", 
                    "message": f"Connection test failed with status {response.status_code}"
                }
                
        except Exception as e:
            logger.error(f"TCS Chroma connection test failed: {str(e)}", exc_info=True)
            return {"status": "failed", "message": str(e)}
        finally:
            self.close_connection()
    
    async def get_available_modules(self) -> List[Dict[str, Any]]:
        """Discover available HR modules with error handling"""
        try:
            modules_url = f"{self.base_url}/api/{self.api_version}/metadata/modules"
            
            response = self.session.get(
                modules_url, 
                headers=self.headers, 
                timeout=self.request_timeout
            )
            
            if response.status_code == 200:
                modules_data = response.json()
                self.available_modules = modules_data.get("modules", [])
                logger.info(f"Discovered {len(self.available_modules)} modules in TCS Chroma")
            else:
                logger.warning(f"Failed to fetch modules (status {response.status_code}), using fallback list")
                self._use_fallback_modules()
            
            return self.available_modules
            
        except Exception as e:
            logger.error(f"Failed to fetch available modules: {str(e)}", exc_info=True)
            self._use_fallback_modules()
            return self.available_modules
    
    def _use_fallback_modules(self):
        """Use fallback list of common HR modules"""
        self.available_modules = [
            {"name": "employees", "display_name": "Employee Master", "type": "core"},
            {"name": "candidates", "display_name": "Recruitment", "type": "core"},
            {"name": "performance", "display_name": "Performance Management", "type": "extended"},
            {"name": "compensation", "display_name": "Compensation & Benefits", "type": "extended"},
            {"name": "leave", "display_name": "Leave Management", "type": "core"},
            {"name": "attendance", "display_name": "Attendance", "type": "core"},
            {"name": "training", "display_name": "Learning & Development", "type": "extended"},
            {"name": "organization", "display_name": "Organization Structure", "type": "core"}
        ]
        logger.info(f"Using {len(self.available_modules)} fallback modules")
    
    async def get_module_schema(self, module_name: str) -> List[TableColumn]:
        """Get schema/fields for a specific module with error handling"""
        try:
            schema_url = f"{self.base_url}/api/{self.api_version}/metadata/modules/{module_name}/schema"
            
            response = self.session.get(
                schema_url, 
                headers=self.headers, 
                timeout=self.request_timeout
            )
            
            if response.status_code != 200:
                logger.warning(f"Failed to fetch schema for {module_name} (status {response.status_code})")
                return []
            
            schema_data = response.json()
            fields = schema_data.get("fields", [])
            
            if not fields:
                logger.warning(f"No fields found in schema for module {module_name}")
                return []
            
            columns = []
            for field in fields:
                try:
                    columns.append(TableColumn(
                        column_name=field.get("name", "unknown"),
                        data_type=field.get("type", "string"),
                        index=field.get("indexed", False),
                        primary_key=field.get("is_primary", False)
                    ))
                except Exception as col_error:
                    logger.warning(f"Failed to parse field {field.get('name')}: {col_error}")
                    continue
            
            logger.info(f"Retrieved {len(columns)} columns for module {module_name}")
            return columns
            
        except Exception as e:
            logger.error(f"Failed to fetch schema for module {module_name}: {str(e)}", exc_info=True)
            return []
    
    async def get_module_row_count(self, module_name: str) -> int:
        """Get total row count for a module with error handling"""
        try:
            count_url = f"{self.base_url}/api/{self.api_version}/data/{module_name}/count"
            
            response = self.session.get(
                count_url, 
                headers=self.headers, 
                timeout=self.request_timeout
            )
            
            if response.status_code == 200:
                count_data = response.json()
                return count_data.get("count", 0)
            else:
                logger.warning(f"Failed to get row count for {module_name} (status {response.status_code})")
                return 0
            
        except Exception as e:
            logger.warning(f"Failed to get row count for {module_name}: {str(e)}")
            return 0
    
    async def get_module_access_controls(self, module_name: str) -> List[Dict]:
        """Fetch access controls for a specific module"""
        try:
            access_url = f"{self.base_url}/api/{self.api_version}/security/modules/{module_name}/permissions"
            
            response = self.session.get(
                access_url, 
                headers=self.headers, 
                timeout=self.request_timeout
            )
            
            if response.status_code != 200:
                logger.warning(f"Failed to fetch access controls for {module_name} (status {response.status_code})")
                return []
            
            permissions_data = response.json()
            permissions = permissions_data.get("permissions", [])
            
            access_controls = []
            for perm in permissions:
                try:
                    role_type = "role" if perm.get("type") == "role" else "user"
                    access_controls.append({
                        "user_or_role": perm.get("principal", "unknown"),
                        "role": role_type,
                        "access": self._map_permissions_to_access(perm.get("actions", [])),
                    })
                except Exception as perm_error:
                    logger.warning(f"Failed to parse permission: {perm_error}")
                    continue
            
            return access_controls
            
        except Exception as e:
            logger.warning(f"Failed to fetch access controls for {module_name}: {str(e)}")
            return []
    
    def _map_permissions_to_access(self, actions: List[str]) -> str:
        """Map API permissions to access levels"""
        if not actions:
            return "none"
        
        actions_lower = [a.lower() for a in actions if isinstance(a, str)]
        
        if "delete" in actions_lower or "admin" in actions_lower or "full" in actions_lower:
            return "full"
        elif "write" in actions_lower or "update" in actions_lower or "create" in actions_lower:
            return "write"
        elif "read" in actions_lower or "view" in actions_lower:
            return "read"
        else:
            return "none"
    
    async def get_encryption_status(self) -> bool:
        """Check if data is encrypted (cloud services typically encrypted by default)"""
        try:
            security_url = f"{self.base_url}/api/{self.api_version}/security/config"
            
            response = self.session.get(
                security_url, 
                headers=self.headers, 
                timeout=10
            )
            
            if response.status_code == 200:
                config = response.json()
                return config.get("encryption_enabled", True)
            
            return True  # Assume encrypted for cloud services
            
        except Exception:
            return True  # Default to encrypted
    
    async def infra_scan(self):
        """Perform infrastructure scan with comprehensive error handling"""
        try:
            logger.info(f"Starting infra scan for TCS Chroma: {self.service_name}")
            await self.get_service_details()
            await self.connect()
            
            # Discover available modules
            modules = await self.get_available_modules()
            
            if not modules:
                logger.warning("No modules discovered")
                return {
                    "modules": [],
                    "region": self.region,
                    "metadata": [],
                    "warning": "No modules discovered"
                }
            
            # Filter modules if specific ones are configured
            if self.modules:
                modules = [m for m in modules if m.get("name") in self.modules]
                logger.info(f"Filtered to {len(modules)} configured modules")
            
            aggregated_metadata = []
            encryption_status = await self.get_encryption_status()
            
            for module in modules:
                module_name = module.get("name")
                display_name = module.get("display_name", module_name)
                
                try:
                    logger.info(f"Scanning module: {module_name}")
                    
                    # Get row count
                    row_count = await self.get_module_row_count(module_name)
                    
                    # Estimate size
                    estimated_size = row_count * 1024  # ~1KB per row estimate
                    
                    # Get access controls
                    access_permissions = await self.get_module_access_controls(module_name)
                    
                    # Prepare access controls for loading
                    access_objs = []
                    for perm in access_permissions:
                        try:
                            access_objs.append({**perm, "asset_name": module_name})
                        except Exception as access_error:
                            logger.warning(f"Failed to prepare access control: {access_error}")
                    
                    # Load access controls
                    if access_objs:
                        try:
                            load_access_controls(access_objs)
                            logger.info(f"Loaded {len(access_objs)} access controls for {module_name}")
                        except Exception as load_error:
                            logger.error(f"Failed to load access controls for {module_name}: {load_error}")
                    
                    # Create asset record
                    try:
                        asset = [AssetsDetails(
                            asset_name=module_name,
                            service_provider=self.service_provider,
                            type="api_entity",
                            category="structured",
                            location=self.region,
                            owner=self.service_owner,
                            security=encryption_status,
                            size=estimated_size,
                            count=row_count,
                            access_category="restricted" if access_objs else "none",
                            service_name=str(self.service_name),
                            steward=str(self.service_steward),
                        )]
                        load_asset_details(asset)
                        logger.info(f"Loaded asset details for {module_name}")
                    except Exception as asset_error:
                        logger.error(f"Failed to load asset details for {module_name}: {asset_error}")
                    
                    # Collect metadata
                    aggregated_metadata.append({
                        "module_name": module_name,
                        "display_name": display_name,
                        "row_count": row_count,
                        "estimated_size_bytes": estimated_size,
                        "region": self.region,
                        "has_data": row_count > 0
                    })
                    
                    logger.info(f"Successfully scanned module: {module_name} ({row_count} rows)")
                    
                except Exception as e:
                    logger.error(f"Failed to scan module {module_name}: {str(e)}", exc_info=True)
                    # Continue with next module
                    continue
            
            logger.info(f"Infra scan completed for {len(aggregated_metadata)} modules")
            return {
                "modules": [m["module_name"] for m in aggregated_metadata],
                "region": self.region,
                "metadata": aggregated_metadata,
            }
            
        except Exception as e:
            logger.error(f"Infrastructure scan failed: {str(e)}", exc_info=True)
            return {"error": str(e)}
    
    async def fetch_module_data(self, module_name: str, limit: int = None) -> List[Dict]:
        """Fetch data from a specific module with comprehensive error handling"""
        try:
            limit = limit or self.sample_count
            data_url = f"{self.base_url}/api/{self.api_version}/data/{module_name}"
            
            params = {
                "limit": limit,
                "offset": 0
            }
            
            response = self.session.get(
                data_url, 
                headers=self.headers, 
                params=params, 
                timeout=self.request_timeout
            )
            
            if response.status_code != 200:
                logger.warning(f"Failed to fetch data from {module_name} (status {response.status_code})")
                return []
            
            data = response.json()
            records = data.get("data", []) or data.get("records", []) or data.get("items", [])
            
            if not records:
                logger.info(f"No data returned for module {module_name}")
                return []
            
            logger.info(f"Fetched {len(records)} records from {module_name}")
            return records
            
        except Exception as e:
            logger.error(f"Failed to fetch data from {module_name}: {str(e)}", exc_info=True)
            return []
    
    async def deep_scan(self) -> AsyncGenerator[TableMetadata, None]:
        """Perform deep scan with comprehensive error handling"""
        try:
            await self.get_service_details()
            await self.connect()
            await self.infra_scan()
            
            modules = await self.get_available_modules()
            
            if not modules:
                logger.warning("No modules available for deep scan")
                return
            
            # Filter modules if configured
            if self.modules:
                modules = [m for m in modules if m.get("name") in self.modules]
            
            logger.info(f"Starting deep scan for {len(modules)} modules")
            
            for module in modules:
                module_name = module.get("name")
                
                try:
                    logger.info(f"Deep scanning module: {module_name}")
                    table_metadata = await self.scan_module(module_name)
                    
                    if table_metadata:
                        yield table_metadata
                    else:
                        logger.info(f"No metadata generated for module {module_name}")
                        
                except Exception as e:
                    logger.error(
                        f"Deep scan failed for module: {module_name}, Service: {self.service_name}",
                        exc_info=True
                    )
                    # Continue with next module
                    continue
                    
        except Exception as e:
            logger.error(f"Deep scan failed. Service: {self.service_name}", exc_info=True)
        finally:
            self.close_connection()
    
    async def scan_module(self, module_name: str) -> Optional[TableMetadata]:
        """Scan a specific module with comprehensive error handling"""
        try:
            logger.info(f"Scanning module: {module_name}")
            
            # Get schema
            columns = await self.get_module_schema(module_name)
            
            if not columns:
                logger.warning(f"No columns found for module {module_name}, skipping")
                return None
            
            # Get row count
            row_count = await self.get_module_row_count(module_name)
            
            # Construct URI
            table_uri = f"tcs_chroma://{self.tenant_id or 'default'}/{module_name}"
            logger.info(f"Constructed table_uri for {module_name}: {table_uri}")
            
            # Check if already processed (skip rule)
            try:
                if self.db.skip_table(table_uri):
                    logger.info(f"Skipping module {module_name} - Already processed")
                    return None
            except Exception as skip_check_error:
                logger.warning(f"Could not check skip status for {table_uri}: {str(skip_check_error)}")
            
            # Fetch sample data
            sample_records = await self.fetch_module_data(module_name, self.sample_count)
            
            if not sample_records:
                logger.warning(f"No data fetched for module {module_name}, creating metadata without sample data")
                # Create empty CSV for consistency
                sample_records = []
            
            # Create CSV file
            temp_folder = self.local_data_dir
            create_folder(temp_folder)
            output_file = os.path.join(temp_folder, f"{module_name}_{str(uuid4())}.csv")
            
            # Write to CSV with error handling
            try:
                with open(output_file, mode="w", newline="", encoding="utf-8") as file:
                    if sample_records:
                        # Get all unique keys from records
                        all_keys = set()
                        for record in sample_records:
                            if isinstance(record, dict):
                                all_keys.update(record.keys())
                        
                        if all_keys:
                            fieldnames = sorted(list(all_keys))
                            writer = csv.DictWriter(file, fieldnames=fieldnames, extrasaction='ignore')
                            writer.writeheader()
                            
                            # Write rows with error handling for each record
                            for record in sample_records:
                                try:
                                    if isinstance(record, dict):
                                        # Clean record - handle non-serializable values
                                        cleaned_record = {}
                                        for key, value in record.items():
                                            try:
                                                if value is None:
                                                    cleaned_record[key] = ""
                                                elif isinstance(value, (dict, list)):
                                                    cleaned_record[key] = json.dumps(value)
                                                else:
                                                    cleaned_record[key] = str(value)
                                            except Exception as val_error:
                                                logger.warning(f"Failed to serialize value for key {key}: {val_error}")
                                                cleaned_record[key] = ""
                                        
                                        writer.writerow(cleaned_record)
                                except Exception as row_error:
                                    logger.warning(f"Failed to write row: {row_error}")
                                    continue
                        else:
                            # Write header from schema if no data
                            writer = csv.writer(file)
                            writer.writerow([col.column_name for col in columns])
                    else:
                        # Write header only
                        writer = csv.writer(file)
                        writer.writerow([col.column_name for col in columns])
                
                logger.info(f"Created CSV file for {module_name}: {output_file}")
                
            except Exception as csv_error:
                logger.error(f"Failed to create CSV for {module_name}: {csv_error}", exc_info=True)
                # Create minimal CSV
                with open(output_file, mode="w", newline="", encoding="utf-8") as file:
                    writer = csv.writer(file)
                    writer.writerow([col.column_name for col in columns])
            
            # Get file size
            file_size = 0
            try:
                file_size = os.path.getsize(output_file)
            except Exception as size_error:
                logger.warning(f"Failed to get file size: {size_error}")
            
            # Build details
            details = {
                "base_url": self.base_url,
                "api_version": self.api_version,
                "region": self.region,
                "tenant_id": self.tenant_id or "default",
                "module_type": "hrms",
                "has_sample_data": len(sample_records) > 0,
                "sample_record_count": len(sample_records)
            }
            
            # Create TableMetadata
            table_metadata = TableMetadata(
                service_name=self.service_name,
                service_type=self.service_type,
                service_provider=self.service_provider,
                sub_service=self.sub_service,
                db_type="api",
                db_name=self.tenant_id or "default",
                table_name=module_name,
                table_size=file_size,
                no_of_rows=row_count,
                table_uri=table_uri,
                columns=columns,
                local_filepath=output_file,
                details=details
            )
            
            logger.info(f"Successfully scanned module {module_name}")
            return table_metadata
            
        except Exception as e:
            logger.error(f"Module scan failed for {module_name}: {str(e)}", exc_info=True)
            return None
    
    async def find_primary_keys(self, module_name: str) -> List[str]:
        """Find primary key fields for a module with error handling"""
        try:
            schema = await self.get_module_schema(module_name)
            pk_fields = [col.column_name for col in schema if col.primary_key]
            
            if not pk_fields:
                logger.info(f"No primary keys found for module {module_name}")
                return []
            
            logger.info(f"Found {len(pk_fields)} primary key(s) for {module_name}: {pk_fields}")
            return pk_fields
            
        except Exception as e:
            logger.error(f"Failed to fetch primary keys for {module_name}: {str(e)}", exc_info=True)
            return []
    
    async def find_related_modules(self, module_name: str, pk_field: str) -> List[Dict[str, str]]:
        """Find modules that reference this module with error handling"""
        try:
            relationships_url = f"{self.base_url}/api/{self.api_version}/metadata/modules/{module_name}/relationships"
            
            response = self.session.get(
                relationships_url, 
                headers=self.headers, 
                timeout=self.request_timeout
            )
            
            if response.status_code != 200:
                logger.warning(f"Failed to fetch relationships for {module_name} (status {response.status_code})")
                return []
            
            relationships = response.json().get("relationships", [])
            related = []
            
            for rel in relationships:
                try:
                    if rel.get("source_field") == pk_field or rel.get("from_field") == pk_field:
                        related.append({
                            "module_name": rel.get("target_module") or rel.get("to_module"),
                            "field_name": rel.get("target_field") or rel.get("to_field"),
                        })
                except Exception as rel_error:
                    logger.warning(f"Failed to parse relationship: {rel_error}")
                    continue
            
            logger.info(f"Found {len(related)} related modules for {module_name}.{pk_field}")
            return related
            
        except Exception as e:
            logger.error(f"Failed to fetch relationships for {module_name}: {str(e)}", exc_info=True)
            return []
    
    async def find_matching_records(self, module_name: str, field_name: str, values: List) -> List[Dict]:
        """Find records in a module where field matches values with error handling"""
        try:
            if not values:
                return []
            
            # Clean and validate values
            clean_values = []
            for v in values:
                if v is not None and str(v).strip():
                    clean_values.append(v)
            
            if not clean_values:
                logger.info(f"No valid values to search in {module_name}.{field_name}")
                return []
            
            data_url = f"{self.base_url}/api/{self.api_version}/data/{module_name}/search"
            
            # Build query filter
            query_filter = {
                field_name: {"$in": clean_values}
            }
            
            payload = {
                "filter": query_filter,
                "limit": 20
            }
            
            response = self.session.post(
                data_url, 
                headers=self.headers, 
                json=payload, 
                timeout=self.request_timeout
            )
            
            if response.status_code != 200:
                logger.warning(f"Search failed for {module_name} (status {response.status_code})")
                return []
            
            data = response.json()
            records = data.get("data", []) or data.get("records", []) or data.get("items", [])
            
            logger.info(f"Found {len(records)} matching records in {module_name}.{field_name}")
            return records
            
        except Exception as e:
            logger.error(f"Failed to search in {module_name}: {str(e)}", exc_info=True)
            return []
    
    async def fetch_related_data_recursively(self, initial_matches: List[Dict]) -> List[Dict]:
        """Traverse relationships to fetch related records with error handling"""
        results = []
        visited_modules = set()
        
        try:
            queue = initial_matches.copy()
            max_depth = 5  # Prevent infinite loops
            current_depth = 0
            
            while queue and current_depth < max_depth:
                current_depth += 1
                next_queue = []
                
                logger.info(f"Processing depth {current_depth} with {len(queue)} modules")
                
                for match in queue:
                    module_name = match.get("module_name")
                    field_name = match.get("field_name")
                    values = match.get("values", [])
                    
                    if not module_name or module_name in visited_modules:
                        continue
                    
                    try:
                        matching_records = await self.find_matching_records(module_name, field_name, values)
                        
                        if not matching_records:
                            visited_modules.add(module_name)
                            continue
                        
                        results.append({
                            "service_name": self.service_name,
                            "service_provider": self.service_provider,
                            "sub_service": self.sub_service,
                            "db_name": self.tenant_id or "default",
                            "module_name": module_name,
                            "field_name": field_name,
                            "matching_records": matching_records,
                        })
                        
                        visited_modules.add(module_name)
                        
                        # Find related modules
                        pk_fields = await self.find_primary_keys(module_name)
                        
                        for pk_field in pk_fields:
                            # Extract PK values from matching records
                            pk_values = []
                            for record in matching_records:
                                if isinstance(record, dict) and record.get(pk_field):
                                    pk_values.append(record.get(pk_field))
                            
                            pk_values = list(set(pk_values))  # Remove duplicates
                            
                            if pk_values:
                                related_modules = await self.find_related_modules(module_name, pk_field)
                                
                                for rel in related_modules:
                                    rel_module = rel.get("module_name")
                                    rel_field = rel.get("field_name")
                                    
                                    if rel_module and rel_field and rel_module not in visited_modules:
                                        next_queue.append({
                                            "module_name": rel_module,
                                            "field_name": rel_field,
                                            "values": pk_values,
                                        })
                    
                    except Exception as match_error:
                        logger.error(f"Error processing match for {module_name}: {match_error}", exc_info=True)
                        visited_modules.add(module_name)
                        continue
                
                queue = next_queue
            
            logger.info(f"Recursive fetch completed. Found data in {len(results)} modules")
            
        except Exception as e:
            logger.error("Error while fetching related data recursively", exc_info=True)
        
        return results
    
    async def user_scan(self, initial_modules: List[Dict]):
        """Perform user scan to find related data across modules with error handling"""
        try:
            await self.get_service_details()
            await self.connect()
            
            if not initial_modules:
                logger.warning("No initial modules provided for user scan")
                return
            
            logger.info(f"Starting user scan with {len(initial_modules)} initial modules")
            
            # Validate initial_modules structure
            valid_modules = []
            for module in initial_modules:
                if isinstance(module, dict) and module.get("module_name") and module.get("field_name"):
                    valid_modules.append(module)
                else:
                    logger.warning(f"Invalid module structure in initial_modules: {module}")
            
            if not valid_modules:
                logger.error("No valid modules in initial_modules")
                return
            
            results = await self.fetch_related_data_recursively(valid_modules)
            
            if results:
                logger.info(f"User scan found {len(results)} related data results")
                yield results
            else:
                logger.info("No related data found in user scan")
                
        except Exception as e:
            logger.error(f"User scan failed. Service: {self.service_name}", exc_info=True)
        finally:
            self.close_connection()
    
    def close_connection(self):
        """Close the session safely"""
        try:
            if self.session:
                self.session.close()
                logger.info("TCS Chroma session closed")
        except Exception as e:
            logger.warning(f"Error closing session: {str(e)}")