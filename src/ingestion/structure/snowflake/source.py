from uuid import uuid4
import csv
import os
from typing import AsyncGenerator, List, Dict
from cryptography.hazmat.primitives import serialization

import snowflake.connector

from src.common.constants import ServiceTypes, DBTypes, ServiceProviders
from src.utils.logger import get_ingestion_logger
from src.modules.repos import DatabaseManager
from src.ingestion.base_source import StructuredSource
from src.ingestion.data_class import TableMetadata, TableColumn
from src.common.config import TEMP_DATA_DIR
from src.utils.helpers import create_folder
from src.utils.exceptions import ServerException
from src.utils.loaders import load_access_controls, load_asset_details
from src.ingestion.data_class import AssetsDetails
from collections import defaultdict

logger = get_ingestion_logger()


class SnowflakeSource(StructuredSource):
    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.SNOWFLAKE.value, service_name)
        self.db = DatabaseManager()
        self.connection = None
        self.cursor = None
        self.service_provider = ServiceProviders.LOCAL
        self.sub_service = DBTypes.SNOWFLAKE.value
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"
        
        # Authentication attributes
        self.user = None
        self.account = None
        self.warehouse = None
        self.role = None  # Added role support
        self.databases = None
        self.schemas = None  # Added schema filtering support
        self.private_key_path = None
        self.private_key = None
        self.sample_count = 10
        self.data_steward = None
        
        # Statistics tracking
        self.scan_stats = {
            'total_databases': 0,
            'scanned_databases': 0,
            'total_schemas': 0,
            'scanned_schemas': 0,
            'total_tables': 0,
            'scanned_tables': 0,
            'skipped_tables': 0,
            'total_columns': 0,
            'total_rows': 0,
            'errors': []
        }

    async def get_service_details(self):
        """Gets service details from the database and loads private key."""
        try:
            self.service = await self.db.get_service_by_service_name(self.service_name)
            credentials = self.service.get("credentials", {})
            
            self.user = credentials.get("user")
            self.account = credentials.get("account")
            self.warehouse = credentials.get("warehouse")
            self.role = credentials.get("role")  # Optional role
            self.databases = credentials.get("databases", [])  # Can be a list of DBs
            self.schemas = credentials.get("schemas", [])  # Optional schema filtering
            self.private_key_path = credentials.get("private_key_path")
            self.sample_count = credentials.get("sample_count", 10)
            self.data_steward = self.service.get("data_steward")
            
            # Required fields validation
            required_fields = [self.user, self.account, self.warehouse, self.private_key_path]
            if not all(required_fields):
                raise ServerException("Missing required Snowflake credentials: user, account, warehouse, private_key_path.")

            # Load and parse the private key
            await self._load_private_key()

            logger.info(f"Service details loaded for {self.service_name}")
            logger.info(f"Account: {self.account}")
            logger.info(f"Warehouse: {self.warehouse}")
            logger.info(f"User: {self.user}")
            logger.info(f"Role: {self.role if self.role else 'Default role will be used'}")
            logger.info(f"Private key loaded from: {self.private_key_path}")
            logger.info(f"Configured databases: {self.databases if self.databases else 'All accessible databases'}")
            logger.info(f"Configured schemas: {self.schemas if self.schemas else 'All accessible schemas'}")
            logger.info(f"Sample count: {self.sample_count}")
            
            return self.service
            
        except Exception as e:
            logger.error(f"Failed to get service details: {str(e)}")
            raise

    async def _load_private_key(self):
        """Load and parse the private key from file."""
        try:
            if not os.path.exists(self.private_key_path):
                raise ServerException(f"Private key file not found: {self.private_key_path}")
            
            with open(self.private_key_path, 'rb') as key_file:
                private_key_data = key_file.read()
            
            # Parse the private key
            self.private_key = serialization.load_pem_private_key(
                private_key_data,
                password=None,  # Assuming no passphrase as per documentation
            )
            
            logger.info(f"Private key successfully loaded and parsed")
            
        except Exception as e:
            error_msg = f"Failed to load private key from {self.private_key_path}: {str(e)}"
            logger.error(error_msg)
            raise ServerException(error_msg, excep=e)

    async def connect(self, database=None):
        """Creates Snowflake connection using key-pair authentication with role support."""
        try:
            # Close existing connection if open
            if self.connection and not self.connection.is_closed():
                self.connection.close()
                
            connection_params = {
                'user': self.user,
                'account': self.account,
                'warehouse': self.warehouse,
                'private_key': self.private_key,  # Use private key instead of password
                'session_parameters': {
                    'QUERY_TAG': 'Unstructured-Ingestion-Scan'
                }
            }
            
            # Add role if specified
            if self.role:
                connection_params['role'] = self.role
                logger.info(f"Using role: {self.role}")
            
            # Add database to connection if specified
            if database:
                connection_params['database'] = database
                
            self.connection = snowflake.connector.connect(**connection_params)
            self.cursor = self.connection.cursor(snowflake.connector.DictCursor)
            
            db_name = database or 'No specific database'
            role_name = self.role or 'Default role'
            logger.info(f"Connected to Snowflake account {self.account} with warehouse {self.warehouse}, database: {db_name}, role: {role_name}")
            
            # Verify connection with a simple query
            self.cursor.execute("SELECT CURRENT_USER(), CURRENT_ROLE(), CURRENT_WAREHOUSE(), CURRENT_DATABASE()")
            result = self.cursor.fetchone()
            logger.info(f"Connection verified - User: {result.get('CURRENT_USER()')}, Role: {result.get('CURRENT_ROLE()')}, Warehouse: {result.get('CURRENT_WAREHOUSE()')}, Database: {result.get('CURRENT_DATABASE()')}")
            
            # Set role explicitly if provided (in case it wasn't set during connection)
            if self.role:
                try:
                    self.cursor.execute(f"USE ROLE {self.role}")
                    logger.info(f"Role set to: {self.role}")
                except Exception as role_error:
                    logger.warning(f"Could not set role {self.role}: {role_error}")
            
        except Exception as e:
            error_msg = f"Snowflake connection failed: {str(e)}"
            logger.error(error_msg)
            raise ServerException(error_msg, excep=e)

    async def test_connection(self):
        """Test the database connection."""
        try:
            logger.info("Testing Snowflake connection...")
            await self.get_service_details()
            await self.connect()
            
            # Test basic connectivity
            self.cursor.execute("SELECT 1 as test_result;")
            test_result = self.cursor.fetchone()
            
            if test_result and test_result.get('TEST_RESULT') == 1:
                logger.info("Snowflake connection test successful")
                
                # Test role permissions
                try:
                    self.cursor.execute("SELECT CURRENT_ROLE(), CURRENT_USER(), CURRENT_WAREHOUSE()")
                    role_info = self.cursor.fetchone()
                    logger.info(f"Connection established with Role: {role_info.get('CURRENT_ROLE()')}, User: {role_info.get('CURRENT_USER()')}, Warehouse: {role_info.get('CURRENT_WAREHOUSE()')}")
                except Exception as role_test_error:
                    logger.warning(f"Role verification failed: {role_test_error}")
                
                return True, "Connection successful"
            else:
                logger.error("Connection test failed - no result returned")
                return False, "Connection failed - no result returned"
                
        except Exception as e:
            error_msg = f"Connection test failed: {str(e)}"
            logger.error(f"{error_msg}")
            return False, error_msg
        finally:
            self.close_connection()

    async def get_databases(self) -> List[str]:
        """Get all accessible databases if not specified in credentials."""
        try:
            if self.databases:
                logger.info(f"Using specified databases: {self.databases}")
                self.scan_stats['total_databases'] = len(self.databases)
                return self.databases
            
            logger.info("Fetching all accessible databases from Snowflake...")
            self.cursor.execute("SHOW DATABASES;")
            all_dbs = [row['name'] for row in self.cursor.fetchall()]
            
            # Filter out system databases
            filtered_dbs = [db for db in all_dbs if not db.upper().startswith('SNOWFLAKE')]
            
            self.scan_stats['total_databases'] = len(filtered_dbs)
            logger.info(f"Found {len(filtered_dbs)} accessible databases: {filtered_dbs}")
            
            return filtered_dbs
            
        except Exception as e:
            error_msg = f"Failed to get databases: {str(e)}"
            logger.error(f"{error_msg}")
            self.scan_stats['errors'].append(error_msg)
            raise ServerException(error_msg, excep=e)

    async def get_schemas_in_db(self, db_name: str) -> List[str]:
        """Get all schemas in a specific database with optional filtering."""
        try:
            logger.info(f"Fetching schemas in database: {db_name}")
            
            # If specific schemas are configured, filter by database
            if self.schemas:
                # Filter schemas that might be database-specific
                db_specific_schemas = []
                for schema in self.schemas:
                    if '.' in schema:
                        # Format: DATABASE.SCHEMA
                        schema_db, schema_name = schema.split('.', 1)
                        if schema_db.upper() == db_name.upper():
                            db_specific_schemas.append(schema_name)
                    else:
                        # Just schema name, apply to all databases
                        db_specific_schemas.append(schema)
                
                if db_specific_schemas:
                    logger.info(f"Using configured schemas for {db_name}: {db_specific_schemas}")
                    self.scan_stats['total_schemas'] += len(db_specific_schemas)
                    return db_specific_schemas
            
            # Fetch all schemas from database
            self.cursor.execute(f'SHOW SCHEMAS IN DATABASE "{db_name}";')
            
            all_schemas = [row['name'] for row in self.cursor.fetchall()]
            schemas = [schema for schema in all_schemas if schema.upper() != 'INFORMATION_SCHEMA']
            
            # Apply schema filtering if configured
            if self.schemas:
                # Convert configured schemas to uppercase for comparison
                configured_schemas_upper = [s.upper() for s in self.schemas]
                schemas = [schema for schema in schemas if schema.upper() in configured_schemas_upper]
                logger.info(f"Filtered schemas based on configuration: {schemas}")
            
            logger.info(f"Scannable schemas in database {db_name} ({len(schemas)} total): {schemas}")
            self.scan_stats['total_schemas'] += len(schemas)
            
            return schemas
            
        except Exception as e:
            error_msg = f"Could not get schemas for database {db_name}: {str(e)}"
            logger.error(f"{error_msg}")
            self.scan_stats['errors'].append(error_msg)
            return []

    async def get_tables_in_schema(self, db_name: str, schema_name: str) -> List[str]:
        """Get all tables in a specific schema."""
        try:
            logger.info(f"Fetching tables in schema: {db_name}.{schema_name}")
            
            tables = []
            views = []
            external_tables = []
            dynamic_tables = []
            
            # Get regular tables
            try:
                self.cursor.execute(f'SHOW TABLES IN SCHEMA "{db_name}"."{schema_name}";')
                tables = [row['name'] for row in self.cursor.fetchall()]
            except Exception as table_error:
                logger.warning(f"Could not fetch tables in {db_name}.{schema_name}: {table_error}")
            
            # Get views
            try:
                self.cursor.execute(f'SHOW VIEWS IN SCHEMA "{db_name}"."{schema_name}";')
                views = [row['name'] for row in self.cursor.fetchall()]
            except Exception as view_error:
                logger.warning(f"Could not fetch views in {db_name}.{schema_name}: {view_error}")
            
            # Get external tables
            try:
                self.cursor.execute(f'SHOW EXTERNAL TABLES IN SCHEMA "{db_name}"."{schema_name}";')
                external_tables = [row['name'] for row in self.cursor.fetchall()]
            except Exception as ext_error:
                logger.debug(f"No external tables in {db_name}.{schema_name}: {ext_error}")
            
            # Get dynamic tables
            try:
                self.cursor.execute(f'SHOW DYNAMIC TABLES IN SCHEMA "{db_name}"."{schema_name}";')
                dynamic_tables = [row['name'] for row in self.cursor.fetchall()]
            except Exception as dyn_error:
                logger.debug(f"No dynamic tables in {db_name}.{schema_name}: {dyn_error}")
            
            # Combine all table types
            all_tables = tables + views + external_tables + dynamic_tables
            
            # Log table types found
            if tables:
                logger.info(f"    Regular Tables: {tables}")
            if views:
                logger.info(f"    Views: {views}")
            if external_tables:
                logger.info(f"    External Tables: {external_tables}")
            if dynamic_tables:
                logger.info(f"    Dynamic Tables: {dynamic_tables}")
            
            self.scan_stats['total_tables'] += len(all_tables)
            
            return all_tables
            
        except Exception as e:
            error_msg = f"Could not get tables for schema {db_name}.{schema_name}: {str(e)}"
            logger.error(f"{error_msg}")
            self.scan_stats['errors'].append(error_msg)
            return []

    async def check_table_permissions(self, full_table_name: str) -> bool:
        """Check if we have SELECT permissions on the table."""
        try:
            # Try to describe the table first
            self.cursor.execute(f"DESCRIBE TABLE {full_table_name};")
            self.cursor.fetchall()  # Consume results
            
            # Try to select from the table with LIMIT 0 to check permissions
            self.cursor.execute(f"SELECT * FROM {full_table_name} LIMIT 0;")
            self.cursor.fetchall()  # Consume results
            
            return True
            
        except Exception as e:
            logger.warning(f"No SELECT permission on table {full_table_name}: {str(e)}")
            return False

    async def get_table_row_count(self, full_table_name: str) -> int:
        """Get accurate row count for a table with fallback methods."""
        try:
            # Direct COUNT - most accurate but can be slow
            self.cursor.execute(f"SELECT COUNT(*) AS row_count FROM {full_table_name};")
            result = self.cursor.fetchone()
            
            if isinstance(result, dict):
                row_count = result.get('ROW_COUNT') or result.get('row_count') or list(result.values())[0]
            else:
                row_count = result[0] if result else 0
                
            return int(row_count) if row_count is not None else 0
            
        except Exception as e:
            logger.warning(f"Could not get row count for {full_table_name}: {str(e)}")
            return 0

    async def get_encryption_status(self):
        # Snowflake always encrypts data at rest and uses SSL for transit
        return True

    async def get_database_access_controls(self, db_name: str):
        """Fetch real user/role grants on database and map to simplified access levels"""
        access_controls = []
        try:
            self.cursor.execute(f'SHOW GRANTS ON DATABASE "{db_name}";')
            rows = self.cursor.fetchall()

            privileges_by_grantee = defaultdict(set)

            for row in rows:
                privilege_type = row['privilege'].upper()
                granted_to = row['granted_to'].lower()
                grantee = row['grantee_name']

                # Skip system roles/users
                if grantee.upper() in ['ACCOUNTADMIN', 'ORGADMIN', 'SECURITYADMIN', 'SYSADMIN', 'USERADMIN']:
                    continue

                privileges_by_grantee[grantee].add(privilege_type)

            # Build final result
            for grantee, privileges in privileges_by_grantee.items():
                access_controls.append({
                    "user_or_role": grantee,
                    "role": granted_to,  # 'user' or 'role'
                    "access": self._map_privileges_to_access(privileges),
                })

            return access_controls

        except Exception as e:
            logger.error(f"Failed to fetch Snowflake access controls for {db_name}: {e}")
            return []

    def _map_privileges_to_access(self, privileges: set) -> str:
        read_privs = {"USAGE", "MONITOR", "REFERENCE_USAGE"}
        write_privs = {"CREATE SCHEMA", "MODIFY", "CREATE SHARE", "IMPORTED PRIVILEGES"}
        if "OWNERSHIP" in privileges:
            return "full"
        elif privileges.issuperset(read_privs | write_privs):
            return "full"
        elif privileges & write_privs:
            return "write"
        elif privileges & read_privs:
            return "read"
        else:
            return "none"

    async def infra_scan(self):
        try:
            logger.info(f"Starting infrastructure scan for service: {self.service_name}")
            await self.get_service_details()
            await self.connect()
            
            databases = await self.get_databases()
            region = self.account.split('.')[1] if '.' in self.account else 'us-west-2'

            # Get all database owners
            self.cursor.execute("SHOW DATABASES;")
            all_dbs = self.cursor.fetchall()
            db_owners = {row['name'].upper(): row.get('owner') for row in all_dbs}

            aggregated_metadata = []

            for db in databases:
                asset = []
                # Database owner
                db_owner = db_owners.get(db.upper(), self.user)

                # Database size in bytes
                self.cursor.execute(f'SELECT SUM(BYTES) AS total_bytes FROM "{db}".INFORMATION_SCHEMA.TABLES;')
                result = self.cursor.fetchone()
                size_bytes = result['TOTAL_BYTES'] or 0
                size_mb = round(size_bytes / 1024 / 1024, 2)

                # Table count
                self.cursor.execute(f'SELECT COUNT(*) AS table_count FROM "{db}".INFORMATION_SCHEMA.TABLES;')
                table_count = self.cursor.fetchone()['TABLE_COUNT'] or 0

                # Access controls
                access_permissions = await self.get_database_access_controls(db)
                access = [{**perm, "asset_name": db} for perm in access_permissions]
                
                # Load access controls with error handling
                try:
                    if access:
                        load_access_controls(access)   # Persist to access_controls table
                        logger.info(f"Loaded {len(access)} access controls for database: {db}")
                    else:
                        logger.info(f"No access controls found for database: {db}")
                except Exception as access_error:
                    logger.error(f"Failed to load access controls for database {db}: {str(access_error)}")

                # Encryption status
                encryption_status = await self.get_encryption_status()

                # Create and persist Asset record
                asset.append(AssetsDetails(
                    asset_name=db,
                    service_provider=self.service_provider,
                    type="database",
                    category="structured",
                    location=region,
                    owner=db_owner if db_owner else self.data_steward,
                    security=encryption_status,
                    size=size_bytes,
                    count=table_count,
                    access_category="restricted" if access else "none",
                    service_name=str(self.service_name),
                    steward=str(self.data_steward),
                ))
                
                # Load asset details with error handling
                try:
                    if asset:
                        load_asset_details(asset)   # Persist to asset_details table
                        logger.info(f"Loaded asset details for database: {db}")
                    else:
                        logger.warning(f"No asset details to load for database: {db}")
                except Exception as asset_error:
                    logger.error(f"Failed to load asset details for database {db}: {str(asset_error)}")

                aggregated_metadata.append({
                    "dbname": db,
                    "db_owner": db_owner,
                    "total_size_mb": size_mb,
                    "table_count": table_count,
                    "region": region,
                })

            logger.info(f"Infra scan completed for {len(aggregated_metadata)} databases")

            return {
                "region": region,
                "account": self.account,
                "warehouse": self.warehouse,
                "role": self.role,
                "metadata": aggregated_metadata,
            }
            
        except Exception as e:
            error_msg = f"Infrastructure scan failed: {str(e)}"
            logger.error(f"{error_msg}")
            return {"error": error_msg}
        # finally:
        #     self.close_connection()

    async def deep_scan(self) -> AsyncGenerator[TableMetadata, None]:
        """Perform deep scan to extract table schema and sample data."""
        try:
            logger.info(f"Starting deep scan for service: {self.service_name}")
            await self.get_service_details()
            await self.connect()
            await self.infra_scan()
            databases_to_scan = await self.get_databases()
            logger.info(f"Will scan {len(databases_to_scan)} databases")

            for db_idx, db in enumerate(databases_to_scan, 1):
                try:
                    logger.info(f"[{db_idx}/{len(databases_to_scan)}] Scanning database: {db}")
                    self.scan_stats['scanned_databases'] += 1
                    
                    schemas = await self.get_schemas_in_db(db)
                    
                    if not schemas:
                        logger.warning(f"No accessible schemas found in database {db}")
                        continue
                    
                    for schema_idx, schema in enumerate(schemas, 1):
                        try:
                            logger.info(f"   [{schema_idx}/{len(schemas)}] Scanning schema: {db}.{schema}")
                            self.scan_stats['scanned_schemas'] += 1
                            
                            tables = await self.get_tables_in_schema(db, schema)
                            
                            if not tables:
                                logger.info(f"No tables found in schema {db}.{schema}")
                                continue
                            
                            for table_idx, table in enumerate(tables, 1):
                                try:
                                    table_metadata = await self.scan_table(db, schema, table)
                                    
                                    if table_metadata:
                                        self.scan_stats['scanned_tables'] += 1
                                        self.scan_stats['total_columns'] += len(table_metadata.columns)
                                        self.scan_stats['total_rows'] += table_metadata.no_of_rows
                                        yield table_metadata
                                    else:
                                        self.scan_stats['skipped_tables'] += 1
                                        logger.info(f"    TABLE SKIPPED: {db}.{schema}.{table}")
                                        
                                except Exception as e:
                                    error_msg = f"Table scan failed for {db}.{schema}.{table}: {str(e)}"
                                    logger.error(f"     {error_msg}")
                                    self.scan_stats['errors'].append(error_msg)
                                    self.scan_stats['skipped_tables'] += 1
                                    continue
                                    
                        except Exception as e:
                            error_msg = f"Schema scan failed for {db}.{schema}: {str(e)}"
                            logger.error(f"   {error_msg}")
                            self.scan_stats['errors'].append(error_msg)
                            continue
                            
                except Exception as e:
                    error_msg = f"Database scan failed for {db}: {str(e)}"
                    logger.error(f" {error_msg}")
                    self.scan_stats['errors'].append(error_msg)
                    continue
              
            # Log final statistics
            logger.info("=" * 60)
            logger.info("SCAN SUMMARY:")
            logger.info(f"    Databases Scanned: {self.scan_stats['scanned_databases']}/{self.scan_stats['total_databases']}")
            logger.info(f"    Schemas Scanned: {self.scan_stats['scanned_schemas']}")
            logger.info(f"    Tables Scanned: {self.scan_stats['scanned_tables']}")
            logger.info(f"    Tables Skipped: {self.scan_stats['skipped_tables']}")
            logger.info(f"    Total Columns: {self.scan_stats['total_columns']}")
            logger.info(f"    Total Rows: {self.scan_stats['total_rows']:,}")
            
            if self.scan_stats['errors']:
                logger.warning(f"    Errors Encountered: {len(self.scan_stats['errors'])}")
            logger.info("=" * 60)
                    
        except Exception as e:
            error_msg = f"Deep scan failed for service {self.service_name}: {str(e)}"
            logger.error(f"{error_msg}")
            self.scan_stats['errors'].append(error_msg)
        finally:
            self.close_connection()

    async def scan_table(self, db_name: str, schema_name: str, table_name: str) -> TableMetadata:
        """Scan a single table and return its metadata."""
        full_table_name = f'"{db_name}"."{schema_name}"."{table_name}"'
        
        try:
            # Check permissions first
            if not await self.check_table_permissions(full_table_name):
                logger.warning(f"     PERMISSION DENIED: {full_table_name}")
                return None
                
            # Get table structure
            self.cursor.execute(f"DESCRIBE TABLE {full_table_name};")
            columns_desc = self.cursor.fetchall()
            
            if not columns_desc:
                logger.warning(f"  NO COLUMNS FOUND: {full_table_name}")
                return None
            
            # Get primary keys
            pk_cols = set()
            try:
                self.cursor.execute(f"SHOW PRIMARY KEYS IN TABLE {full_table_name};")
                pk_result = self.cursor.fetchall()
                pk_cols = {row['column_name'] for row in pk_result}
            except Exception as pk_error:
                logger.warning(f"    PRIMARY KEY CHECK FAILED for {full_table_name}: {pk_error}")

            # Build columns metadata
            columns = []
            for idx, row in enumerate(columns_desc, 1):
                col_name = row['name']
                col_type = row['type']
                is_pk = col_name in pk_cols
                
                columns.append(
                    TableColumn(
                        column_name=col_name,
                        data_type=col_type,
                        index=is_pk,
                        primary_key=is_pk
                    )
                )

            # Get row count
            row_count = await self.get_table_row_count(full_table_name)
            
            if row_count == 0:
                logger.info(f"  EMPTY TABLE SKIPPED: {full_table_name} (0 rows)")
                return None

            # Generate table URI
            table_uri = f"snowflake://{self.account}/{db_name}/{schema_name}/{table_name}"
            
            # Check if table should be skipped based on previous processing
            if self.db.skip_table(table_uri):
                logger.info(f"     ALREADY PROCESSED: {table_uri} - skipping")
                return None

            # Sample data extraction
            column_names = [col['name'] for col in columns_desc]
            sample_rows = []
            
            try:
                self.cursor.execute(f"SELECT * FROM {full_table_name} LIMIT {self.sample_count};")
                rows = self.cursor.fetchall()
                sample_rows = [[row.get(col) for col in column_names] for row in rows]
                
                logger.info(f"     SAMPLE DATA EXTRACTED: {len(sample_rows)} rows from {full_table_name}")
                
            except Exception as sample_error:
                logger.error(f"     SAMPLE DATA EXTRACTION FAILED for {full_table_name}: {sample_error}")
                sample_rows = []

            # Create local CSV file
            create_folder(self.local_data_dir)
            output_file = os.path.join(
                self.local_data_dir, 
                f"{db_name}_{schema_name}_{table_name}_{uuid4()}.csv"
            )
            
            with open(output_file, mode="w", newline="", encoding="utf-8") as file:
                writer = csv.writer(file)
                writer.writerow(column_names)
                if sample_rows:
                    writer.writerows(sample_rows)
                    
            table_size = os.path.getsize(output_file)

            # Create table metadata
            table_metadata = TableMetadata(
                service_name=self.service_name,
                service_type=self.service_type,
                service_provider=self.service_provider,
                sub_service=self.sub_service,
                db_type=DBTypes.SNOWFLAKE.value,
                db_name=db_name,
                table_name=table_name,
                table_size=table_size,
                no_of_rows=row_count,
                table_uri=table_uri,
                columns=columns,
                local_filepath=output_file,
                details={
                    "account": self.account,
                    "warehouse": self.warehouse,
                    "role": self.role,
                    "schema": schema_name,
                    "region": self.account.split('.')[1] if '.' in self.account else 'us-west-2',
                    "column_count": len(columns),
                    "sample_row_count": len(sample_rows),
                    "primary_key_columns": list(pk_cols)
                }
            )
            
            logger.info(f"     TABLE SCAN COMPLETED: {full_table_name}")
            return table_metadata
            
        except Exception as e:
            error_msg = f"Table scan failed for {full_table_name}: {str(e)}"
            logger.error(f"     TABLE SCAN FAILED: {error_msg}")
            return None

    def close_connection(self):
        """Close Snowflake connection safely."""
        try:
            if self.cursor:
                self.cursor.close()
                self.cursor = None
                
            if self.connection and not self.connection.is_closed():
                self.connection.close()
                logger.info("Snowflake connection closed")
                
        except Exception as e:
            logger.warning(f"Error closing connection: {str(e)}")
        finally:
            self.connection = None