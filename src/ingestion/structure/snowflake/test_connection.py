import snowflake.connector
import logging
from src.utils.exceptions import ClientException, ServerException

logger = logging.getLogger("Unstructure")

async def test_snowflake_connection(data: dict) -> dict:
    creds = data.get("credentials", {})
    try:
        user = creds.get("user")
        password = creds.get("password")
        account = creds.get("account")
        warehouse = creds.get("warehouse")
        database = creds.get("databases", [None])[0]
        if not all([user, password, account, warehouse]):
            logger.warning("Missing required Snowflake credentials")
            raise ClientException(
                "Missing required credentials: user, password, account, warehouse",
                code=400,
                excep=None
            )
        logger.info(f"Testing Snowflake connection to account: {account}")
        try:
            conn = snowflake.connector.connect(
                user=user,
                password=password,
                account=account,
                warehouse=warehouse,
                database=database,
                session_parameters={'QUERY_TAG': 'Unstructured-Ingestion-Test'}
            )
        except Exception as e:
            logger.warning(f"Snowflake authentication/connection failed: {str(e)}")
            raise ClientException(
                "Invalid Snowflake credentials or connection failed", code=401, excep=e
            )
        try:
            cursor = conn.cursor()
            cursor.execute("SELECT CURRENT_USER(), CURRENT_DATABASE(), CURRENT_ROLE()")
            cursor.fetchone()
            cursor.execute("SHOW TABLES")
            cursor.fetchall()
            logger.info("Snowflake connection test successful and read access verified.")
        except Exception as e:
            logger.warning(f"Snowflake read access failed: {str(e)}")
            conn.close()
            raise ClientException(
                "Snowflake read access failed (check read-only permissions)", code=403, excep=e
            )
        conn.close()
        return {
            "status": True,
            "message": "Snowflake connection successful and read access verified."
        }
    except ClientException:
        raise
    except Exception as e:
        logger.exception("Unexpected error during Snowflake connection test")
        raise ServerException("Snowflake test failed", excep=e) 