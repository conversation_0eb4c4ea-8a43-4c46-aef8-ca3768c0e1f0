from uuid import uuid4
from typing import List
import json
from datetime import datetime
from src.utils.logger import get_ingestion_logger

from src.ingestion.profile_client import Process, StructProfiler
from src.common.config import WAREHOUSE_FOLDER
from src.utils.helpers import remove_file, create_file, create_folder
from src.ingestion.structure.postgres.source import PostgresSource
from src.ingestion.structure.mysql.source import MySQLSource
from src.ingestion.structure.oracle.source import OracleSource
from src.ingestion.structure.mssql.source import MSSQLSource
from src.ingestion.structure.sqlite.source import SQLiteSource
from src.ingestion.base_workflow import StructIngestionFlow


logger = get_ingestion_logger()

"""
Implement Warehouse formatters, helper classes for every source

"""


def save_to_warhouse_folder(data: dict):
    create_folder(WAREHOUSE_FOLDER)
    file = datetime.now().strftime("%Y%m%d%H%M%s") + str(uuid4())
    create_file(f"{WAREHOUSE_FOLDER}/{file}.json", json.dumps(data))


class PostgresWorkflow(StructIngestionFlow):
    def __init__(self, service_type: str, service_name: str, ingestion_id: str):
        super().__init__(service_type, service_name, ingestion_id)

    async def get_source(self):
        """Get source"""
        return PostgresSource(self.service_name)

    async def execute_internal(self) -> None:
        """Workflow-specific logic to execute safely"""
        source = await self.get_source()
        profiler = StructProfiler()

        # self.steps = await self.create_ingestion_pipeline()

        infra_res = await source.infra_scan()
        logger.info(
            f"Resolved region/country for host is: {infra_res.get('region', 'Unknown')}"
        )

        async for record in source.deep_scan():
            sample_filepath = record.get("sample_data_file", "")
            if sample_filepath:
                token = await profiler.run(record)
                remove_file(sample_filepath)

            data = {
                "service_name": self.service_name,
                "service_type": self.service_type,
                "db_type": record.get("db_type"),
                "db_name": record.get("db_name"),
                "table_size": record.get("table_size", 0.0),
                "no_of_rows": record.get("no_of_rows", 0),
                "table_name": record.get("table_name"),
                "columns": record.get("columns", []),
                "table_uri": record.get("table_uri"),
                "piis": [],
                "token": token,
                "scan_type": "table_metadata",
                "details": {**infra_res},
            }
            save_to_warhouse_folder(data)

    async def calculate_success(self) -> float:
        """Get the success % of the internal execution"""
        return 0.0

    async def get_failures(self):
        """Get the failures to flag whether if the workflow succeeded or not"""

    async def execute(self) -> None:
        """
        Main entrypoint:
        1. Start logging timer. It will be closed at `stop`
        2. Execute the workflow
        3. Validate the pipeline status
        4. Update the pipeline status at the end
        """
        try:
            await self.execute_internal()
        except Exception as err:
            logger.error(f"Ingestion failed for the service {self.service_name}, {err}")
        finally:
            await self.stop()

    async def stop(self) -> None:
        """
        Main stopping logic
        """

    async def create_ingestion_pipeline(self) -> List[Process]:
        """Creates Ingestion pipeline"""
        return [StructProfiler()]


class MySQLWorkflow(StructIngestionFlow):
    def __init__(self, service_type: str, service_name: str, ingestion_id: str):
        super().__init__(service_type, service_name, ingestion_id)

    async def get_source(self):
        """Get MySQL source"""
        return MySQLSource(self.service_name)

    async def execute_internal(self) -> None:
        """Workflow-specific logic to execute safely"""
        source = await self.get_source()
        profiler = StructProfiler()

        infra_res = await source.infra_scan()
        logger.info(
            f"Resolved region/country for host {infra_res.get('host', 'Unknown')}: {infra_res.get('region', 'Unknown')}"
        )

        async for record in source.deep_scan():
            sample_filepath = record.get("sample_data_file", "")
            if sample_filepath:
                token = await profiler.run(record)
                remove_file(sample_filepath)

            data = {
                "service_name": self.service_name,
                "service_type": self.service_type,
                "db_type": record.get("db_type"),
                "db_name": record.get("db_name"),
                "table_size": record.get("table_size", 0.0),
                "no_of_rows": record.get("no_of_rows", 0),
                "table_name": record.get("table_name"),
                "columns": record.get("columns", []),
                "table_uri": record.get("table_uri"),
                "piis": [],
                "token": token,
                "scan_type": "table_metadata",
                "details": {**infra_res},
            }
            save_to_warhouse_folder(data)

    async def calculate_success(self) -> float:
        """Get the success % of the internal execution"""
        return 0.0

    async def get_failures(self):
        """Get the failures to flag whether the workflow succeeded or not"""

    async def execute(self) -> None:
        """
        Main entrypoint:
        1. Start logging timer. It will be closed at `stop`
        2. Execute the workflow
        3. Validate the pipeline status
        4. Update the pipeline status at the end
        """
        try:
            await self.execute_internal()
        except Exception as err:
            logger.error(f"Ingestion failed for the service {self.service_name}, {err}")
        finally:
            await self.stop()

    async def stop(self) -> None:
        """
        Main stopping logic
        """

    async def create_ingestion_pipeline(self) -> List[Process]:
        """Creates Ingestion pipeline"""
        return [StructProfiler()]


class MSSQLWorkflow(StructIngestionFlow):
    def __init__(self, service_type: str, service_name: str, ingestion_id: str):
        super().__init__(service_type, service_name, ingestion_id)

    async def get_source(self):
        return MSSQLSource(self.service_name)

    async def execute_internal(self) -> None:
        source = await self.get_source()
        profiler = StructProfiler()

        infra_res = await source.infra_scan()
        logger.info(
            f"Resolved region/country for host {infra_res.get('host', 'Unknown')}: {infra_res.get('region', 'Unknown')}"
        )

        async for record in source.deep_scan():
            sample_filepath = record.get("sample_data_file", "")
            if sample_filepath:
                token = await profiler.run(record)
                remove_file(sample_filepath)

            data = {
                "service_name": self.service_name,
                "service_type": self.service_type,
                "db_type": record.get("db_type"),
                "db_name": record.get("db_name"),
                "table_size": record.get("table_size", 0.0),
                "no_of_rows": record.get("no_of_rows", 0),
                "table_name": record.get("table_name"),
                "columns": record.get("columns", []),
                "table_uri": record.get("table_uri"),
                "piis": [],
                "token": token,
                "scan_type": "table_metadata",
                "details": {**infra_res},
            }
            save_to_warhouse_folder(data)

    async def calculate_success(self) -> float:
        """Get the success % of the internal execution"""
        return 0.0

    async def get_failures(self):
        """Get the failures to flag whether the workflow succeeded or not"""
        pass

    async def execute(self) -> None:
        """
        Main entrypoint:
        1. Start logging timer. It will be closed at `stop`
        2. Execute the workflow
        3. Validate the pipeline status
        4. Update the pipeline status at the end
        """
        try:
            await self.execute_internal()
        except Exception as err:
            logger.error(f"Ingestion failed for the service {self.service_name}, {err}")
        finally:
            await self.stop()

    async def stop(self) -> None:
        """
        Main stopping logic
        """
        pass

    async def create_ingestion_pipeline(self) -> List[Process]:
        """Creates Ingestion pipeline"""
        return [StructProfiler()]


class SQLiteWorkflow(StructIngestionFlow):
    def __init__(self, service_type: str, service_name: str, ingestion_id: str):
        super().__init__(service_type, service_name, ingestion_id)

    async def get_source(self):
        """Get SQLite source"""
        return SQLiteSource(self.service_name)

    async def execute_internal(self) -> None:
        """Workflow-specific logic to execute safely"""
        source = await self.get_source()
        profiler = StructProfiler()

        infra_res = await source.infra_scan()
        logger.info(
            f"Resolved region/country for host {infra_res.get('host', 'Unknown')}: {infra_res.get('region', 'Unknown')}"
        )

        async for record in source.deep_scan():
            sample_filepath = record.get("sample_data_file", "")
            if sample_filepath:
                token = await profiler.run(record)
                remove_file(sample_filepath)

            data = {
                "service_name": self.service_name,
                "service_type": self.service_type,
                "db_type": record.get("db_type"),
                "db_name": record.get("db_name"),
                "table_size": record.get("table_size", 0.0),
                "no_of_rows": record.get("no_of_rows", 0),
                "table_name": record.get("table_name"),
                "columns": record.get("columns", []),
                "table_uri": record.get("table_uri"),
                "piis": [],
                "token": token,
                "scan_type": "table_metadata",
                "details": {**infra_res},
            }
            save_to_warhouse_folder(data)

    async def calculate_success(self) -> float:
        """Get the success % of the internal execution"""
        return 0.0

    async def get_failures(self):
        """Get the failures to flag whether the workflow succeeded or not"""
        pass

    async def execute(self) -> None:
        """
        Main entrypoint:
        1. Start logging timer. It will be closed at `stop`
        2. Execute the workflow
        3. Validate the pipeline status
        4. Update the pipeline status at the end
        """
        try:
            await self.execute_internal()
        except Exception as err:
            logger.error(f"Ingestion failed for the service {self.service_name}, {err}")
        finally:
            await self.stop()

    async def stop(self) -> None:
        """
        Main stopping logic
        """
        pass

    async def create_ingestion_pipeline(self) -> List[Process]:
        """Creates Ingestion pipeline"""
        return [StructProfiler()]
