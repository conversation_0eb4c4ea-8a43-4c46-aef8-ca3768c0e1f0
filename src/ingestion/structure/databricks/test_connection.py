import logging
from databricks import sql
from src.utils.exceptions import ClientException, ServerException

logger = logging.getLogger("Unstructure")

async def test_databricks_connection(data: dict) -> dict:
    creds = data.get("credentials", {})
    try:
        host = creds.get("host")
        port = creds.get("port", 443)
        token = creds.get("token")
        http_path = creds.get("http_path")
        connection_timeout = creds.get("connectionTimeout", 10)

        if not all([host, token, http_path]):
            logger.warning("Missing required Databricks credentials")
            raise ClientException("Missing required credential", code=400, excep=None)

        logger.info(f"Testing Databricks connection to {host}:{port}")

        try:
            conn = sql.connect(
                server_hostname=host,
                http_path=http_path,
                access_token=token,
                port=port,
                timeout=connection_timeout,
            )
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            cursor.close()
            conn.close()
        except Exception as e:
            logger.error(f"Databricks connection failed: {e}")
            raise ClientException("Databricks connection failed", code=401, excep=e)

        logger.info("Databricks connection test successful.")
        return {"status": True, "message": "Databricks connection successful"}

    except ClientException:
        raise
    except Exception as e:
        logger.exception("Databricks connection test failed")
        raise ServerException("Databricks test failed", excep=e) 