import csv
import os
from uuid import uuid4
from databricks import sql
from typing import AsyncGenerator

from src.common.constants import ServiceTypes, ServiceProviders, LocalSubservice
from src.utils.logger import get_ingestion_logger
from src.modules.repos import DatabaseManager
from src.ingestion.base_source import StructuredSource
from src.ingestion.data_class import TableMetadata, TableColumn
from src.common.config import TEMP_DATA_DIR
from src.utils.helpers import create_folder
from src.utils.exceptions import ServerException


logger = get_ingestion_logger()


class DatabricksSource(StructuredSource):
    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.DATABRICKS.value, service_name)
        self.db = DatabaseManager()
        self.connection = None
        self.cursor = None
        self.service_provider = ServiceProviders.LOCAL
        self.sub_service = LocalSubservice.DATABRICKS
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"

    async def get_service_details(self):
        """Gets service details"""
        self.service = await self.db.get_service_by_service_name(
            self.service_name
        )
        credentials = self.service.get("credentials", {})
        self.host = credentials.get("host")
        self.port = credentials.get("port", 443)
        self.token = credentials.get("token")
        self.http_path = credentials.get("http_path")
        self.connection_timeout = credentials.get("connectionTimeout", 10)
        self.catalog = credentials.get("catalog")
        self.schema = credentials.get("databaseSchema")
        self.sample_count = credentials.get("sample_count", 10)
        return self.service

    def connect(self):
        """Creates Databricks connection"""
        try:
            self.connection = sql.connect(
                server_hostname=self.host,
                http_path=self.http_path,
                access_token=self.token,
                port=self.port,
                timeout=self.connection_timeout,
            )
            self.cursor = self.connection.cursor()
            logger.info(f"Connected to Databricks at {self.host}")
        except Exception as e:
            raise ServerException(f"Databricks connection failed: {str(e)}", excep=e)

    async def test_connection(self):
        """Test the database connection"""
        try:
            await self.get_service_details()
            self.connect()
            self.cursor.execute("SELECT 1 AS test")
            result = self.cursor.fetchone()
            logger.info(f"Successfully connected to Databricks")
            self.connection.close()
            return True
        except Exception as e:
            logger.error(f"Databricks connection test failed: {str(e)}")
            return False

    async def get_catalogs(self):
        """Get all catalogs in Databricks"""
        try:
            self.cursor.execute("SHOW CATALOGS")
            catalogs = [row[0] for row in self.cursor.fetchall()]
            logger.info(f"Found {len(catalogs)} catalogs")
            return catalogs
        except Exception as e:
            logger.error(f"Failed to get catalogs: {str(e)}")
            return []

    async def get_schemas(self, catalog_name):
        """Get all schemas in a catalog"""
        try:
            self.cursor.execute(f"SHOW SCHEMAS IN {catalog_name}")
            schemas = [row[0] for row in self.cursor.fetchall()]
            logger.info(f"Found {len(schemas)} schemas in catalog {catalog_name}")
            return schemas
        except Exception as e:
            logger.error(f"Failed to get schemas for catalog {catalog_name}: {str(e)}")
            return []

    async def get_tables(self, catalog_name, schema_name):
        """Get all tables in a schema"""
        try:
            self.cursor.execute(f"SHOW TABLES IN {catalog_name}.{schema_name}")
            tables = [row[1] for row in self.cursor.fetchall()]
            logger.info(f"Found {len(tables)} tables in {catalog_name}.{schema_name}")
            return tables
        except Exception as e:
            logger.error(f"Failed to get tables for {catalog_name}.{schema_name}: {str(e)}")
            return []

    async def get_table_columns(self, catalog_name, schema_name, table_name):
        """Get column information for a table"""
        try:
            self.cursor.execute(f"DESCRIBE {catalog_name}.{schema_name}.{table_name}")
            columns = []
            for row in self.cursor.fetchall():
                if row[0] and not row[0].startswith('#'):  # Skip comments
                    column_name = row[0]
                    data_type = row[1] if len(row) > 1 else "unknown"
                    columns.append(
                        TableColumn(
                            column_name=column_name,
                            data_type=data_type,
                            index=False,  # Databricks doesn't expose index info easily
                            primary_key=False  # Databricks doesn't expose PK info easily
                        )
                    )
            return columns
        except Exception as e:
            logger.error(f"Failed to get columns for {catalog_name}.{schema_name}.{table_name}: {str(e)}")
            return []

    async def get_table_row_count(self, catalog_name, schema_name, table_name):
        """Get row count for a table"""
        try:
            self.cursor.execute(f"SELECT COUNT(*) FROM {catalog_name}.{schema_name}.{table_name}")
            result = self.cursor.fetchone()
            return result[0] if result else 0
        except Exception as e:
            logger.error(f"Failed to get row count for {catalog_name}.{schema_name}.{table_name}: {str(e)}")
            return 0

    async def get_table_sample_data(self, catalog_name, schema_name, table_name):
        """Get sample data from a table"""
        try:
            self.cursor.execute(f"SELECT * FROM {catalog_name}.{schema_name}.{table_name} LIMIT {self.sample_count}")
            rows = self.cursor.fetchall()
            return rows
        except Exception as e:
            logger.error(f"Failed to get sample data for {catalog_name}.{schema_name}.{table_name}: {str(e)}")
            return []

    async def table_exists(self, catalog_name, schema_name, table_name):
        """Check if table exists"""
        try:
            self.cursor.execute(f"SHOW TABLES IN {catalog_name}.{schema_name} LIKE '{table_name}'")
            result = self.cursor.fetchall()
            return len(result) > 0
        except Exception as e:
            logger.warning(f"Failed to check existence of table '{catalog_name}.{schema_name}.{table_name}': {e}")
            return False

    async def scan_table(self, catalog_name, schema_name, table_name):
        """Scan a single table and return metadata"""
        try:
            if not await self.table_exists(catalog_name, schema_name, table_name):
                logger.warning(f"Table {catalog_name}.{schema_name}.{table_name} does not exist or is not accessible")
                return None

            # Get table information
            columns = await self.get_table_columns(catalog_name, schema_name, table_name)
            row_count = await self.get_table_row_count(catalog_name, schema_name, table_name)
            sample_data = await self.get_table_sample_data(catalog_name, schema_name, table_name)

            # Create table URI
            table_uri = f"databricks://{catalog_name}/{schema_name}/{table_name}"
            
            # Check if table should be skipped
            if self.db.skip_table(table_uri):
                logger.info(f"Skipping table {table_name} - Already processed")
                return None

            # Create output directory and file
            create_folder(self.local_data_dir)
            output_file = os.path.join(
                self.local_data_dir,
                f"{table_name}_{str(uuid4())}.csv"
            )

            # Write sample data to CSV
            if sample_data and len(sample_data) > 0:
                column_names = [col.column_name for col in columns]
                with open(output_file, mode="w", newline="", encoding="utf-8") as file:
                    writer = csv.writer(file)
                    writer.writerow(column_names)
                    writer.writerows(sample_data)

            return TableMetadata(
                service_name=self.service_name,
                service_type=self.service_type,
                service_provider=self.service_provider,
                sub_service=self.sub_service,
                db_type="databricks",
                db_name=catalog_name,
                table_name=table_name,
                table_size=0,  # Databricks doesn't expose table size easily
                no_of_rows=row_count,
                table_uri=table_uri,
                columns=columns,
                local_filepath=output_file,
                details={
                    "catalog": catalog_name,
                    "schema": schema_name,
                    "host": self.host,
                    "port": self.port
                }
            )

        except Exception as e:
            logger.error(f"Table scan failed for {catalog_name}.{schema_name}.{table_name}: {str(e)}")
            return None

    async def infra_scan(self):
        """Infrastructure scan to get catalog and schema information"""
        try:
            logger.info(f"Starting infra scan for service: {self.service_name}")
            await self.get_service_details()
            self.connect()

            catalogs = await self.get_catalogs()
            if self.catalog:
                catalogs = [c for c in catalogs if c == self.catalog]

            catalog_info = {}
            for catalog in catalogs:
                schemas = await self.get_schemas(catalog)
                if self.schema:
                    schemas = [s for s in schemas if s == self.schema]
                catalog_info[catalog] = schemas

            logger.info(f"Infra scan completed. Found {len(catalogs)} catalogs")
            return {
                "catalogs": catalog_info,
                "host": self.host,
                "port": self.port
            }

        except Exception as e:
            logger.error(f"Infrastructure scan failed: {e}")
            return {"error": str(e)}
        finally:
            if self.connection:
                self.connection.close()

    async def deep_scan(self) -> AsyncGenerator[TableMetadata, None]:
        """Deep scan to get all tables and their metadata"""
        try:
            logger.info(f"Starting deep scan for service: {self.service_name}")
            await self.get_service_details()
            self.connect()

            # Get catalogs to scan
            catalogs = await self.get_catalogs()
            if self.catalog:
                catalogs = [c for c in catalogs if c == self.catalog]

            for catalog in catalogs:
                try:
                    # Get schemas in this catalog
                    schemas = await self.get_schemas(catalog)
                    if self.schema:
                        schemas = [s for s in schemas if s == self.schema]

                    for schema in schemas:
                        try:
                            # Get tables in this schema
                            tables = await self.get_tables(catalog, schema)
                            
                            for table in tables:
                                try:
                                    logger.info(f"Processing table: {catalog}.{schema}.{table}")
                                    table_res = await self.scan_table(catalog, schema, table)
                                    if table_res:
                                        yield table_res
                                except Exception as e:
                                    logger.error(f"Table scan failed. Catalog: {catalog}, Schema: {schema}, Table: {table}. Service: {self.service_name}", exc_info=e)
                                    continue

                        except Exception as e:
                            logger.error(f"Schema scan failed for {catalog}.{schema}. Service: {self.service_name}", exc_info=e)
                            continue

                except Exception as e:
                    logger.error(f"Catalog scan failed for {catalog}. Service: {self.service_name}", exc_info=e)
                    continue

        except Exception as e:
            logger.error(f"Deep scan failed. Service: {self.service_name}", exc_info=e)
        finally:
            if self.connection:
                self.connection.close()

    def close_connection(self):
        """Close the database connection"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
            logger.info("Databricks connection closed") 