import os
import csv
import json
import hashlib
from uuid import uuid4
from datetime import datetime, timedelta
from typing import AsyncGenerator, List, Dict, Any, Optional
from collections import defaultdict
import requests
from requests.auth import HTTPBasicAuth
from urllib.parse import quote
from hikvisionapi import Client as HikvisionClient

from src.common.constants import ServiceTypes, DBTypes, AuthTypes, ServiceProviders
from src.utils.logger import get_ingestion_logger
from src.utils.loaders import load_access_controls, load_asset_details
from src.modules.repos import DatabaseManager
from src.ingestion.base_source import StructuredSource
from src.ingestion.data_class import TableMetadata, TableColumn, AssetsDetails
from src.common.config import TEMP_DATA_DIR
from src.utils.helpers import create_folder
from src.utils.exceptions import ServerException

logger = get_ingestion_logger()


class HikvisionFRSource(StructuredSource):
    """
    Production-ready connector for Hikvision Facial Recognition systems
    
    Supports:
    - ISAPI (Direct device connection)
    - OpenAPI (Hik-Connect cloud)
    
    Data extracted:
    - Face libraries metadata
    - Person/subject records with biometric data
    - Recognition events and access logs
    - Device information and capabilities
    """

    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.HIKVISION_FR.value, service_name)
        self.db = DatabaseManager()
        self.service_provider = ServiceProviders.HIKVISION
        self.sub_service = DBTypes.HIKVISION_FR.value
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"

        # API clients
        self.session = requests.Session()
        self.hik_client = None
        self.access_token = None
        self.token_expiry = None
        
        # Connection state
        self.is_connected = False
        self.connection_retries = 3
        
        # Unique identifiers for file tracking
        self.scan_id = str(uuid4())[:8]
        self.scan_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # ==================== Service Configuration ====================

    async def get_service_details(self):
        """Load and validate service configuration from database"""
        try:
            logger.info(f"[HIKVISION] Loading service details for: {self.service_name}")
            self.service = await self.db.get_service_by_service_name(self.service_name)
            
            if not self.service:
                raise ServerException(f"Service '{self.service_name}' not found in database")
            
            credentials = self.service.get("credentials", {})
            
            if not credentials:
                raise ServerException(f"No credentials found for service '{self.service_name}'")

            # Validate required credentials
            self._validate_required_credentials(credentials)

            # Authentication type
            self.auth_type = credentials.get("auth_type", "isapi").lower()
            logger.info(f"[HIKVISION] Authentication type: {self.auth_type.upper()}")

            # OpenAPI credentials
            self.api_key = credentials.get("api_key")
            self.api_secret = credentials.get("api_secret")
            self.base_url = credentials.get("base_url", "").rstrip('/')

            # ISAPI/Device credentials
            self.device_host = credentials.get("device_host")
            self.device_port = int(credentials.get("device_port", 80))
            self.device_username = credentials.get("device_username", "admin")
            self.device_password = credentials.get("device_password")

            # Common settings
            self.protocol = credentials.get("protocol", "http")
            self.ssl_verify = credentials.get("ssl_verify", True)
            self.region = credentials.get("region", "Unknown")
            self.sample_count = int(credentials.get("sample_count", 100))
            self.timeout = int(credentials.get("timeout", 30))

            # Filters
            self.face_libraries = credentials.get("face_libraries", [])
            self.devices = credentials.get("devices", [])

            # Stewardship
            self.service_steward = self.service.get("data_steward", "admin")
            self.service_owner = self.service.get("data_owner", "system")

            logger.info(f"[HIKVISION] Configuration loaded successfully")
            logger.info(f"[HIKVISION]   - Region: {self.region}")
            logger.info(f"[HIKVISION]   - Sample Count: {self.sample_count}")
            logger.info(f"[HIKVISION]   - Timeout: {self.timeout}s")
            
            if self.face_libraries:
                logger.info(f"[HIKVISION]   - Filtered Libraries: {len(self.face_libraries)}")
            
            return self.service

        except Exception as e:
            logger.error(f"[HIKVISION] Failed to load service details: {e}", exc_info=True)
            raise ServerException(f"Service configuration error: {str(e)}")

    def _validate_required_credentials(self, credentials: dict):
        """Validate that all required credentials are provided based on auth type"""
        auth_type = credentials.get("auth_type", "isapi").lower()
        missing_fields = []

        if auth_type not in ["isapi", "openapi"]:
            raise ServerException(
                f"Invalid auth_type: '{auth_type}'. Must be 'isapi' or 'openapi'"
            )

        if auth_type == "isapi":
            required_fields = {
                "device_host": "Device IP address or hostname",
                "device_username": "Device admin username",
                "device_password": "Device admin password"
            }
            
            for field, description in required_fields.items():
                if not credentials.get(field):
                    missing_fields.append(f"{field} ({description})")

            device_port = credentials.get("device_port")
            if device_port:
                try:
                    port = int(device_port)
                    if not (1 <= port <= 65535):
                        raise ServerException(
                            f"device_port must be between 1 and 65535, got: {port}"
                        )
                except (ValueError, TypeError):
                    raise ServerException(
                        f"device_port must be a valid number, got: {device_port}"
                    )

            device_host = credentials.get("device_host", "")
            if device_host:
                if device_host.startswith(("http://", "https://")):
                    raise ServerException(
                        "device_host should be IP/hostname only (no http:// or https://). "
                        f"Use 'protocol' field instead. Got: {device_host}"
                    )

        elif auth_type == "openapi":
            required_fields = {
                "base_url": "HikConnect API base URL",
                "api_key": "OAuth2 Client ID / AppKey",
                "api_secret": "OAuth2 Client Secret / AppSecret"
            }
            
            for field, description in required_fields.items():
                if not credentials.get(field):
                    missing_fields.append(f"{field} ({description})")

            base_url = credentials.get("base_url", "")
            if base_url and not (base_url.startswith("http://") or base_url.startswith("https://")):
                raise ServerException(
                    f"base_url must start with http:// or https://. Got: {base_url}"
                )

        if missing_fields:
            raise ServerException(
                f"Missing required credentials for {auth_type.upper()}:\n" + 
                "\n".join(f"  - {field}" for field in missing_fields)
            )

        logger.info(f"[HIKVISION] Credential validation passed for {auth_type.upper()}")

    # ==================== Authentication & Connection ====================

    def generate_openapi_token(self) -> str:
        """Generate OAuth2 access token for Hik-Connect OpenAPI"""
        if not self.api_key or not self.api_secret:
            raise ServerException("API Key and Secret required for OpenAPI authentication")

        try:
            logger.info(f"[HIKVISION] Requesting OAuth2 token from: {self.base_url}/oauth/token")
            
            url = f"{self.base_url}/oauth/token"
            payload = {
                "grant_type": "client_credentials",
                "client_id": self.api_key,
                "client_secret": self.api_secret
            }

            response = requests.post(
                url, 
                json=payload, 
                verify=self.ssl_verify, 
                timeout=self.timeout
            )
            
            if response.status_code != 200:
                logger.error(f"[HIKVISION] Token request failed: {response.status_code} - {response.text}")
                raise ServerException(
                    f"OAuth2 token request failed: {response.status_code} - {response.text[:200]}"
                )
            
            data = response.json()
            
            self.access_token = data.get("access_token")
            if not self.access_token:
                raise ServerException(f"No access_token in response: {data}")
            
            expires_in = data.get("expires_in", 3600)
            self.token_expiry = datetime.now() + timedelta(seconds=expires_in - 60)

            logger.info(f"[HIKVISION] OAuth2 token generated (expires in {expires_in}s)")
            return self.access_token

        except requests.exceptions.RequestException as e:
            logger.error(f"[HIKVISION] Network error during token generation: {e}")
            raise ServerException(f"Failed to connect to OpenAPI server: {str(e)}")
        except Exception as e:
            logger.error(f"[HIKVISION] Token generation error: {e}", exc_info=True)
            raise ServerException(f"Failed to generate OpenAPI token: {str(e)}")

    def init_isapi_client(self):
        """Initialize ISAPI client using hikvisionapi library with retry logic"""
        if not self.device_host or not self.device_username or not self.device_password:
            raise ServerException(
                "Device host, username, and password required for ISAPI authentication"
            )

        device_url = f"{self.protocol}://{self.device_host}:{self.device_port}"
        
        for attempt in range(1, self.connection_retries + 1):
            try:
                logger.info(
                    f"[HIKVISION] Initializing ISAPI client (attempt {attempt}/{self.connection_retries})"
                )
                logger.info(f"[HIKVISION]   - Device URL: {device_url}")
                logger.info(f"[HIKVISION]   - Username: {self.device_username}")
                
                self.hik_client = HikvisionClient(
                    host=device_url,
                    login=self.device_username,
                    password=self.device_password,
                    timeout=self.timeout
                )
                
                # Test connection immediately
                device_info = self.hik_client.System.deviceInfo(method='get')
                device_name = device_info.get('DeviceInfo', {}).get('deviceName', 'Unknown')
                logger.info(f"[HIKVISION] ISAPI client connected to: {device_name}")
                return
                    
            except Exception as e:
                logger.warning(
                    f"[HIKVISION] ISAPI initialization attempt {attempt} failed: {e}"
                )
                if attempt == self.connection_retries:
                    raise ServerException(
                        f"Failed to initialize ISAPI client after {self.connection_retries} attempts: {str(e)}"
                    )
                import time
                time.sleep(2 ** attempt)

    async def connect(self):
        """Establish connection and authenticate based on auth type"""
        if self.is_connected:
            logger.info(f"[HIKVISION] Already connected")
            return

        try:
            logger.info(f"[HIKVISION] ========== CONNECTION START ==========")
            logger.info(f"[HIKVISION] Service: {self.service_name}")
            logger.info(f"[HIKVISION] Auth Type: {self.auth_type.upper()}")

            if self.auth_type == "openapi":
                logger.info(f"[HIKVISION] Connecting to OpenAPI: {self.base_url}")
                self.generate_openapi_token()
                
                test_response = self.make_openapi_request("/api/v1/devices", method="GET")
                device_count = len(test_response.get("data", {}).get("devices", []))
                logger.info(
                    f"[HIKVISION] Connected to HikConnect OpenAPI ({device_count} devices accessible)"
                )

            elif self.auth_type == "isapi":
                logger.info(
                    f"[HIKVISION] Connecting to ISAPI device: {self.device_host}:{self.device_port}"
                )
                self.init_isapi_client()
                logger.info(f"[HIKVISION] Connected to HikVision device via ISAPI")
                
            else:
                raise ServerException(f"Invalid auth_type: {self.auth_type}")

            self.is_connected = True
            logger.info(f"[HIKVISION] ========== CONNECTION SUCCESS ==========")

        except Exception as e:
            self.is_connected = False
            logger.error(f"[HIKVISION] Connection failed: {str(e)}", exc_info=True)
            raise ServerException(f"Connection failed: {str(e)}")

    async def test_connection(self) -> dict:
        """Test connection to Hikvision system (BASE CLASS METHOD)"""
        try:
            logger.info(f"[HIKVISION] ========== CONNECTION TEST ==========")
            logger.info(f"[HIKVISION] Service: {self.service_name}")
            
            await self.get_service_details()
            await self.connect()
            
            devices = await self.get_devices()
            device_count = len(devices)
            
            total_libraries = 0
            total_faces = 0
            
            for device in devices[:1]:
                libraries = await self.get_face_libraries(device)
                total_libraries += len(libraries)
                for lib in libraries:
                    total_faces += lib.get("face_count", 0)
            
            result = {
                "status": "success",
                "message": f"Connected successfully via {self.auth_type.upper()}",
                "details": {
                    "auth_type": self.auth_type,
                    "device_count": device_count,
                    "sample_library_count": total_libraries,
                    "sample_face_count": total_faces,
                    "region": self.region
                }
            }
            
            logger.info(f"[HIKVISION] Connection test PASSED")
            logger.info(f"[HIKVISION]   - Devices: {device_count}")
            logger.info(f"[HIKVISION]   - Sample Libraries: {total_libraries}")
            
            return result
            
        except Exception as e:
            logger.error(f"[HIKVISION] Connection test FAILED: {e}", exc_info=True)
            return {
                "status": "failed",
                "message": f"Connection test failed: {str(e)}",
                "details": {
                    "error_type": type(e).__name__,
                    "error_message": str(e)
                }
            }
        finally:
            self.close_connection()

    # ==================== API Request Methods ====================

    def get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for OpenAPI requests with auto-refresh"""
        if self.auth_type == "openapi":
            if not self.access_token or (self.token_expiry and datetime.now() >= self.token_expiry):
                logger.info(f"[HIKVISION] Access token expired, refreshing...")
                self.generate_openapi_token()
            
            return {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }
        return {}

    def make_openapi_request(
        self, 
        endpoint: str, 
        method: str = "GET",
        params: dict = None, 
        data: dict = None,
        retry_count: int = 2
    ) -> Dict:
        """Make OpenAPI request with error handling and retry logic"""
        url = f"{self.base_url}{endpoint}"
        
        for attempt in range(1, retry_count + 1):
            try:
                headers = self.get_auth_headers()
                
                logger.debug(f"[HIKVISION] API Request (attempt {attempt}): {method} {endpoint}")
                
                response = self.session.request(
                    method=method,
                    url=url,
                    headers=headers,
                    params=params,
                    json=data,
                    verify=self.ssl_verify,
                    timeout=self.timeout
                )
                
                if response.status_code == 401 and attempt < retry_count:
                    logger.warning(f"[HIKVISION] 401 Unauthorized, refreshing token...")
                    self.access_token = None
                    continue
                
                response.raise_for_status()
                return response.json()
                
            except requests.exceptions.Timeout:
                logger.warning(f"[HIKVISION] Request timeout (attempt {attempt})")
                if attempt == retry_count:
                    raise ServerException(f"Request timeout after {retry_count} attempts")
            except requests.exceptions.RequestException as e:
                logger.error(f"[HIKVISION] Request failed: {e}")
                if attempt == retry_count:
                    raise ServerException(f"OpenAPI request failed: {str(e)}")
            except Exception as e:
                logger.error(f"[HIKVISION] Unexpected error: {e}")
                raise ServerException(f"Request error: {str(e)}")

        raise ServerException("Request failed after all retry attempts")

    # ==================== Device Discovery ====================

    async def get_devices_openapi(self) -> List[Dict]:
        """Get devices via OpenAPI"""
        try:
            logger.info(f"[HIKVISION] Fetching devices via OpenAPI...")
            data = self.make_openapi_request("/api/v1/devices", method="GET")
            devices = data.get("data", {}).get("devices", [])
            
            # Filter devices if specified
            if self.devices:
                devices = [d for d in devices if d.get("device_id") in self.devices]
            
            logger.info(f"[HIKVISION] Found {len(devices)} device(s) via OpenAPI")
            return devices
        except Exception as e:
            logger.error(f"[HIKVISION] Failed to get devices via OpenAPI: {e}")
            return []

    async def get_device_info_isapi(self) -> Dict:
        """Get device info via ISAPI"""
        try:
            response = self.hik_client.System.deviceInfo(method='get')
            device_info_data = response.get('DeviceInfo', {})

            device_info = {
                "device_id": device_info_data.get("deviceID", f"{self.device_host}_{self.device_port}"),
                "device_name": device_info_data.get("deviceName", f"Device_{self.device_host}"),
                "model": device_info_data.get("model", "unknown"),
                "serial_number": device_info_data.get("serialNumber", "unknown"),
                "firmware_version": device_info_data.get("firmwareVersion", "unknown"),
                "device_type": device_info_data.get("deviceType", "FaceRecognitionTerminal"),
                "host": self.device_host,
                "port": self.device_port
            }

            logger.info(f"[HIKVISION] Device info: {device_info['device_name']} ({device_info['model']})")
            return device_info
        except Exception as e:
            logger.error(f"[HIKVISION] Failed to get device info: {e}")
            return {
                "device_id": f"{self.device_host}_{self.device_port}",
                "device_name": f"Device_{self.device_host}",
                "model": "unknown",
                "host": self.device_host,
                "port": self.device_port
            }

    async def get_devices(self) -> List[Dict]:
        """Get all devices based on auth type"""
        logger.info(f"[HIKVISION] Discovering devices...")
        if self.auth_type == "openapi":
            devices = await self.get_devices_openapi()
        else:
            device_info = await self.get_device_info_isapi()
            devices = [device_info]

        logger.info(f"[HIKVISION] Total devices discovered: {len(devices)}")
        return devices

    # ==================== Face Library Methods ====================

    async def get_face_libraries_isapi(self, device_info: Dict) -> List[Dict]:
        """Get face libraries via ISAPI"""
        libraries = []
        try:
            logger.info(f"[HIKVISION] Fetching face libraries via ISAPI...")
            response = self.hik_client.Intelligent.FDLib(method='get')

            fdlib_list = response.get('FaceLibList', {}) or response.get('FDLibList', {})
            fd_libs = fdlib_list.get('FaceLib', []) or fdlib_list.get('FDLib', [])

            if not isinstance(fd_libs, list):
                fd_libs = [fd_libs] if fd_libs else []

            for fd_lib in fd_libs:
                lib_id = fd_lib.get("id") or fd_lib.get("FDID") or fd_lib.get("fdid") or "0"
                lib_name = fd_lib.get("name", "DefaultLibrary")

                library = {
                    "library_id": str(lib_id),
                    "library_name": lib_name,
                    "library_type": fd_lib.get("type", "whitelist"),
                    "capacity": int(fd_lib.get("capacity", "0")),
                    "face_count": int(fd_lib.get("FDLibSize", {}).get("totalNum", "0")) if isinstance(fd_lib.get("FDLibSize"), dict) else 0,
                    "device_id": device_info.get("device_id")
                }
                libraries.append(library)

            logger.info(f"[HIKVISION] Found {len(libraries)} face libraries")
            return libraries
        except Exception as e:
            logger.warning(f"[HIKVISION] Failed to get face libraries: {e}")
            return [{
                "library_id": "1",
                "library_name": "DefaultLibrary",
                "library_type": "whitelist",
                "capacity": 500,
                "face_count": 0,
                "device_id": device_info.get("device_id")
            }]

    async def get_face_libraries_openapi(self, device_id: str) -> List[Dict]:
        """Get face libraries via OpenAPI"""
        try:
            logger.info(f"[HIKVISION] Fetching face libraries via OpenAPI for device {device_id}...")
            data = self.make_openapi_request(f"/api/v1/devices/{device_id}/face-libraries")
            libraries = data.get("data", {}).get("libraries", [])
            logger.info(f"[HIKVISION] Found {len(libraries)} face libraries")
            return libraries
        except Exception as e:
            logger.warning(f"[HIKVISION] Failed to get face libraries via OpenAPI: {e}")
            return []

    async def get_face_libraries(self, device_info: Dict) -> List[Dict]:
        """Get face libraries for a device"""
        if self.auth_type == "openapi":
            libraries = await self.get_face_libraries_openapi(device_info.get("device_id"))
        else:
            libraries = await self.get_face_libraries_isapi(device_info)
        
        # Apply library filters
        if self.face_libraries:
            libraries = [lib for lib in libraries 
                        if lib.get("library_name") in self.face_libraries 
                        or lib.get("library_id") in self.face_libraries]
            logger.info(f"[HIKVISION] After filtering: {len(libraries)} libraries")
        
        return libraries

    # ==================== Face Person Methods ====================

    async def get_face_persons_isapi(self, library_id: str, start: int = 0, count: int = 100) -> List[Dict]:
        """Get face persons from library via ISAPI"""
        persons = []
        try:
            response = self.hik_client.Intelligent.FDLib[library_id].FaceDataRecord(
                method='get',
                data={'searchPosition': str(start), 'maxResults': str(count)}
            )

            match_list = response.get('FaceDataRecord', {}).get('FaceData', [])

            if not isinstance(match_list, list):
                match_list = [match_list] if match_list else []

            for person_data in match_list:
                person = {
                    "person_id": person_data.get("id", ""),
                    "person_name": person_data.get("name", "Unknown"),
                    "certificate_type": person_data.get("certificateType", ""),
                    "certificate_number": person_data.get("certificateNumber", ""),
                    "enrollment_date": person_data.get("addTime", ""),
                    "face_picture_url": person_data.get("facePictureURL", ""),
                    "library_id": library_id
                }
                persons.append(person)

            logger.debug(f"[HIKVISION] Retrieved {len(persons)} persons from library {library_id} (start={start})")
            return persons
        except Exception as e:
            logger.error(f"[HIKVISION] Failed to get face persons from library {library_id}: {e}")
            return []

    async def get_all_face_persons_isapi(self, library_id: str) -> List[Dict]:
        """Get all face persons with pagination"""
        all_persons = []
        start = 0
        batch_size = 100

        while True:
            persons = await self.get_face_persons_isapi(library_id, start, batch_size)
            if not persons:
                break
            all_persons.extend(persons)
            start += batch_size

            if start > 10000 or len(persons) < batch_size:
                break

        logger.info(f"[HIKVISION] Total persons retrieved from library {library_id}: {len(all_persons)}")
        return all_persons

    async def get_face_persons_openapi(self, library_id: str) -> List[Dict]:
        """Get face persons via OpenAPI"""
        try:
            data = self.make_openapi_request(f"/api/v1/face-libraries/{library_id}/persons")
            return data.get("data", {}).get("persons", [])
        except Exception as e:
            logger.error(f"[HIKVISION] Failed to get face persons via OpenAPI: {e}")
            return []

    async def get_face_persons(self, library_id: str) -> List[Dict]:
        """Get face persons for a library"""
        if self.auth_type == "openapi":
            return await self.get_face_persons_openapi(library_id)
        else:
            return await self.get_all_face_persons_isapi(library_id)

    # ==================== Recognition Events ====================

    async def get_recognition_events_isapi(
        self, 
        start_time: datetime = None,
        end_time: datetime = None, 
        max_results: int = 100
    ) -> List[Dict]:
        """Get recognition events via ISAPI"""
        events = []
        try:
            if not start_time:
                start_time = datetime.now() - timedelta(days=7)
            if not end_time:
                end_time = datetime.now()

            search_params = {
                'searchID': str(uuid4()),
                'searchResultPosition': '0',
                'maxResults': str(max_results),
                'major': '0',
                'minor': '0',
                'startTime': start_time.strftime("%Y-%m-%dT%H:%M:%SZ"),
                'endTime': end_time.strftime("%Y-%m-%dT%H:%M:%SZ")
            }

            response = self.hik_client.Event.notification.httpHosts(
                method='post',
                data=search_params
            )

            match_list = response.get('EventNotificationAlert', [])
            if not isinstance(match_list, list):
                match_list = [match_list] if match_list else []

            for event_data in match_list:
                event = {
                    "event_id": event_data.get("eventId", ""),
                    "timestamp": event_data.get("dateTime", ""),
                    "person_id": event_data.get("targetAttrs", {}).get("personID", ""),
                    "person_name": event_data.get("targetAttrs", {}).get("personName", "Unknown"),
                    "similarity": float(event_data.get("similarity", "0")),
                    "device_id": event_data.get("channelID", ""),
                    "picture_url": event_data.get("pictureURL", ""),
                    "recognition_mode": "1:N"
                }
                events.append(event)

            logger.info(f"[HIKVISION] Retrieved {len(events)} recognition events")
            return events
        except Exception as e:
            logger.warning(f"[HIKVISION] Failed to get recognition events: {e}")
            return []

    async def get_recognition_events_openapi(
        self, 
        device_id: str,
        start_time: datetime = None, 
        end_time: datetime = None
    ) -> List[Dict]:
        """Get recognition events via OpenAPI"""
        try:
            params = {}
            if start_time:
                params["startTime"] = start_time.isoformat()
            if end_time:
                params["endTime"] = end_time.isoformat()

            data = self.make_openapi_request(
                f"/api/v1/devices/{device_id}/events/face-recognition",
                params=params
            )
            return data.get("data", {}).get("events", [])
        except Exception as e:
            logger.warning(f"[HIKVISION] Failed to get recognition events via OpenAPI: {e}")
            return []

    # ==================== Unique File URI Generation ====================
    def _build_table_uri(self, name: str) -> str:
        safe_name = name.replace(" ", "_")
        return f"hikvision/{self.service_name}/{safe_name}"

    def _build_columns_for_libraries(self) -> List[TableColumn]:
        return [
            TableColumn("library_id", "string", primary_key=True),
            TableColumn("library_name", "string"),
            TableColumn("library_type", "string"),
            TableColumn("capacity", "int"),
            TableColumn("face_count", "int"),
            TableColumn("device_id", "string"),
        ]

    def _build_columns_for_persons(self) -> List[TableColumn]:
        return [
            TableColumn("person_id", "string", primary_key=True),
            TableColumn("person_name", "string"),
            TableColumn("certificate_type", "string"),
            TableColumn("certificate_number", "string"),
            TableColumn("enrollment_date", "string"),
            TableColumn("face_picture_url", "string"),
            TableColumn("library_id", "string"),
        ]

    def _build_columns_for_events(self) -> List[TableColumn]:
        return [
            TableColumn("event_id", "string", primary_key=True),
            TableColumn("timestamp", "string"),
            TableColumn("person_id", "string"),
            TableColumn("person_name", "string"),
            TableColumn("similarity", "float"),
            TableColumn("device_id", "string"),
            TableColumn("picture_url", "string"),
            TableColumn("recognition_mode", "string"),
        ]

    async def infra_scan(self) -> Dict:
        """Perform infrastructure scan: devices, libraries, counts."""
        try:
            await self.get_service_details()
            await self.connect()

            devices = await self.get_devices()
            total_devices = len(devices)
            total_libraries = 0
            total_persons = 0

            for device in devices:
                libs = await self.get_face_libraries(device)
                total_libraries += len(libs)
                for lib in libs:
                    if self.auth_type == "openapi":
                        persons = await self.get_face_persons_openapi(lib.get("library_id"))
                    else:
                        persons = await self.get_all_face_persons_isapi(lib.get("library_id"))
                    total_persons += len(persons)

            return {
                "devices": total_devices,
                "libraries": total_libraries,
                "persons": total_persons,
                "region": self.region,
            }
        except Exception as e:
            logger.error(f"[HIKVISION] Infra scan failed: {e}", exc_info=True)
            return {"devices": 0, "libraries": 0, "persons": 0, "region": self.region}

    async def deep_scan(self) -> AsyncGenerator[TableMetadata, None]:
        """Yield table-metadata snapshots for libraries, persons, and events."""
        try:
            await self.get_service_details()
            await self.connect()

            devices = await self.get_devices()
            for device in devices:
                device_id = device.get("device_id") or device.get("host")

                # Libraries table per device
                libraries = await self.get_face_libraries(device)
                lib_columns = self._build_columns_for_libraries()
                libs_table = TableMetadata(
                    service_name=self.service_name,
                    service_type=self.service_type,
                    service_provider=self.service_provider,
                    sub_service=self.sub_service,
                    db_type=DBTypes.HIKVISION_FR.value,
                    db_name=self.service_name,
                    table_name=f"libraries_{device_id}",
                    table_size=0,
                    no_of_rows=len(libraries),
                    table_uri=self._build_table_uri(f"libraries_{device_id}"),
                    columns=lib_columns,
                    details={"device": device},
                )
                yield libs_table

                # Persons per library
                for lib in libraries:
                    library_id = lib.get("library_id")
                    if not library_id:
                        continue
                    persons = await self.get_face_persons(library_id)
                    persons_columns = self._build_columns_for_persons()
                    persons_table = TableMetadata(
                        service_name=self.service_name,
                        service_type=self.service_type,
                        service_provider=self.service_provider,
                        sub_service=self.sub_service,
                        db_type=DBTypes.HIKVISION_FR.value,
                        db_name=self.service_name,
                        table_name=f"persons_{library_id}",
                        table_size=0,
                        no_of_rows=len(persons),
                        table_uri=self._build_table_uri(f"persons_{library_id}"),
                        columns=persons_columns,
                        details={"device_id": device_id, "library": lib},
                    )
                    yield persons_table

                # Recognition events per device (sample window)
                if self.auth_type == "openapi":
                    events = await self.get_recognition_events_openapi(device_id)
                else:
                    events = await self.get_recognition_events_isapi()

                events_columns = self._build_columns_for_events()
                events_table = TableMetadata(
                    service_name=self.service_name,
                    service_type=self.service_type,
                    service_provider=self.service_provider,
                    sub_service=self.sub_service,
                    db_type=DBTypes.HIKVISION_FR.value,
                    db_name=self.service_name,
                    table_name=f"recognition_events_{device_id}",
                    table_size=0,
                    no_of_rows=len(events),
                    table_uri=self._build_table_uri(f"recognition_events_{device_id}"),
                    columns=events_columns,
                    details={"device_id": device_id},
                )
                yield events_table
        except Exception as e:
            logger.error(f"[HIKVISION] Deep scan failed: {e}", exc_info=True)
        finally:
            self.close_connection()

    def close_connection(self):
        try:
            if self.session:
                self.session.close()
        except Exception:
            pass
        try:
            self.hik_client = None
        except Exception:
            pass