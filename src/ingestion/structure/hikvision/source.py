import os
import csv
import json
import base64
from uuid import uuid4
from datetime import datetime, timedelta
from typing import AsyncGenerator, List, Dict, Any, Optional
from collections import defaultdict
import requests
from requests.auth import HTTPBasicAuth

# Hikvision library for ISAPI protocol
try:
    from hikvisionapi import Client as HikvisionClient
    HIKVISION_API_AVAILABLE = True
except ImportError:
    HIKVISION_API_AVAILABLE = False
    HikvisionClient = None

from src.common.constants import ServiceTypes, DBTypes, AuthTypes, ServiceProviders
from src.utils.logger import get_ingestion_logger
from src.utils.loaders import load_access_controls, load_asset_details
from src.modules.repos import DatabaseManager
from src.ingestion.base_source import StructuredSource
from src.ingestion.data_class import TableMetadata, TableColumn, AssetsDetails
from src.common.config import TEMP_DATA_DIR
from src.utils.helpers import create_folder
from src.utils.exceptions import ServerException

logger = get_ingestion_logger()


class HikvisionFRSource(StructuredSource):
    """Connector for Hikvision Facial Recognition systems"""
    
    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.HIKVISION_FR.value, service_name)
        self.db = DatabaseManager()
        self.service_provider = ServiceProviders.HIKVISION
        self.sub_service = DBTypes.HIKVISION_FR.value
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"
        
        # API clients
        self.session = requests.Session()
        self.hik_client = None  # HikvisionAPI client for ISAPI
        self.access_token = None
        self.token_expiry = None
        
    async def get_service_details(self):
        """Gets service details from database"""
        self.service = await self.db.get_service_by_service_name(self.service_name)
        credentials = self.service.get("credentials", {})
        
        # Validate required credentials
        self._validate_required_credentials(credentials)
        
        # Authentication
        self.auth_type = credentials.get("auth_type", "isapi").lower()
        
        # OpenAPI credentials
        self.api_key = credentials.get("api_key")
        self.api_secret = credentials.get("api_secret")
        self.base_url = credentials.get("base_url", "").rstrip('/')
        
        # ISAPI/Device credentials
        self.device_host = credentials.get("device_host")
        self.device_port = credentials.get("device_port", 80)
        self.device_username = credentials.get("device_username", "admin")
        self.device_password = credentials.get("device_password")
        
        # Common settings
        self.protocol = credentials.get("protocol", "http")
        self.ssl_verify = credentials.get("ssl_verify", True)
        self.region = credentials.get("region", "Unknown")
        self.sample_count = credentials.get("sample_count", 100)
        self.timeout = credentials.get("timeout", 30)
        
        # Filters
        self.face_libraries = credentials.get("face_libraries", [])
        self.devices = credentials.get("devices", [])
        
        self.service_steward = self.service.get("data_steward")
        self.service_owner = self.service.get("data_owner")
        
        return self.service
    
    def _validate_required_credentials(self, credentials: dict):
        """Validate that all required credentials are provided"""
        auth_type = credentials.get("auth_type", "isapi").lower()
        missing_fields = []
        
        # Validate auth_type
        if auth_type not in ["isapi", "openapi"]:
            raise ServerException("auth_type must be either 'isapi' or 'openapi'")
        
        if auth_type == "isapi":
            # ISAPI required fields
            required_fields = ["device_host", "device_username", "device_password"]
            for field in required_fields:
                if not credentials.get(field):
                    missing_fields.append(field)
            
            # Validate device_port if provided
            device_port = credentials.get("device_port")
            if device_port:
                try:
                    port = int(device_port)
                    if not (1 <= port <= 65535):
                        raise ServerException("device_port must be between 1 and 65535")
                except (ValueError, TypeError):
                    raise ServerException("device_port must be a valid number")
        
        elif auth_type == "openapi":
            # OpenAPI required fields
            required_fields = ["base_url", "api_key", "api_secret"]
            for field in required_fields:
                if not credentials.get(field):
                    missing_fields.append(field)
            
            # Validate base_url format
            base_url = credentials.get("base_url", "")
            if base_url and not (base_url.startswith("http://") or base_url.startswith("https://")):
                raise ServerException("base_url must start with http:// or https://")
        
        if missing_fields:
            raise ServerException(f"Missing required credentials for {auth_type}: {', '.join(missing_fields)}")

    # ==================== Authentication ====================
    
    def generate_openapi_token(self) -> str:
        """Generate access token for Hik-Connect OpenAPI"""
        if not self.api_key or not self.api_secret:
            raise ServerException("API Key and Secret required for OpenAPI authentication")
        
        try:
            url = f"{self.base_url}/oauth/token"
            payload = {
                "grant_type": "client_credentials",
                "client_id": self.api_key,
                "client_secret": self.api_secret
            }
            
            response = requests.post(url, json=payload, verify=self.ssl_verify, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            self.access_token = data.get("access_token")
            expires_in = data.get("expires_in", 3600)
            self.token_expiry = datetime.now() + timedelta(seconds=expires_in)
            
            logger.info("OpenAPI access token generated successfully")
            return self.access_token
            
        except Exception as e:
            raise ServerException(f"Failed to generate OpenAPI token: {str(e)}")
    
    def init_isapi_client(self):
        """Initialize ISAPI client using hikvisionapi library"""
        if not HIKVISION_API_AVAILABLE:
            raise ServerException(
                "hikvisionapi library not installed. Install with: pip install hikvisionapi"
            )
        
        if not self.device_host or not self.device_username or not self.device_password:
            raise ServerException("Device host, username, and password required for ISAPI")
        
        try:
            device_url = f"{self.protocol}://{self.device_host}:{self.device_port}"
            self.hik_client = HikvisionClient(
                host=device_url,
                login=self.device_username,
                password=self.device_password,
                timeout=self.timeout
            )
            logger.info(f"ISAPI client initialized for {device_url}")
        except Exception as e:
            raise ServerException(f"Failed to initialize ISAPI client: {str(e)}")

    async def connect(self):
        """Establish connection and authenticate"""
        try:
            logger.info(f"[HIKVISION] Attempting to connect...")
            logger.info(f"[HIKVISION] Auth Type: {self.auth_type}")
            
            if self.auth_type == "openapi":
                logger.info(f"[HIKVISION] Connecting to OpenAPI: {self.base_url}")
                self.generate_openapi_token()
                # Test connection
                self.make_openapi_request("/api/v1/devices", method="GET")
                logger.info(f"[HIKVISION] ✅ Connected to Hikvision OpenAPI at {self.base_url}")
                
            elif self.auth_type == "isapi":
                logger.info(f"[HIKVISION] Connecting to ISAPI device: {self.device_host}:{self.device_port}")
                self.init_isapi_client()
                # Test connection by getting device info
                device_info = self.hik_client.System.deviceInfo(method='get')
                device_name = device_info.get('DeviceInfo', {}).get('deviceName', 'Unknown')
                logger.info(f"[HIKVISION] ✅ Connected to Hikvision device: {device_name}")
            else:
                raise ServerException(f"Invalid auth_type: {self.auth_type}")
        except Exception as e:
            logger.error(f"[HIKVISION] ❌ Connection failed: {str(e)}")
            raise ServerException(f"Connection failed: {str(e)}")

    async def test_connection(self) -> dict:
        """Test connection to Hikvision system"""
        try:
            logger.info(f"[HIKVISION] Testing connection for service: {self.service_name}")
            await self.get_service_details()
            await self.connect()
            logger.info(f"[HIKVISION] ✅ Connection test successful")
            return {"status": "success", "message": "Connection successful"}
        except Exception as e:
            logger.error(f"[HIKVISION] ❌ Connection test failed: {e}")
            return {"status": "failed", "message": str(e)}

    # ==================== API Request Methods ====================
    
    def get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for OpenAPI requests"""
        if self.auth_type == "openapi":
            # Check if token needs refresh
            if not self.access_token or (self.token_expiry and datetime.now() >= self.token_expiry):
                self.generate_openapi_token()
            return {"Authorization": f"Bearer {self.access_token}"}
        return {}
    
    def make_openapi_request(self, endpoint: str, method: str = "GET", 
                            params: dict = None, data: dict = None) -> Dict:
        """Make OpenAPI request"""
        url = f"{self.base_url}{endpoint}"
        headers = self.get_auth_headers()
        headers["Content-Type"] = "application/json"
        
        try:
            response = self.session.request(
                method=method,
                url=url,
                headers=headers,
                params=params,
                json=data,
                verify=self.ssl_verify,
                timeout=self.timeout
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"OpenAPI request failed for {endpoint}: {e}")
            raise ServerException(f"OpenAPI request failed: {str(e)}")

    # ==================== Device Discovery ====================
    
    async def get_devices_openapi(self) -> List[Dict]:
        """Get devices via OpenAPI"""
        try:
            data = self.make_openapi_request("/api/v1/devices")
            devices = data.get("data", {}).get("devices", [])
            logger.info(f"Discovered {len(devices)} devices via OpenAPI")
            return devices
        except Exception as e:
            logger.warning(f"Failed to get devices via OpenAPI: {e}")
            return []
    
    async def get_device_info_isapi(self) -> Dict:
        """Get device info via ISAPI using hikvisionapi library"""
        try:
            response = self.hik_client.System.deviceInfo(method='get')
            device_info_data = response.get('DeviceInfo', {})
            
            device_info = {
                "device_id": device_info_data.get("deviceID", "unknown"),
                "device_name": device_info_data.get("deviceName", "unknown"),
                "model": device_info_data.get("model", "unknown"),
                "serial_number": device_info_data.get("serialNumber", "unknown"),
                "firmware_version": device_info_data.get("firmwareVersion", "unknown"),
                "device_type": device_info_data.get("deviceType", "FaceRecognitionTerminal"),
                "host": self.device_host,
                "port": self.device_port
            }
            
            logger.info(f"Retrieved device info: {device_info['device_name']} ({device_info['model']})")
            return device_info
        except Exception as e:
            logger.error(f"Failed to get device info via ISAPI: {e}")
            return {
                "device_id": f"{self.device_host}:{self.device_port}",
                "device_name": f"Device_{self.device_host}",
                "model": "unknown",
                "host": self.device_host,
                "port": self.device_port
            }

    async def get_devices(self) -> List[Dict]:
        """Get all devices based on auth type"""
        logger.info(f"[HIKVISION] Getting devices using {self.auth_type} mode...")
        if self.auth_type == "openapi":
            devices = await self.get_devices_openapi()
        else:
            # For ISAPI, we have single device connection
            device_info = await self.get_device_info_isapi()
            devices = [device_info]
        
        logger.info(f"[HIKVISION] Retrieved {len(devices)} device(s)")
        return devices

    # ==================== Face Library Methods ====================
    
    async def get_face_libraries_isapi(self, device_info: Dict) -> List[Dict]:
        """Get face libraries via ISAPI"""
        libraries = []
        try:
            logger.info(f"[HIKVISION] Fetching face libraries via ISAPI for device {device_info.get('device_name')}...")
            # Get face library list using hikvisionapi
            response = self.hik_client.Intelligent.FDLib(method='get')
            
            logger.debug(f"[HIKVISION] Raw response: {response}")
            
            # Handle response structure
            fdlib_list = response.get('FaceLibList', {}) or response.get('FDLibList', {})
            # Try both 'FaceLib' and 'FDLib' keys for compatibility
            fd_libs = fdlib_list.get('FaceLib', []) or fdlib_list.get('FDLib', [])
            
            logger.debug(f"[HIKVISION] FDLib data: {fd_libs}")
            
            # Ensure fd_libs is a list
            if not isinstance(fd_libs, list):
                fd_libs = [fd_libs] if fd_libs else []
            
            for fd_lib in fd_libs:
                # Hikvision uses 'FDID' in XML, but hikvisionapi library converts it
                # Try multiple field names for compatibility
                lib_id = fd_lib.get("id") or fd_lib.get("FDID") or fd_lib.get("fdid") or "0"
                lib_name = fd_lib.get("name", "DefaultLibrary")
                
                library = {
                    "library_id": str(lib_id),
                    "library_name": lib_name,
                    "library_type": fd_lib.get("type", "whitelist"),
                    "capacity": int(fd_lib.get("capacity", "0")),
                    "face_count": int(fd_lib.get("FDLibSize", {}).get("totalNum", "0")) if isinstance(fd_lib.get("FDLibSize"), dict) else 0,
                    "device_id": device_info.get("device_id")
                }
                libraries.append(library)
            
            logger.info(f"[HIKVISION] Found {len(libraries)} face libraries on device {device_info['device_name']}")
            return libraries
        except Exception as e:
            logger.warning(f"[HIKVISION] Failed to get face libraries via ISAPI: {e}")
            logger.info(f"[HIKVISION] Using default library as fallback")
            # Return default library
            return [{
                "library_id": "1",
                "library_name": "DefaultLibrary",
                "library_type": "whitelist",
                "capacity": 500,
                "face_count": 0,
                "device_id": device_info.get("device_id")
            }]
    
    async def get_face_libraries_openapi(self, device_id: str) -> List[Dict]:
        """Get face libraries via OpenAPI"""
        try:
            logger.info(f"[HIKVISION] Fetching face libraries via OpenAPI for device {device_id}...")
            data = self.make_openapi_request(f"/api/v1/devices/{device_id}/face-libraries")
            libraries = data.get("data", {}).get("libraries", [])
            logger.info(f"[HIKVISION] Found {len(libraries)} face libraries for device {device_id}")
            return libraries
        except Exception as e:
            logger.warning(f"[HIKVISION] Failed to get face libraries via OpenAPI: {e}")
            return []

    async def get_face_libraries(self, device_info: Dict) -> List[Dict]:
        """Get face libraries for a device"""
        if self.auth_type == "openapi":
            return await self.get_face_libraries_openapi(device_info.get("device_id"))
        else:
            return await self.get_face_libraries_isapi(device_info)

    # ==================== Face Person Methods ====================
    
    async def get_face_persons_isapi(self, library_id: str, start: int = 0, count: int = 100) -> List[Dict]:
        """Get face persons from library via ISAPI"""
        persons = []
        try:
            # Use hikvisionapi library to get face persons
            response = self.hik_client.Intelligent.FDLib[library_id].FaceDataRecord(
                method='get',
                data={'searchPosition': str(start), 'maxResults': str(count)}
            )
            
            # Parse response
            match_list = response.get('FaceDataRecord', {}).get('FaceData', [])
            
            # Ensure it's a list
            if not isinstance(match_list, list):
                match_list = [match_list] if match_list else []
            
            for person_data in match_list:
                person = {
                    "person_id": person_data.get("id", ""),
                    "person_name": person_data.get("name", "Unknown"),
                    "certificate_type": person_data.get("certificateType", ""),
                    "certificate_number": person_data.get("certificateNumber", ""),
                    "enrollment_date": person_data.get("addTime", ""),
                    "face_picture_url": person_data.get("facePictureURL", ""),
                    "library_id": library_id
                }
                persons.append(person)
            
            logger.info(f"Retrieved {len(persons)} persons from library {library_id} (start={start})")
            return persons
        except Exception as e:
            logger.error(f"Failed to get face persons from library {library_id}: {e}")
            return []
    
    async def get_all_face_persons_isapi(self, library_id: str) -> List[Dict]:
        """Get all face persons with pagination"""
        all_persons = []
        start = 0
        batch_size = 100
        
        while True:
            persons = await self.get_face_persons_isapi(library_id, start, batch_size)
            if not persons:
                break
            all_persons.extend(persons)
            start += batch_size
            
            # Safety limit
            if start > 10000 or len(persons) < batch_size:
                break
        
        logger.info(f"Retrieved total of {len(all_persons)} persons from library {library_id}")
        return all_persons
    
    async def get_face_persons_openapi(self, library_id: str) -> List[Dict]:
        """Get face persons via OpenAPI"""
        try:
            data = self.make_openapi_request(f"/api/v1/face-libraries/{library_id}/persons")
            return data.get("data", {}).get("persons", [])
        except Exception as e:
            logger.error(f"Failed to get face persons via OpenAPI: {e}")
            return []

    async def get_face_persons(self, library_id: str) -> List[Dict]:
        """Get face persons for a library"""
        if self.auth_type == "openapi":
            return await self.get_face_persons_openapi(library_id)
        else:
            return await self.get_all_face_persons_isapi(library_id)

    # ==================== Recognition Events ====================
    
    async def get_recognition_events_isapi(self, start_time: datetime = None, 
                                          end_time: datetime = None, 
                                          max_results: int = 100) -> List[Dict]:
        """Get recognition events via ISAPI"""
        events = []
        try:
            # Default to last 7 days if not specified
            if not start_time:
                start_time = datetime.now() - timedelta(days=7)
            if not end_time:
                end_time = datetime.now()
            
            search_params = {
                'searchID': str(uuid4()),
                'searchResultPosition': '0',
                'maxResults': str(max_results),
                'major': '0',  # Face recognition major type
                'minor': '0',  # All minor types
                'startTime': start_time.strftime("%Y-%m-%dT%H:%M:%SZ"),
                'endTime': end_time.strftime("%Y-%m-%dT%H:%M:%SZ")
            }
            
            response = self.hik_client.Event.notification.httpHosts(
                method='post',
                data=search_params
            )
            
            # Parse events from response
            match_list = response.get('EventNotificationAlert', [])
            if not isinstance(match_list, list):
                match_list = [match_list] if match_list else []
            
            for event_data in match_list:
                event = {
                    "event_id": event_data.get("eventId", ""),
                    "timestamp": event_data.get("dateTime", ""),
                    "person_id": event_data.get("targetAttrs", {}).get("personID", ""),
                    "person_name": event_data.get("targetAttrs", {}).get("personName", "Unknown"),
                    "similarity": float(event_data.get("similarity", "0")),
                    "device_id": event_data.get("channelID", ""),
                    "picture_url": event_data.get("pictureURL", ""),
                    "recognition_mode": "1:N"  # Default for face recognition terminals
                }
                events.append(event)
            
            logger.info(f"Retrieved {len(events)} recognition events")
            return events
        except Exception as e:
            logger.warning(f"Failed to get recognition events: {e}")
            return []
    
    async def get_recognition_events_openapi(self, device_id: str, 
                                            start_time: datetime = None, 
                                            end_time: datetime = None) -> List[Dict]:
        """Get recognition events via OpenAPI"""
        try:
            params = {}
            if start_time:
                params["startTime"] = start_time.isoformat()
            if end_time:
                params["endTime"] = end_time.isoformat()
            
            data = self.make_openapi_request(
                f"/api/v1/devices/{device_id}/events/face-recognition",
                params=params
            )
            return data.get("data", {}).get("events", [])
        except Exception as e:
            logger.warning(f"Failed to get recognition events via OpenAPI: {e}")
            return []

    # ==================== Infrastructure Scan ====================
    
    async def infra_scan(self) -> Dict[str, Any]:
        """Discover devices, libraries, and load asset details"""
        try:
            logger.info(f"Starting infra scan for Hikvision FR service: {self.service_name}")
            await self.get_service_details()
            await self.connect()
            
            devices = await self.get_devices()
            aggregated_metadata = []
            
            for device_info in devices:
                device_id = device_info.get("device_id", device_info.get("device_name"))
                
                # Get face libraries for device
                libraries = await self.get_face_libraries(device_info)
                
                for library in libraries:
                    library_id = library.get("library_id")
                    library_name = library.get("library_name")
                    
                    # Get face count
                    face_count = library.get("face_count", 0)
                    capacity = library.get("capacity", 0)
                    
                    # Create asset record
                    asset = AssetsDetails(
                        asset_name=f"{device_info.get('device_name', device_id)}_{library_name}",
                        service_provider=self.service_provider,
                        type="face_library",
                        category="biometric",
                        location=self.region,
                        owner=self.service_owner or "admin",
                        security=True,  # Biometric data is always secured
                        size=face_count,  # Number of faces
                        count=face_count,
                        access_category="privileged",  # Biometric data requires privileged access
                        service_name=str(self.service_name),
                        steward=str(self.service_steward)
                    )
                    
                    try:
                        load_asset_details([asset])
                        logger.info(f"Loaded asset: {asset.asset_name}")
                    except Exception as e:
                        logger.error(f"Failed to load asset {asset.asset_name}: {e}")
                    
                    # Access controls - biometric systems typically have strict controls
                    access_controls = [
                        {
                            "asset_name": f"{device_info.get('device_name', device_id)}_{library_name}",
                            "user_or_role": "system_admin",
                            "role": "role",
                            "access": "full"
                        }
                    ]
                    
                    try:
                        load_access_controls(access_controls)
                    except Exception as e:
                        logger.error(f"Failed to load access controls: {e}")
                    
                    # Metadata for return
                    aggregated_metadata.append({
                        "device_id": device_id,
                        "device_name": device_info.get("device_name"),
                        "device_model": device_info.get("model"),
                        "library_id": library_id,
                        "library_name": library_name,
                        "library_type": library.get("library_type"),
                        "face_count": face_count,
                        "capacity": capacity,
                        "region": self.region
                    })
            
            logger.info(f"Infra scan completed: {len(aggregated_metadata)} libraries discovered")
            return {
                "devices": [d.get("device_id") for d in devices],
                "libraries": [m["library_name"] for m in aggregated_metadata],
                "region": self.region,
                "metadata": aggregated_metadata
            }
            
        except Exception as e:
            logger.error(f"Infrastructure scan failed: {e}", exc_info=True)
            return {"error": str(e)}

    # ==================== Deep Scan ====================
    
    async def deep_scan(self) -> AsyncGenerator[TableMetadata, None]:
        """Extract face persons and recognition events"""
        try:
            logger.info(f"[HIKVISION] ========== STARTING DEEP SCAN ==========")
            logger.info(f"[HIKVISION] Service: {self.service_name}")
            
            await self.get_service_details()
            logger.info(f"[HIKVISION] Auth Type: {self.auth_type}")
            
            await self.connect()
            
            logger.info(f"[HIKVISION] Connection established successfully")
            logger.info(f"[HIKVISION] Sample Count: {self.sample_count}")
            
            # Run infra scan first
            logger.info(f"[HIKVISION] Running infrastructure scan...")
            infra_result = await self.infra_scan()
            logger.info(f"[HIKVISION] Infrastructure scan completed")
            
            logger.info(f"[HIKVISION] Discovering devices...")
            devices = await self.get_devices()
            logger.info(f"[HIKVISION] Found {len(devices)} device(s)")
            
            total_tables = 0
            for device_idx, device_info in enumerate(devices, 1):
                device_id = device_info.get("device_id", device_info.get("device_name"))
                logger.info(f"[HIKVISION] {'='*60}")
                logger.info(f"[HIKVISION] Processing device {device_idx}/{len(devices)}: {device_id}")
                
                logger.info(f"[HIKVISION] Discovering face libraries...")
                libraries = await self.get_face_libraries(device_info)
                logger.info(f"[HIKVISION] Found {len(libraries)} face library(ies)")
                
                # Filter libraries if specified
                if self.face_libraries:
                    original_count = len(libraries)
                    libraries = [lib for lib in libraries 
                               if lib.get("library_name") in self.face_libraries 
                               or lib.get("library_id") in self.face_libraries]
                    logger.info(f"[HIKVISION] Filtered to {len(libraries)}/{original_count} specified libraries")
                
                for lib_idx, library in enumerate(libraries, 1):
                    library_name = library.get("library_name", "Unknown")
                    logger.info(f"[HIKVISION] {'='*60}")
                    logger.info(f"[HIKVISION] Processing library {lib_idx}/{len(libraries)}: {library_name}")
                    
                    # Scan face persons
                    try:
                        logger.info(f"[HIKVISION] Scanning face persons in {library_name}...")
                        table_metadata = await self.scan_face_library(
                            device_info, library
                        )
                        if table_metadata:
                            logger.info(f"[HIKVISION] ✅ Successfully scanned {library_name} persons")
                            logger.info(f"[HIKVISION] 📤 Yielding TableMetadata to workflow...")
                            total_tables += 1
                            yield table_metadata
                        else:
                            logger.warning(f"[HIKVISION] ⏭️  Skipped {library_name} persons (no data or already processed)")
                    except Exception as e:
                        logger.error(
                            f"[HIKVISION] ❌ Failed to scan library {library_name}: {e}",
                            exc_info=True
                        )
                    
                    # Scan recognition events
                    try:
                        logger.info(f"[HIKVISION] Scanning recognition events for {library_name}...")
                        events_metadata = await self.scan_recognition_events(
                            device_info, library
                        )
                        if events_metadata:
                            logger.info(f"[HIKVISION] ✅ Successfully scanned {library_name} events")
                            logger.info(f"[HIKVISION] 📤 Yielding TableMetadata to workflow...")
                            total_tables += 1
                            yield events_metadata
                        else:
                            logger.warning(f"[HIKVISION] ⏭️  Skipped {library_name} events (no data or already processed)")
                    except Exception as e:
                        logger.error(
                            f"[HIKVISION] ❌ Failed to scan events for library {library_name}: {e}",
                            exc_info=True
                        )
            
            logger.info(f"[HIKVISION] ========== DEEP SCAN COMPLETE ==========")
            logger.info(f"[HIKVISION] Total tables/datasets scanned: {total_tables}")
            logger.info(f"[HIKVISION] Devices processed: {len(devices)}")
            logger.info(f"[HIKVISION] ==========================================")
                        
        except Exception as e:
            logger.error(f"[HIKVISION] ❌ Deep scan failed: {e}", exc_info=True)
        finally:
            self.close_connection()
            logger.info(f"[HIKVISION] Connection closed")

    async def scan_face_library(self, device_info: Dict, library: Dict) -> Optional[TableMetadata]:
        """Scan face persons in a library and export to CSV"""
        try:
            library_id = library.get("library_id")
            library_name = library.get("library_name")
            device_id = device_info.get("device_id")
            
            logger.info(f"[HIKVISION] Starting scan for face library: {library_name} (ID: {library_id})")
            logger.info(f"[HIKVISION] Device: {device_id} - {device_info.get('device_name')}")
            
            # Get all face persons
            persons = await self.get_face_persons(library_id)
            
            if not persons:
                logger.info(f"[HIKVISION] No persons found in library {library_name}")
                return None
            
            logger.info(f"[HIKVISION] Retrieved {len(persons)} persons from library {library_name}")
            
            # Define columns
            columns = [
                TableColumn(column_name="person_id", data_type="VARCHAR", primary_key=True, index=True),
                TableColumn(column_name="person_name", data_type="VARCHAR", primary_key=False, index=True),
                TableColumn(column_name="certificate_type", data_type="VARCHAR", primary_key=False, index=False),
                TableColumn(column_name="certificate_number", data_type="VARCHAR", primary_key=False, index=True),
                TableColumn(column_name="enrollment_date", data_type="TIMESTAMP", primary_key=False, index=True),
                TableColumn(column_name="face_picture_url", data_type="TEXT", primary_key=False, index=False),
                TableColumn(column_name="library_id", data_type="VARCHAR", primary_key=False, index=True),
            ]
            
            logger.info(f"[HIKVISION] Created {len(columns)} TableColumn objects:")
            for i, col in enumerate(columns, 1):
                logger.info(f"[HIKVISION]   {i}. {col.column_name} ({col.data_type}) - PK:{col.primary_key}, Index:{col.index}")
            
            # Create CSV file
            create_folder(self.local_data_dir)
            output_file = os.path.join(
                self.local_data_dir, 
                f"face_library_{library_name}_{str(uuid4())}.csv"
            )
            
            sample_persons = persons[:self.sample_count]
            logger.info(f"[HIKVISION] Writing {len(sample_persons)} sample records to CSV (requested: {self.sample_count})")
            
            with open(output_file, mode="w", newline="", encoding="utf-8") as file:
                writer = csv.DictWriter(file, fieldnames=[col.column_name for col in columns])
                writer.writeheader()
                for person in sample_persons:
                    writer.writerow({
                        "person_id": person.get("person_id", ""),
                        "person_name": person.get("person_name", "Unknown"),
                        "certificate_type": person.get("certificate_type", ""),
                        "certificate_number": person.get("certificate_number", ""),
                        "enrollment_date": person.get("enrollment_date", ""),
                        "face_picture_url": person.get("face_picture_url", ""),
                        "library_id": library_id
                    })
            
            logger.info(f"[HIKVISION] CSV file created: {output_file}")
            logger.info(f"[HIKVISION] CSV contains {len(sample_persons)} rows with {len(columns)} columns")
            
            table_uri = f"hikvision://{device_id}/{library_name}/persons"
            logger.info(f"[HIKVISION] Table URI: {table_uri}")
            
            # Check if already processed
            if self.db.skip_table(table_uri):
                logger.info(f"[HIKVISION] ⏭️  Skipping library {library_name} - Already processed")
                return None
            
            file_size = os.path.getsize(output_file)
            logger.info(f"[HIKVISION] Creating TableMetadata object:")
            logger.info(f"[HIKVISION]   - Service: {self.service_name}")
            logger.info(f"[HIKVISION]   - DB Name: {device_id}")
            logger.info(f"[HIKVISION]   - Table Name: {library_name}_persons")
            logger.info(f"[HIKVISION]   - Table Size: {file_size} bytes")
            logger.info(f"[HIKVISION]   - Row Count: {len(persons)}")
            logger.info(f"[HIKVISION]   - Columns: {len(columns)}")
            logger.info(f"[HIKVISION]   - CSV File: {output_file}")
            
            table_metadata = TableMetadata(
                service_name=self.service_name,
                service_type=self.service_type,
                service_provider=self.service_provider,
                sub_service=self.sub_service,
                db_type=DBTypes.HIKVISION_FR.value,
                db_name=device_id,
                table_name=f"{library_name}_persons",
                table_size=file_size,
                no_of_rows=len(persons),
                table_uri=table_uri,
                columns=columns,
                local_filepath=output_file,
                details={
                    "device_id": device_id,
                    "device_name": device_info.get("device_name"),
                    "device_model": device_info.get("model"),
                    "library_id": library_id,
                    "library_name": library_name,
                    "library_type": library.get("library_type"),
                    "capacity": library.get("capacity"),
                    "region": self.region,
                    "auth_type": self.auth_type
                }
            )
            
            logger.info(f"[HIKVISION] ✅ TableMetadata created successfully for {library_name}_persons")
            logger.info(f"[HIKVISION] TableMetadata will be yielded to workflow for processing")
            
            return table_metadata
            
        except Exception as e:
            logger.error(f"[HIKVISION] ❌ Failed to scan face library: {e}", exc_info=True)
            return None

    async def scan_recognition_events(self, device_info: Dict, library: Dict) -> Optional[TableMetadata]:
        """Scan recognition events and export to CSV"""
        try:
            device_id = device_info.get("device_id")
            library_name = library.get("library_name")
            library_id = library.get("library_id")
            
            logger.info(f"[HIKVISION] Starting scan for recognition events")
            logger.info(f"[HIKVISION] Device: {device_id} - {device_info.get('device_name')}")
            logger.info(f"[HIKVISION] Library: {library_name} (ID: {library_id})")
            
            # Get events from last 7 days
            start_time = datetime.now() - timedelta(days=7)
            end_time = datetime.now()
            logger.info(f"[HIKVISION] Event time range: {start_time.strftime('%Y-%m-%d %H:%M:%S')} to {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            if self.auth_type == "openapi":
                logger.info(f"[HIKVISION] Using OpenAPI to fetch recognition events")
                events = await self.get_recognition_events_openapi(device_id, start_time, end_time)
            else:
                logger.info(f"[HIKVISION] Using ISAPI to fetch recognition events")
                events = await self.get_recognition_events_isapi(start_time, end_time, self.sample_count)
            
            if not events:
                logger.info(f"[HIKVISION] No recognition events found for device {device_id}")
                return None
            
            logger.info(f"[HIKVISION] Retrieved {len(events)} recognition events from {library_name}")
            
            # Define columns
            columns = [
                TableColumn(column_name="event_id", data_type="VARCHAR", primary_key=True, index=True),
                TableColumn(column_name="timestamp", data_type="TIMESTAMP", primary_key=False, index=True),
                TableColumn(column_name="person_id", data_type="VARCHAR", primary_key=False, index=True),
                TableColumn(column_name="person_name", data_type="VARCHAR", primary_key=False, index=False),
                TableColumn(column_name="similarity", data_type="FLOAT", primary_key=False, index=False),
                TableColumn(column_name="device_id", data_type="VARCHAR", primary_key=False, index=True),
                TableColumn(column_name="picture_url", data_type="TEXT", primary_key=False, index=False),
                TableColumn(column_name="recognition_mode", data_type="VARCHAR", primary_key=False, index=False),
            ]
            
            logger.info(f"[HIKVISION] Created {len(columns)} TableColumn objects for recognition events:")
            for i, col in enumerate(columns, 1):
                logger.info(f"[HIKVISION]   {i}. {col.column_name} ({col.data_type}) - PK:{col.primary_key}, Index:{col.index}")
            
            # Create CSV file
            create_folder(self.local_data_dir)
            output_file = os.path.join(
                self.local_data_dir,
                f"recognition_events_{device_id}_{str(uuid4())}.csv"
            )
            
            sample_events = events[:self.sample_count]
            logger.info(f"[HIKVISION] Writing {len(sample_events)} sample events to CSV (requested: {self.sample_count})")
            
            with open(output_file, mode="w", newline="", encoding="utf-8") as file:
                writer = csv.DictWriter(file, fieldnames=[col.column_name for col in columns])
                writer.writeheader()
                for event in sample_events:
                    writer.writerow({
                        "event_id": event.get("event_id", ""),
                        "timestamp": event.get("timestamp", ""),
                        "person_id": event.get("person_id", ""),
                        "person_name": event.get("person_name", "Unknown"),
                        "similarity": event.get("similarity", 0.0),
                        "device_id": event.get("device_id", device_id),
                        "picture_url": event.get("picture_url", ""),
                        "recognition_mode": event.get("recognition_mode", "1:N")
                    })
            
            logger.info(f"[HIKVISION] CSV file created: {output_file}")
            logger.info(f"[HIKVISION] CSV contains {len(sample_events)} rows with {len(columns)} columns")
            
            table_uri = f"hikvision://{device_id}/recognition_events"
            logger.info(f"[HIKVISION] Table URI: {table_uri}")
            
            # Check if already processed
            if self.db.skip_table(table_uri):
                logger.info(f"[HIKVISION] ⏭️  Skipping recognition events for {device_id} - Already processed")
                return None
            
            file_size = os.path.getsize(output_file)
            logger.info(f"[HIKVISION] Creating TableMetadata object for recognition events:")
            logger.info(f"[HIKVISION]   - Service: {self.service_name}")
            logger.info(f"[HIKVISION]   - DB Name: {device_id}")
            logger.info(f"[HIKVISION]   - Table Name: recognition_events")
            logger.info(f"[HIKVISION]   - Table Size: {file_size} bytes")
            logger.info(f"[HIKVISION]   - Row Count: {len(events)}")
            logger.info(f"[HIKVISION]   - Columns: {len(columns)}")
            logger.info(f"[HIKVISION]   - CSV File: {output_file}")
            
            table_metadata = TableMetadata(
                service_name=self.service_name,
                service_type=self.service_type,
                service_provider=self.service_provider,
                sub_service=self.sub_service,
                db_type=DBTypes.HIKVISION_FR.value,
                db_name=device_id,
                table_name="recognition_events",
                table_size=file_size,
                no_of_rows=len(events),
                table_uri=table_uri,
                columns=columns,
                local_filepath=output_file,
                details={
                    "device_id": device_id,
                    "device_name": device_info.get("device_name"),
                    "device_model": device_info.get("model"),
                    "event_count": len(events),
                    "time_range": {
                        "start": start_time.isoformat(),
                        "end": end_time.isoformat()
                    },
                    "region": self.region,
                    "auth_type": self.auth_type
                }
            )
            
            logger.info(f"[HIKVISION] ✅ TableMetadata created successfully for recognition_events")
            logger.info(f"[HIKVISION] TableMetadata will be yielded to workflow for processing")
            
            return table_metadata
            
        except Exception as e:
            logger.error(f"[HIKVISION] ❌ Failed to scan recognition events: {e}", exc_info=True)
            return None

    # ==================== User Scan ====================
    
    async def find_person_by_id(self, person_id: str, library_id: str) -> Optional[Dict]:
        """Find a specific person by ID in a library"""
        try:
            persons = await self.get_face_persons(library_id)
            for person in persons:
                if person.get("person_id") == person_id:
                    return person
            return None
        except Exception as e:
            logger.error(f"Failed to find person {person_id}: {e}")
            return None
    
    async def find_person_by_name(self, person_name: str, library_id: str) -> List[Dict]:
        """Find persons by name (partial match)"""
        try:
            persons = await self.get_face_persons(library_id)
            matches = []
            for person in persons:
                if person_name.lower() in person.get("person_name", "").lower():
                    matches.append(person)
            return matches
        except Exception as e:
            logger.error(f"Failed to find persons by name {person_name}: {e}")
            return []
    
    async def find_person_by_certificate(self, cert_number: str, library_id: str) -> Optional[Dict]:
        """Find person by certificate number"""
        try:
            persons = await self.get_face_persons(library_id)
            for person in persons:
                if person.get("certificate_number") == cert_number:
                    return person
            return None
        except Exception as e:
            logger.error(f"Failed to find person by certificate {cert_number}: {e}")
            return None
    
    async def get_person_recognition_history(self, person_id: str, device_id: str = None) -> List[Dict]:
        """Get recognition event history for a person"""
        try:
            start_time = datetime.now() - timedelta(days=30)  # Last 30 days
            end_time = datetime.now()
            
            if self.auth_type == "openapi" and device_id:
                events = await self.get_recognition_events_openapi(device_id, start_time, end_time)
            else:
                events = await self.get_recognition_events_isapi(start_time, end_time, 1000)
            
            # Filter by person_id
            person_events = [e for e in events if e.get("person_id") == person_id]
            logger.info(f"Found {len(person_events)} recognition events for person {person_id}")
            return person_events
            
        except Exception as e:
            logger.error(f"Failed to get recognition history for person {person_id}: {e}")
            return []
    
    async def fetch_related_person_data(self, initial_matches: List[Dict]) -> List[Dict]:
        """Fetch related data for persons (recognition events, access logs)"""
        results = []
        
        try:
            devices = await self.get_devices()
            
            for match in initial_matches:
                person_id = match.get("person_id")
                library_id = match.get("library_id")
                device_id = match.get("device_id")
                
                if not person_id:
                    continue
                
                # Get person details
                person = await self.find_person_by_id(person_id, library_id)
                
                if not person:
                    continue
                
                # Get recognition history
                recognition_events = await self.get_person_recognition_history(person_id, device_id)
                
                results.append({
                    "service_name": self.service_name,
                    "service_provider": self.service_provider,
                    "sub_service": self.sub_service,
                    "device_id": device_id,
                    "library_id": library_id,
                    "person_details": person,
                    "recognition_events": recognition_events,
                    "event_count": len(recognition_events)
                })
                
                logger.info(f"Collected data for person {person_id}: {len(recognition_events)} events")
                
        except Exception as e:
            logger.error(f"Error fetching related person data: {e}", exc_info=True)
        
        return results

    async def user_scan(self, initial_matches: List[Dict]) -> AsyncGenerator[List[Dict], None]:
        """
        Perform user scan to find related biometric data
        
        Args:
            initial_matches: List of dicts with keys:
                - person_id: Person ID to search for
                - person_name: Person name to search for (optional)
                - certificate_number: Certificate number (optional)
                - library_id: Face library ID
                - device_id: Device ID
        """
        try:
            logger.info(f"Starting user scan for Hikvision FR service: {self.service_name}")
            await self.get_service_details()
            await self.connect()
            
            devices = await self.get_devices()
            
            for device_info in devices:
                device_id = device_info.get("device_id")
                
                # Filter matches for this device
                device_matches = [m for m in initial_matches 
                                if m.get("device_id") == device_id or not m.get("device_id")]
                
                if not device_matches:
                    continue
                
                libraries = await self.get_face_libraries(device_info)
                
                for library in libraries:
                    library_id = library.get("library_id")
                    
                    # Filter matches for this library
                    library_matches = [m for m in device_matches 
                                     if m.get("library_id") == library_id or not m.get("library_id")]
                    
                    if not library_matches:
                        continue
                    
                    # Enrich matches with library_id and device_id
                    enriched_matches = []
                    for match in library_matches:
                        enriched_match = match.copy()
                        enriched_match["library_id"] = library_id
                        enriched_match["device_id"] = device_id
                        enriched_matches.append(enriched_match)
                    
                    # Fetch related data
                    results = await self.fetch_related_person_data(enriched_matches)
                    
                    if results:
                        logger.info(f"User scan found {len(results)} results for device {device_id}, library {library_id}")
                        yield results
                    
        except Exception as e:
            logger.error(f"User scan failed: {e}", exc_info=True)
        finally:
            self.close_connection()

    # ==================== Utility Methods ====================
    
    def close_connection(self):
        """Close session and cleanup"""
        try:
            if self.session:
                self.session.close()
            if self.hik_client:
                # hikvisionapi client doesn't need explicit close
                self.hik_client = None
            logger.info("Hikvision FR connection closed")
        except Exception as e:
            logger.warning(f"Error closing connection: {e}")
    
    async def get_device_capacity(self, device_info: Dict) -> Dict[str, int]:
        """Get device capacity information"""
        try:
            if self.auth_type == "isapi" and self.hik_client:
                response = self.hik_client.System.capabilities(method='get')
                capabilities = response.get('DeviceCap', {})
                return {
                    "max_face_count": int(capabilities.get("maxFaceCount", "500")),
                    "max_libraries": int(capabilities.get("maxLibraries", "10")),
                    "max_events": int(capabilities.get("maxEvents", "10000"))
                }
            return {"max_face_count": 500, "max_libraries": 10, "max_events": 10000}
        except Exception as e:
            logger.warning(f"Failed to get device capacity: {e}")
            return {"max_face_count": 500, "max_libraries": 10, "max_events": 10000}
    
    async def export_person_images(self, persons: List[Dict], output_dir: str) -> Dict[str, str]:
        """
        Export face images for persons (optional - for future use)
        Returns dict mapping person_id to local image path
        """
        image_paths = {}
        create_folder(output_dir)
        
        for person in persons:
            person_id = person.get("person_id")
            image_url = person.get("face_picture_url")
            
            if not image_url:
                continue
            
            try:
                # Download image
                if self.auth_type == "isapi":
                    # ISAPI images need authentication
                    auth = HTTPBasicAuth(self.device_username, self.device_password)
                    response = requests.get(
                        image_url,
                        auth=auth,
                        verify=self.ssl_verify,
                        timeout=10
                    )
                else:
                    headers = self.get_auth_headers()
                    response = requests.get(
                        image_url,
                        headers=headers,
                        verify=self.ssl_verify,
                        timeout=10
                    )
                
                if response.status_code == 200:
                    image_path = os.path.join(output_dir, f"{person_id}.jpg")
                    with open(image_path, 'wb') as f:
                        f.write(response.content)
                    image_paths[person_id] = image_path
                    
            except Exception as e:
                logger.warning(f"Failed to download image for person {person_id}: {e}")
        
        logger.info(f"Exported {len(image_paths)} face images")
        return image_paths
    
    def __del__(self):
        """Cleanup on destruction"""
        self.close_connection()