import csv
import os
from uuid import uuid4
from typing import AsyncGenerator, Optional, List

from simple_salesforce import Salesforce, SalesforceLogin

from src.common.constants import ServiceTypes, DBTypes, ServiceProviders
from src.utils.logger import get_ingestion_logger
from src.modules.repos import DatabaseManager
from src.ingestion.base_source import StructuredSource
from src.ingestion.data_class import TableMetadata, TableColumn
from src.common.config import TEMP_DATA_DIR
from src.utils.helpers import create_folder
from src.utils.exceptions import ServerException

logger = get_ingestion_logger()

class SalesforceSource(StructuredSource):
    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.SALESFORCE.value, service_name)
        self.db = DatabaseManager()
        self.connection = None
        self.sf = None
        self.service_provider = ServiceProviders.LOCAL
        self.sub_service = DBTypes.SALESFORCE.value
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"

    async def get_service_details(self):
        self.service = await self.db.get_service_by_service_name(self.service_name)
        credentials = self.service.get("credentials", {})
        self.username = credentials.get("username")
        self.password = credentials.get("password")
        self.security_token = credentials.get("security_token")  # Optional
        self.organization_id = credentials.get("organization_id")  # Optional
        self.domain = credentials.get("domain", "login")
        self.object_name = credentials.get("object_name", None)
        self.sample_count = credentials.get("sample_count", 10)
        self.region = credentials.get("region")

        # Only username and password are required
        if not all([self.username, self.password]):
            raise ServerException(
                "Missing required Salesforce credentials: username, password. "
                "Note: If accessing from outside trusted networks, you may also need "
                "to provide 'security_token' or append it to the password."
            )
        
        logger.info(f"Loaded Salesforce credentials for user: {self.username}")
        if self.security_token:
            logger.info("Security token provided separately")
        else:
            logger.info("No separate security token - will attempt direct login")
            
        return self.service

    async def connect(self):
        try:
            # Build login parameters, only including non-None values
            login_params = {
                "username": self.username,
                "password": self.password,
                "domain": self.domain
            }
            
            # Add optional parameters if they exist
            if self.security_token:
                login_params["security_token"] = self.security_token
                logger.info("Using separate security token for authentication")
            else:
                logger.info("Attempting authentication without separate security token")
            
            if self.organization_id:
                login_params["organizationId"] = self.organization_id
            
            # Try login - SalesforceLogin returns (session_id, instance)
            try:
                result = SalesforceLogin(**login_params)
                # Handle different return formats from SalesforceLogin
                if isinstance(result, tuple) and len(result) >= 2:
                    session_id = result[0]
                    instance = result[1]
                else:
                    raise ValueError(f"Unexpected return format from SalesforceLogin: {result}")
                
            except Exception as login_error:
                if "LOGIN_MUST_USE_SECURITY_TOKEN" in str(login_error) and not self.security_token:
                    logger.warning("Security token required. Please provide security_token in credentials or append it to password")
                    raise ServerException(
                        "Security token required for Salesforce API access. "
                        "Please either: 1) Add 'security_token' field to credentials, or "
                        "2) Append your security token to the password field. "
                        "Get your security token from Salesforce: Setup > Reset My Security Token"
                    )
                else:
                    # Re-raise the original error
                    raise login_error
            
            # Create Salesforce instance
            self.sf = Salesforce(
                session_id=session_id,
                instance=instance
            )
            
            # Get organization info if not provided
            if not self.organization_id:
                try:
                    org_info = self.sf.query("SELECT Id, Name FROM Organization LIMIT 1")
                    if org_info["records"]:
                        self.organization_id = org_info["records"][0]["Id"]
                        logger.info(f"Auto-detected organization ID: {self.organization_id}")
                    else:
                        self.organization_id = "unknown"
                except Exception as org_error:
                    logger.warning(f"Could not auto-detect organization ID: {org_error}")
                    self.organization_id = "unknown"
            
            logger.info(f"Connected to Salesforce org {self.organization_id} as {self.username}")
            
        except ServerException:
            # Re-raise ServerException as-is
            raise
        except Exception as e:
            logger.error(f"Salesforce connection failed: {e}")
            raise ServerException(f"Salesforce connection failed: {e}")

    async def test_connection(self):
        try:
            await self.get_service_details()
            await self.connect()
            # Simple test query to verify connection
            result = self.sf.query("SELECT Id FROM User LIMIT 1")
            logger.info("Successfully connected to Salesforce.")
            return True, "Connection successful"
        except Exception as e:
            logger.error(f"Salesforce connection test failed: {e}")
            return False, f"Connection failed: {str(e)}"

    async def get_objects(self) -> List[str]:
        if self.object_name:
            return [self.object_name]
        desc = self.sf.describe()
        # Filter out objects that commonly have query restrictions
        restricted_objects = {
            'AppTabMember', 'ColorDefinition', 'ContentDocumentLink', 'ContentFolderItem', 
            'ContentFolderMember', 'EntityParticle', 'FieldDefinition', 'FlowTestView', 
            'FlowVariableView', 'FlowVersionView', 'Vote', 'DataStatistics', 'UserEmailCalendarSync',
            'IconDefinition', 'ListViewChartInstance', 'NetworkUserHistoryRecent', 
            'OutgoingEmail', 'OutgoingEmailRelation', 'UserRecordAccess'
        }
        return [obj["name"] for obj in desc["sobjects"] 
                if obj["queryable"] and obj["name"] not in restricted_objects]

    async def get_object_fields(self, object_name: str) -> List[TableColumn]:
        obj_desc = self.sf.__getattr__(object_name).describe()
        columns = []
        for field in obj_desc["fields"]:
            columns.append(
                TableColumn(
                    column_name=field["name"],
                    data_type=field["type"],
                    index=field.get("unique", False),
                    primary_key=field.get("name") == "Id"
                )
            )
        return columns

    async def infra_scan(self):
        try:
            logger.info(f"Starting infra scan for Salesforce service: {self.service_name}")
            await self.get_service_details()
            await self.connect()
            objects = await self.get_objects()
            region = self.region or "Unknown"
            return {
                "objects": objects, 
                "organization_id": self.organization_id or "unknown",
                "total_objects": len(objects),
                "region": region
            }
        except Exception as e:
            logger.error(f"Infrastructure scan failed: {e}")
            return {"error": str(e)}

    async def deep_scan(self) -> AsyncGenerator[TableMetadata, None]:
        try:
            await self.get_service_details()
            await self.connect()
            objects = await self.get_objects()
            for object_name in objects:
                try:
                    table_metadata = await self.scan_object(object_name)
                    if table_metadata:
                        yield table_metadata
                except Exception as e:
                    logger.error(f"Object scan failed: {object_name}", exc_info=e)
        except Exception as e:
            logger.error(f"Deep scan failed. Service: {self.service_name}", exc_info=e)

    async def scan_object(self, object_name: str) -> Optional[TableMetadata]:
        try:
            columns = await self.get_object_fields(object_name)
            soql = f"SELECT {', '.join([col.column_name for col in columns])} FROM {object_name} LIMIT {self.sample_count}"
            
            try:
                records = self.sf.query_all(soql)["records"]
            except Exception as query_error:
                error_msg = str(query_error).lower()
                if any(keyword in error_msg for keyword in [
                    "malformed_query", "implementation restriction", 
                    "external_object_unsupported_exception", "filter on a reified column",
                    "unsupported", "not supported", "requires a filter"
                ]):
                    logger.warning(f"Skipping {object_name}: Salesforce query restriction - {query_error}")
                    return None
                else:
                    raise query_error
            
            for rec in records:
                rec.pop("attributes", None)
            row_lists = [[rec.get(col.column_name) for col in columns] for rec in records]

            # Skip tables with 0 rows - no need to save empty tables
            if len(records) == 0:
                logger.info(f"Skipping {object_name}: No data found (0 rows)")
                return None

            table_uri = f"salesforce://{self.organization_id or 'unknown'}/{object_name}"
            if self.db.skip_table(table_uri):
                logger.info(f"Skipping object {object_name} - Already processed")
                return None

            create_folder(self.local_data_dir)
            output_file = os.path.join(self.local_data_dir, f"{object_name}_{str(uuid4())}.csv")
            with open(output_file, mode="w", newline="", encoding="utf-8") as file:
                writer = csv.writer(file)
                writer.writerow([col.column_name for col in columns])
                writer.writerows(row_lists)

            table_metadata = TableMetadata(
                service_name=self.service_name,
                service_type=self.service_type,
                service_provider=self.service_provider,
                sub_service=self.sub_service,
                db_type=DBTypes.SALESFORCE.value,
                db_name=self.organization_id or "unknown",
                table_name=object_name,
                table_size=os.path.getsize(output_file),
                no_of_rows=len(records),
                table_uri=table_uri,
                columns=columns,
                local_filepath=output_file,
                details={
                    "organization_id": self.organization_id or "unknown",
                    "domain": self.domain,
                    "region": self.region or "Unknown"
                }
            )
            
            logger.info(f"Successfully scanned Salesforce object: {object_name} ({len(records)} records)")
            logger.info(f"TableMetadata created for {object_name} - will be saved to database by workflow")
            logger.info(f"Table URI: {table_uri}")
            logger.info(f"CSV file created at: {output_file}")
            return table_metadata
            
        except Exception as e:
            logger.error(f"Object scan failed for {object_name}", exc_info=e)
            return None

    def close_connection(self):
        pass 