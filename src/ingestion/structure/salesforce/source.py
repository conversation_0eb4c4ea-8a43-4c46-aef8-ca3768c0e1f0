import csv
import os
from uuid import uuid4
from typing import AsyncGenerator, Optional, List

from simple_salesforce import Salesforce, SalesforceLogin

from src.common.constants import ServiceTypes, DBTypes, ServiceProviders
from src.utils.logger import get_ingestion_logger
from src.modules.repos import DatabaseManager
from src.ingestion.base_source import StructuredSource
from src.ingestion.data_class import TableMetadata, TableColumn
from src.common.config import TEMP_DATA_DIR
from src.utils.helpers import create_folder
from src.utils.exceptions import ServerException
from src.utils.loaders import load_access_controls, load_asset_details
from src.ingestion.data_class import AssetsDetails as AssetDetailsSchema

logger = get_ingestion_logger()

class SalesforceSource(StructuredSource):
    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.SALESFORCE.value, service_name)
        self.db = DatabaseManager()
        self.connection = None
        self.sf = None
        self.service_provider = ServiceProviders.LOCAL
        self.sub_service = DBTypes.SALESFORCE.value
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"

    async def get_service_details(self):
        self.service = await self.db.get_service_by_service_name(self.service_name)
        credentials = self.service.get("credentials", {})
        self.username = credentials.get("username")
        self.password = credentials.get("password")
        self.security_token = credentials.get("security_token")  # Optional
        self.organization_id = credentials.get("organization_id")  # Optional
        self.domain = credentials.get("domain", "login")
        self.object_name = credentials.get("object_name", None)
        self.sample_count = credentials.get("sample_count", 10)
        self.region = credentials.get("region")
        # Additional users who have access (optional)
        self.additional_users = credentials.get("additional_users", [])

        # Only username and password are required
        if not all([self.username, self.password]):
            raise ServerException(
                "Missing required Salesforce credentials: username, password. "
                "Note: If accessing from outside trusted networks, you may also need "
                "to provide 'security_token' or append it to the password."
            )
        
        logger.info(f"Loaded Salesforce credentials for user: {self.username}")
        if self.security_token:
            logger.info("Security token provided separately")
        else:
            logger.info("No separate security token - will attempt direct login")
            
        return self.service

    async def connect(self):
        try:
            # Build login parameters, only including non-None values
            login_params = {
                "username": self.username,
                "password": self.password,
                "domain": self.domain
            }
            
            # Add optional parameters if they exist
            if self.security_token:
                login_params["security_token"] = self.security_token
                logger.info("Using separate security token for authentication")
            else:
                logger.info("Attempting authentication without separate security token")
            
            if self.organization_id:
                login_params["organizationId"] = self.organization_id
            
            # Try login - SalesforceLogin returns (session_id, instance)
            try:
                result = SalesforceLogin(**login_params)
                # Handle different return formats from SalesforceLogin
                if isinstance(result, tuple) and len(result) >= 2:
                    session_id = result[0]
                    instance = result[1]
                else:
                    raise ValueError(f"Unexpected return format from SalesforceLogin: {result}")
                
            except Exception as login_error:
                if "LOGIN_MUST_USE_SECURITY_TOKEN" in str(login_error) and not self.security_token:
                    logger.warning("Security token required. Please provide security_token in credentials or append it to password")
                    raise ServerException(
                        "Security token required for Salesforce API access. "
                        "Please either: 1) Add 'security_token' field to credentials, or "
                        "2) Append your security token to the password field. "
                        "Get your security token from Salesforce: Setup > Reset My Security Token"
                    )
                else:
                    # Re-raise the original error
                    raise login_error
            
            # Create Salesforce instance
            self.sf = Salesforce(
                session_id=session_id,
                instance=instance
            )
            
            # Get organization info if not provided
            if not self.organization_id:
                try:
                    org_info = self.sf.query("SELECT Id, Name FROM Organization LIMIT 1")
                    if org_info["records"]:
                        self.organization_id = org_info["records"][0]["Id"]
                        logger.info(f"Auto-detected organization ID: {self.organization_id}")
                    else:
                        self.organization_id = "unknown"
                except Exception as org_error:
                    logger.warning(f"Could not auto-detect organization ID: {org_error}")
                    self.organization_id = "unknown"
            
            logger.info(f"Connected to Salesforce org {self.organization_id} as {self.username}")
            
        except ServerException:
            # Re-raise ServerException as-is
            raise
        except Exception as e:
            logger.error(f"Salesforce connection failed: {e}")
            raise ServerException(f"Salesforce connection failed: {e}")

    async def test_connection(self):
        try:
            await self.get_service_details()
            await self.connect()
            # Simple test query to verify connection
            result = self.sf.query("SELECT Id FROM User LIMIT 1")
            logger.info("Successfully connected to Salesforce.")
            return True, "Connection successful"
        except Exception as e:
            logger.error(f"Salesforce connection test failed: {e}")
            return False, f"Connection failed: {str(e)}"

    async def get_objects(self) -> List[str]:
        if self.object_name:
            return [self.object_name]
        desc = self.sf.describe()
        # Filter out objects that commonly have query restrictions
        restricted_objects = {
            'AppTabMember', 'ColorDefinition', 'ContentDocumentLink', 'ContentFolderItem', 
            'ContentFolderMember', 'EntityParticle', 'FieldDefinition', 'FlowTestView', 
            'FlowVariableView', 'FlowVersionView', 'Vote', 'DataStatistics', 'UserEmailCalendarSync',
            'IconDefinition', 'ListViewChartInstance', 'NetworkUserHistoryRecent', 
            'OutgoingEmail', 'OutgoingEmailRelation', 'UserRecordAccess'
        }
        return [obj["name"] for obj in desc["sobjects"] 
                if obj["queryable"] and obj["name"] not in restricted_objects]

    async def get_object_fields(self, object_name: str) -> List[TableColumn]:
        obj_desc = self.sf.__getattr__(object_name).describe()
        columns = []
        for field in obj_desc["fields"]:
            columns.append(
                TableColumn(
                    column_name=field["name"],
                    data_type=field["type"],
                    index=field.get("unique", False),
                    primary_key=field.get("name") == "Id"
                )
            )
        return columns

    async def get_salesforce_access_controls(self, object_name: str):
        """Fetch user permissions for Salesforce object"""
        access_controls = []
        try:
            # Simplified approach: Only add the authenticated user with full access
            if self.username:
                logger.info(f"Adding user {self.username} with full access to {object_name}")
                access_controls.append({
                    "user_or_role": self.username,
                    "role": "user",
                    "access": "full"
                })
            
            # If username is not available, add system as fallback
            if not access_controls:
                logger.info(f"No username available, adding system access to {object_name}")
                access_controls.append({
                    "user_or_role": "system",
                    "role": "system",
                    "access": "full"
                })
                
            # Log the access controls being returned
            logger.info(f"Returning {len(access_controls)} access control entries for {object_name}")
            return access_controls
            
        except Exception as e:
            logger.error(f"Failed to fetch Salesforce access controls: {e}")
            # Always ensure we return a valid access control entry even on error
            return [{"user_or_role": "system", "role": "system", "access": "full"}]

    async def get_object_details(self, object_name: str):
        """Get details for a specific Salesforce object"""
        try:
            obj_desc = None
            try:
                # Use a cached descriptor if available to reduce API calls
                if hasattr(self, '_obj_desc_cache') and object_name in self._obj_desc_cache:
                    obj_desc = self._obj_desc_cache[object_name]
                else:
                    obj_desc = self.sf.__getattr__(object_name).describe()
                    
                    # Initialize cache if needed
                    if not hasattr(self, '_obj_desc_cache'):
                        self._obj_desc_cache = {}
                    # Cache the descriptor
                    self._obj_desc_cache[object_name] = obj_desc
            except Exception as desc_error:
                logger.warning(f"Could not get object descriptor for {object_name}: {desc_error}")
                return None
                
            record_count = 0
            size_bytes = 0
            
            # Get record count - try alternative methods if COUNT() query fails
            try:
                # Try standard COUNT() query first
                try:
                    result = self.sf.query(f"SELECT COUNT() FROM {object_name}")
                    record_count = result.get("totalSize", 0)
                except Exception as count_error:
                    # If MALFORMED_QUERY error, try a LIMIT 1 query to see if we can access the object
                    if "MALFORMED_QUERY" in str(count_error) or "filter on a reified column is required" in str(count_error).lower():
                        try:
                            # Try to get just one record to see if table exists and has data
                            sample_result = self.sf.query(f"SELECT Id FROM {object_name} LIMIT 1")
                            if sample_result.get("records"):
                                # If we got a record, assume there's at least 1 record
                                # This won't be accurate but it's better than nothing
                                record_count = 1
                                logger.info(f"Using estimated record count for {object_name} due to query restrictions")
                        except Exception as sample_error:
                            logger.warning(f"Could not get sample record for {object_name}: {sample_error}")
                    else:
                        logger.warning(f"Could not get record count for {object_name}: {count_error}")
            except Exception as count_error:
                logger.warning(f"Could not get record count for {object_name}: {count_error}")
            
            # Estimate size - approximate based on field count
            field_count = len(obj_desc.get("fields", []))
            # Basic size estimation formula (very approximate)
            size_bytes = field_count * max(record_count, 1) * 100  # Average 100 bytes per field, minimum 1 record
            
            return {
                "object_name": object_name,
                "field_count": field_count,
                "record_count": record_count,
                "size_bytes": size_bytes,
                "createable": obj_desc.get("createable", False),
                "updateable": obj_desc.get("updateable", False),
                "queryable": obj_desc.get("queryable", False),
            }
        except Exception as e:
            logger.error(f"Failed to get details for {object_name}: {e}")
            return None

    async def infra_scan(self):
        """Perform infrastructure scan to get Salesforce metadata and persist asset details + access controls."""
        try:
            logger.info(f"Starting infra scan for Salesforce service: {self.service_name} (fixed version)")
            await self.get_service_details()
            await self.connect()
            
            objects = await self.get_objects()
            region = self.region or "Unknown"
            aggregated_metadata = []
            total_size_bytes = 0
            
            # Get organization info
            org_id = self.organization_id or "unknown"
            try:
                org_info = self.sf.query("SELECT Id, Name FROM Organization LIMIT 1")
                org_name = org_info["records"][0]["Name"] if org_info.get("records") else "Unknown"
            except Exception:
                org_name = "Unknown"
            
            # Create assets for each object
            for object_name in objects:
                try:
                    object_details = await self.get_object_details(object_name)
                    if not object_details:
                        continue
                    
                    asset = []
                    record_count = object_details["record_count"]
                    size_bytes = object_details["size_bytes"]
                    
                    # Skip creating assets for objects with 0 records
                    if record_count == 0:
                        logger.info(f"Skipping asset creation for {object_name}: No data (0 records)")
                        continue
                    
                    total_size_bytes += size_bytes
                    
                    # Get access controls for the object first
                    access_permissions = await self.get_salesforce_access_controls(object_name)
                    
                    # Get simplified access controls for the object - just the username or system fallback
                    access_permissions = await self.get_salesforce_access_controls(object_name)
                    
                    # Create asset for this object - similar to MySQL's approach
                    asset_name = f"{object_name}"
                    asset.append(AssetDetailsSchema(
                        asset_name=asset_name,
                        service_provider=self.service_provider,
                        type="database",
                        category="structured",
                        location=region,
                        owner=self.username or "system",
                        security=True,  # Salesforce has built-in security
                        size=size_bytes,
                        count=record_count,
                        access_category="restricted",  # Always restricted since we always have at least one access control
                        service_name=str(self.service_name),
                        steward=self.service.get("data_steward", "admin"),
                    ))
                    
                    # Log asset creation
                    logger.info(f"Creating asset for Salesforce object: {object_name}")
                    load_asset_details(asset)
                    
                    # Create access controls for this object - simplified approach with just one user or system
                    access = [{**perm, "asset_name": asset_name} for perm in access_permissions]
                    logger.info(f"Setting access controls for {asset_name} with {len(access)} permissions")
                    load_access_controls(access)
                    
                    # Add to metadata
                    aggregated_metadata.append({
                        "object_name": object_name,
                        "record_count": record_count,
                        "field_count": object_details["field_count"],
                        "size_bytes": size_bytes,
                        "createable": object_details["createable"],
                        "updateable": object_details["updateable"],
                    })
                    
                    logger.info(f"Created asset for Salesforce object: {object_name} with {record_count} records")
                
                except Exception as obj_error:
                    logger.error(f"Failed to process object {object_name}: {obj_error}")
            
            # No aggregate asset needed
            # Count objects with data for reporting only
            non_empty_object_count = len([m for m in aggregated_metadata if m["record_count"] > 0])
            logger.info(f"Found {non_empty_object_count} Salesforce objects with data")
            logger.info(f"Created assets for {non_empty_object_count} Salesforce objects with data")
            
            # Mark that infra_scan has been run so deep_scan knows
            self._infra_scan_run = True
            
            logger.info(f"✅ Infra scan completed for {len(objects)} Salesforce objects with {non_empty_object_count} assets created")
            return {
                "objects": objects,
                "organization_id": org_id,
                "organization_name": org_name,
                "total_objects": len(objects),
                "region": region,
                "metadata": aggregated_metadata,
            }
            
        except Exception as e:
            logger.error(f"Infrastructure scan failed: {e}")
            return {"error": str(e)}

    async def deep_scan(self) -> AsyncGenerator[TableMetadata, None]:
        """Perform deep scan to extract object schema and sample data.
           Asset details are now handled in infra_scan, not here."""
        try:
            logger.info("Starting deep scan for Salesforce. Asset creation should be handled by infra_scan.")
            await self.get_service_details()
            await self.connect()
            
            # Call infra_scan first to ensure assets are created - this is a workaround
            # since the workflow system doesn't automatically call infra_scan first
            if not hasattr(self, '_infra_scan_run') or not self._infra_scan_run:
                logger.info("Automatically calling infra_scan before deep_scan to ensure assets are created")
                await self.infra_scan()
                # Don't automatically call infra_scan here as it may be a deliberate choice
            
            # Proceed with the deep scan (assets are created separately in infra_scan)
            objects = await self.get_objects()
            
            for object_name in objects:
                try:
                    table_metadata = await self.scan_object(object_name)
                    if table_metadata:
                        yield table_metadata
                except Exception as e:
                    logger.error(f"Object scan failed: {object_name}", exc_info=e)
                    
        except Exception as e:
            logger.error(f"Deep scan failed. Service: {self.service_name}", exc_info=e)
        finally:
            # No need to generate asset details and access controls here
            # as they are now handled in the infra_scan method
            logger.info(f"Deep scan completed for Salesforce service: {self.service_name}")

    async def scan_object(self, object_name: str) -> Optional[TableMetadata]:
        try:
            columns = await self.get_object_fields(object_name)
            soql = f"SELECT {', '.join([col.column_name for col in columns])} FROM {object_name} LIMIT {self.sample_count}"
            
            try:
                records = self.sf.query_all(soql)["records"]
            except Exception as query_error:
                error_msg = str(query_error).lower()
                if any(keyword in error_msg for keyword in [
                    "malformed_query", "implementation restriction", 
                    "external_object_unsupported_exception", "filter on a reified column",
                    "unsupported", "not supported", "requires a filter"
                ]):
                    logger.warning(f"Skipping {object_name}: Salesforce query restriction - {query_error}")
                    return None
                else:
                    raise query_error
            
            for rec in records:
                rec.pop("attributes", None)
            row_lists = [[rec.get(col.column_name) for col in columns] for rec in records]

            # Skip tables with 0 rows - no need to save empty tables
            if len(records) == 0:
                logger.info(f"Skipping {object_name}: No data found (0 rows)")
                return None

            table_uri = f"salesforce://{self.organization_id or 'unknown'}/{object_name}"
            if self.db.skip_table(table_uri):
                logger.info(f"Skipping object {object_name} - Already processed")
                return None

            create_folder(self.local_data_dir)
            output_file = os.path.join(self.local_data_dir, f"{object_name}_{str(uuid4())}.csv")
            with open(output_file, mode="w", newline="", encoding="utf-8") as file:
                writer = csv.writer(file)
                writer.writerow([col.column_name for col in columns])
                writer.writerows(row_lists)

            file_size = os.path.getsize(output_file)
            table_metadata = TableMetadata(
                service_name=self.service_name,
                service_type=self.service_type,
                service_provider=self.service_provider,
                sub_service=self.sub_service,
                db_type=DBTypes.SALESFORCE.value,
                db_name=self.organization_id or "unknown",
                table_name=object_name,
                table_size=file_size,
                no_of_rows=len(records),
                table_uri=table_uri,
                columns=columns,
                local_filepath=output_file,
                details={
                    "organization_id": self.organization_id or "unknown",
                    "domain": self.domain,
                    "region": self.region or "Unknown"
                }
            )
            
            logger.info(f"Successfully scanned Salesforce object: {object_name} ({len(records)} records)")
            logger.info(f"Table size: {file_size} bytes ({round(file_size / 1024 / 1024, 2)} MB)")
            logger.info(f"Table URI: {table_uri}")
            logger.info(f"CSV file created at: {output_file}")
            return table_metadata
            
        except Exception as e:
            logger.error(f"Object scan failed for {object_name}", exc_info=e)
            return None
                
        except Exception as e:
            logger.error(f"Object scan failed for {object_name}", exc_info=e)
            return None

    def close_connection(self):
        pass 