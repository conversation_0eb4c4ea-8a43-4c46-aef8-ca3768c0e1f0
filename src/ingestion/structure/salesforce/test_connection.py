from simple_salesforce import Salesforce, SalesforceLogin
import logging
from src.utils.exceptions import ClientException, ServerException

logger = logging.getLogger("Unstructure")

async def test_salesforce_connection(data: dict) -> dict:
    creds = data.get("credentials", {})
    try:
        username = creds.get("username")
        password = creds.get("password")
        security_token = creds.get("security_token")
        organization_id = creds.get("organization_id")
        domain = creds.get("domain", "login")
        if not all([username, password, organization_id]):
            logger.warning("Missing required Salesforce credentials")
            raise ClientException(
                "Missing required credentials: username, password, organization_id",
                code=400,
                excep=None
            )
        logger.info(f"Testing Salesforce connection for org: {organization_id}")
        try:
            session_id, instance, _ = SalesforceLogin(
                username=username,
                password=password,
                security_token=security_token,
                organizationId=organization_id,
                domain=domain
            )
            sf = Salesforce(session_id=session_id, instance=instance)
        except Exception as e:
            logger.warning(f"Salesforce authentication/connection failed: {str(e)}")
            raise ClientException(
                "Invalid Salesforce credentials or connection failed", code=401, excep=e
            )
        try:
            sf.query("SELECT Id FROM User LIMIT 1")
            desc = sf.describe()
            objects = [obj["name"] for obj in desc["sobjects"] if obj["queryable"]]
            logger.info("Salesforce connection test successful and read access verified.")
        except Exception as e:
            logger.warning(f"Salesforce read access failed: {str(e)}")
            raise ClientException(
                "Salesforce read access failed (check read-only permissions)", code=403, excep=e
            )
        return {
            "status": True,
            "message": "Salesforce connection successful and read access verified."
        }
    except ClientException:
        raise
    except Exception as e:
        logger.exception("Unexpected error during Salesforce connection test")
        raise ServerException("Salesforce test failed", excep=e) 