import aiohttp
import logging
from src.utils.exceptions import ClientException, ServerException

logger = logging.getLogger("Unstructure")


async def test_keka_connection(data: dict) -> dict:
    creds = data.get("credentials", {})

    try:
        logger.info(f"Attempting to connect to Keka identity server: {creds.get('identity_url')}")

        token_data = {
            "client_id": creds.get("client_id"),
            "client_secret": creds.get("client_secret"),
            "grant_type": "client_credentials",
            "scope": creds.get("scope", "read")
        }

        headers = {"x-api-key": creds.get("api_key")}

        async with aiohttp.ClientSession() as session:
            async with session.post(creds["identity_url"], data=token_data, headers=headers) as resp:
                if resp.status != 200:
                    msg = await resp.text()
                    logger.warning(f"Failed to authenticate with <PERSON>ka: {msg}")
                    raise ClientException(
                        f"Invalid Keka credentials or API key: {resp.status}",
                        code=401,
                        excep=Exception(msg)
                    )

                response_data = await resp.json()
                access_token = response_data.get("access_token")

                if not access_token:
                    raise ServerException("Keka token not found in response.")

        logger.info("Keka HR connection test successful.")
        return {"status": True, "message": "Keka connection successful"}

    except ClientException as ce:
        raise ce

    except Exception as e:
        logger.exception("Unexpected error during Keka connection test")
        raise ServerException("Keka connection test failed", excep=e)