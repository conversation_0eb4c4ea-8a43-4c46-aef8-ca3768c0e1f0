import os
import csv
import aiohttp
from uuid import uuid4
from typing import Dict, AsyncGenerator, Optional
from datetime import datetime
from src.utils.logger import get_ingestion_logger
from src.common.constants import ServiceTypes, ServiceProviders, DBTypes
from src.ingestion.base_source import StructuredSource
from src.modules.repos import DatabaseManager
from src.ingestion.data_class import TableMetadata, TableColumn
from src.common.config import TEMP_DATA_DIR
from src.utils.helpers import create_folder
from src.utils.exceptions import ServerException

logger = get_ingestion_logger()

class KekaSource(StructuredSource):
    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.KEKA.value, service_name)
        self.db = DatabaseManager()
        self.token: Optional[str] = None
        self.service_provider = ServiceProviders.LOCAL
        self.sub_service = DBTypes.KEKA.value
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"
        create_folder(self.local_data_dir)
        self.scopes = []  

    async def get_service_details(self):
        self.service = await self.db.get_service_by_service_name(self.service_name)
        credentials = self.service.get("credentials", {})
        self.client_id = credentials.get("client_id")
        self.client_secret = credentials.get("client_secret")
        self.api_key = credentials.get("api_key")
        self.scope = credentials.get("scope", "default_scope")
        self.token_url = credentials.get("token_url", "https://login.keka.com/connect/token")  
        self.base_url = credentials.get("base_url", "https://company.keka.com/api/v1/") 
        self.attendance_url = credentials.get("attendance_url", "https://assigned_server.a.keka.com/v1/logs")  
        self.sample_count = credentials.get("sample_count", 10)
        self.scopes = credentials.get("scopes", [])

        if not all([self.client_id, self.client_secret, self.api_key, self.token_url, self.base_url]):
            raise ServerException("Missing required credentials for Keka connector")

        logger.info(f"Keka connector initialized with base URL: {self.base_url}")
        return self.service

    async def connect(self):
        try:
            if self.token: 
                logger.info("Reusing existing token")
                return

            data = {
                "client_id": self.client_id,
                "client_secret": self.client_secret,
                "grant_type": "client_credentials",
                "scope": self.scope,
            }
            headers = {"x-api-key": self.api_key}

            async with aiohttp.ClientSession() as session:
                async with session.post(self.token_url, data=data, headers=headers) as resp:
                    if resp.status != 200:
                        raise ServerException(f"Failed to get token: {resp.status} {await resp.text()}")
                    response_data = await resp.json()
                    self.token = response_data.get("access_token")
                    logger.info("Successfully obtained Keka OAuth token")
        except Exception as e:
            raise ServerException("Keka HR OAuth token generation failed") from e

    async def refresh_token(self):
        logger.info("Refreshing expired token...")
        await self.connect()

    async def fetch_scopes(self) -> list:
        try:
            url = f"{self.base_url}/v1/scopes" 
            headers = {"Authorization": f"Bearer {self.token}", "x-api-key": self.api_key}

            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as resp:
                    if resp.status == 200:
                        scopes = await resp.json()
                        logger.info(f"Fetched {len(scopes)} scopes dynamically")
                        return scopes
                    elif resp.status == 401: 
                        logger.warning("Token expired. Refreshing token...")
                        await self.refresh_token()
                        return await self.fetch_scopes()
                    else:
                        logger.warning(f"Failed to fetch scopes: {resp.status} {await resp.text()}")
                        return []
        except Exception as e:
            logger.error(f"Error fetching scopes: {str(e)}", exc_info=True)
            return []

    async def deep_scan(self) -> AsyncGenerator[TableMetadata, None]:
        try:
            await self.get_service_details()
            await self.connect()

            if not self.scopes:
                self.scopes = await self.fetch_scopes()

            if not self.scopes:
                logger.warning("No scopes available for Keka connector")
                return

            for scope in self.scopes:
                name = scope.get("name")
                path = scope.get("path")
                if not name or not path:
                    logger.warning(f"Invalid scope data: {scope}")
                    continue

                url = f"{self.base_url}/{path}"
                headers = {"Authorization": f"Bearer {self.token}", "x-api-key": self.api_key}
                logger.info(f"Fetching data from scope: {url}")

                async with aiohttp.ClientSession() as session:
                    retries = 3  
                    for attempt in range(retries):
                        try:
                            async with session.get(url, headers=headers) as resp:
                                if resp.status == 200:
                                    data = await resp.json()
                                    if not data:
                                        logger.warning(f"No data returned for {name}")
                                        continue
                                    yield await self.generate_metadata(name, data)
                                    break
                                elif resp.status == 401:  
                                    logger.warning("Token expired. Refreshing token...")
                                    await self.refresh_token()
                                else:
                                    logger.warning(f"Failed to fetch {name}: {resp.status} {await resp.text()}")
                        except Exception as e:
                            logger.error(f"Error fetching data for {name}: {str(e)}")
                            if attempt == retries - 1:
                                logger.error(f"Max retries reached for {name}. Skipping...")
        except Exception as e:
            logger.error(f"Keka deep scan failed: {str(e)}", exc_info=True)
        finally:
            self.token = None

    async def generate_metadata(self, name: str, data: list) -> Optional[TableMetadata]:
        rows = data[: self.sample_count]
        if not rows:
            return None

        def is_unique(rows, column_name):
            values = [row[column_name] for row in rows]
            return len(values) == len(set(values))

        def is_high_cardinality(rows, column_name):
            values = [row[column_name] for row in rows]
            return len(set(values)) > len(values) * 0.5  

        columns = [
            TableColumn(
                column_name=key,
                data_type=type(value).__name__,
                primary_key=is_unique(rows, key),
                index=is_high_cardinality(rows, key)
            )
            for key, value in rows[0].items()
        ]

        local_file = os.path.join(self.local_data_dir, f"{name}_{str(uuid4())}.csv")
        with open(local_file, "w", newline="", encoding="utf-8") as f:
            writer = csv.DictWriter(f, fieldnames=rows[0].keys())
            writer.writeheader()
            writer.writerows(rows)

        return TableMetadata(
            service_name=self.service_name,
            service_type=self.service_type,
            service_provider=ServiceProviders.LOCAL,
            sub_service=self.sub_service,
            db_type=self.sub_service,
            db_name=DBTypes.KEKA.value,
            table_name=name,
            table_uri=f"keka://{name}",
            table_size=float(os.path.getsize(local_file)),
            no_of_rows=len(rows),
            columns=columns,
            local_filepath=local_file,
            details={
                "url": self.base_url,
                "scope": name,
                "timestamp": datetime.now().isoformat()
            },
        )