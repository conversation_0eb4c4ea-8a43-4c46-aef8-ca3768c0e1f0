import asyncpg
import logging
from src.utils.exceptions import ClientException, ServerException

logger = logging.getLogger("Unstructure")


async def test_postgres_connection(data: dict) -> dict:
    creds = data.get("credentials", {})
    try:
        logger.info(f"Attempting PostgreSQL connection to host: {creds.get('host')}")

        conn = await asyncpg.connect(
            host=creds["host"],
            port=creds["port"],
            user=creds["user"],
            password=creds["password"],
            database=creds["dbname"]
        )

        await conn.fetch("SELECT 1")
        await conn.close()

        logger.info(" PostgreSQL connection test successful.")
        return {
            "status": True,
            "message": "Postgres connection successful"
        }

    except asyncpg.InvalidAuthorizationSpecificationError as e:
        logger.warning(f"Invalid Postgres credentials: {str(e)}")
        raise ClientException("Invalid Postgres credentials", code=401, excep=e)

    except Exception as e:
        logger.exception("Unexpected error during PostgreSQL connection test")
        raise ServerException("Postgres test failed", excep=e)
