import os
import csv
from uuid import uuid4
import boto3
from botocore.config import Config
import psycopg2
from psycopg2 import sql
from typing import AsyncGenerator, List, Optional, Dict, Any
from collections import defaultdict
from src.common.constants import ServiceTypes, DBTypes, AuthTypes, ServiceProviders
from src.utils.logger import get_ingestion_logger
from src.utils.loaders import load_access_controls,load_asset_details
from src.modules.repos import DatabaseManager
from src.ingestion.base_source import StructuredSource
from src.ingestion.data_class import TableMetadata, TableColumn
from src.common.config import TEMP_DATA_DIR
from src.utils.helpers import create_folder
from src.ingestion.data_class import AssetsDetails as AssetDetailsSchema
from src.utils.exceptions import ServerException, CustomBaseException
from src.ingestion.structure.postgres.connection import get_region_from_host
import ipaddress
import socket
logger = get_ingestion_logger()


class PostgresSource(StructuredSource):
    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.POSTGRES.value, service_name)
        self.db = DatabaseManager()
        self.connection = None
        self.cursor = None
        self.service_provider = ServiceProviders.LOCAL
        self.sub_service = DBTypes.POSTGRES.value
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"

    async def get_service_details(self):
        """Gets service details"""
        self.service = await self.db.get_service_by_service_name(self.service_name)
        credentials = self.service.get("credentials", {})
        self.host = credentials.get("host")
        self.port = credentials.get("port", 5432)
        self.dbname = credentials.get("dbname")
        self.user = credentials.get("user")
        self.auth_type = credentials.get("auth_type")
        self.password = credentials.get("password")
        self.sample_count = credentials.get("sample_count", 10)
        self.databases = credentials.get("databases", [])
        self.region = credentials.get("region")
        self.service_steward = self.service.get("data_steward")
        self.service_owner = self.service.get("data_owner")
        return self.service

    def generate_iam_token(self):
        if not self.region or not self.host or not self.user:
            raise ServerException("Missing required fields for IAM authentication.")

        try:
            logger.info("Generating RDS IAM token using EC2 IAM role...")

            boto_config = Config(
                retries={"max_attempts": 3, "mode": "standard"},
                connect_timeout=2,
                read_timeout=2,
            )

            rds_client = boto3.client(
                "rds", region_name=self.region, config=boto_config
            )
            token = rds_client.generate_db_auth_token(
                DBHostname=self.host,
                Port=self.port,
                DBUsername=self.user,
            )
            logger.info("IAM token successfully generated.")
            return token
        except Exception as e:
            if "Unable to locate credentials" in str(e):
                raise ServerException(
                    "IAM role is not attached to the EC2 instance.", excep=e
                )
            raise ServerException("Failed to generate IAM token.", excep=e)

    def connect_using_iam(self):
        """Connect to Postgres using IAM token"""
        try:
            token = self.generate_iam_token()
            self.connection = psycopg2.connect(
                host=self.host,
                port=self.port,
                dbname=self.dbname,
                user=self.user,
                password=token,
                sslmode="require",
            )
            self.cursor = self.connection.cursor()
            logger.info("Connected to Postgres using IAM authentication.")
        except Exception as e:
            raise ServerException("IAM-based Postgres connection failed.", excep=e)

    def connect_using_creds(self):
        """Connect to Postgres using Credentials"""
        try:
            self.connection = psycopg2.connect(
                host=self.host,
                port=self.port,
                dbname=self.dbname,
                user=self.user,
                password=self.password,
            )
            self.cursor = self.connection.cursor()
            logger.info(
                f"Connected using credentials to Postgres database: {self.dbname}"
            )
        except Exception as e:
            raise ServerException("Password Based Postgres connection failed.", excep=e)

    async def connect(self):
        """Creates connection"""
        if self.auth_type and self.auth_type.lower() == AuthTypes.BASIC.value:
            self.connect_using_creds()
        elif self.auth_type and self.auth_type.lower() == AuthTypes.IAM.value:
            self.connect_using_iam()
        else:
            raise ServerException("Invalid Authentication Type.")

    async def test_connection(self) -> dict:
        """Test connection by verifying database access"""
        try:
            await self.connect()
            self.cursor.execute("SELECT 1;")
            return {"status": "success", "message": "Connection successful."}
        except Exception as e:
            return {"status": "failed", "message": str(e)}
        finally:
            self.close_connection()

    async def get_databases(self):
        """Retrieve all non-template databases."""
        self.cursor.execute(
            "SELECT datname FROM pg_database WHERE datistemplate = false;"
        )
        return [row[0] for row in self.cursor.fetchall()]

    async def get_all_schemas(self):
        """Retrieve all schemas excluding system schemas"""
        self.cursor.execute("""
            SELECT schema_name 
            FROM information_schema.schemata 
            WHERE schema_name NOT IN ('pg_catalog', 'information_schema', 'pg_toast')
            AND schema_name NOT LIKE 'pg_temp_%'
            AND schema_name NOT LIKE 'pg_toast_temp_%'
            ORDER BY schema_name;
        """)
        schemas = [row[0] for row in self.cursor.fetchall()]
        logger.info(f"Found {len(schemas)} schemas in database '{self.dbname}': {schemas}")
        return schemas

    async def switch_database(self, db_name):
        """Switch to a different database by reconnecting."""
        self.close_connection()
        self.dbname = db_name
        await self.connect()

    async def get_tables_from_all_schemas(self):
        """Retrieve all tables from all non-system schemas with their schema names"""
        self.cursor.execute("""
            SELECT schemaname, tablename 
            FROM pg_tables 
            WHERE schemaname NOT IN ('pg_catalog', 'information_schema')
            ORDER BY schemaname, tablename;
        """)
        tables_with_schema = self.cursor.fetchall()
        logger.info(f"Found {len(tables_with_schema)} tables across all schemas in database '{self.dbname}'")
        
        # Group by schema for logging
        schema_table_count = defaultdict(int)
        for schema, table in tables_with_schema:
            schema_table_count[schema] += 1
        
        for schema, count in schema_table_count.items():
            logger.info(f"  Schema '{schema}': {count} tables")
        
        return tables_with_schema

    async def get_tables(self, schema=None):
        """Retrieve all tables in the specified schema or all schemas"""
        if schema:
            self.cursor.execute(
                "SELECT tablename FROM pg_tables WHERE schemaname=%s ORDER BY tablename;",
                (schema,)
            )
            tables = [row[0] for row in self.cursor.fetchall()]
            logger.info(f"Found {len(tables)} tables in schema '{schema}' of database '{self.dbname}'")
        else:
            # Get tables from all non-system schemas
            tables_with_schema = await self.get_tables_from_all_schemas()
            tables = tables_with_schema
        
        return tables

    async def table_exists(self, table, schema):
        """Check if a table exists in the specified schema"""
        self.cursor.execute(
            sql.SQL(
                """
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = %s
                    AND table_name = %s
                );
                """
            ),
            [schema, table],
        )
        exists = self.cursor.fetchone()[0]
        if exists:
            logger.info(f"Table {schema}.{table} exists")
        else:
            logger.warning(f"Table {schema}.{table} does NOT exist")
        return exists

    async def check_postgres_network_config(self) -> Dict[str, Any]:
        """Check PostgreSQL network configuration for non-RDS instances"""

        network_config = {
            "listen_addresses": None,
            "pg_hba_rules": [],
            "ssl_enabled": False,
            "port": self.port,
            "host_reachable_externally": False
        }

        try:
            # Get listen_addresses setting
            self.cursor.execute("SHOW listen_addresses;")
            result = self.cursor.fetchone()
            if result:
                network_config["listen_addresses"] = result[0]

            # Get SSL status
            network_config["ssl_enabled"] = await self.get_encryption_status()

            # Check pg_hba.conf rules (requires superuser privileges)
            try:
                try:
                    # PG 15+ (auth_method column)
                    self.cursor.execute("""
                        SELECT type, database, user_name, address, auth_method
                        FROM pg_hba_file_rules
                        WHERE type IN ('host', 'hostssl', 'hostnossl')
                        ORDER BY line_number;
                    """)
                    hba_rules = self.cursor.fetchall()
                    for rule in hba_rules:
                        network_config["pg_hba_rules"].append({
                            "type": rule[0],
                            "database": rule[1],
                            "user": rule[2],
                            "address": rule[3],
                            "auth_method": rule[4]
                        })

                except Exception as e:
                    # Rollback and try PG < 15 (method column)
                    if self.connection:
                        self.connection.rollback()
                    self.cursor.execute("""
                        SELECT type, database, user_name, address, method
                        FROM pg_hba_file_rules
                        WHERE type IN ('host', 'hostssl', 'hostnossl')
                        ORDER BY line_number;
                    """)
                    hba_rules = self.cursor.fetchall()
                    for rule in hba_rules:
                        network_config["pg_hba_rules"].append({
                            "type": rule[0],
                            "database": rule[1],
                            "user": rule[2],
                            "address": rule[3],
                            "auth_method": rule[4]
                        })

            except Exception as e:
                if self.connection:
                    self.connection.rollback()

            # Check if host is externally reachable
            if self.host:
                try:
                    ip = socket.gethostbyname(self.host)
                    ip_obj = ipaddress.IPv4Address(ip)
                    if ip_obj.is_global:
                        network_config["host_reachable_externally"] = True
                    else:
                        logger.info(f"PostgreSQL host {self.host} resolves to private IP: {ip}")
                except Exception as e:
                    logger.warning(f"Failed to resolve PostgreSQL host {self.host}: {e}")

        except Exception as e:
            if self.connection:
                self.connection.rollback()
            logger.error(f"Error checking PostgreSQL network configuration: {e}")

        return network_config

    async def check_rds_public_accessibility(self, instance_info: Dict[str, Any]) -> Dict[str, Any]:
        """Check RDS PostgreSQL instance for public accessibility"""
        
        accessibility_info = {
            "publicly_accessible": instance_info.get("publicly_accessible", False),
            "vpc_security_groups": [],
            "subnet_group_public": False,
            "endpoint_resolvable": False,
            "public_ip_detected": False
        }
        
        try:
            # Check VPC Security Groups
            vpc_sgs = instance_info.get("vpc_security_groups", [])
            
            for sg in vpc_sgs:
                sg_id = sg.get("VpcSecurityGroupId")
                if sg_id:
                    accessibility_info["vpc_security_groups"].append({
                        "group_id": sg_id,
                        "status": sg.get("Status", "unknown")
                    })
            
            # Check if endpoint resolves to public IP
            host = instance_info.get("host")
            if host:
                try:
                    ip = socket.gethostbyname(host)
                    accessibility_info["endpoint_resolvable"] = True
                    
                    # Check if IP is public
                    ip_obj = ipaddress.IPv4Address(ip)
                    if ip_obj.is_global:
                        accessibility_info["public_ip_detected"] = True
                    else:
                        logger.info(f"Private IP detected for RDS instance {instance_info.get('instance_id')}: {ip}")
                        
                except Exception as e:
                    logger.warning(f"Failed to resolve endpoint {host}: {e}")
            
            # Check subnet group
            subnet_group = instance_info.get("db_subnet_group", {})
            if subnet_group:
                subnets = subnet_group.get("Subnets", [])
                for subnet in subnets:
                    az = subnet.get("AvailabilityZone", {}).get("Name", "unknown")
                    logger.info(f"RDS instance {instance_info.get('instance_id')} uses subnet in AZ: {az}")
            
        except Exception as e:
            logger.error(f"Error checking RDS public accessibility: {e}")
        
        return accessibility_info

    async def get_database_access_controls(self):
        try:
            query = """
            SELECT
                grantee,
                privilege_type
            FROM
                information_schema.role_table_grants
            WHERE table_catalog = current_database();
            """
            self.cursor.execute(query)
            rows = self.cursor.fetchall()

            # Exclude system/service accounts and unwanted principals
            EXCLUDED_PRINCIPALS = {
                "postgres", "rdsadmin", "pg_monitor", "pg_read_all_settings",
                "pg_read_all_stats", "pg_stat_scan_tables", "pg_signal_backend",
                "pg_read_server_files", "pg_write_server_files", "pg_execute_server_program",
                "pg_database_owner", "pg_read_all_data", "pg_write_all_data",
                "pg_backup", "pg_restore", "pg_policy", "pg_authid", "pg_auth_members",
                "pg_replication", "pg_wal", "pg_signal_backend", "pg_read_all_stats",
                "pg_read_all_settings", "pg_stat_scan_tables", "pg_monitor", "pg_read_server_files",
                "pg_write_server_files", "pg_execute_server_program", "rds_superuser"
            }
            
            privileges_by_grantee = defaultdict(set)
            for row in rows:
                grantee = row[0]
                privilege = row[1]
                if grantee in EXCLUDED_PRINCIPALS:
                    continue
                privileges_by_grantee[grantee].add(privilege)

            self.cursor.execute("SELECT rolname FROM pg_roles WHERE rolcanlogin = false;")
            role_names = {row[0] for row in self.cursor.fetchall()}

            access_controls = []
            for grantee, privileges in privileges_by_grantee.items():
                if grantee.upper() == "PUBLIC":
                    continue
                elif grantee in role_names:
                    role_type = "role"
                else:
                    role_type = "user"

                access_controls.append({
                    "user_or_role": grantee,
                    "role": role_type,
                    "access": self._map_privileges_to_access(privileges)
                })

            return access_controls

        except Exception as e:
            logger.error(f"Failed to get PostgreSQL access controls: {e}")
            return []

    def _map_privileges_to_access(self, privileges: set) -> str:
        read_privs = {"SELECT"}
        write_privs = {"INSERT", "UPDATE", "DELETE"}
        full_privs = read_privs | write_privs | {"TRUNCATE", "REFERENCES", "TRIGGER"}

        if privileges >= full_privs:
            return "full"
        elif privileges & write_privs and privileges & read_privs:
            return "write"
        elif privileges & read_privs:
            return "read"
        else:
            return "none"

    async def get_encryption_status(self):
        try:
            self.cursor.execute("SHOW ssl;")
            result = self.cursor.fetchone()
            if result and result[0].lower() == "on":
                return True
            else:
                return False
        except Exception as e:
            logger.warning(f"Failed to check SSL encryption status: {e}")
            return False

    async def find_primary_keys(self, table_name: str, table_schema: str):
        query = """
        SELECT a.attname
        FROM   pg_index i
        JOIN   pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
        WHERE  i.indrelid = %s::regclass AND i.indisprimary;
        """
        pk_keys = []
        try:
            self.cursor.execute(query, (f'"{table_schema}"."{table_name}"',))
            pk_keys = [row[0] for row in self.cursor.fetchall()]
        except Exception as e:
            logger.error(
                f"Failed to fetch primary key details for table {table_schema}.{table_name}: {e}"
            )

        return pk_keys

    async def find_matching_rows_from_table(
        self, table_schema: str, table_name: str, column_name: str, values: List[str]
    ):
        matched_rows = []
        try:
            # Get data type of the column
            self.cursor.execute(
                """
                SELECT data_type
                FROM information_schema.columns
                WHERE table_schema = %s AND table_name = %s AND column_name = %s
            """,
                (table_schema, table_name, column_name),
            )
            result = self.cursor.fetchone()
            if not result:
                logger.info(
                    f"Column {column_name} not found in {table_schema}.{table_name}"
                )
                return []
            data_type = result[0]

            if data_type in ("character varying", "text"):
                patterns = [f"%{v}%" for v in values]
                query = f"""
                    SELECT * FROM "{table_schema}"."{table_name}"
                    WHERE "{column_name}" ILIKE ANY (%s)
                    LIMIT 20;
                """
                self.cursor.execute(query, (patterns,))
            else:
                casted_values = []
                for v in values:
                    try:
                        casted_values.append(int(v))
                    except ValueError:
                        continue
                if not casted_values:
                    return []

                placeholders = ",".join(["%s"] * len(casted_values))
                query = f"""
                    SELECT * FROM "{table_schema}"."{table_name}"
                    WHERE "{column_name}" IN ({placeholders})
                    LIMIT 20;
                """
                self.cursor.execute(query, casted_values)

            rows = self.cursor.fetchall()
            if not rows:
                return []

            # Get column names
            self.cursor.execute(
                """
                SELECT column_name
                FROM information_schema.columns
                WHERE table_schema = %s AND table_name = %s
                ORDER BY ordinal_position
            """,
                (table_schema, table_name),
            )
            col_names = [col[0] for col in self.cursor.fetchall()]

            for row in rows:
                record = {col_names[i]: row[i] for i in range(len(col_names))}
                matched_rows.append(record)

        except Exception as e:
            logger.error(f"Error fetching from {table_schema}.{table_name}: {e}")

        return matched_rows

    async def find_foreign_key_tables(self, pk_table: str, pk_column):
        query = """
        SELECT
            kcu.table_schema,
            kcu.table_name,
            kcu.column_name
        FROM
            information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
            ON tc.constraint_name = kcu.constraint_name
            AND tc.constraint_schema = kcu.constraint_schema
        JOIN information_schema.referential_constraints AS rc
            ON tc.constraint_name = rc.constraint_name
            AND tc.constraint_schema = rc.constraint_schema
        JOIN information_schema.constraint_column_usage AS ccu
            ON rc.unique_constraint_name = ccu.constraint_name
            AND rc.constraint_schema = ccu.constraint_schema
        WHERE
            tc.constraint_type = 'FOREIGN KEY'
            AND ccu.table_name = %s
            AND ccu.column_name = %s;
        """
        fk_tables = []
        try:
            self.cursor.execute(query, (pk_table, pk_column))
            for table in self.cursor.fetchall():
                fk_tables.append(
                    {
                        "table_schema": table[0],
                        "table_name": table[1],
                        "column_name": table[2],
                    }
                )
        except Exception as e:
            logger.error(
                f"Failed to fetch foreign key details from {pk_table}: {e}"
            )

        return fk_tables

    async def fetch_related_data_recursively(self, matching_tables: List[dict]) -> List:
        results = []
        visited_tables = set()

        try:
            while len(matching_tables) > 0:
                next_tables = []
                for pii_match in matching_tables:
                    table_schema = pii_match.get("table_schema")
                    table_name = pii_match.get("table_name")
                    column_name = pii_match.get("column_name")
                    values = pii_match.get("values")
                    if table_name in visited_tables:
                        continue

                    matching_rows = await self.find_matching_rows_from_table(
                        table_schema, table_name, column_name, values
                    )

                    if len(matching_rows) == 0:
                        continue

                    results.append(
                        {
                            "service_name": self.service_name,
                            "service_provider": self.service_provider,
                            "sub_service": self.sub_service,
                            "db_name": self.dbname,
                            "table_schema": table_schema,
                            "table_name": table_name,
                            "column_name": column_name,
                            "matching_rows": matching_rows,
                        }
                    )
                    visited_tables.add(table_name)

                    pk_keys = await self.find_primary_keys(table_name, table_schema)
                    for pk_key in pk_keys:
                        values = list(set([m.get(pk_key) for m in matching_rows]))
                        fk_tables = await self.find_foreign_key_tables(
                            table_name, pk_key
                        )
                        for fk_table in fk_tables:
                            fk_table_schema = fk_table.get("table_schema")
                            fk_table_name = fk_table.get("table_name")
                            fk_column_name = fk_table.get("column_name")
                            next_tables.append(
                                {
                                    "table_schema": fk_table_schema,
                                    "table_name": fk_table_name,
                                    "column_name": fk_column_name,
                                    "values": values,
                                }
                            )

                matching_tables = next_tables

        except CustomBaseException:
            pass

        except Exception as e:
            logger.error("Error while fetching user related data", exc_info=e)

        return results

    async def get_database_roles_and_permissions(self, db_name: str) -> Dict[str, Any]:
        """Get detailed role and permission information for a database"""
        
        role_info = {
            "database_owner": None,
            "public_access": False,
            "superusers": [],
            "role_memberships": [],
            "database_permissions": []
        }
        
        try:
            # Get database owner
            self.cursor.execute("""
                SELECT d.datname, pg_catalog.pg_get_userbyid(d.datdba) as owner
                FROM pg_catalog.pg_database d
                WHERE d.datname = %s;
            """, (db_name,))
            
            result = self.cursor.fetchone()
            if result:
                role_info["database_owner"] = result[1]
            
            # Get superusers
            self.cursor.execute("""
                SELECT rolname FROM pg_roles WHERE rolsuper = true;
            """)
            superusers = [row[0] for row in self.cursor.fetchall()]
            role_info["superusers"] = superusers
            
            # Check for PUBLIC role permissions
            self.cursor.execute("""
                SELECT has_database_privilege('public', %s, 'CONNECT') as can_connect,
                       has_database_privilege('public', %s, 'CREATE') as can_create;
            """, (db_name, db_name))
            
            result = self.cursor.fetchone()
            if result and result[0]:
                role_info["public_access"] = True
            
            # Get role memberships and permissions
            self.cursor.execute("""
                SELECT r.rolname, r.rolsuper, r.rolcreaterole, r.rolcreatedb, 
                       r.rolcanlogin, r.rolconnlimit, r.rolvaliduntil
                FROM pg_roles r
                WHERE r.rolcanlogin = true
                ORDER BY r.rolname;
            """)
            
            roles = self.cursor.fetchall()
            for role in roles:
                role_info["role_memberships"].append({
                    "role_name": role[0],
                    "is_superuser": role[1],
                    "can_create_role": role[2],
                    "can_create_db": role[3],
                    "can_login": role[4],
                    "connection_limit": role[5],
                    "valid_until": str(role[6]) if role[6] else None
                })
            
        except Exception as e:
            logger.error(f"Error getting roles and permissions for database {db_name}: {e}")
        
        return role_info

    async def determine_database_accessibility_level(self, db_name: str, instance_info: Dict = None) -> Dict[str, Any]:
        """Determine the accessibility level of a PostgreSQL database"""
        
        accessibility_analysis = {
            "database_name": db_name,
            "accessibility_level": "restricted",
            "risk_factors": [],
            "access_details": {},
            "recommendations": []
        }
        
        try:
            # Get role and permission information
            role_info = await self.get_database_roles_and_permissions(db_name)
            accessibility_analysis["access_details"]["role_info"] = role_info
            
            # Check network accessibility
            if instance_info and "publicly_accessible" in instance_info:
                # RDS Instance
                rds_access = await self.check_rds_public_accessibility(instance_info)
                accessibility_analysis["access_details"]["rds_config"] = rds_access
                
                if rds_access["publicly_accessible"] and rds_access["public_ip_detected"]:
                    accessibility_analysis["risk_factors"].append("RDS instance is publicly accessible")
                    if role_info["public_access"]:
                        accessibility_analysis["accessibility_level"] = "public"
                        accessibility_analysis["risk_factors"].append("PUBLIC role has database access")
                    else:
                        accessibility_analysis["accessibility_level"] = "external"
                else:
                    logger.info(f"RDS database {db_name} is not publicly accessible")
            else:
                # Self-managed PostgreSQL
                network_config = await self.check_postgres_network_config()
                accessibility_analysis["access_details"]["network_config"] = network_config
                
                # Analyze network configuration
                is_network_public = False
                
                if network_config["listen_addresses"] == "*":
                    accessibility_analysis["risk_factors"].append("PostgreSQL listens on all interfaces (*)")
                    is_network_public = True
                
                # Check pg_hba rules for open access
                for rule in network_config["pg_hba_rules"]:
                    if rule["address"] in ["0.0.0.0/0", "::/0"] or rule["address"] == "all":
                        accessibility_analysis["risk_factors"].append(f"pg_hba allows access from {rule['address']}")
                        is_network_public = True
                
                if network_config["host_reachable_externally"]:
                    accessibility_analysis["risk_factors"].append("Host resolves to public IP")
                    is_network_public = True
                
                # Determine accessibility level for self-managed
                if is_network_public:
                    if role_info["public_access"]:
                        accessibility_analysis["accessibility_level"] = "public"
                    else:
                        accessibility_analysis["accessibility_level"] = "external"
                else:
                    logger.info(f"Self-managed database {db_name} appears to be internal/restricted")
            
            # Check for privileged access
            if len(role_info["superusers"]) > 0:
                accessibility_analysis["access_details"]["privileged_users"] = role_info["superusers"]
                if accessibility_analysis["accessibility_level"] in ["restricted", "internal"]:
                    non_super_users = [r for r in role_info["role_memberships"] if not r["is_superuser"]]
                    if len(non_super_users) == 0:
                        accessibility_analysis["accessibility_level"] = "privileged"
            
            # Generate recommendations
            if accessibility_analysis["accessibility_level"] == "public":
                accessibility_analysis["recommendations"].extend([
                    "URGENT: Remove PUBLIC role database access",
                    "Restrict network access to specific IP ranges",
                    "Enable SSL/TLS encryption",
                    "Implement IP whitelisting"
                ])
            elif accessibility_analysis["accessibility_level"] == "external":
                accessibility_analysis["recommendations"].extend([
                    "Consider restricting access to internal networks only",
                    "Implement strong authentication mechanisms",
                    "Enable SSL/TLS encryption",
                    "Monitor access logs regularly"
                ])
            
        except Exception as e:
            logger.error(f"Error determining accessibility level for database {db_name}: {e}")
            accessibility_analysis["error"] = str(e)
        
        return accessibility_analysis

    async def infra_scan(self):
        """Perform infrastructure scan to get database metadata and load assets + access controls."""
        try:
            logger.info(f"Starting infra scan for service: {self.service_name}")
            await self.get_service_details()

            self.discovered_instances = [{
                "host": self.host,
                "port": self.port,
                "db_name": self.dbname,
                "instance_id": self.service_name,
            }]

            aggregated_metadata = []
            accessible_dbs = []  # Track only databases we can connect to successfully

            for instance in self.discovered_instances:
                host = instance["host"]
                port = instance["port"]
                dbname = instance.get("db_name") or self.dbname

                # Connect to the base instance
                await self.connect()

                # Get list of all databases
                databases = await self.get_databases()

                # Region discovery
                region = self.region or get_region_from_host(host)
                if region == "Unknown" and self.service.get("location"):
                    region = self.service["location"]

                encryption_status = await self.get_encryption_status()

                db_metadata = []

                for db in databases:
                    try:
                        await self.switch_database(db)

                        # Verify connect privilege
                        try:
                            self.cursor.execute("SELECT 1;")
                            accessible_dbs.append(db)
                        except psycopg2.OperationalError as e:
                            logger.error(f"Skipping database '{db}' (permission denied): {e}")
                            continue

                        # Current DB Owner
                        self.cursor.execute("SELECT CURRENT_USER;")
                        db_owner = self.cursor.fetchone()[0]

                        # DB Size
                        self.cursor.execute("SELECT pg_database_size(%s);", (db,))
                        size_bytes = self.cursor.fetchone()[0] or 0
                        size_mb = round(size_bytes / (1024 * 1024), 2)

                        # Get ALL schemas
                        all_schemas = await self.get_all_schemas()

                        # Table and Column counts
                        self.cursor.execute("""
                            SELECT COUNT(*) FROM information_schema.tables
                            WHERE table_schema NOT IN ('pg_catalog', 'information_schema')
                              AND table_type = 'BASE TABLE';
                        """)
                        table_count = self.cursor.fetchone()[0]

                        self.cursor.execute("""
                            SELECT COUNT(*) FROM information_schema.columns
                            WHERE table_schema NOT IN ('pg_catalog', 'information_schema');
                        """)
                        column_count = self.cursor.fetchone()[0]

                        logger.info(f"Database: {db}, Schemas: {len(all_schemas)}, Tables: {table_count}, Columns: {column_count}")
                        logger.info(f"  Schemas found: {all_schemas}")

                        # Access Permissions (unchanged)
                        access_permissions = await self.get_database_access_controls()
                        access_objs = [{**perm, "asset_name": db} for perm in access_permissions]
                        try:
                            load_access_controls(access_objs)
                            logger.info(f"Access controls loaded successfully for database {db}")
                        except Exception as e:
                            logger.error(f"Failed to load access controls for database {db}: {e}", exc_info=e)

                        # Accessibility Info (unchanged)
                        accessibility_info = await self.determine_database_accessibility_level(db, instance)
                        access_level = accessibility_info.get("accessibility_level", "none")

                        db_metadata.append({
                            "dbname": db,
                            "db_owner": db_owner,
                            "total_size_mb": size_mb,
                            "schemas": all_schemas,
                            "schema_count": len(all_schemas),
                            "table_count": table_count,
                            "column_count": column_count,
                            "access_permissions": access_permissions,
                            "accessibility_info": accessibility_info,
                            "region": region,
                        })

                        # Asset Details (unchanged)
                        try:
                            asset_obj = AssetDetailsSchema(
                                asset_name=db,
                                service_provider=self.service_provider,
                                type="database",
                                category="structured",
                                location=region,
                                owner=db_owner if db_owner else self.service_owner,
                                security=encryption_status,
                                size=size_bytes,
                                count=table_count,
                                access_category=access_level,
                                service_name=str(self.service_name),
                                steward=str(self.service_steward),
                            )
                            load_asset_details([asset_obj])
                        except Exception as e:
                            logger.error(f"Failed to load asset details for db {db}: {e}", exc_info=e)

                    except Exception as e:
                        logger.error(f"Failed infra scan for database {db}: {e}", exc_info=e)
                        continue

                aggregated_metadata.extend(db_metadata)

            logger.info(f"Infra scan completed for {len(aggregated_metadata)} databases")
            return {
                "accessible_databases": accessible_dbs,
                "region": region,
                "metadata": aggregated_metadata,
            }

        except Exception as e:
            logger.error(f"Infrastructure scan failed: {e}", exc_info=e)
            return {"error": str(e)}

    async def deep_scan(self):
        """Perform deep scan on accessible databases and their schemas/tables."""
        try:
            await self.get_service_details()
            logger.info("Running infra_scan before deep_scan")

            # Run infra scan to discover accessible DBs
            infra_result = await self.infra_scan()
            accessible_dbs = infra_result.get("accessible_databases", [])

            if not accessible_dbs:
                logger.warning("No accessible databases found for deep scan.")
                return

            logger.info(f"Starting deep scan for {len(accessible_dbs)} accessible databases: {accessible_dbs}")

            for db in accessible_dbs:
                try:
                    logger.info(f"=== Deep scanning database: {db} ===")
                    await self.switch_database(db)

                    # Get all tables from all schemas
                    tables_with_schema = await self.get_tables_from_all_schemas()
                    logger.info(f"Database '{db}' has {len(tables_with_schema)} tables across all schemas")

                    if len(tables_with_schema) == 0:
                        logger.warning(f"No tables found in database '{db}'")
                        continue

                    for schema, table in tables_with_schema:
                        try:
                            logger.info(f"Scanning table: {schema}.{table}")
                            table_result = await self.scan_table(table, schema=schema)
                            if table_result:
                                logger.info(f"Deep scan successful for {schema}.{table}")
                                yield table_result  
                            else:
                                logger.warning(f" Table scan returned None for {schema}.{table}")
                        except Exception as e:
                            logger.error(f"Failed to deep scan table {schema}.{table} in db {db}: {e}", exc_info=e)

                    logger.info(f" Completed deep scan for database: {db}")

                except Exception as e:
                    logger.error(f"Deep scan failed for db: {db}: {e}", exc_info=e)
                    continue

            logger.info(" Deep scan completed for all accessible databases.")

        except Exception as e:
            logger.error(f"Deep scan failed at top level: {e}", exc_info=e)


    async def user_scan(self, inital_tables: List[dict]):
        try:
            await self.get_service_details()
            await self.connect()
            databases = await self.get_databases()
            if len(self.databases) > 0:
                databases = [d for d in self.databases if d in databases]

            for db in databases:
                try:
                    await self.switch_database(db)
                    current_db_tables = [
                        m for m in inital_tables if m.get("db_name") == db
                    ]
                    if len(current_db_tables) == 0:
                        continue

                    logger.info(
                        f"db_name: {db}, current_db_tables: {current_db_tables}"
                    )
                    results = await self.fetch_related_data_recursively(
                        current_db_tables
                    )

                    if len(results):
                        yield results
                except Exception as e:
                    logger.error(
                        f"User scan failed for db: {db}. Service: {self.service_name}",
                        exc_info=e,
                    )
        except Exception as e:
            logger.error(f"User scan failed. Service: {self.service_name}", exc_info=e)

    async def scan_table(self, table, schema='public'):
        """Scan a table with proper schema specification"""
        try:
            logger.info(f"Starting scan for table: {schema}.{table} in database: {self.dbname}")
            
            # Check if table exists
            if not await self.table_exists(table, schema):
                logger.warning(f"Table {schema}.{table} does not exist in database {self.dbname}")
                return None

            # Get column information with schema specification
            logger.info(f"Fetching column information for {schema}.{table}")
            self.cursor.execute(
                sql.SQL(
                    """
                    SELECT column_name, data_type
                    FROM information_schema.columns
                    WHERE table_schema = %s AND table_name = %s
                    ORDER BY ordinal_position;
                    """
                ),
                [schema, table],
            )
            
            column_rows = self.cursor.fetchall()
            logger.info(f"Found {len(column_rows)} columns for table {schema}.{table}")
            
            if not column_rows:
                logger.warning(f"No columns found for table {schema}.{table}")
                return None

            column_constraints_map = {}
            columns = []
            
            # Extract constraints
            foreign_keys_info = await self.extract_foreign_keys(table, schema)
            unique_constraints_info = await self.extract_unique_constraints(table, schema)
            check_constraints_info = await self.extract_check_constraints(table, schema)
            column_properties_info = await self.extract_column_properties(table, schema)
            
            logger.info(f"Extracted constraints for {schema}.{table}: FK={len(foreign_keys_info)}, UC={len(unique_constraints_info)}, CC={len(check_constraints_info)}")

            for row in column_rows:
                col_name = row[0]
                data_type = row[1]
                column_constraints = {}
                
                if col_name in foreign_keys_info:
                    column_constraints["foreign_keys"] = foreign_keys_info[col_name]

                if col_name in unique_constraints_info:
                    column_constraints["unique_constraints"] = unique_constraints_info[col_name]

                if col_name in check_constraints_info:
                    column_constraints["check_constraints"] = check_constraints_info[col_name]

                if col_name in column_properties_info:
                    column_constraints["properties"] = column_properties_info[col_name]

                column_constraints_map[col_name] = column_constraints
                
                # Check if column is primary key
                is_primary = False
                has_index = False
                
                if col_name in unique_constraints_info:
                    for constraint in unique_constraints_info[col_name]:
                        if constraint.get("is_primary_key", False):
                            is_primary = True
                            has_index = True
                        elif constraint.get("constraint_type") == "UNIQUE":
                            has_index = True

                columns.append(
                    TableColumn(
                        column_name=col_name,
                        data_type=data_type,
                        primary_key=is_primary,
                        index=has_index,
                    )
                )

            logger.info(f"Processed {len(columns)} columns for table {schema}.{table}")

            # Get row count
            self.cursor.execute(
                sql.SQL("SELECT COUNT(*) FROM {}.{};").format(
                    sql.Identifier(schema),
                    sql.Identifier(table)
                )
            )
            row_count = self.cursor.fetchone()[0]
            logger.info(f"Table {schema}.{table} has {row_count} rows")

            # Get table size
            try:
                self.cursor.execute(
                    sql.SQL("SELECT pg_total_relation_size(%s);"), 
                    [f'{schema}.{table}']
                )
                table_size = self.cursor.fetchone()[0]
            except Exception as e:
                logger.warning(f"Could not get table size for {schema}.{table}: {e}")
                table_size = None

            # Get sample data
            self.cursor.execute(
                sql.SQL("SELECT * FROM {}.{} LIMIT %s;").format(
                    sql.Identifier(schema),
                    sql.Identifier(table)
                ),
                [self.sample_count],
            )
            rows = self.cursor.fetchall()
            logger.info(f"Retrieved {len(rows)} sample rows from {schema}.{table}")

            # Create output file
            create_folder(self.local_data_dir)
            output_file = os.path.join(
                self.local_data_dir, f"{schema}_{table}_{str(uuid4())}.csv"
            )
            with open(output_file, mode="w", newline="", encoding="utf-8") as file:
                writer = csv.writer(file)
                writer.writerow([col.column_name for col in columns])
                writer.writerows(rows)

            table_uri = f"postgres://{self.dbname}/{schema}/{table}"
            logger.info(f"Constructed table_uri: {table_uri}")

            # Check if table is already processed
            if self.db.skip_table(table_uri):
                logger.info(f"Skipping table {schema}.{table} - Already processed")
                return None
            
            total_foreign_keys = sum(len(fks) for fks in foreign_keys_info.values())
            total_unique_constraints = sum(len(ucs) for ucs in unique_constraints_info.values())
            total_check_constraints = sum(len(ccs) for ccs in check_constraints_info.values())
            columns_with_constraints = len([col for col in columns if column_constraints_map.get(col.column_name)])
          
            details = {
                "host": self.host,
                "port": self.port,
                "schema": schema,
                "region": self.region or get_region_from_host(self.host),
                "constraints_summary": {
                    "total_foreign_keys": total_foreign_keys,
                    "total_unique_constraints": total_unique_constraints,
                    "total_check_constraints": total_check_constraints,
                    "total_columns": len(columns),
                    "columns_with_constraints": columns_with_constraints,
                    "constraint_coverage_percentage": round((columns_with_constraints / len(columns)) * 100, 2) if columns else 0
                },
                "table_constraints": {
                    "foreign_keys": foreign_keys_info,
                    "unique_constraints": unique_constraints_info,
                    "check_constraints": check_constraints_info
                },
                "columns_metadata": [
                    {
                        "column_name": col.column_name,
                        "data_type": col.data_type,
                        "primary_key": col.primary_key,
                        "index": col.index,
                        "has_foreign_key": bool(column_constraints_map.get(col.column_name, {}).get('foreign_keys')),
                        "has_unique_constraint": bool(column_constraints_map.get(col.column_name, {}).get('unique_constraints')),
                        "has_check_constraint": bool(column_constraints_map.get(col.column_name, {}).get('check_constraints')),
                    } for col in columns
                ]
            }
            
            logger.info(f"Successfully completed scan for table {schema}.{table}")
            
            return TableMetadata(
                service_name=self.service_name,
                service_type=self.service_type,
                service_provider=self.service_provider,
                sub_service=self.sub_service,
                db_type=DBTypes.POSTGRES.value,
                db_name=self.dbname,
                table_name=table,
                table_size=table_size,
                no_of_rows=row_count,
                table_uri=table_uri,
                columns=columns,
                local_filepath=output_file,
                details=details
            )

        except Exception as e:
            if self.connection:
                self.connection.rollback()
            logger.error(
                f"Table scan failed. DB: {self.dbname}, Schema: {schema}, Table: {table}",
                exc_info=e,
            )
            return None

    async def extract_foreign_keys(self, table_name: str, schema: str = 'public') -> dict:
        try:
            self.cursor.execute("""
                SELECT
                    tc.constraint_name,
                    kcu.column_name,
                    ccu.table_name AS referenced_table,
                    ccu.column_name AS referenced_column,
                    rc.delete_rule AS delete_action,
                    rc.update_rule AS update_action
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                JOIN information_schema.constraint_column_usage ccu
                    ON ccu.constraint_name = tc.constraint_name
                    AND ccu.table_schema = tc.table_schema
                JOIN information_schema.referential_constraints rc
                    ON tc.constraint_name = rc.constraint_name
                    AND tc.table_schema = rc.constraint_schema
                WHERE tc.constraint_type = 'FOREIGN KEY'
                    AND tc.table_name = %s
                    AND tc.table_schema = %s
                ORDER BY kcu.ordinal_position
            """, (table_name, schema))

            rows = self.cursor.fetchall()

            fk_data = {}
            for row in rows:
                column_name = row[1]
                if column_name not in fk_data:
                    fk_data[column_name] = []
                fk_data[column_name].append({
                    "constraint_name": row[0],
                    "referenced_table": row[2],
                    "referenced_column": row[3],
                    "delete_action": row[4],
                    "update_action": row[5]
                })
            return fk_data
        except Exception as e:
            logger.error(f"Failed to extract foreign keys for table '{schema}.{table_name}': {e}")
            return {}

    async def extract_unique_constraints(self, table_name: str, schema: str = 'public') -> dict:
        try:
            self.cursor.execute("""
                SELECT
                    tc.constraint_name,
                    kcu.column_name,
                    tc.constraint_type
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                WHERE tc.table_name = %s
                    AND tc.table_schema = %s
                    AND tc.constraint_type IN ('UNIQUE', 'PRIMARY KEY')
                ORDER BY kcu.ordinal_position
            """, (table_name, schema))

            rows = self.cursor.fetchall()

            unique_data = {}
            for row in rows:
                column_name = row[1]
                constraint_type = row[2]
                if column_name not in unique_data:
                    unique_data[column_name] = []
                unique_data[column_name].append({
                    "constraint_name": row[0],
                    "constraint_type": constraint_type,
                    "is_primary_key": constraint_type == "PRIMARY KEY"
                })
            return unique_data
        except Exception as e:
            logger.error(f"Failed to extract unique constraints for table '{schema}.{table_name}': {e}")
            return {}

    async def extract_check_constraints(self, table_name: str, schema: str = 'public') -> dict:
        try:
            self.cursor.execute("""
                SELECT
                    tc.constraint_name,
                    cc.check_clause,
                    kcu.column_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.check_constraints cc
                    ON tc.constraint_name = cc.constraint_name
                    AND tc.constraint_schema = cc.constraint_schema
                LEFT JOIN information_schema.key_column_usage kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                WHERE tc.table_name = %s
                    AND tc.table_schema = %s
                    AND tc.constraint_type = 'CHECK'
            """, (table_name, schema))

            rows = self.cursor.fetchall()

            check_data = {}
            for row in rows:
                constraint_name = row[0]
                check_clause = row[1]
                column_name = row[2]
                if not column_name:
                    column_name = "table_level_constraint"
                if column_name not in check_data:
                    check_data[column_name] = []
                check_data[column_name].append({
                    "constraint_name": constraint_name,
                    "check_clause": check_clause,
                    "constraint_type": "CHECK"
                })
            return check_data
        except Exception as e:
            logger.error(f"Failed to extract check constraints for table '{schema}.{table_name}': {e}")
            return {}

    async def extract_column_properties(self, table_name: str, schema: str = 'public') -> dict:
        try:
            self.cursor.execute("""
                SELECT
                    column_name,
                    is_nullable,
                    column_default,
                    character_maximum_length,
                    numeric_precision,
                    numeric_scale,
                    data_type,
                    ordinal_position,
                    is_identity,
                    identity_generation
                FROM information_schema.columns
                WHERE table_name = %s
                    AND table_schema = %s
                ORDER BY ordinal_position
            """, (table_name, schema))

            rows = self.cursor.fetchall()

            properties_data = {}
            for row in rows:
                column_name = row[0]
                is_nullable = row[1] == "YES"
                properties_data[column_name] = {
                    "nullable": is_nullable,
                    "default_value": row[2],
                    "character_maximum_length": row[3],
                    "numeric_precision": row[4],
                    "numeric_scale": row[5],
                    "data_type": row[6],
                    "ordinal_position": row[7],
                    "is_identity": row[8] == "YES" if row[8] else False,
                    "identity_generation": row[9]
                }
            return properties_data
        except Exception as e:
            logger.error(f"Failed to extract column properties for table '{schema}.{table_name}': {e}")
            return {}
        
    def close_connection(self):
        """Close database connection and cursor"""
        if self.cursor:
            try:
                self.cursor.close()
                logger.info("Postgres cursor closed")
            except Exception as e:
                logger.warning(f"Error closing cursor: {e}")
        if self.connection:
            try:
                self.connection.close()
                logger.info("Postgres connection closed")
            except Exception as e:
                logger.warning(f"Error closing connection: {e}")