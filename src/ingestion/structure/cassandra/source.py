import os
import csv
from uuid import uuid4
import boto3
from botocore.config import Config
from cassandra.cluster import Cluster
from cassandra.auth import PlainTextAuthProvider
from cassandra.policies import RetryPolicy
from typing import AsyncGenerator, List, Optional, Dict, Any
from collections import defaultdict
from src.common.constants import ServiceTypes, DBTypes, AuthTypes, ServiceProviders
from src.utils.logger import get_ingestion_logger
from src.utils.loaders import load_access_controls, load_asset_details
from src.modules.repos import DatabaseManager
from src.ingestion.base_source import StructuredSource
from src.ingestion.data_class import TableMetadata, TableColumn
from src.common.config import TEMP_DATA_DIR
from src.utils.helpers import create_folder
from src.ingestion.data_class import AssetsDetails as AssetDetailsSchema
from src.utils.exceptions import ServerException, CustomBaseException
import time
import asyncio
import socket
import ipaddress
logger = get_ingestion_logger()


class CassandraSource(StructuredSource):
    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.CASSANDRA.value, service_name)
        self.db = DatabaseManager()
        self.cluster = None
        self.session = None
        self.service_provider = ServiceProviders.LOCAL
        self.sub_service = DBTypes.CASSANDRA.value
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"
        self.connection_params = {}  # For reconnection
        self.current_keyspace = None
        self.max_retries = 3
        self.retry_delay = 2
        self.request_timeout = 30

    async def get_service_details(self):
        """Gets service details"""
        self.service = await self.db.get_service_by_service_name(self.service_name)
        credentials = self.service.get("credentials", {})
        self.host = credentials.get("host")
        self.port = credentials.get("port", 9042)
        self.keyspace = credentials.get("keyspace")  # Optional default keyspace
        self.user = credentials.get("user")
        self.auth_type = credentials.get("auth_type")
        self.password = credentials.get("password")
        self.token = credentials.get("token")  # For Astra/token auth
        self.sample_count = credentials.get("sample_count", 10)
        self.keyspaces = credentials.get("keyspaces", [])  # List to scan
        self.region = credentials.get("region")
        self.ssl_enabled = credentials.get("ssl_enabled", False)
        self.ca_certificate = credentials.get("ca_certificate")
        self.service_steward = self.service.get("data_steward")
        self.service_owner = self.service.get("data_owner")
        return self.service

    def connect_using_creds(self):
        """Connect using username/password"""
        try:
            auth_provider = PlainTextAuthProvider(username=self.user, password=self.password)
            self.cluster = Cluster(
                contact_points=[self.host],
                port=self.port,
                auth_provider=auth_provider,
                ssl_options={'ca_certs': self.ca_certificate} if self.ssl_enabled else None,
                protocol_version=4
            )
            self.session = self.cluster.connect(keyspace=self.keyspace if self.keyspace else None)
            self.session.default_timeout = self.request_timeout
            self.session.default_retry_policy = RetryPolicy()
            self.current_keyspace = self.keyspace
            logger.info(f"Connected to Cassandra using credentials. Keyspace: {self.keyspace or 'default'}")
        except Exception as e:
            raise ServerException("Password-based Cassandra connection failed.", excep=e)

    def connect_using_token(self):
        """Connect using token (e.g., for DataStax Astra)"""
        try:
            # For Astra, token is used in auth_provider
            auth_provider = PlainTextAuthProvider(username="token", password=self.token)
            self.cluster = Cluster(
                contact_points=[self.host],
                port=self.port,
                auth_provider=auth_provider,
                ssl_options={'ca_certs': self.ca_certificate} if self.ssl_enabled else None,
                protocol_version=4
            )
            self.session = self.cluster.connect(keyspace=self.keyspace if self.keyspace else None)
            self.session.default_timeout = self.request_timeout
            self.session.default_retry_policy = RetryPolicy()
            self.current_keyspace = self.keyspace
            logger.info(f"Connected to Cassandra using token. Keyspace: {self.keyspace or 'default'}")
        except Exception as e:
            raise ServerException("Token-based Cassandra connection failed.", excep=e)

    async def connect(self):
        """Creates connection"""
        if self.auth_type and self.auth_type.lower() == AuthTypes.BASIC.value:
            self.connect_using_creds()
        elif self.auth_type and self.auth_type.lower() == AuthTypes.OAUTH.value:  # For DataStax Astra tokens
            self.connect_using_token()
        else:
            raise ServerException("Invalid Authentication Type.")

    async def test_connection(self) -> dict:
        """Test connection"""
        try:
            await self.connect()
            self.session.execute("SELECT now() FROM system.local;")
            return {"status": "success", "message": "Connection successful."}
        except Exception as e:
            return {"status": "failed", "message": str(e)}
        finally:
            self.close_connection()

    async def safe_execute_query(self, query: str, params: Optional[tuple] = None, retry_count: int = 0) -> Any:
        """Execute CQL query with retries and proper error handling"""
        try:
            # For system schema queries, don't use prepared statements
            if 'system_schema' in query.lower() or 'system.' in query.lower():
                # Use simple string formatting for system queries (safe as we control the inputs)
                if params:
                    # For system queries, format the query directly
                    formatted_query = query
                    for param in params:
                        formatted_query = formatted_query.replace('%s', f"'{param}'", 1)
                    return self.session.execute(formatted_query, timeout=self.request_timeout)
                else:
                    return self.session.execute(query, timeout=self.request_timeout)
            else:
                # For user table queries, use prepared statements
                if params:
                    prepared = self.session.prepare(query)
                    return self.session.execute(prepared, params, timeout=self.request_timeout)
                else:
                    return self.session.execute(query, timeout=self.request_timeout)
        except Exception as e:
            error_str = str(e).lower()
            # Import Cassandra-specific exceptions for better error handling
            from cassandra.cluster import NoHostAvailable, OperationTimedOut
            from cassandra import Unauthorized, AuthenticationFailed
            
            if isinstance(e, (OperationTimedOut,)) or "timeout" in error_str:
                if retry_count < self.max_retries:
                    wait_time = self.retry_delay * (2 ** retry_count)
                    logger.warning(f"Timeout on query. Retrying in {wait_time} seconds... (Attempt {retry_count + 1}/{self.max_retries})")
                    await asyncio.sleep(wait_time)
                    return await self.safe_execute_query(query, params, retry_count + 1)
            elif isinstance(e, (Unauthorized, AuthenticationFailed)) or "unauthorized" in error_str or "authentication" in error_str:
                if retry_count < self.max_retries:
                    logger.warning("Authentication error, attempting reconnect...")
                    await self.connect()
                    return await self.safe_execute_query(query, params, retry_count + 1)
            elif isinstance(e, NoHostAvailable) or "overloaded" in error_str or "rate limit" in error_str:
                if retry_count < self.max_retries:
                    wait_time = self.retry_delay * (2 ** retry_count)
                    logger.warning(f"Host unavailable or rate limited. Retrying in {wait_time} seconds... (Attempt {retry_count + 1}/{self.max_retries})")
                    await asyncio.sleep(wait_time)
                    return await self.safe_execute_query(query, params, retry_count + 1)
            
            logger.error(f"Query failed after {retry_count} retries: {str(e)}")
            raise e

    async def get_keyspaces(self):
        """Retrieve all keyspaces (like databases)"""
        rows = await self.safe_execute_query("SELECT keyspace_name FROM system_schema.keyspaces;")
        keyspaces = [row.keyspace_name for row in rows]
        
        # Filter out system keyspaces unless explicitly requested
        if self.keyspaces:
            # User specified keyspaces to scan
            keyspaces = [k for k in keyspaces if k in self.keyspaces]
        else:
            # Exclude system keyspaces by default
            system_keyspaces = ['system', 'system_schema', 'system_auth', 'system_distributed', 'system_traces', 'system_virtual_schema']
            keyspaces = [k for k in keyspaces if k not in system_keyspaces]
        
        logger.info(f"Found {len(keyspaces)} keyspaces: {keyspaces}")
        return keyspaces

    async def use_keyspace(self, keyspace_name):
        """Switch to a keyspace"""
        try:
            self.session.set_keyspace(keyspace_name)
            self.current_keyspace = keyspace_name
            logger.info(f"Switched to keyspace: {keyspace_name}")
        except Exception as e:
            logger.error(f"Failed to switch to keyspace {keyspace_name}: {str(e)}")

    async def get_tables(self, keyspace_name=None):
        """Retrieve all tables in the keyspace"""
        if keyspace_name and keyspace_name != self.current_keyspace:
            await self.use_keyspace(keyspace_name)
        rows = await self.safe_execute_query("SELECT table_name FROM system_schema.tables WHERE keyspace_name = %s;", (self.current_keyspace,))
        tables = [row.table_name for row in rows]
        logger.info(f"Found {len(tables)} tables in keyspace {self.current_keyspace}")
        return tables

    async def table_exists(self, table_name, keyspace_name):
        """Check if table exists in keyspace"""
        rows = await self.safe_execute_query(
            "SELECT table_name FROM system_schema.tables WHERE keyspace_name = %s AND table_name = %s;",
            (keyspace_name, table_name)
        )
        result = list(rows)
        exists = bool(result)
        logger.info(f"Table {keyspace_name}.{table_name} {'exists' if exists else 'does NOT exist'}")
        return exists

    async def get_encryption_status(self):
        """Check if SSL is enabled (simplified; Cassandra encryption is config-based)"""
        return self.ssl_enabled  # Based on connection config

    async def find_primary_keys(self, table_name: str, keyspace_name: str):
        """Find primary keys (partition keys and clustering columns)"""
        rows = await self.safe_execute_query(
            "SELECT column_name FROM system_schema.columns WHERE keyspace_name = %s AND table_name = %s AND (kind = 'partition_key' OR kind = 'clustering');",
            (keyspace_name, table_name)
        )
        pk_keys = [row.column_name for row in rows]
        return pk_keys

    async def find_foreign_key_tables(self, pk_table: str, pk_column: str):
        """Cassandra doesn't have traditional FKs; simulate via metadata if defined"""
        # Placeholder: Cassandra lacks built-in FKs, so return empty or implement custom logic if needed
        return []

    async def find_matching_rows_from_table(self, keyspace_name: str, table_name: str, column_name: str, values: List[str]):
        """Find matching rows - Note: Cassandra has limited filtering capabilities"""
        matched_rows = []
        try:
            # Get data type
            rows = await self.safe_execute_query(
                "SELECT type FROM system_schema.columns WHERE keyspace_name = %s AND table_name = %s AND column_name = %s;",
                (keyspace_name, table_name, column_name)
            )
            result = list(rows)
            if not result:
                return []
            data_type = result[0].type

            # For text types, use equality checks (LIKE is not well supported in Cassandra)
            if 'text' in data_type or 'varchar' in data_type:
                # Use IN clause for multiple values
                placeholders = ','.join(['%s'] * len(values))
                query = f"SELECT * FROM {keyspace_name}.{table_name} WHERE {column_name} IN ({placeholders}) LIMIT 20 ALLOW FILTERING;"
                rows = await self.safe_execute_query(query, tuple(values))
                matched_rows.extend([dict(row._asdict()) for row in rows])
            else:
                # For numeric types
                casted_values = []
                for v in values:
                    try:
                        casted_values.append(int(v) if v.isdigit() else float(v))
                    except (ValueError, AttributeError):
                        continue
                if casted_values:
                    placeholders = ','.join(['%s'] * len(casted_values))
                    query = f"SELECT * FROM {keyspace_name}.{table_name} WHERE {column_name} IN ({placeholders}) LIMIT 20 ALLOW FILTERING;"
                    rows = await self.safe_execute_query(query, tuple(casted_values))
                    matched_rows = [dict(row._asdict()) for row in rows]
        except Exception as e:
            logger.error(f"Error fetching from {keyspace_name}.{table_name}: {e}")
        return matched_rows

    async def get_keyspace_access_controls(self, keyspace_name: str) -> list:
        """Fetch access controls (permissions) for a specific keyspace"""
        try:
            access_controls = []
            
            # Query system_auth.role_permissions to get permissions for the keyspace
            # Note: This table exists if authentication/authorization is enabled
            query = """
                SELECT role, resource, permissions 
                FROM system_auth.role_permissions;
            """
            
            try:
                rows = await self.safe_execute_query(query)
            except Exception as e:
                logger.warning(f"Could not query role_permissions (auth may not be enabled): {e}")
                return []
            
            # Excluded system roles
            EXCLUDED_ROLES = {
                "cassandra", "system", "system_auth", "system_schema", 
                "system_distributed", "system_traces"
            }
            
            # Process permissions for this keyspace
            for row in rows:
                role = row.role if hasattr(row, 'role') else None
                resource = row.resource if hasattr(row, 'resource') else None
                permissions = row.permissions if hasattr(row, 'permissions') else []
                
                # Skip system roles
                if role in EXCLUDED_ROLES:
                    continue
                
                # Check if this permission is for our keyspace
                # Resource format: data/<keyspace>/<table> or data/<keyspace>
                if resource and keyspace_name in resource:
                    # Map Cassandra permissions to read/write/full
                    access_type = self._map_cassandra_permissions(permissions)
                    
                    # Determine if this is a role or user
                    role_type = "role"  # Cassandra primarily uses roles
                    
                    access_controls.append({
                        "user_or_role": role,
                        "role": role_type,
                        "access": access_type,
                        "asset_name": keyspace_name
                    })
            
            # If no permissions found and we have credentials, add default access control for connecting user
            if not access_controls and hasattr(self, 'credentials'):
                username = self.credentials.get('user') or self.credentials.get('username')
                if username and username not in EXCLUDED_ROLES:
                    logger.info(f"No role permissions found; adding default access control for user: {username}")
                    access_controls.append({
                        "user_or_role": username,
                        "role": "user",
                        "access": "full",  # Assume full access if they can connect
                        "asset_name": keyspace_name
                    })
            
            logger.info(f"Found {len(access_controls)} access controls for keyspace: {keyspace_name}")
            return access_controls
            
        except Exception as e:
            logger.warning(f"Failed to get access controls for keyspace {keyspace_name}: {e}")
            return []
    
    def _map_cassandra_permissions(self, permissions: list) -> str:
        """Map Cassandra permissions to read/write/full access levels"""
        if not permissions:
            return "none"
        
        # Convert to set for easier checking
        perms = set(permissions) if isinstance(permissions, list) else {permissions}
        
        read_perms = {"SELECT"}
        write_perms = {"INSERT", "UPDATE", "DELETE", "MODIFY"}
        full_perms = read_perms | write_perms | {"CREATE", "DROP", "ALTER", "AUTHORIZE"}
        
        # Check access level
        has_read = any(p in perms for p in read_perms)
        has_write = any(p in perms for p in write_perms)
        has_full = any(p in perms for p in full_perms)
        
        if has_full or (has_read and has_write):
            return "full"
        elif has_write:
            return "write"
        elif has_read:
            return "read"
        else:
            return "other"

    async def get_keyspace_size(self, keyspace_name: str) -> float:
        """Calculate approximate keyspace size by estimating from row counts and column counts"""
        try:
            total_size = 0.0
            
            # First try system.size_estimates
            query = """
                SELECT table_name, mean_partition_size, partitions_count 
                FROM system.size_estimates 
                WHERE keyspace_name = %s;
            """
            rows = await self.safe_execute_query(query, (keyspace_name,))
            
            # Calculate from size_estimates if available
            size_from_estimates = 0.0
            for row in rows:
                mean_size = row.mean_partition_size if hasattr(row, 'mean_partition_size') else 0
                partition_count = row.partitions_count if hasattr(row, 'partitions_count') else 0
                if mean_size > 0 and partition_count > 0:
                    size_from_estimates += (mean_size * partition_count)
            
            if size_from_estimates > 0:
                total_size = size_from_estimates
                logger.info(f"Calculated size from system.size_estimates for keyspace {keyspace_name}: {total_size} bytes ({total_size / (1024*1024):.2f} MB)")
            else:
                # Fallback: Estimate size based on row counts and average row size
                logger.info(f"size_estimates empty for {keyspace_name}, using row count estimation")
                tables = await self.get_tables(keyspace_name)
                
                for table in tables:
                    try:
                        # Get approximate row count (limited to avoid performance issues)
                        count_query = f"SELECT COUNT(*) FROM {keyspace_name}.{table} LIMIT 100000;"
                        count_rows = await self.safe_execute_query(count_query)
                        row_count = list(count_rows)[0][0] if count_rows else 0
                        
                        if row_count > 0:
                            # Get column count to estimate row size
                            col_query = f"SELECT COUNT(*) FROM system_schema.columns WHERE keyspace_name = %s AND table_name = %s;"
                            col_rows = await self.safe_execute_query(col_query, (keyspace_name, table))
                            col_count = list(col_rows)[0][0] if col_rows else 0
                            
                            # Rough estimate: assume average of 50 bytes per column
                            estimated_row_size = col_count * 50
                            table_size = row_count * estimated_row_size
                            total_size += table_size
                            logger.debug(f"  Table {table}: {row_count} rows × {col_count} cols ≈ {table_size} bytes")
                    except Exception as e:
                        logger.warning(f"Could not estimate size for table {keyspace_name}.{table}: {e}")
                        continue
                
                if total_size > 0:
                    logger.info(f"Estimated size for keyspace {keyspace_name}: {total_size} bytes ({total_size / (1024*1024):.2f} MB)")
                else:
                    logger.info(f"Keyspace {keyspace_name} appears to be empty or very small")
            
            return total_size
        except Exception as e:
            logger.warning(f"Could not calculate size for keyspace {keyspace_name}: {e}")
            return 0.0

    async def infra_scan(self) -> Dict:
        """Perform infrastructure scan"""
        try:
            logger.info("Starting Cassandra infrastructure scan...")
            
            # Only connect if not already connected (to support being called from deep_scan)
            if not self.session or self.session.is_shutdown:
                await self.get_service_details()
                await self.connect()
            
            keyspaces = await self.get_keyspaces()
            aggregated_metadata = []
            region = self.region or "unknown"
            logger.info(f"Scanning {len(keyspaces)} keyspaces for infrastructure metadata")

            for ks in keyspaces:
                logger.info(f"Processing keyspace: {ks}")
                await self.use_keyspace(ks)
                tables = await self.get_tables()
                table_count = len(tables)
                column_count = 0
                
                for table in tables:
                    rows = await self.safe_execute_query(f"SELECT * FROM system_schema.columns WHERE keyspace_name = %s AND table_name = %s;", (ks, table))
                    column_count += len(list(rows))

                # Calculate keyspace size
                keyspace_size_bytes = await self.get_keyspace_size(ks)
                keyspace_size_mb = round(keyspace_size_bytes / (1024 * 1024), 2)
                
                logger.info(f"Keyspace {ks}: {table_count} tables, {column_count} columns, {keyspace_size_mb} MB")

                # Get access controls for this keyspace
                logger.info(f"Fetching access controls for keyspace: {ks}")
                access_permissions = await self.get_keyspace_access_controls(ks)
                
                # Encryption status
                encryption_status = await self.get_encryption_status()

                aggregated_metadata.append({
                    "keyspace": ks,
                    "table_count": table_count,
                    "column_count": column_count,
                    "size_bytes": keyspace_size_bytes,
                    "size_mb": keyspace_size_mb,
                    "region": region,
                })

                # Load asset details
                logger.info(f"Creating asset details for keyspace: {ks}")
                asset_obj = AssetDetailsSchema(
                    asset_name=ks,
                    service_provider=self.service_provider,
                    type="database",
                    category="structured",
                    location=region,
                    owner=self.service_owner,
                    security="encrypted" if encryption_status else "unencrypted",
                    size=keyspace_size_bytes,  # Calculated size from system.size_estimates
                    count=table_count,
                    access_category="restricted",  # Placeholder
                    service_name=self.service_name,
                    steward=self.service_steward,
                )
                logger.info(f"Loading asset details for keyspace: {ks} (size: {keyspace_size_mb} MB) - {asset_obj}")
                try:
                    load_asset_details([asset_obj])
                    logger.info(f"✓ Successfully loaded asset details for keyspace: {ks}")
                except Exception as e:
                    logger.error(f"✗ Failed to load asset details for keyspace {ks}: {e}", exc_info=True)
                
                # Load access controls
                if access_permissions:
                    logger.info(f"Loading {len(access_permissions)} access controls for keyspace: {ks}")
                    try:
                        load_access_controls(access_permissions)
                        logger.info(f"✓ Successfully loaded access controls for keyspace: {ks}")
                    except Exception as e:
                        logger.error(f"✗ Failed to load access controls for keyspace {ks}: {e}", exc_info=True)
                else:
                    logger.info(f"No access controls found for keyspace: {ks}")

            logger.info(f"Infrastructure scan completed. Total keyspaces processed: {len(keyspaces)}")
            return {"accessible_keyspaces": keyspaces, "region": region, "metadata": aggregated_metadata}
        except Exception as e:
            logger.error(f"Infrastructure scan failed: {e}", exc_info=True)
            return {"error": str(e)}

    async def deep_scan(self) -> AsyncGenerator[TableMetadata, None]:
        """Perform deep scan on tables"""
        try:
            logger.info("Starting Cassandra deep scan...")
            await self.get_service_details()
            await self.connect()
            
            # Run infra_scan first to load asset_details (following PostgreSQL/MSSQL pattern)
            logger.info("Running infra_scan before deep_scan to load asset details")
            infra_result = await self.infra_scan()
            if "error" in infra_result:
                logger.error(f"Infra scan failed: {infra_result['error']}")
            
            keyspaces = await self.get_keyspaces()
            logger.info(f"Deep scanning {len(keyspaces)} keyspaces")

            for ks in keyspaces:
                logger.info(f"Deep scanning keyspace: {ks}")
                await self.use_keyspace(ks)
                tables = await self.get_tables()
                logger.info(f"Found {len(tables)} tables in keyspace {ks}")
                
                for table in tables:
                    try:
                        logger.info(f"Processing table: {ks}.{table}")
                        if not await self.table_exists(table, ks):
                            logger.warning(f"Table {ks}.{table} does not exist, skipping")
                            continue

                        # Get columns
                        rows = await self.safe_execute_query(f"SELECT column_name, type FROM system_schema.columns WHERE keyspace_name = %s AND table_name = %s;", (ks, table))
                        columns = [TableColumn(column_name=row.column_name, data_type=row.type) for row in rows]
                        logger.info(f"Table {ks}.{table} has {len(columns)} columns")

                        # Get approximate row count (Cassandra doesn't efficiently support COUNT)
                        # Using a small sample to estimate or set to 0
                        try:
                            rows = await self.safe_execute_query(f"SELECT COUNT(*) FROM {ks}.{table} LIMIT 10000;")
                            result = list(rows)
                            row_count = result[0][0] if result else 0
                            logger.info(f"Table {ks}.{table} has approximately {row_count} rows")
                        except Exception as e:
                            logger.warning(f"Could not get row count for {ks}.{table}: {e}")
                            row_count = 0

                        # Sample data - wrap in try-except to handle timestamp issues
                        try:
                            rows = await self.safe_execute_query(f"SELECT * FROM {ks}.{table} LIMIT {self.sample_count};")
                            sample_rows = [list(row) for row in rows]
                            logger.info(f"Fetched {len(sample_rows)} sample rows from {ks}.{table}")
                        except Exception as e:
                            logger.warning(f"Could not fetch sample data from {ks}.{table}: {e}")
                            sample_rows = []

                        # Output file
                        create_folder(self.local_data_dir)
                        output_file = os.path.join(self.local_data_dir, f"{ks}_{table}_{str(uuid4())}.csv")
                        
                        # Calculate table size from output file after writing
                        with open(output_file, mode="w", newline="", encoding="utf-8") as file:
                            writer = csv.writer(file, quoting=csv.QUOTE_MINIMAL, escapechar='\\')
                            writer.writerow([col.column_name for col in columns])
                            writer.writerows(sample_rows)
                        
                        table_size = os.path.getsize(output_file) if os.path.exists(output_file) else 0
                        logger.info(f"Created CSV file for {ks}.{table}: {output_file} ({table_size} bytes)")

                        table_uri = f"cassandra://{ks}/{table}"
                        logger.info(f"Yielding TableMetadata for {ks}.{table}")
                        yield TableMetadata(
                            service_name=self.service_name,
                            service_type=self.service_type,
                            service_provider=self.service_provider,
                            sub_service=self.sub_service,
                            db_type=DBTypes.CASSANDRA.value,
                            db_name=ks,
                            table_name=table,
                            table_size=table_size,
                            no_of_rows=row_count,
                            table_uri=table_uri,
                            columns=columns,
                            local_filepath=output_file,
                            details={"host": self.host, "port": self.port, "region": self.region}
                        )
                        logger.info(f"✓ Successfully yielded TableMetadata for {ks}.{table}")
                    except Exception as e:
                        logger.error(f"✗ Skipping table {ks}.{table} due to error: {e}", exc_info=True)
                        continue
            
            logger.info("Deep scan completed successfully")
        except Exception as e:
            logger.error(f"Deep scan failed: {e}", exc_info=True)

    async def user_scan(self, initial_tables: List[dict]):
        """User scan for PII traversal"""
        try:
            await self.get_service_details()
            await self.connect()
            keyspaces = await self.get_keyspaces()
            if self.keyspaces:
                keyspaces = [k for k in keyspaces if k in self.keyspaces]

            for ks in keyspaces:
                await self.use_keyspace(ks)
                current_ks_tables = [m for m in initial_tables if m.get("db_name") == ks]
                if not current_ks_tables:
                    continue

                results = await self.fetch_related_data_recursively(current_ks_tables)
                if results:
                    yield results
        except Exception as e:
            logger.error(f"User scan failed: {e}", exc_info=True)

    async def fetch_related_data_recursively(self, matching_tables: List[dict]) -> List[dict]:
        """Traverse related tables (simulated FKs)"""
        results = []
        visited_tables = set()
        while matching_tables:
            next_tables = []
            for pii_match in matching_tables:
                ks = pii_match.get("keyspace_name") or self.current_keyspace
                table_name = pii_match.get("table_name")
                column_name = pii_match.get("column_name")
                values = pii_match.get("values")
                qualified = f"{ks}.{table_name}"
                if qualified in visited_tables:
                    continue

                matching_rows = await self.find_matching_rows_from_table(ks, table_name, column_name, values)
                if not matching_rows:
                    continue

                results.append({
                    "service_name": self.service_name,
                    "service_provider": self.service_provider,
                    "sub_service": self.sub_service,
                    "db_name": ks,
                    "table_name": table_name,
                    "column_name": column_name,
                    "matching_rows": matching_rows,
                })
                visited_tables.add(qualified)

                pk_keys = await self.find_primary_keys(table_name, ks)
                for pk_key in pk_keys:
                    values = list({m.get(pk_key) for m in matching_rows if pk_key in m})
                    fk_tables = await self.find_foreign_key_tables(table_name, pk_key)
                    for fk_table in fk_tables:
                        next_tables.append({
                            "keyspace_name": fk_table.get("keyspace_name", ks),
                            "table_name": fk_table.get("table_name"),
                            "column_name": fk_table.get("column_name"),
                            "values": values,
                        })

            matching_tables = next_tables
        return results

    def close_connection(self):
        """Close connection"""
        if self.session:
            self.session.shutdown()
        if self.cluster:
            self.cluster.shutdown()
        logger.info("Cassandra connection closed")