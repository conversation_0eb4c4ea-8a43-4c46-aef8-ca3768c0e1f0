import pymssql
from uuid import uuid4
import csv
import os
import boto3
from src.utils.loaders import load_access_controls,load_asset_details
from src.ingestion.data_class import AssetsDetails
from botocore.config import Config
from src.common.constants import ServiceTypes, DBTypes, AuthTypes, ServiceProviders
from src.utils.logger import get_ingestion_logger
from src.modules.repos import DatabaseManager
from src.ingestion.base_source import StructuredSource
from src.ingestion.data_class import TableMetadata, TableColumn
from src.common.config import TEMP_DATA_DIR
from src.utils.helpers import create_folder
from src.utils.exceptions import ServerException
from src.ingestion.structure.mssql.connection import get_region_from_host
import time
import asyncio
from typing import List
from collections import defaultdict
from datetime import datetime
logger = get_ingestion_logger()


class MSSQLSource(StructuredSource):
	def __init__(self, service_name: str):
		super().__init__(ServiceTypes.MSSQL.value, service_name)
		self.db = DatabaseManager()
		self.connection = None
		self.cursor = None
		self.service_provider = ServiceProviders.LOCAL
		self.sub_service = DBTypes.MSSQL.value
		self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"
		self.accessible_databases = []
		self.current_database = None
		self.schema_cache = {}  # Cache for schema information
		self.connection_params = {}  # Store connection parameters for reconnection
		self.last_connection_time = None
		self.connection_timeout_seconds = 300  # 5 minutes

	async def get_service_details(self):
		self.service = await self.db.get_service_by_service_name(self.service_name)
		credentials = self.service.get("credentials", {})
		self.host = credentials.get("host")
		self.port = credentials.get("port", 1433)
		self.dbname = credentials.get("dbname")
		self.user = credentials.get("user")
		self.auth_type = credentials.get("auth_type")
		self.password = credentials.get("password")
		self.sample_count = credentials.get("sample_count", 10)
		self.databases = credentials.get("databases", [])
		self.region = credentials.get("region")
		self.service_steward = self.service.get("data_steward")

		# Validate required credentials
		if not self.host or not self.user:
			raise ServerException("Missing required credentials: host and user are mandatory")
		
		# Validate port
		try:
			self.port = int(self.port)
		except (ValueError, TypeError):
			logger.warning(f"Invalid port '{self.port}', using default 1433")
			self.port = 1433

		return self.service

	def generate_iam_token(self):
		if not self.region or not self.host or not self.user:
			raise ServerException("Missing required fields for IAM authentication.")

		try:
			logger.info("Generating SQL Server IAM token using EC2 IAM role...")

			boto_config = Config(
				retries={"max_attempts": 3, "mode": "standard"},
				connect_timeout=2,
				read_timeout=2,
			)

			rds_client = boto3.client(
				"rds", region_name=self.region, config=boto_config
			)
			token = rds_client.generate_db_auth_token(
				DBHostname=self.host,
				Port=self.port,
				DBUsername=self.user,
			)
			logger.info("IAM token successfully generated.")
			return token
		except Exception as e:
			if "Unable to locate credentials" in str(e):
				raise ServerException(
					"IAM role is not attached to the EC2 instance.", excep=e
				)
			raise ServerException("Failed to generate IAM token.", excep=e)

	def connect_using_iam(self):
		try:
			token = self.generate_iam_token()
			
			connection_params = {
				"server": self.host,
				"port": self.port,
				"user": self.user,
				"password": token,
				"timeout": 30,
				"tds_version": "7.4",
				"encrypt": True
			}
			
			# Add database only if specified
			if self.dbname:
				connection_params["database"] = self.dbname
				
			# Store connection params for reconnection (but don't store the token as it expires)
			self.connection_params = {
				"server": self.host,
				"port": self.port,
				"user": self.user,
				"timeout": 30,
				"tds_version": "7.4",
				"encrypt": True,
				"auth_type": "iam"
			}
			if self.dbname:
				self.connection_params["database"] = self.dbname
				
			try:
				self.connection = pymssql.connect(**connection_params)
				self.cursor = self.connection.cursor(as_dict=True)
				self.current_database = self.dbname
				self.last_connection_time = time.time()
				logger.info(f"Connected to MSSQL using IAM authentication with pymssql. Database: {self.dbname or 'default'}")
				return
			except Exception as e:
				logger.error(f"pymssql IAM connection failed: {str(e)}")
				raise ServerException(f"IAM-based MSSQL connection failed: {str(e)}", excep=e)
				
		except Exception as e:
			if not isinstance(e, ServerException):
				raise ServerException("IAM-based MSSQL connection failed.", excep=e)
			raise e

	def connect_using_creds(self):
		try:
			connection_params = {
				"server": self.host,
				"port": self.port,
				"user": self.user,
				"password": self.password,
				"timeout": 70
			}
			
			# Add database only if specified
			if self.dbname:
				connection_params["database"] = self.dbname
				
			# Store connection params for reconnection
			self.connection_params = connection_params.copy()
			self.connection_params["auth_type"] = "basic"
				
			self.connection = pymssql.connect(**connection_params)
			self.cursor = self.connection.cursor(as_dict=True)
			self.current_database = self.dbname
			self.last_connection_time = time.time()
			logger.info(f"Connected to MSSQL using pymssql. Database: {self.dbname or 'default'}")
			
		except Exception as e:
			error_msg = str(e)
			if "18456" in error_msg:
				raise ServerException(f"MSSQL authentication failed. Please verify username and password. Error: {error_msg}", excep=e)
			elif "20002" in error_msg or "connection failed" in error_msg.lower():
				raise ServerException(f"MSSQL connection failed. Please verify host and port. Host: {self.host}, Port: {self.port}. Error: {error_msg}", excep=e)
			else:
				raise ServerException(f"MSSQL connection failed: {error_msg}", excep=e)

	async def connect(self):
		if self.auth_type and self.auth_type.lower() == AuthTypes.BASIC.value:
			self.connect_using_creds()
		elif self.auth_type and self.auth_type.lower() == AuthTypes.IAM.value:
			self.connect_using_iam()
		else:
			# Default to basic auth if not specified
			logger.warning("No authentication type specified, defaulting to basic authentication")
			self.connect_using_creds()

	async def ensure_connection(self, operation_name="operation"):
		"""Ensure we have a valid connection, reconnect if necessary"""
		try:
			# Check if connection exists and is alive
			if self.connection and self.cursor:
				try:
					# Test connection with a simple query
					self.cursor.execute("SELECT 1 AS test")
					result = self.cursor.fetchone()
					if result:
						return True
				except Exception as conn_test_error:
					logger.warning(f"Connection test failed during {operation_name}: {str(conn_test_error)}")
					# Connection is dead, will reconnect below
			
			# Connection is dead or doesn't exist, reconnect
			logger.info(f"Reconnecting for operation: {operation_name}")
			await self.reconnect()
			return True
			
		except Exception as e:
			logger.error(f"Failed to ensure connection for {operation_name}: {str(e)}")
			return False

	async def reconnect(self):
		"""Reconnect using stored connection parameters"""
		try:
			# Close existing connection
			self.close_connection()
			
			# Wait a moment before reconnecting
			time.sleep(1)
			
			# Reconnect using stored parameters
			if not self.connection_params:
				# If no stored params, use original connect method
				await self.connect()
			else:
				auth_type = self.connection_params.get("auth_type", "basic")
				if auth_type == "iam":
					self.connect_using_iam()
				else:
					# Use stored basic auth params
					params = {k: v for k, v in self.connection_params.items() if k != "auth_type"}
					self.connection = pymssql.connect(**params)
					self.cursor = self.connection.cursor(as_dict=True)
					self.last_connection_time = time.time()
					logger.info("Reconnected to MSSQL using stored credentials")
			
			# Restore database context if we were connected to a specific database
			if self.current_database:
				try:
					await self.use_database(self.current_database)
				except Exception as db_switch_error:
					logger.warning(f"Failed to switch back to database {self.current_database}: {str(db_switch_error)}")
					
		except Exception as e:
			logger.error(f"Reconnection failed: {str(e)}")
			raise e

	async def test_connection(self):
		try:
			await self.connect()
			if not await self.ensure_connection("test_connection"):
				return False
			self.cursor.execute("SELECT 1 AS test")
			result = self.cursor.fetchone()
			logger.info(f"Successfully connected to MSSQL server {self.host}:{self.port}")
			return True
		except Exception as e:
			logger.error(f"MSSQL connection test failed: {str(e)}")
			return False
		finally:
			self.close_connection()

	async def get_databases(self):
		"""Get list of databases the user has access to - ENHANCED with better error handling"""
		try:
			if not await self.ensure_connection("get_databases"):
				return []
				
			accessible_databases = []
			
			# Try multiple approaches to get database list
			try:
				# First try: Get all databases with permissions check
				self.cursor.execute("""
					SELECT name FROM sys.databases 
					WHERE database_id > 4 
					AND state = 0 
					AND is_read_only = 0
					ORDER BY name
				""")
				all_databases = [row["name"] for row in self.cursor.fetchall()]
				
				# Test access to each database
				for db_name in all_databases:
					try:
						# Safer permission check
						self.cursor.execute("""
							SELECT CASE 
								WHEN HAS_PERMS_BY_NAME(%s, 'DATABASE', 'CONNECT') = 1 
								THEN 1 
								ELSE 0 
							END AS has_access
						""", (db_name,))
						result = self.cursor.fetchone()
						if result and result.get("has_access") == 1:
							accessible_databases.append(db_name)
							logger.info(f"User has access to database: {db_name}")
					except Exception as perm_error:
						logger.warning(f"Could not check permissions for database {db_name}: {str(perm_error)}")
						# Try to test by actually switching to the database
						try:
							self.cursor.execute(f"USE [{db_name}]")
							self.cursor.execute("SELECT 1")
							accessible_databases.append(db_name)
							logger.info(f"Database {db_name} is accessible (verified by USE)")
						except Exception as use_error:
							logger.debug(f"Database {db_name} is not accessible: {str(use_error)}")
							
			except Exception as sys_error:
				logger.warning(f"Cannot query sys.databases: {str(sys_error)}")
				# Fallback: try current database or configured databases
				if self.current_database:
					try:
						self.cursor.execute("SELECT DB_NAME() as current_db")
						result = self.cursor.fetchone()
						if result:
							accessible_databases.append(result["current_db"])
					except Exception as fallback_error:
						logger.error(f"Fallback database detection failed: {str(fallback_error)}")
			
			# If we have configured databases, filter to only include accessible ones from that list
			if self.databases:
				configured_accessible = [db for db in accessible_databases if db in self.databases]
				if configured_accessible:
					accessible_databases = configured_accessible
					logger.info(f"Filtered to configured databases: {accessible_databases}")
			
			self.accessible_databases = accessible_databases
			logger.info(f"Found {len(accessible_databases)} accessible databases: {accessible_databases}")
			return accessible_databases
			
		except Exception as e:
			logger.error(f"Failed to get databases: {str(e)}")
			# Final fallback
			if self.current_database:
				return [self.current_database]
			if self.dbname:
				return [self.dbname]
			return []

	async def use_database(self, db_name):
		"""Switch to a different database - ENHANCED with better error handling"""
		try:
			if not await self.ensure_connection(f"use_database_{db_name}"):
				return False
				
			if db_name == self.current_database:
				return True
				
			# Sanitize database name to prevent SQL injection
			if not db_name.replace('_', '').replace('-', '').isalnum():
				if not all(c.isalnum() or c in '_-' for c in db_name):
					logger.error(f"Invalid database name format: {db_name}")
					return False
			
			try:
				# Use parameterized query where possible, but USE statement requires direct name
				safe_db_name = db_name.replace('[', '').replace(']', '')  # Remove existing brackets
				use_query = f"USE [{safe_db_name}]"
				
				self.cursor.execute(use_query)
				
				# Verify the switch was successful
				self.cursor.execute("SELECT DB_NAME() AS current_db")
				result = self.cursor.fetchone()
				
				if result and result["current_db"] == db_name:
					self.current_database = db_name
					logger.info(f"Successfully switched to database: {db_name}")
					return True
				else:
					logger.error(f"Database switch verification failed. Expected: {db_name}, Got: {result.get('current_db') if result else 'None'}")
					return False
					
			except Exception as switch_error:
				error_msg = str(switch_error).lower()
				if "invalid object name" in error_msg or "could not locate" in error_msg:
					logger.error(f"Database '{db_name}' does not exist or is not accessible")
				elif "permission" in error_msg or "access" in error_msg:
					logger.error(f"No permission to access database '{db_name}'")
				else:
					logger.error(f"Failed to switch to database '{db_name}': {str(switch_error)}")
				return False
				
		except Exception as e:
			logger.error(f"Unexpected error switching to database {db_name}: {str(e)}")
			return False

	async def get_all_schemas(self, db_name=None):
		"""Get all schemas - ENHANCED with better error handling"""
		try:
			if db_name and db_name != self.current_database:
				if not await self.use_database(db_name):
					logger.warning(f"Could not switch to database {db_name}, using current database")
			
			if not await self.ensure_connection("get_all_schemas"):
				return ['dbo']  # Return default fallback
			
			schemas = []
			
			try:
				# Primary method: Use sys.schemas
				self.cursor.execute("""
					SELECT 
						s.name as schema_name,
						s.schema_id
					FROM sys.schemas s
					WHERE s.schema_id BETWEEN 1 AND 16383  -- User schemas only
					ORDER BY 
						CASE WHEN s.name = 'dbo' THEN 0 ELSE 1 END,
						s.name
				""")
				
				for row in self.cursor.fetchall():
					schema_name = row["schema_name"]
					schemas.append(schema_name)
					
			except Exception as sys_error:
				logger.warning(f"sys.schemas query failed: {str(sys_error)}")
				# Fallback method: Use INFORMATION_SCHEMA
				try:
					self.cursor.execute("""
						SELECT DISTINCT SCHEMA_NAME 
						FROM INFORMATION_SCHEMA.SCHEMATA 
						WHERE SCHEMA_NAME NOT IN ('information_schema', 'sys')
						ORDER BY SCHEMA_NAME
					""")
					
					for row in self.cursor.fetchall():
						schema_name = row["SCHEMA_NAME"]
						schemas.append(schema_name)
						
				except Exception as info_error:
					logger.warning(f"INFORMATION_SCHEMA query failed: {str(info_error)}")
					# Ultimate fallback
					schemas = ['dbo']
			
			if not schemas:
				schemas = ['dbo']
				
			logger.info(f"Found {len(schemas)} schemas in database {self.current_database or 'current'}: {schemas}")
			return schemas
			
		except Exception as e:
			logger.error(f"Failed to get schemas: {str(e)}")
			return ['dbo']  # Default fallback

	async def get_tables_by_schema(self, schema_name, db_name=None):
		"""Get all tables in a specific schema - ENHANCED with better error handling"""
		try:
			if db_name and db_name != self.current_database:
				if not await self.use_database(db_name):
					return []
			
			if not await self.ensure_connection(f"get_tables_by_schema_{schema_name}"):
				return []
			
			tables = []
			
			try:
				# Primary method: Use sys.tables with proper escaping
				query = """
					SELECT 
						t.name as table_name,
						s.name as schema_name,
						t.object_id
					FROM sys.tables t
					INNER JOIN sys.schemas s ON t.schema_id = s.schema_id
					WHERE s.name = %s
					ORDER BY t.name
				"""
				
				self.cursor.execute(query, (schema_name,))
				table_rows = self.cursor.fetchall()
				
				for row in table_rows:
					table_name = row["table_name"]
					
					# Check if user has SELECT permission on this specific table
					try:
						# Test permission using a safer method
						perm_check_query = """
							SELECT HAS_PERMS_BY_NAME(
								QUOTENAME(%s) + '.' + QUOTENAME(%s), 
								'OBJECT', 
								'SELECT'
							) AS has_select_permission
						"""
						
						self.cursor.execute(perm_check_query, (schema_name, table_name))
						perm_result = self.cursor.fetchone()
						
						if perm_result and perm_result.get("has_select_permission") == 1:
							tables.append({
								"table_name": table_name,
								"schema": schema_name,
								"qualified_name": f"{schema_name}.{table_name}"
							})
						else:
							logger.debug(f"No SELECT permission on table {schema_name}.{table_name}")
							
					except Exception as perm_error:
						logger.debug(f"Permission check failed for {schema_name}.{table_name}: {str(perm_error)}")
						# If permission check fails, try to access the table directly
						try:
							test_query = f"SELECT TOP 1 * FROM [{schema_name}].[{table_name}]"
							self.cursor.execute(test_query)
							# If this succeeds, user has access
							tables.append({
								"table_name": table_name,
								"schema": schema_name,
								"qualified_name": f"{schema_name}.{table_name}"
							})
						except Exception:
							logger.debug(f"Cannot access table {schema_name}.{table_name}")
				
			except Exception as sys_error:
				logger.warning(f"sys.tables query failed for schema {schema_name}: {str(sys_error)}")
				# Fallback method: Use INFORMATION_SCHEMA
				try:
					fallback_query = """
						SELECT TABLE_NAME 
						FROM INFORMATION_SCHEMA.TABLES 
						WHERE TABLE_SCHEMA = %s AND TABLE_TYPE = 'BASE TABLE'
						ORDER BY TABLE_NAME
					"""
					
					self.cursor.execute(fallback_query, (schema_name,))
					
					for row in self.cursor.fetchall():
						table_name = row["TABLE_NAME"]
						tables.append({
							"table_name": table_name,
							"schema": schema_name,
							"qualified_name": f"{schema_name}.{table_name}"
						})
						
				except Exception as info_error:
					logger.error(f"INFORMATION_SCHEMA fallback failed for schema {schema_name}: {str(info_error)}")
			
			logger.info(f"Found {len(tables)} accessible tables in schema {schema_name}")
			return tables
			
		except Exception as e:
			logger.error(f"Failed to get tables for schema {schema_name}: {str(e)}")
			return []

	async def get_tables(self, db_name=None):
		"""Get all tables from current database - ENHANCED with better error handling"""
		try:
			if db_name and db_name != self.current_database:
				if not await self.use_database(db_name):
					return []
			
			# Get all schemas first
			schemas = await self.get_all_schemas(db_name)
			
			all_tables = []
			for schema in schemas:
				try:
					schema_tables = await self.get_tables_by_schema(schema, db_name)
					all_tables.extend(schema_tables)
					
					# Cache schema mappings
					for table_info in schema_tables:
						cache_key = f"{self.current_database}.{table_info['table_name']}"
						self.schema_cache[cache_key] = schema
						
				except Exception as schema_error:
					logger.warning(f"Failed to get tables for schema {schema}: {str(schema_error)}")
					continue
					
			logger.info(f"Found {len(all_tables)} accessible tables in database {self.current_database or 'current'}")
			return all_tables
			
		except Exception as e:
			logger.error(f"Failed to get tables from database {db_name or self.current_database}: {str(e)}")
			return []

	async def find_table_in_schemas(self, table_name, db_name=None):
		"""Find which schema contains the specified table - ENHANCED with better error handling"""
		try:
			if db_name and db_name != self.current_database:
				if not await self.use_database(db_name):
					return None
			
			# Check cache first
			cache_key = f"{self.current_database}.{table_name}"
			if cache_key in self.schema_cache:
				return self.schema_cache[cache_key]
			
			if not await self.ensure_connection(f"find_table_in_schemas_{table_name}"):
				return None
			
			try:
				# Primary method: Use sys.tables with proper escaping
				query = """
					SELECT TOP 1
						s.name as schema_name
					FROM sys.tables t
					INNER JOIN sys.schemas s ON t.schema_id = s.schema_id
					WHERE t.name = %s
					ORDER BY 
						CASE WHEN s.name = 'dbo' THEN 0 ELSE 1 END,
						s.name
				"""
				
				self.cursor.execute(query, (table_name,))
				result = self.cursor.fetchone()
				
				if result:
					schema = result["schema_name"]
					
					# Verify user has access to this table
					try:
						perm_check = """
							SELECT HAS_PERMS_BY_NAME(
								QUOTENAME(%s) + '.' + QUOTENAME(%s), 
								'OBJECT', 
								'SELECT'
							) AS has_permission
						"""
						
						self.cursor.execute(perm_check, (schema, table_name))
						perm_result = self.cursor.fetchone()
						
						if perm_result and perm_result.get("has_permission") == 1:
							self.schema_cache[cache_key] = schema
							logger.info(f"Found table '{table_name}' in schema '{schema}' in database '{self.current_database}'")
							return schema
						else:
							logger.warning(f"Table '{table_name}' found in schema '{schema}' but user lacks SELECT permission")
							return None
							
					except Exception as perm_error:
						logger.debug(f"Permission check failed, trying direct access test: {str(perm_error)}")
						# Try direct access test
						try:
							test_query = f"SELECT TOP 1 * FROM [{schema}].[{table_name}]"
							self.cursor.execute(test_query)
							self.schema_cache[cache_key] = schema
							logger.info(f"Found accessible table '{table_name}' in schema '{schema}'")
							return schema
						except Exception:
							logger.warning(f"Table '{table_name}' found but not accessible in schema '{schema}'")
							return None
				
			except Exception as sys_error:
				logger.warning(f"sys.tables query failed: {str(sys_error)}")
				# Fallback method
				try:
					fallback_query = """
						SELECT TOP 1 TABLE_SCHEMA 
						FROM INFORMATION_SCHEMA.TABLES 
						WHERE TABLE_NAME = %s AND TABLE_TYPE = 'BASE TABLE'
						ORDER BY 
							CASE WHEN TABLE_SCHEMA = 'dbo' THEN 0 ELSE 1 END,
							TABLE_SCHEMA
					"""
					
					self.cursor.execute(fallback_query, (table_name,))
					result = self.cursor.fetchone()
					
					if result:
						schema = result["TABLE_SCHEMA"]
						self.schema_cache[cache_key] = schema
						logger.info(f"Found table '{table_name}' in schema '{schema}' (fallback method)")
						return schema
						
				except Exception as fallback_error:
					logger.error(f"Fallback table search failed: {str(fallback_error)}")
			
			logger.warning(f"Table '{table_name}' not found in any accessible schema in database '{self.current_database}'")
			return None
				
		except Exception as e:
			logger.error(f"Failed to find schema for table '{table_name}': {str(e)}")
			return None

	async def table_exists_with_access(self, table_name, schema_name=None, db_name=None):
		"""Check if table exists and user has access - ENHANCED with better error handling"""
		try:
			if db_name and db_name != self.current_database:
				if not await self.use_database(db_name):
					return False, None
			
			if not await self.ensure_connection(f"table_exists_with_access_{table_name}"):
				return False, None
			
			if schema_name:
				# Check specific schema
				try:
					query = """
						SELECT 
							s.name as schema_name
						FROM sys.tables t
						INNER JOIN sys.schemas s ON t.schema_id = s.schema_id
						WHERE t.name = %s AND s.name = %s
					"""
					
					self.cursor.execute(query, (table_name, schema_name))
					result = self.cursor.fetchone()
					
					if result:
						found_schema = result["schema_name"]
						
						# Test access
						try:
							access_test = f"SELECT TOP 1 * FROM [{found_schema}].[{table_name}]"
							self.cursor.execute(access_test)
							logger.info(f"Table '{table_name}' exists and is accessible in schema '{found_schema}'")
							return True, found_schema
						except Exception as access_error:
							logger.warning(f"Table '{table_name}' exists in schema '{found_schema}' but is not accessible: {str(access_error)}")
							return False, None
							
				except Exception as specific_error:
					logger.debug(f"Specific schema check failed: {str(specific_error)}")
					
			else:
				# Find in any schema
				found_schema = await self.find_table_in_schemas(table_name, db_name)
				if found_schema:
					return True, found_schema
			
			return False, None
			
		except Exception as e:
			logger.error(f"Failed to check table existence for '{table_name}': {str(e)}")
			return False, None

	async def safe_execute_query(self, query, params=None, operation="execute", retry_count=0):
		"""Execute query with enhanced error handling and automatic retry"""
		max_retries = 2
		
		try:
			# Ensure connection before executing
			if not await self.ensure_connection(f"safe_execute_query_{operation}"):
				raise Exception("Failed to establish database connection")
			
			# Execute query with proper parameter handling
			if params:
				self.cursor.execute(query, params)
			else:
				self.cursor.execute(query)
				
			if operation == "fetchone":
				return self.cursor.fetchone()
			elif operation == "fetchall":
				return self.cursor.fetchall()
			else:
				return True
				
		except Exception as e:
			error_msg = str(e).lower()
			
			# Enhanced error categorization
			connection_errors = [
				"connection", "timeout", "dead", "20003", "20047", 
				"not connected", "broken pipe", "connection reset"
			]
			
			table_errors = [
				"invalid object name", "could not find", "does not exist",
				"object name", "invalid column", "multipart identifier"
			]
			
			permission_errors = [
				"permission denied", "access denied", "insufficient privilege",
				"select permission", "permission was denied"
			]
			
			# Handle different error types
			if any(err in error_msg for err in connection_errors) and retry_count < max_retries:
				logger.warning(f"Connection issue detected, retrying query (attempt {retry_count + 1}/{max_retries + 1}): {str(e)}")
				
				# Wait before retry
				time.sleep(2 ** retry_count)  # Exponential backoff
				
				# Force reconnection
				try:
					await self.reconnect()
					# Retry the query
					return await self.safe_execute_query(query, params, operation, retry_count + 1)
				except Exception as reconnect_error:
					logger.error(f"Failed to reconnect during retry: {str(reconnect_error)}")
			
			elif any(err in error_msg for err in table_errors):
				logger.error(f"Table/Object not found error: {str(e)}")
				logger.error(f"Query: {query}")
				if params:
					logger.error(f"Params: {params}")
				raise ServerException(f"Table or object not found: {str(e)}", excep=e)
				
			elif any(err in error_msg for err in permission_errors):
				logger.error(f"Permission denied error: {str(e)}")
				raise ServerException(f"Insufficient permissions: {str(e)}", excep=e)
			
			# Log the error details for debugging
			logger.error(f"Query execution failed: {str(e)}")
			logger.error(f"Query: {query}")
			if params:
				logger.error(f"Params: {params}")
			raise e

	async def get_table_row_count(self, schema_name, table_name):
		"""Get row count for a table - ENHANCED with better error handling"""
		try:
			# Validate inputs to prevent SQL injection
			if not schema_name or not table_name:
				logger.error("Schema name and table name are required")
				return 0
				
			# Try exact count first with timeout protection
			try:
				query = f"SELECT COUNT(*) AS row_count FROM [{schema_name}].[{table_name}]"
				result = await self.safe_execute_query(query, operation="fetchone")
				if result and result.get("row_count") is not None:
					count = int(result["row_count"])
					logger.info(f"Exact row count for {schema_name}.{table_name}: {count}")
					return count
			except Exception as exact_error:
				logger.warning(f"Exact count failed for {schema_name}.{table_name}: {str(exact_error)}")
			
			# Fallback to approximate count using system tables
			try:
				query = """
					SELECT SUM(p.rows) AS approximate_count
					FROM sys.tables t
					INNER JOIN sys.schemas s ON t.schema_id = s.schema_id
					INNER JOIN sys.partitions p ON t.object_id = p.object_id
					WHERE s.name = %s AND t.name = %s
					AND p.index_id IN (0, 1)
				"""
				result = await self.safe_execute_query(query, (schema_name, table_name), "fetchone")
				if result and result.get("approximate_count") is not None:
					count = int(result["approximate_count"])
					logger.info(f"Approximate row count for {schema_name}.{table_name}: {count}")
					return count
			except Exception as approx_error:
				logger.warning(f"Approximate count failed for {schema_name}.{table_name}: {str(approx_error)}")
			
			# Final fallback - try a simple existence check
			try:
				query = f"SELECT TOP 1 * FROM [{schema_name}].[{table_name}]"
				result = await self.safe_execute_query(query, operation="fetchone")
				if result:
					logger.info(f"Table {schema_name}.{table_name} has data (count unavailable)")
					return -1  # Indicate table has data but count unknown
			except Exception as exist_error:
				logger.warning(f"Existence check failed for {schema_name}.{table_name}: {str(exist_error)}")
			
			return 0
			
		except Exception as e:
			logger.error(f"All row count methods failed for {schema_name}.{table_name}: {str(e)}")
			return 0

	async def get_table_sample_data(self, schema_name, table_name):
		"""Get sample data from table - ENHANCED with better error handling"""
		try:
			if not schema_name or not table_name:
				logger.error("Schema name and table name are required")
				return []
				
			# Sanitize sample count
			safe_sample_count = min(max(int(self.sample_count or 10), 1), 1000)
			
			query = f"SELECT TOP {safe_sample_count} * FROM [{schema_name}].[{table_name}]"
			result = await self.safe_execute_query(query, operation="fetchall")
			
			if result:
				logger.info(f"Retrieved {len(result)} sample rows from {schema_name}.{table_name}")
				return result
			else:
				logger.info(f"No sample data found in {schema_name}.{table_name}")
				return []
				
		except Exception as e:
			logger.warning(f"Failed to get sample data for {schema_name}.{table_name}: {str(e)}")
			return []

	async def get_table_columns(self, schema_name, table_name):
		"""Get column information for a table - ENHANCED with better error handling"""
		try:
			if not schema_name or not table_name:
				logger.error("Schema name and table name are required")
				return []
				
			columns = []
			
			try:
				# Primary method: Use sys.columns for comprehensive information
				query = """
					SELECT 
						c.name AS column_name,
						t.name AS data_type,
						c.max_length,
						c.precision,
						c.scale,
						c.is_nullable,
						CASE WHEN pk.column_name IS NOT NULL THEN 1 ELSE 0 END AS is_primary_key,
						CASE WHEN idx.column_name IS NOT NULL THEN 1 ELSE 0 END AS has_index
					FROM sys.columns c
					INNER JOIN sys.tables tb ON c.object_id = tb.object_id
					INNER JOIN sys.schemas s ON tb.schema_id = s.schema_id
					INNER JOIN sys.types t ON c.user_type_id = t.user_type_id
					LEFT JOIN (
						SELECT 
							ic.object_id,
							c.name AS column_name
						FROM sys.index_columns ic
						INNER JOIN sys.indexes i ON ic.object_id = i.object_id AND ic.index_id = i.index_id
						INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
						WHERE i.is_primary_key = 1
					) pk ON c.object_id = pk.object_id AND c.name = pk.column_name
					LEFT JOIN (
						SELECT DISTINCT
							ic.object_id,
							c.name AS column_name
						FROM sys.index_columns ic
						INNER JOIN sys.indexes i ON ic.object_id = i.object_id AND ic.index_id = i.index_id
						INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
						WHERE i.is_primary_key = 0
					) idx ON c.object_id = idx.object_id AND c.name = idx.column_name
					WHERE s.name = %s AND tb.name = %s
					ORDER BY c.column_id
				"""
				
				result = await self.safe_execute_query(query, (schema_name, table_name), "fetchall")
				
				for row in result:
					column_name = row["column_name"]
					data_type = row["data_type"]
					max_length = row.get("max_length", 0)
					precision = row.get("precision", 0)
					scale = row.get("scale", 0)
					is_nullable = row.get("is_nullable", True)
					is_primary_key = bool(row.get("is_primary_key", False))
					has_index = bool(row.get("has_index", False))
					
					# Format data type with appropriate length/precision info
					formatted_type = self._format_data_type(data_type, max_length, precision, scale)
					
					columns.append(
						TableColumn(
							column_name=column_name,
							data_type=formatted_type,
							index=has_index,
							primary_key=is_primary_key
						)
					)
					
			except Exception as sys_error:
				logger.warning(f"sys.columns query failed: {str(sys_error)}")
				# Fallback method: Use INFORMATION_SCHEMA
				try:
					fallback_query = """
						SELECT 
							COLUMN_NAME,
							DATA_TYPE,
							CHARACTER_MAXIMUM_LENGTH,
							NUMERIC_PRECISION,
							NUMERIC_SCALE,
							IS_NULLABLE
						FROM INFORMATION_SCHEMA.COLUMNS
						WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s
						ORDER BY ORDINAL_POSITION
					"""
					
					result = await self.safe_execute_query(fallback_query, (schema_name, table_name), "fetchall")
					
					for row in result:
						column_name = row["COLUMN_NAME"]
						data_type = row["DATA_TYPE"]
						max_length = row.get("CHARACTER_MAXIMUM_LENGTH", 0)
						precision = row.get("NUMERIC_PRECISION", 0)
						scale = row.get("NUMERIC_SCALE", 0)
						
						formatted_type = self._format_data_type(data_type, max_length, precision, scale)
						
						columns.append(
							TableColumn(
								column_name=column_name,
								data_type=formatted_type,
								index=False,  # Cannot determine from INFORMATION_SCHEMA easily
								primary_key=False  # Cannot determine from INFORMATION_SCHEMA easily
							)
						)
						
				except Exception as fallback_error:
					logger.error(f"Fallback column query failed: {str(fallback_error)}")
			
			logger.info(f"Retrieved {len(columns)} columns for {schema_name}.{table_name}")
			return columns
			
		except Exception as e:
			logger.error(f"Failed to get columns for {schema_name}.{table_name}: {str(e)}")
			return []

	def _format_data_type(self, data_type, max_length=0, precision=0, scale=0):
		"""Format data type with appropriate length/precision information"""
		try:
			data_type_lower = data_type.lower()
			
			# String types with length
			if data_type_lower in ['varchar', 'char', 'nvarchar', 'nchar', 'binary', 'varbinary']:
				if max_length and max_length > 0:
					if max_length == -1:
						return f"{data_type}(MAX)"
					else:
						# For Unicode types, divide by 2
						if data_type_lower.startswith('n'):
							display_length = max_length // 2 if max_length > 1 else max_length
						else:
							display_length = max_length
						return f"{data_type}({display_length})"
				else:
					return data_type
			
			# Numeric types with precision and scale
			elif data_type_lower in ['decimal', 'numeric']:
				if precision and precision > 0:
					if scale and scale > 0:
						return f"{data_type}({precision},{scale})"
					else:
						return f"{data_type}({precision})"
				else:
					return data_type
			
			# Float types
			elif data_type_lower == 'float':
				if precision and precision > 0:
					return f"{data_type}({precision})"
				else:
					return data_type
			
			# Time types with precision
			elif data_type_lower in ['time', 'datetime2', 'datetimeoffset']:
				if scale and scale > 0:
					return f"{data_type}({scale})"
				else:
					return data_type
			
			# Default - return as is
			else:
				return data_type
				
		except Exception as e:
			logger.debug(f"Error formatting data type {data_type}: {str(e)}")
			return data_type

	async def get_database_access_controls(self):
		try:
			privileges_by_grantee = defaultdict(set)
			EXCLUDED_PRINCIPALS = {
				"guest", "INFORMATION_SCHEMA", "sys", "public",
				"NT AUTHORITY\\NETWORK SERVICE", "BUILTIN\\Administrators"
				}

			def is_relevant_principal(name: str) -> bool:
				if name.startswith("##") and name.endswith("##"):
					return False
				if name in EXCLUDED_PRINCIPALS:
					return False
				return True

			def fetch_as_dicts(query):
				self.cursor.execute(query)
				rows = self.cursor.fetchall()
				return rows

			# --- Object-level (tables, views, procs)
			query_objects = """
			SELECT 
				COALESCE(prin.name, 'UNKNOWN') AS principal_name,
				perm.permission_name,
				perm.state_desc,
				obj.name AS object_name
			FROM sys.database_permissions perm
			JOIN sys.database_principals prin 
				ON perm.grantee_principal_id = prin.principal_id
			LEFT JOIN sys.objects obj 
				ON perm.major_id = obj.object_id
			"""

			# --- Schema-level
			query_schema = """
			SELECT
				COALESCE(prin.name, 'UNKNOWN') AS principal_name,
				perm.permission_name,
				perm.state_desc,
				sch.name AS object_name
			FROM sys.database_permissions perm
			JOIN sys.database_principals prin 
				ON perm.grantee_principal_id = prin.principal_id
			LEFT JOIN sys.schemas sch 
				ON perm.major_id = sch.schema_id
			"""

			# --- Database-level
			query_db = """
			SELECT  
				COALESCE(prin.name, 'UNKNOWN') AS principal_name,
				perm.permission_name,  
				perm.state_desc,  
				NULL AS object_name
			FROM sys.database_permissions perm  
			JOIN sys.database_principals prin  
				ON perm.grantee_principal_id = prin.principal_id
			"""

			# --- Server-level sysadmin detection
			query_sysadmins = """
			SELECT sp.name AS principal_name
			FROM sys.server_role_members rm
			JOIN sys.server_principals sp 
				ON rm.member_principal_id = sp.principal_id
			JOIN sys.server_principals rp 
				ON rm.role_principal_id = rp.principal_id
			WHERE rp.name = 'sysadmin'
			"""

			all_rows = []
			for q in (query_objects, query_schema, query_db):
				all_rows.extend(fetch_as_dicts(q))

			# Map privileges per grantee
			for row in all_rows:
				grantee = row["principal_name"]
				if not is_relevant_principal(grantee):
					continue
				privilege = f"{row['permission_name']} ({row['state_desc']})"
				privileges_by_grantee[grantee].add(privilege)

			# Detect sysadmins
			sysadmin_rows = fetch_as_dicts(query_sysadmins)
			sysadmins = {r["principal_name"] for r in sysadmin_rows}

			# Get role names
			role_rows = fetch_as_dicts("""
			SELECT name FROM sys.database_principals
			WHERE type = 'R' AND is_fixed_role = 0;
			""")
			role_names = {r["name"] for r in role_rows}

			access_controls = []
			for grantee, privileges in privileges_by_grantee.items():
				if not is_relevant_principal(grantee):
					continue 
				if grantee.upper() == "PUBLIC":
					role_type = "public"
				elif grantee in role_names:
					role_type = "role"
				else:
					role_type = "user"

				# Override if sysadmin
				if grantee in sysadmins:
					effective_access = "full"
				else:
					effective_access = self._map_privileges_to_access(grantee,privileges)

				access_controls.append({
					"user_or_role": grantee,
					"role": role_type,
					"access": effective_access,
				})

			# Also include any sysadmins that had no explicit DB permissions
			for sa in sysadmins:
				if sa not in privileges_by_grantee and is_relevant_principal(sa):
					access_controls.append({
						"user_or_role": sa,
						"role": "user",
						"access": "full",
					})

			return access_controls

		except Exception as e:
			logger.error(f"Failed to get MSSQL access controls: {e}")
			return []

	def _map_privileges_to_access(self, user_or_role: str, privileges: set) -> str:
		if user_or_role.lower() == "dbo":
			return "full" 

		perms = {p.split()[0].upper() for p in privileges}

		read_privs  = {"SELECT", "VIEW"}
		write_privs = {"INSERT", "UPDATE", "DELETE", "ALTER", "TRUNCATE"}
		exec_privs  = {"EXECUTE"}
		admin_privs = {"CONTROL", "IMPERSONATE", "TAKE", "ALTER"}

		if perms & admin_privs:
			return "full"
		if perms & write_privs:
			return "write"
		if perms & read_privs:
			return "read"
		if perms & exec_privs:
			return "execute"
		return "none"

	async def get_encryption_status(self):
		try:
			self.cursor.execute("SELECT encrypt_option FROM sys.dm_exec_connections WHERE session_id = @@SPID;")
			result = self.cursor.fetchone()
			if result and result.get("encrypt_option", "").lower() == "true":
				return True
			else:
				return False
		except Exception as e:
			logger.warning(f"Failed to check MSSQL encryption status: {e}")
			return "Unknown"

	async def infra_scan(self):
			try:
				logger.info(f"Starting infra scan for service: {self.service_name}")
				await self.get_service_details()

				access_permissions = []
				access_permissions = await self.get_database_access_controls()
				

				if self.auth_type.lower() == AuthTypes.IAM.value and not self.host:
					self.discovered_instances = await self.discover_rds_instances()
				else:
					self.discovered_instances = [{
						"host": self.host,
						"port": self.port,
						"db_name": self.dbname,
						"instance_id": self.service_name,
					}]
				
				aggregated_metadata = []
				system_schemas = {
					"SYS", "SYSTEM", "XDB", "OUTLN", "DBSNMP", "CTXSYS", "ORDSYS", "ORDDATA", "MDSYS",
					"OLAPSYS", "WMSYS", "APPQOSSYS", "AUDSYS", "ANONYMOUS", "OJVMSYS", "INFORMATION_SCHEMA"
				}

				for instance in self.discovered_instances:
					host = instance["host"]
					port = instance["port"]
					dbname = instance.get("db_name") or self.dbname
					region = self.region or get_region_from_host(host)
					if region == "Unknown" and self.service.get("location"):
						region = self.service["location"]

					self.host = host
					self.port = port
					self.dbname = dbname

					await self.connect()
					encryption_status = await self.get_encryption_status()

					databases = await self.get_databases()

					for db in databases:
						try:
							logger.info(f"Switching to database: {db}")
							await self.use_database(db)

							self.cursor.execute("""
								SELECT name FROM sys.schemas WHERE principal_id IS NOT NULL
							""")
						

							schemas = [row["name"] for row in self.cursor.fetchall()]
							valid_schemas = [s for s in schemas if s.upper() not in system_schemas]
							logger.info(f"Valid schemas in {db}: {valid_schemas}")
							
							access = [{**perm, "asset_name": db} for perm in access_permissions]
							load_access_controls(access)
							
							for schema in valid_schemas:
								try:
									logger.info(f"Processing schema: {schema} in DB: {db}")

									self.cursor.execute(f"""
										SELECT COUNT(*) AS table_count 
										FROM INFORMATION_SCHEMA.TABLES 
										WHERE TABLE_SCHEMA = '{schema}'
									""")
									table_count = self.cursor.fetchone()["table_count"]

									if table_count == 0:
										logger.info(f"Skipping schema {schema} - 0 tables found.")
										continue

									self.cursor.execute("SELECT SYSTEM_USER AS user_name")
									db_owner = self.cursor.fetchone()["user_name"]

									self.cursor.execute(f"""
										SELECT SUM(a.total_pages) * 8 * 1024 AS total_bytes
										FROM sys.tables t
										INNER JOIN sys.indexes i ON t.object_id = i.object_id
										INNER JOIN sys.partitions p ON i.object_id = p.object_id AND i.index_id = p.index_id
										INNER JOIN sys.allocation_units a ON p.partition_id = a.container_id
										WHERE SCHEMA_NAME(t.schema_id) = '{schema}'
									""")
									size_bytes = self.cursor.fetchone()["total_bytes"] or 0
									size_mb = round(size_bytes / 1024 / 1024, 2)

									access_permissionss = [{"role": "db_datareader"}, {"role": "db_datawriter"}]

									aggregated_metadata.append({
										"dbname": f"{db}.{schema}",
										"db_owner": db_owner,
										"total_size_mb": size_mb,
										"table_count": table_count,
										"access_permissions": access_permissionss,
										"region": region,
									})

									asset = AssetsDetails(
										asset_name=db,
										service_provider=self.service_provider,
										type="database",
										category="structured",
										location=region,
										owner=db_owner,
										security=bool(encryption_status),
										size=size_bytes,
										count=table_count,
										service_name=self.service_name,
										steward=str(self.service_steward),
					
									)
									load_asset_details([asset])

								except Exception as ex:
									logger.error(f"Failed to process schema {schema} in {db}. Error: {ex}", exc_info=True)
									continue

						except Exception as db_ex:
							logger.error(f"Failed to process DB {db}: {db_ex}", exc_info=True)
							continue

					self.close_connection()

				return {
					"databases": [inst["db_name"] for inst in self.discovered_instances],
					"region": self.region,
					"metadata": aggregated_metadata,
				}

			except Exception as e:
				logger.error(f"Infrastructure scan failed: {e}", exc_info=True)
				return {"error": str(e)}

			finally:
				self.close_connection()


	# async def infra_scan(self):
		# try:
		# 	logger.info(f"Starting infra scan for service: {self.service_name}")
		# 	await self.get_service_details()
		# 	await self.connect()
			
		# 	databases = await self.get_databases()
		# 	region = self.region or get_region_from_host(self.host)
			
		# 	logger.info(
		# 		f"Infra scan result - Region: {region}, Host: {self.host}, Port: {self.port}, "
		# 		f"Accessible Databases: {len(databases)}, Database List: {databases}"
		# 	)
			
		# 	return {
		# 		"databases": databases, 
		# 		"region": region,
		# 		"host": self.host,
		# 		"port": self.port,
		# 		"accessible_databases_count": len(databases)
		# 	}

		# except Exception as e:
		# 	logger.error(f"Infrastructure scan failed: {e}")
		# 	return {"error": str(e)}
		# finally:
		# 	self.close_connection()

	
	async def deep_scan(self):
		"""Enhanced deep scan with comprehensive error handling"""
		try:
			await self.get_service_details()
			await self.connect()
			await self.infra_scan()
			# Get databases user has access to
			databases = await self.get_databases()
			
			# If specific databases are configured, filter to only those
			if self.databases:
				databases = [d for d in databases if d in self.databases]
				logger.info(f"Filtering to configured databases: {databases}")

			if not databases:
				logger.warning("No accessible databases found")
				return
			
			total_tables_processed = 0
			total_tables_found = 0
			
			for db in databases:
				try:
					logger.info(f"Scanning database: {db}")
					
					# Switch to database
					if not await self.use_database(db):
						logger.warning(f"Skipping database {db} - no access")
						continue
					
					# Get tables in this database
					tables = await self.get_tables(db)
					total_tables_found += len(tables)
					
					if not tables:
						logger.warning(f"No accessible tables found in database: {db}")
						continue
					
					logger.info(f"Found {len(tables)} tables in database {db}")
					
					for table_info in tables:
						try:
							table_name = table_info["table_name"]
							schema = table_info["schema"]
							qualified_name = table_info["qualified_name"]
							
							table_res = await self.scan_table(table_name, db, schema, qualified_name)
							if table_res:
								total_tables_processed += 1
								yield table_res
								
						except Exception as table_error:
							logger.error(
								f"Table scan failed. DB: {db}, Table: {table_info.get('table_name', 'unknown')}, "
								f"Schema: {table_info.get('schema', 'unknown')}. Service: {self.service_name}. "
								f"Error: {str(table_error)}",
								exc_info=True,
							)
							continue
							
				except Exception as db_error:
					logger.error(
						f"Deep scan failed for db: {db}. Service: {self.service_name}. Error: {str(db_error)}",
						exc_info=True,
					)
					continue
					
			logger.info(f"Deep scan completed. Processed {total_tables_processed} out of {total_tables_found} tables found")
					
		except Exception as e:
			logger.error(f"Deep scan failed. Service: {self.service_name}. Error: {str(e)}", exc_info=True)
		finally:
			self.close_connection()

	# async def scan_table(self, table_name, db_name, schema_name, qualified_name):
	# 	"""Enhanced table scanning with comprehensive error handling"""
	# 	try:
	# 		logger.info(f"Scanning table: {qualified_name} in database: {db_name}")
			
	# 		# Ensure connection before starting table scan
	# 		if not await self.ensure_connection(f"scan_table_{qualified_name}"):
	# 			logger.error(f"Failed to establish connection for table scan: {qualified_name}")
	# 			return None
			
	# 		# Verify table exists and user has access
	# 		exists, actual_schema = await self.table_exists_with_access(table_name, schema_name, db_name)
	# 		if not exists:
	# 			logger.warning(f"Table '{qualified_name}' does not exist or is not accessible in database '{db_name}'")
	# 			return None
				
	# 		if actual_schema and actual_schema != schema_name:
	# 			schema_name = actual_schema
	# 			qualified_name = f"{schema_name}.{table_name}"
	# 			logger.info(f"Using actual schema '{schema_name}' for table '{table_name}'")

	# 		# Get table columns with error handling
	# 		columns = await self.get_table_columns(schema_name, table_name)
	# 		if not columns:
	# 			logger.warning(f"No columns found for table '{qualified_name}' in database '{db_name}'")
	# 			# Try to proceed anyway - maybe table has no explicit columns metadata but still has data
	# 			columns = []
			
	# 		# Get row count with error handling
	# 		try:
	# 			row_count = await self.get_table_row_count(schema_name, table_name)
	# 		except Exception as row_count_error:
	# 			logger.warning(f"Could not get row count for {qualified_name}: {str(row_count_error)}")
	# 			row_count = 0
			
	# 		# Get sample data with error handling
	# 		try:
	# 			rows = await self.get_table_sample_data(schema_name, table_name)
	# 		except Exception as sample_error:
	# 			logger.warning(f"Could not get sample data for {qualified_name}: {str(sample_error)}")
	# 			rows = []

	# 		# If we have no columns but have sample data, infer columns from sample
	# 		if not columns and rows:
	# 			try:
	# 				first_row = rows[0]
	# 				inferred_columns = []
	# 				for col_name in first_row.keys():
	# 					inferred_columns.append(
	# 						TableColumn(
	# 							column_name=col_name,
	# 							data_type="unknown",
	# 							index=False,
	# 							primary_key=False
	# 						)
	# 					)
	# 				columns = inferred_columns
	# 				logger.info(f"Inferred {len(columns)} columns from sample data for {qualified_name}")
	# 			except Exception as infer_error:
	# 				logger.warning(f"Could not infer columns from sample data: {str(infer_error)}")

	# 		# Prepare CSV data with enhanced error handling
	# 		column_names = [col.column_name for col in columns] if columns else []
	# 		row_lists = []
			
	# 		try:
	# 			for row in rows:
	# 				row_list = []
	# 				if column_names:
	# 					for col in column_names:
	# 						value = row.get(col)
	# 						# Handle None values and convert to string safely
	# 						if value is None:
	# 							row_list.append("")
	# 						else:
	# 							try:
	# 								# Handle special characters and newlines
	# 								str_value = str(value).replace('\r\n', ' ').replace('\n', ' ').replace('\r', ' ')
	# 								# Limit extremely long values
	# 								if len(str_value) > 32000:  # Excel cell limit
	# 									str_value = str_value[:32000] + "..."
	# 								row_list.append(str_value)
	# 							except Exception as str_error:
	# 								logger.debug(f"String conversion error for column {col}: {str(str_error)}")
	# 								row_list.append("[CONVERSION_ERROR]")
	# 				else:
	# 					# If no column info, try to get all values
	# 					for key, value in row.items():
	# 						try:
	# 							str_value = str(value) if value is not None else ""
	# 							str_value = str_value.replace('\r\n', ' ').replace('\n', ' ').replace('\r', ' ')
	# 							if len(str_value) > 32000:
	# 								str_value = str_value[:32000] + "..."
	# 							row_list.append(str_value)
	# 						except Exception:
	# 							row_list.append("[ERROR]")
					
	# 				row_lists.append(row_list)
					
	# 		except Exception as row_processing_error:
	# 			logger.warning(f"Error processing rows for {qualified_name}: {str(row_processing_error)}")

	# 		# Generate table URI
	# 		table_uri = f"mssql://{db_name}/{qualified_name}"
	# 		logger.info(f"Constructed table_uri for {qualified_name}: {table_uri}")

	# 		# Check if table is already processed
	# 		try:
	# 			if self.db.skip_table(table_uri):
	# 				logger.info(f"Skipping table {qualified_name} - Already processed")
	# 				return None
	# 		except Exception as skip_check_error:
	# 			logger.warning(f"Could not check skip status for {table_uri}: {str(skip_check_error)}")

	# 		# Create CSV file with error handling
	# 		try:
	# 			create_folder(self.local_data_dir)
	# 			safe_filename = f"{db_name}_{schema_name}_{table_name}_{str(uuid4())}.csv"
	# 			output_file = os.path.join(self.local_data_dir, safe_filename)
				
	# 			with open(output_file, mode="w", newline="", encoding="utf-8") as file:
	# 				writer = csv.writer(file, quoting=csv.QUOTE_ALL)
					
	# 				# Write headers
	# 				if column_names:
	# 					writer.writerow(column_names)
	# 				elif row_lists:
	# 					# If no column names but have data, write generic headers
	# 					first_row_len = len(row_lists[0]) if row_lists else 0
	# 					generic_headers = [f"column_{i+1}" for i in range(first_row_len)]
	# 					writer.writerow(generic_headers)
					
	# 				# Write data rows
	# 				writer.writerows(row_lists)
					
	# 		except Exception as csv_error:
	# 			logger.error(f"Failed to create CSV file for {qualified_name}: {str(csv_error)}")
	# 			return None

	# 		logger.info(f"Successfully scanned table {qualified_name} in database {db_name}. Rows: {row_count}, Columns: {len(columns)}, Sample rows: {len(row_lists)}")

	# 		return TableMetadata(
	# 			service_name=self.service_name,
	# 			service_type=self.service_type,
	# 			service_provider=self.service_provider,
	# 			sub_service=self.sub_service,
	# 			db_type=DBTypes.MSSQL.value,
	# 			db_name=db_name,
	# 			table_name=qualified_name,
	# 			table_size=0,
	# 			no_of_rows=row_count,
	# 			table_uri=table_uri,
	# 			columns=columns,
	# 			local_filepath=output_file,
	# 			details={
	# 				"host": self.host,
	# 				"port": self.port,
	# 				"region": self.region or get_region_from_host(self.host),
	# 				"sample_rows": len(row_lists),
	# 				"accessible": True,
	# 				"schema": schema_name,
	# 				"original_table_name": table_name
	# 			}
	# 		)
			
	# 	except Exception as e:
	# 		logger.error(
	# 			f"Table scan failed for table '{table_name}' (schema: {schema_name}) in database '{db_name}'. Error: {str(e)}", 
	# 			exc_info=True
	# 		)
	# 		return None

	async def scan_table(self, table_name, db_name, schema_name, qualified_name):
		try:
			logger.info(f"Scanning table: {qualified_name} in database: {db_name}")
			
			# Ensure connection before starting table scan
			if not await self.ensure_connection(f"scan_table_{qualified_name}"):
				logger.error(f"Failed to establish connection for table scan: {qualified_name}")
				return None
			
			# Verify table exists and user has access
			exists, actual_schema = await self.table_exists_with_access(table_name, schema_name, db_name)
			if not exists:
				logger.warning(f"Table '{qualified_name}' does not exist or is not accessible in database '{db_name}'")
				return None
				
			if actual_schema and actual_schema != schema_name:
				schema_name = actual_schema
				qualified_name = f"{schema_name}.{table_name}"
				logger.info(f"Using actual schema '{schema_name}' for table '{table_name}'")

			columns = await self.get_table_columns(schema_name, table_name)
			if not columns:
				logger.warning(f"No columns found for table '{qualified_name}' in database '{db_name}'")
				columns = []

			try:
				foreign_keys_info = await self.extract_foreign_keys(table_name)
				unique_constraints_info = await self.extract_unique_constraints(table_name)
				check_constraints_info = await self.extract_check_constraints(table_name)
				column_properties_info = await self.extract_column_properties(table_name)
			except Exception as constraint_error:
				logger.warning(f"Failed to extract constraints for {qualified_name}: {str(constraint_error)}")
				foreign_keys_info, unique_constraints_info, check_constraints_info, column_properties_info = {}, {}, {}, {}
			# ---------------------------------------------------------

			# Get row count
			try:
				row_count = await self.get_table_row_count(schema_name, table_name)
			except Exception as row_count_error:
				logger.warning(f"Could not get row count for {qualified_name}: {str(row_count_error)}")
				row_count = 0

			# Get sample data
			try:
				rows = await self.get_table_sample_data(schema_name, table_name)
			except Exception as sample_error:
				logger.warning(f"Could not get sample data for {qualified_name}: {str(sample_error)}")
				rows = []

			# Infer columns if missing
			if not columns and rows:
				try:
					first_row = rows[0]
					inferred_columns = [
						TableColumn(column_name=col_name, data_type="unknown", index=False, primary_key=False)
						for col_name in first_row.keys()
					]
					columns = inferred_columns
				except Exception as infer_error:
					logger.warning(f"Could not infer columns from sample data: {str(infer_error)}")

			# CSV prep
			column_names = [col.column_name for col in columns] if columns else []
			row_lists = []
			try:
				for row in rows:
					row_list = []
					for col in column_names:
						value = row.get(col)
						if value is None:
							row_list.append("")
						else:
							try:
								str_value = str(value).replace('\r\n', ' ').replace('\n', ' ').replace('\r', ' ')
								if len(str_value) > 32000:
									str_value = str_value[:32000] + "..."
								row_list.append(str_value)
							except Exception as str_error:
								logger.debug(f"String conversion error for column {col}: {str(str_error)}")
								row_list.append("[CONVERSION_ERROR]")
					row_lists.append(row_list)
			except Exception as row_processing_error:
				logger.warning(f"Error processing rows for {qualified_name}: {str(row_processing_error)}")

			# Generate table URI
			table_uri = f"mssql://{db_name}/{qualified_name}"
			logger.info(f"Constructed table_uri for {qualified_name}: {table_uri}")

			try:
				if self.db.skip_table(table_uri):
					logger.info(f"Skipping table {qualified_name} - Already processed")
					return None
			except Exception as skip_check_error:
				logger.warning(f"Could not check skip status for {table_uri}: {str(skip_check_error)}")

			try:
				create_folder(self.local_data_dir)
				safe_filename = f"{db_name}_{schema_name}_{table_name}_{str(uuid4())}.csv"
				output_file = os.path.join(self.local_data_dir, safe_filename)
				
				with open(output_file, mode="w", newline="", encoding="utf-8") as file:
					writer = csv.writer(file, quoting=csv.QUOTE_ALL)
					if column_names:
						writer.writerow(column_names)
					elif row_lists:
						generic_headers = [f"column_{i+1}" for i in range(len(row_lists[0]))]
						writer.writerow(generic_headers)
					writer.writerows(row_lists)
			except Exception as csv_error:
				logger.error(f"Failed to create CSV file for {qualified_name}: {str(csv_error)}")
				return None

			logger.info(f"Successfully scanned table {qualified_name} in database {db_name}. "
						f"Rows: {row_count}, Columns: {len(columns)}, Sample rows: {len(row_lists)}")

			total_foreign_keys = sum(len(fks) for fks in foreign_keys_info.values())
			total_unique_constraints = sum(len(ucs) for ucs in unique_constraints_info.values())
			total_check_constraints = sum(len(ccs) for ccs in check_constraints_info.values())
			columns_with_constraints = len([
				col for col, cons in column_properties_info.items() if cons
			])

			details = {
				"host": self.host,
				"port": self.port,
				"region": self.region or get_region_from_host(self.host),
				"sample_rows": len(row_lists),
				"accessible": True,
				"schema": schema_name,
				"original_table_name": table_name,
				"constraints_summary": {
					"total_foreign_keys": total_foreign_keys,
					"total_unique_constraints": total_unique_constraints,
					"total_check_constraints": total_check_constraints,
					"columns_with_constraints": columns_with_constraints,
					"total_columns": len(columns),
					"constraint_coverage_percentage": round((columns_with_constraints / len(columns)) * 100, 2) if columns else 0
				},
				"table_constraints": {
					"foreign_keys": foreign_keys_info,
					"unique_constraints": unique_constraints_info,
					"check_constraints": check_constraints_info
				}
			}

			return TableMetadata(
				service_name=self.service_name,
				service_type=self.service_type,
				service_provider=self.service_provider,
				sub_service=self.sub_service,
				db_type=DBTypes.MSSQL.value,
				db_name=db_name,
				table_name=qualified_name,
				table_size=0,
				no_of_rows=row_count,
				table_uri=table_uri,
				columns=columns,
				local_filepath=output_file,
				details=details
			)
			
		except Exception as e:
			logger.error(
				f"Table scan failed for table '{table_name}' (schema: {schema_name}) in database '{db_name}'. Error: {str(e)}", 
				exc_info=True
			)
			return None

	# Legacy methods for backward compatibility - Enhanced
	async def table_exists(self, table_name: str, db_name: str = None) -> bool:
		"""Legacy method - maintained for backward compatibility"""
		try:
			exists, _ = await self.table_exists_with_access(table_name, None, db_name)
			return exists
		except Exception as e:
			logger.error(f"Error checking table existence for {table_name}: {str(e)}")
			return False

	async def extract_foreign_keys(self, table_name: str) -> dict:
		try:
			self.cursor.execute("""
				SELECT
					fk.name AS constraint_name,
					COL_NAME(fkc.parent_object_id, fkc.parent_column_id) AS column_name,
					OBJECT_NAME(fk.referenced_object_id) AS referenced_table,
					COL_NAME(fkc.referenced_object_id, fkc.referenced_column_id) AS referenced_column,
					fk.delete_referential_action_desc AS delete_action,
					fk.update_referential_action_desc AS update_action
				FROM sys.foreign_keys fk
				INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
				WHERE OBJECT_NAME(fk.parent_object_id) = %s
			""", (table_name,))

			rows = self.cursor.fetchall()
			logger.info(f"Foreign key query returned {len(rows)} rows for table '{table_name}'")

			fk_data = {}
			for row in rows:
				column_name = row["column_name"]
				if column_name not in fk_data:
					fk_data[column_name] = []
				fk_data[column_name].append({
					"constraint_name": row["constraint_name"],
					"referenced_table": row["referenced_table"],
					"referenced_column": row["referenced_column"],
					"delete_action": row["delete_action"],
					"update_action": row["update_action"]
				})
			return fk_data
		except Exception as e:
			logger.error(f"Failed to extract foreign keys for table '{table_name}': {e}")
			return {}

	async def extract_unique_constraints(self, table_name: str) -> dict:
		try:
			self.cursor.execute("""
				SELECT
					tc.CONSTRAINT_NAME,
					kcu.COLUMN_NAME,
					tc.CONSTRAINT_TYPE
				FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
				INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
					ON tc.CONSTRAINT_NAME = kcu.CONSTRAINT_NAME
					AND tc.TABLE_NAME = kcu.TABLE_NAME
				WHERE tc.TABLE_NAME = %s
					AND tc.CONSTRAINT_TYPE = 'UNIQUE'
				ORDER BY kcu.ORDINAL_POSITION
			""", (table_name,))

			rows = self.cursor.fetchall()

			unique_data = {}
			for row in rows:
				column_name = row["COLUMN_NAME"]
				if column_name not in unique_data:
					unique_data[column_name] = []
				constraint_name = row["CONSTRAINT_NAME"]
				is_system_named = constraint_name.startswith(('UQ__', 'PK__', 'DF__'))
				unique_data[column_name].append({
					"constraint_name": constraint_name,
					"constraint_type": row["CONSTRAINT_TYPE"],
					"is_system_named": is_system_named
				})
			return unique_data
		except Exception as e:
			logger.error(f"Failed to extract unique constraints for table '{table_name}': {e}")
			return {}
		
	async def extract_column_properties(self, table_name: str) -> dict:
		try:
			self.cursor.execute("""
				SELECT
					c.COLUMN_NAME,
					c.IS_NULLABLE,
					c.COLUMN_DEFAULT,
					c.CHARACTER_MAXIMUM_LENGTH,
					c.NUMERIC_PRECISION,
					c.NUMERIC_SCALE,
					c.COLLATION_NAME,
					COLUMNPROPERTY(OBJECT_ID(c.TABLE_SCHEMA + '.' + c.TABLE_NAME), c.COLUMN_NAME, 'IsIdentity') AS is_identity,
					IDENT_SEED(c.TABLE_SCHEMA + '.' + c.TABLE_NAME) AS identity_seed,
					IDENT_INCR(c.TABLE_SCHEMA + '.' + c.TABLE_NAME) AS identity_increment
				FROM INFORMATION_SCHEMA.COLUMNS c
				WHERE c.TABLE_NAME = %s
				ORDER BY c.ORDINAL_POSITION
			""", (table_name,))

			properties_data = {}
			for row in self.cursor.fetchall():
				column_name = row["COLUMN_NAME"]
				is_nullable = row["IS_NULLABLE"] == "YES"
				default_value = row["COLUMN_DEFAULT"]
				if default_value and default_value.startswith("(") and default_value.endswith(")"):
					default_value = default_value[1:-1]
				properties_data[column_name] = {
					"nullable": is_nullable,
					"default_value": default_value,
					"is_identity": bool(row["is_identity"]),
					"character_maximum_length": row["CHARACTER_MAXIMUM_LENGTH"],
					"numeric_precision": row["NUMERIC_PRECISION"],
					"numeric_scale": row["NUMERIC_SCALE"],
					"collation_name": row["COLLATION_NAME"]
				}
				if row["is_identity"]:
					properties_data[column_name].update({
						"identity_seed": row["identity_seed"],
						"identity_increment": row["identity_increment"]
					})
			return properties_data
		except Exception as e:
			logger.error(f"Failed to extract column properties for table '{table_name}': {e}")
			return {}

	async def extract_check_constraints(self, table_name: str) -> dict:
		try:
			self.cursor.execute("""
				SELECT
					tc.CONSTRAINT_NAME,
					cc.CHECK_CLAUSE,
					kcu.COLUMN_NAME
				FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
				INNER JOIN INFORMATION_SCHEMA.CHECK_CONSTRAINTS cc
					ON tc.CONSTRAINT_NAME = cc.CONSTRAINT_NAME
				LEFT JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
					ON tc.CONSTRAINT_NAME = kcu.CONSTRAINT_NAME
				WHERE tc.TABLE_NAME = %s
					AND tc.CONSTRAINT_TYPE = 'CHECK'
			""", (table_name,))

			check_data = {}
			for row in self.cursor.fetchall():
				constraint_name = row["CONSTRAINT_NAME"]
				check_clause = row["CHECK_CLAUSE"]
				column_name = row["COLUMN_NAME"]
				if not column_name:
					column_name = "table_level_constraint"
				if column_name not in check_data:
					check_data[column_name] = []
				check_data[column_name].append({
					"constraint_name": constraint_name,
					"check_clause": check_clause,
					"constraint_type": "CHECK"
				})
			return check_data
		except Exception as e:
			logger.error(f"Failed to extract check constraints for table '{table_name}': {e}")
			return {}


	async def get_schema_for_table(self, table_name, db_name=None):
		"""Legacy method - maintained for backward compatibility"""
		try:
			return await self.find_table_in_schemas(table_name, db_name)
		except Exception as e:
			logger.error(f"Error finding schema for table {table_name}: {str(e)}")
			return None

	def close_connection(self):
		"""Enhanced connection cleanup"""
		try:
			if self.cursor:
				try:
					self.cursor.close()
				except Exception as cursor_error:
					logger.debug(f"Error closing cursor: {str(cursor_error)}")
		except:
			pass
			
		try:
			if self.connection:
				try:
					self.connection.close()
					logger.info("MSSQL connection closed")
				except Exception as conn_error:
					logger.debug(f"Error closing connection: {str(conn_error)}")
		except:
			pass
			
		self.cursor = None
		self.connection = None
		self.current_database = None
		self.schema_cache.clear()
		self.last_connection_time = None

	async def handle_connection_errors(self, operation_name: str):
		"""Enhanced connection error handling"""
		max_reconnect_attempts = 3
		reconnect_count = 0
		
		while reconnect_count < max_reconnect_attempts:
			try:
				logger.info(f"Attempting to reconnect for operation: {operation_name} (attempt {reconnect_count + 1})")
				self.close_connection()
				await asyncio.sleep(1 * (reconnect_count + 1))  # Progressive delay
				await self.connect()
				return True
			except Exception as e:
				reconnect_count += 1
				logger.warning(f"Reconnection attempt {reconnect_count} failed: {str(e)}")
				if reconnect_count >= max_reconnect_attempts:
					logger.error(f"Failed to reconnect after {max_reconnect_attempts} attempts")
					return False
					
		return False

	async def validate_table_access(self, table_name: str, db_name: str = None):
		"""Enhanced table access validation"""
		try:
			if db_name and db_name != self.current_database:
				if not await self.use_database(db_name):
					return None
			
			# Check if table_name already includes schema
			if '.' in table_name:
				parts = table_name.split('.', 1)
				if len(parts) == 2:
					schema_part, table_part = parts[0], parts[1]
					exists, verified_schema = await self.table_exists_with_access(table_part, schema_part, db_name)
					if exists:
						return {
							"table_name": table_part,
							"schema": verified_schema,
							"qualified_name": f"{verified_schema}.{table_part}",
							"accessible": True
						}
			
			# Find table in any accessible schema
			exists, found_schema = await self.table_exists_with_access(table_name, None, db_name)
			if exists and found_schema:
				return {
					"table_name": table_name,
					"schema": found_schema,
					"qualified_name": f"{found_schema}.{table_name}",
					"accessible": True
				}
				
			logger.warning(f"Table '{table_name}' not found or not accessible in database '{db_name or self.current_database}'")
			return None
			
		except Exception as e:
			logger.error(f"Failed to validate table access for '{table_name}': {str(e)}")
			return None

	async def get_database_summary(self, db_name):
		"""Enhanced database summary with error handling"""
		try:
			if not await self.use_database(db_name):
				return None
				
			# Get schemas
			schemas = await self.get_all_schemas(db_name)
			
			# Get table count per schema
			schema_info = {}
			total_tables = 0
			
			for schema in schemas:
				try:
					tables = await self.get_tables_by_schema(schema, db_name)
					schema_info[schema] = {
						"table_count": len(tables),
						"tables": [t["table_name"] for t in tables]
					}
					total_tables += len(tables)
				except Exception as schema_error:
					logger.warning(f"Error getting tables for schema {schema}: {str(schema_error)}")
					schema_info[schema] = {
						"table_count": 0,
						"tables": [],
						"error": str(schema_error)
					}
			
			summary = {
				"database": db_name,
				"total_schemas": len(schemas),
				"total_tables": total_tables,
				"schemas": schema_info,
				"accessible": True
			}
			
			logger.info(f"Database summary for {db_name}: {total_tables} tables across {len(schemas)} schemas")
			return summary
			
		except Exception as e:
			logger.error(f"Failed to get database summary for {db_name}: {str(e)}")
			return {"database": db_name, "error": str(e), "accessible": False}

	async def diagnose_connection_issues(self):
		"""Diagnose common connection and permission issues"""
		issues = []
		suggestions = []
		
		try:
			# Test basic connection
			if not self.connection:
				issues.append("No active database connection")
				suggestions.append("Check connection parameters (host, port, username, password)")
				return {"issues": issues, "suggestions": suggestions}
			
			# Test current database access
			if self.current_database:
				try:
					if not await self.ensure_connection("diagnose_connection"):
						issues.append("Failed to ensure valid connection")
						suggestions.append("Connection may be unstable or timing out")
						return {"issues": issues, "suggestions": suggestions}
						
					self.cursor.execute("SELECT DB_NAME() as current_db")
					result = self.cursor.fetchone()
					if result:
						actual_db = result["current_db"]
						if actual_db != self.current_database:
							issues.append(f"Connected to different database: expected {self.current_database}, got {actual_db}")
				except Exception as e:
					issues.append(f"Cannot verify current database: {str(e)}")
			
			# Test schema access
			try:
				schemas = await self.get_all_schemas()
				if not schemas:
					issues.append("No accessible schemas found")
					suggestions.append("Check if user has proper permissions to view schemas")
				else:
					logger.info(f"Found {len(schemas)} accessible schemas: {schemas}")
			except Exception as e:
				issues.append(f"Cannot access schema information: {str(e)}")
				suggestions.append("User may not have sufficient permissions to query system tables")
			
			# Test table access
			try:
				tables = await self.get_tables()
				if not tables:
					issues.append("No accessible tables found")
					suggestions.append("Check if user has SELECT permissions on tables")
				else:
					logger.info(f"Found {len(tables)} accessible tables")
			except Exception as e:
				issues.append(f"Cannot access table information: {str(e)}")
				suggestions.append("User may not have sufficient permissions to query table metadata")
				
		except Exception as e:
			issues.append(f"Connection diagnosis failed: {str(e)}")
			suggestions.append("Check basic connectivity and authentication")
		
		return {"issues": issues, "suggestions": suggestions}

	async def test_table_operations(self, table_name, schema_name=None):
		"""Test various operations on a specific table for debugging"""
		results = {}
		
		try:
			# Test table existence
			exists, found_schema = await self.table_exists_with_access(table_name, schema_name)
			results["exists"] = exists
			results["found_schema"] = found_schema
			
			if not exists:
				results["error"] = "Table does not exist or is not accessible"
				return results
			
			actual_schema = found_schema or schema_name
			qualified_name = f"{actual_schema}.{table_name}"
			
			# Test column access
			try:
				columns = await self.get_table_columns(actual_schema, table_name)
				results["columns_count"] = len(columns)
				results["columns"] = [col.column_name for col in columns[:5]]  # First 5 columns
			except Exception as e:
				results["columns_error"] = str(e)
			
			# Test row count
			try:
				row_count = await self.get_table_row_count(actual_schema, table_name)
				results["row_count"] = row_count
			except Exception as e:
				results["row_count_error"] = str(e)
			
			# Test sample data access
			try:
				sample_data = await self.get_table_sample_data(actual_schema, table_name)
				results["sample_rows"] = len(sample_data)
			except Exception as e:
				results["sample_data_error"] = str(e)
				
		except Exception as e:
			results["test_error"] = str(e)
		
		return results

	# ------------------ Added for user_scan functionality parity with Postgres ------------------
	async def find_primary_keys(self, table_name: str, schema_name: str) -> List[str]:
		"""Find primary key columns for a given table in a schema"""
		try:
			query = """
				SELECT c.COLUMN_NAME
				FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
				JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE c
					ON tc.CONSTRAINT_NAME = c.CONSTRAINT_NAME
					AND tc.CONSTRAINT_SCHEMA = c.CONSTRAINT_SCHEMA
				WHERE tc.TABLE_SCHEMA = %s AND tc.TABLE_NAME = %s AND tc.CONSTRAINT_TYPE = 'PRIMARY KEY'
				ORDER BY c.ORDINAL_POSITION
			"""
			rows = await self.safe_execute_query(query, (schema_name, table_name), operation="fetchall")
			return [row["COLUMN_NAME"] for row in rows] if rows else []
		except Exception as e:
			logger.error(f"Failed to fetch primary key details for table {schema_name}.{table_name}", exc_info=True)
			return []

	async def find_foreign_key_tables(self, pk_table: str, pk_column: str) -> List[dict]:
		"""Find tables and columns that reference the given pk_table.pk_column"""
		try:
			query = """
				SELECT 
					s.name AS table_schema,
					t.name AS table_name,
					c.name AS column_name
				FROM sys.foreign_key_columns fkc
				JOIN sys.tables t ON fkc.parent_object_id = t.object_id
				JOIN sys.schemas s ON t.schema_id = s.schema_id
				JOIN sys.columns c ON fkc.parent_object_id = c.object_id AND fkc.parent_column_id = c.column_id
				JOIN sys.tables rt ON fkc.referenced_object_id = rt.object_id
				JOIN sys.columns rc ON fkc.referenced_object_id = rc.object_id AND fkc.referenced_column_id = rc.column_id
				WHERE rt.name = %s AND rc.name = %s
			"""
			rows = await self.safe_execute_query(query, (pk_table, pk_column), operation="fetchall")
			fk_tables = []
			for row in rows or []:
				fk_tables.append({
					"table_schema": row["table_schema"],
					"table_name": row["table_name"],
					"column_name": row["column_name"],
				})
			return fk_tables
		except Exception as e:
			logger.error(f"Failed to fetch foreign key details for referenced {pk_table}.{pk_column}", exc_info=True)
			return []

	async def find_matching_rows_from_table(self, table_schema: str, table_name: str, column_name: str, values: List[str]) -> List[dict]:
		"""Find rows in schema.table where column matches any of the provided values (LIKE for strings, IN for numbers)"""
		try:
			# Determine data type
			data_type_query = """
				SELECT DATA_TYPE 
				FROM INFORMATION_SCHEMA.COLUMNS 
				WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s AND COLUMN_NAME = %s
			"""
			row = await self.safe_execute_query(data_type_query, (table_schema, table_name, column_name), operation="fetchone")
			if not row:
				logger.info(f"Column {column_name} not found in {table_schema}.{table_name}")
				return []
			data_type = (row.get("DATA_TYPE") or "").lower()

			# Build and run query
			if data_type in ("varchar", "nvarchar", "nchar", "char", "text", "ntext"):
				patterns = [f"%{v}%" for v in values]
				if not patterns:
					return []
				conditions = " OR ".join([f"[{column_name}] LIKE %s" for _ in patterns])
				query = f"SELECT TOP 20 * FROM [{table_schema}].[{table_name}] WHERE {conditions}"
				rows = await self.safe_execute_query(query, tuple(patterns), operation="fetchall")
			else:
				# numeric-ish: try to cast to int/float
				casted_values: List[float] = []
				for v in values:
					try:
						if "." in str(v):
							casted_values.append(float(v))
						else:
							casted_values.append(int(v))
					except ValueError:
						continue
				if not casted_values:
					return []
				placeholders = ",".join(["%s"] * len(casted_values))
				query = f"SELECT TOP 20 * FROM [{table_schema}].[{table_name}] WHERE [{column_name}] IN ({placeholders})"
				rows = await self.safe_execute_query(query, tuple(casted_values), operation="fetchall")

			return rows or []
		except Exception as e:
			logger.error(f"Error fetching from {table_schema}.{table_name}: {e}")
			return []

	async def fetch_related_data_recursively(self, matching_tables: List[dict]) -> List[dict]:
		"""Traverse FK graph to fetch related rows starting from initial matches"""
		results: List[dict] = []
		visited_tables = set()
		try:
			while len(matching_tables) > 0:
				next_tables: List[dict] = []
				for pii_match in matching_tables:
					table_schema = pii_match.get("table_schema")
					table_name = pii_match.get("table_name")
					column_name = pii_match.get("column_name")
					values = pii_match.get("values")
					qualified = f"{table_schema}.{table_name}"
					if qualified in visited_tables:
						continue

					matching_rows = await self.find_matching_rows_from_table(table_schema, table_name, column_name, values)
					if len(matching_rows) == 0:
						continue

					results.append({
						"service_name": self.service_name,
						"service_provider": self.service_provider,
						"sub_service": self.sub_service,
						"db_name": self.current_database or self.dbname,
						"table_schema": table_schema,
						"table_name": table_name,
						"column_name": column_name,
						"matching_rows": matching_rows,
					})
					visited_tables.add(qualified)

					pk_keys = await self.find_primary_keys(table_name, table_schema)
					for pk_key in pk_keys:
						values = list({(m.get(pk_key)) for m in matching_rows if pk_key in m})
						fk_tables = await self.find_foreign_key_tables(table_name, pk_key)
						for fk_table in fk_tables:
							next_tables.append({
								"table_schema": fk_table.get("table_schema"),
								"table_name": fk_table.get("table_name"),
								"column_name": fk_table.get("column_name"),
								"values": values,
							})

				matching_tables = next_tables
		except Exception as e:
			logger.error("Error while fetching user related data", exc_info=True)
		return results

	async def user_scan(self, inital_tables: List[dict]):
		"""Start from initial PII matches and traverse related tables via FKs, yielding results per database"""
		try:
			await self.get_service_details()
			await self.connect()
			
			databases = await self.get_databases()
			if len(self.databases) > 0:
				databases = [d for d in self.databases if d in databases]
			
			for db in databases:
				try:
					if not await self.use_database(db):
						continue
					current_db_tables = [m for m in inital_tables if m.get("db_name") == db]
					if len(current_db_tables) == 0:
						continue
					logger.info(f"db_name: {db}, current_db_tables: {current_db_tables}")
					# Normalize schema and table names in inputs
					normalized_tables = []
					for item in current_db_tables:
						schema = item.get("table_schema") or "dbo"
						tname = item.get("table_name") or ""
						# If table_name includes schema (e.g., 'dbo.Customers'), extract it
						if "." in tname:
							parts = tname.split(".", 1)
							if len(parts) == 2:
								schema_from_name, table_only = parts[0], parts[1]
								if schema.lower() == "public" or schema.lower() not in ("dbo", schema_from_name.lower()):
									schema = schema_from_name
								tname = table_only
						normalized_tables.append({
							**item,
							"table_schema": schema,
							"table_name": tname
						})
					logger.info(f"db_name: {db}, normalized_tables: {normalized_tables}")
					results = await self.fetch_related_data_recursively(normalized_tables)
					if len(results):
						yield results
				except Exception as e:
					logger.error(
						f"Deep scan failed for db: {db}. Service: {self.service_name}",
						exc_info=True,
					)
		except Exception as e:
			logger.error(f"Deep scan failed. Service: {self.service_name}", exc_info=True)