import pymssql
import logging
from src.utils.exceptions import ClientException, ServerException


logger = logging.getLogger("Unstructure")


async def test_mssql_connection(data: dict) -> dict:
    creds = data.get("credentials", {})
    try:
        logger.info(f"Attempting MSSQL connection to host: {creds.get('host')}")

        conn = pymssql.connect(
            server=creds.get('host'),
            port=int(creds.get('port', 1433)),
            user=creds.get('user'),
            password=creds.get('password'),
            database=creds.get('dbname'),
            timeout=30
        )
        
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        cursor.close()
        conn.close()

        logger.info("MSSQL connection test successful.")
        return {
            "status": True,
            "message": "MSSQL connection successful"
        }

    except pymssql.OperationalError as e:
        logger.warning(f"MSSQL authentication failed: {str(e)}")
        raise ClientException("Invalid MSSQL credentials or connection issue", code=401, excep=e)

    except Exception as e:
        logger.exception("Unexpected error during MSSQL connection test")
        raise ServerException("MSSQL test failed", excep=e)
