import re


def get_region_from_host(host: str) -> str:
    try:
        # Pattern for AWS RDS SQL Server instances
        match = re.search(
            r"\.(?P<region>[a-z]{2}-[a-z]+-\d)\.rds\.amazonaws\.com", str(host)
        )
        if match:
            region = match.group("region")
            if region:
                return region

        # Pattern for Azure SQL Server instances
        azure_match = re.search(
            r"\.database\.windows\.net", str(host)
        )
        if azure_match:
            return "azure"

    except Exception:
        pass

    return "Unknown"