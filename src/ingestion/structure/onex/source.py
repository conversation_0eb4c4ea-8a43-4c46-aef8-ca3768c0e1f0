import psycopg2
import pymssql
import requests
from uuid import uuid4
import csv
import os
import boto3
from src.utils.loaders import load_access_controls, load_asset_details
from src.ingestion.data_class import AssetsDetails
from botocore.config import Config
from src.common.constants import ServiceTypes, DBTypes, AuthTypes, ServiceProviders
from src.utils.logger import get_ingestion_logger
from src.modules.repos import DatabaseManager
from src.ingestion.base_source import StructuredSource
from src.ingestion.data_class import TableMetadata, TableColumn
from src.common.config import TEMP_DATA_DIR
from src.utils.helpers import create_folder
from src.utils.exceptions import ServerException
import time
import asyncio
from typing import List, Dict, Optional, Any
from collections import defaultdict
from datetime import datetime
import json

logger = get_ingestion_logger()


class ONEXSource(StructuredSource):
    """
    ONEX Finance Application Connector
    Supports both Database-based and API-based integration
    """
    
    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.ONEX.value, service_name)
        self.db = DatabaseManager()
        self.connection = None
        self.cursor = None
        self.service_provider = ServiceProviders.LOCAL
        self.sub_service = DBTypes.ONEX.value
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"
        self.accessible_databases = []
        self.current_database = None
        self.schema_cache = {}
        self.connection_params = {}
        self.last_connection_time = None
        self.connection_timeout_seconds = 300
        
        # Integration mode
        self.integration_mode = None  # 'database' or 'api'
        
        # API-specific attributes
        self.api_base_url = None
        self.api_version = "v1"
        self.access_token = None
        self.token_expires_at = None
        self.client_id = None
        self.client_secret = None
        self.session = None
        
        # Database-specific attributes
        self.db_type = None  # 'postgresql' or 'sqlserver'
        self.host = None
        self.port = None
        self.dbname = None
        self.user = None
        self.password = None
        self.auth_type = None
        self.region = None
        self.sample_count = 10
        self.databases = []
        self.service_steward = None

    async def get_service_details(self):
        """Extract service configuration and credentials"""
        try:
            self.service = await self.db.get_service_by_service_name(self.service_name)
            credentials = self.service.get("credentials", {})
            
            # Determine integration mode
            if credentials.get("api_base_url"):
                self.integration_mode = "api"
                await self._extract_api_credentials(credentials)
            elif credentials.get("host"):
                self.integration_mode = "database"
                await self._extract_db_credentials(credentials)
            else:
                raise ServerException("Invalid credentials: either api_base_url or host must be provided")
            
            self.service_steward = self.service.get("data_steward")
            logger.info(f"ONEX service configured in {self.integration_mode} mode")
            
            return self.service
            
        except Exception as e:
            logger.error(f"Failed to get service details: {str(e)}")
            raise ServerException(f"Failed to get service details: {str(e)}", excep=e)

    async def _extract_api_credentials(self, credentials: dict):
        """Extract API-specific credentials"""
        self.api_base_url = credentials.get("api_base_url")
        self.client_id = credentials.get("client_id")
        self.client_secret = credentials.get("client_secret")
        self.auth_type = credentials.get("auth_type", "oauth")
        self.api_version = credentials.get("api_version", "v1")
        self.region = credentials.get("region", "us-east-1")
        self.sample_count = credentials.get("sample_count", 10)
        
        if not self.api_base_url or not self.client_id or not self.client_secret:
            raise ServerException("Missing required API credentials: api_base_url, client_id, client_secret")
        
        # Initialize requests session
        self.session = requests.Session()
        self.session.headers.update({
            "Content-Type": "application/json",
            "User-Agent": "ONEX-Scanner/1.0"
        })

    async def _extract_db_credentials(self, credentials: dict):
        """Extract database-specific credentials"""
        self.host = credentials.get("host")
        self.port = credentials.get("port")
        self.dbname = credentials.get("dbname")
        self.user = credentials.get("user")
        self.password = credentials.get("password")
        self.auth_type = credentials.get("auth_type", "basic")
        self.region = credentials.get("region", "us-east-1")
        self.sample_count = credentials.get("sample_count", 10)
        self.databases = credentials.get("databases", [])
        
        # Determine database type from port or explicit setting
        self.db_type = credentials.get("db_type", "postgresql")
        if not self.port:
            self.port = 5432 if self.db_type == "postgresql" else 1433
        
        if not self.host or not self.user:
            raise ServerException("Missing required DB credentials: host and user")
        
        # Validate port
        try:
            self.port = int(self.port)
        except (ValueError, TypeError):
            default_port = 5432 if self.db_type == "postgresql" else 1433
            logger.warning(f"Invalid port '{self.port}', using default {default_port}")
            self.port = default_port

    # ===========================================
    # API-BASED METHODS
    # ===========================================

    async def connect_api(self):
        """Establish API connection by obtaining access token"""
        try:
            logger.info(f"Connecting to ONEX API at {self.api_base_url}")
            
            # Check if token is still valid
            if self.access_token and self.token_expires_at:
                if time.time() < self.token_expires_at - 60:  # 1 minute buffer
                    logger.info("Using existing valid access token")
                    return
            
            # Obtain new token
            token_url = f"{self.api_base_url}/oauth/token"
            payload = {
                "grant_type": "client_credentials",
                "client_id": self.client_id,
                "client_secret": self.client_secret
            }
            
            response = self.session.post(token_url, json=payload, timeout=30)
            
            if response.status_code == 200:
                token_data = response.json()
                self.access_token = token_data.get("access_token")
                expires_in = token_data.get("expires_in", 3600)
                self.token_expires_at = time.time() + expires_in
                
                # Update session headers with token
                self.session.headers.update({
                    "Authorization": f"Bearer {self.access_token}"
                })
                
                logger.info("Successfully authenticated with ONEX API")
            else:
                error_msg = response.text
                raise ServerException(f"API authentication failed: {response.status_code} - {error_msg}")
                
        except requests.exceptions.RequestException as e:
            raise ServerException(f"API connection failed: {str(e)}", excep=e)
        except Exception as e:
            raise ServerException(f"API authentication failed: {str(e)}", excep=e)

    async def api_request(self, endpoint: str, method: str = "GET", params: dict = None, 
                         data: dict = None) -> dict:
        """Make authenticated API request"""
        try:
            # Ensure we have valid token
            await self.connect_api()
            
            url = f"{self.api_base_url}/{self.api_version}/{endpoint}"
            
            if method == "GET":
                response = self.session.get(url, params=params, timeout=60)
            elif method == "POST":
                response = self.session.post(url, json=data, timeout=60)
            else:
                raise ServerException(f"Unsupported HTTP method: {method}")
            
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 401:
                # Token expired, retry once
                logger.warning("Token expired, refreshing...")
                self.access_token = None
                await self.connect_api()
                return await self.api_request(endpoint, method, params, data)
            else:
                error_msg = response.text
                raise ServerException(f"API request failed: {response.status_code} - {error_msg}")
                
        except requests.exceptions.RequestException as e:
            raise ServerException(f"API request failed: {str(e)}", excep=e)

    async def get_databases_api(self) -> List[str]:
        """Get list of databases via API"""
        try:
            result = await self.api_request("metadata/databases")
            databases = result.get("databases", [])
            self.accessible_databases = databases
            logger.info(f"Found {len(databases)} databases via API: {databases}")
            return databases
        except Exception as e:
            logger.error(f"Failed to get databases via API: {str(e)}")
            return []

    async def get_tables_api(self, db_name: str) -> List[dict]:
        """Get tables from database via API"""
        try:
            result = await self.api_request("metadata/tables", params={"database": db_name})
            tables = result.get("tables", [])
            
            formatted_tables = []
            for table in tables:
                formatted_tables.append({
                    "table_name": table.get("name"),
                    "schema": table.get("schema", "public"),
                    "qualified_name": f"{table.get('schema', 'public')}.{table.get('name')}",
                    "row_count": table.get("row_count", 0)
                })
            
            logger.info(f"Found {len(formatted_tables)} tables in {db_name} via API")
            return formatted_tables
            
        except Exception as e:
            logger.error(f"Failed to get tables via API: {str(e)}")
            return []

    async def get_table_columns_api(self, db_name: str, table_name: str, schema: str) -> List[TableColumn]:
        """Get table columns via API"""
        try:
            result = await self.api_request("metadata/columns", params={
                "database": db_name,
                "schema": schema,
                "table": table_name
            })
            
            columns_data = result.get("columns", [])
            columns = []
            
            for col in columns_data:
                columns.append(TableColumn(
                    column_name=col.get("name"),
                    data_type=col.get("data_type"),
                    index=col.get("is_indexed", False),
                    primary_key=col.get("is_primary_key", False)
                ))
            
            logger.info(f"Retrieved {len(columns)} columns for {schema}.{table_name} via API")
            return columns
            
        except Exception as e:
            logger.error(f"Failed to get columns via API: {str(e)}")
            return []

    async def get_sample_data_api(self, db_name: str, table_name: str, schema: str) -> List[dict]:
        """Get sample data via API"""
        try:
            result = await self.api_request("data/sample", params={
                "database": db_name,
                "schema": schema,
                "table": table_name,
                "limit": self.sample_count
            })
            
            rows = result.get("data", [])
            logger.info(f"Retrieved {len(rows)} sample rows for {schema}.{table_name} via API")
            return rows
            
        except Exception as e:
            logger.warning(f"Failed to get sample data via API: {str(e)}")
            return []

    # ===========================================
    # DATABASE-BASED METHODS
    # ===========================================

    async def connect_database(self):
        """Establish database connection"""
        try:
            if self.db_type == "postgresql":
                await self.connect_postgresql()
            elif self.db_type == "sqlserver":
                await self.connect_sqlserver()
            else:
                raise ServerException(f"Unsupported database type: {self.db_type}")
        except Exception as e:
            if not isinstance(e, ServerException):
                raise ServerException(f"Database connection failed: {str(e)}", excep=e)
            raise e

    async def connect_postgresql(self):
        """Connect to PostgreSQL database"""
        try:
            connection_params = {
                "host": self.host,
                "port": self.port,
                "database": self.dbname or "postgres",
                "user": self.user,
                "password": self.password,
                "connect_timeout": 30,
                "sslmode": "prefer"
            }
            
            self.connection_params = connection_params.copy()
            self.connection = psycopg2.connect(**connection_params)
            self.cursor = self.connection.cursor()
            self.current_database = self.dbname or "postgres"
            self.last_connection_time = time.time()
            
            logger.info(f"Connected to PostgreSQL ONEX database at {self.host}:{self.port}")
            
        except psycopg2.Error as e:
            error_msg = str(e)
            if "authentication failed" in error_msg.lower():
                raise ServerException(f"PostgreSQL authentication failed: {error_msg}", excep=e)
            elif "could not connect" in error_msg.lower():
                raise ServerException(f"PostgreSQL connection failed: {error_msg}", excep=e)
            else:
                raise ServerException(f"PostgreSQL error: {error_msg}", excep=e)

    async def connect_sqlserver(self):
        """Connect to SQL Server database"""
        try:
            connection_params = {
                "server": self.host,
                "port": self.port,
                "user": self.user,
                "password": self.password,
                "timeout": 30
            }
            
            if self.dbname:
                connection_params["database"] = self.dbname
            
            self.connection_params = connection_params.copy()
            self.connection = pymssql.connect(**connection_params)
            self.cursor = self.connection.cursor(as_dict=True)
            self.current_database = self.dbname
            self.last_connection_time = time.time()
            
            logger.info(f"Connected to SQL Server ONEX database at {self.host}:{self.port}")
            
        except Exception as e:
            error_msg = str(e)
            if "18456" in error_msg:
                raise ServerException(f"SQL Server authentication failed: {error_msg}", excep=e)
            else:
                raise ServerException(f"SQL Server connection failed: {error_msg}", excep=e)

    async def ensure_connection(self, operation_name="operation"):
        """Ensure valid database connection"""
        if self.integration_mode == "api":
            return True  # API doesn't need persistent connection
        
        try:
            if self.connection and self.cursor:
                try:
                    if self.db_type == "postgresql":
                        self.cursor.execute("SELECT 1")
                        self.cursor.fetchone()
                    else:
                        self.cursor.execute("SELECT 1 AS test")
                        self.cursor.fetchone()
                    return True
                except Exception:
                    logger.warning(f"Connection test failed, reconnecting for {operation_name}")
            
            await self.reconnect()
            return True
            
        except Exception as e:
            logger.error(f"Failed to ensure connection: {str(e)}")
            return False

    async def reconnect(self):
        """Reconnect to database"""
        try:
            self.close_connection()
            time.sleep(1)
            await self.connect_database()
            
            if self.current_database:
                await self.use_database(self.current_database)
                
        except Exception as e:
            logger.error(f"Reconnection failed: {str(e)}")
            raise e

    async def get_databases_db(self) -> List[str]:
        """Get databases from database connection"""
        try:
            if not await self.ensure_connection("get_databases"):
                return []
            
            if self.db_type == "postgresql":
                self.cursor.execute("""
                    SELECT datname FROM pg_database 
                    WHERE datistemplate = false AND datname NOT IN ('postgres', 'template0', 'template1')
                    ORDER BY datname
                """)
                databases = [row[0] for row in self.cursor.fetchall()]
            else:  # SQL Server
                self.cursor.execute("""
                    SELECT name FROM sys.databases 
                    WHERE database_id > 4 AND state = 0
                    ORDER BY name
                """)
                databases = [row["name"] for row in self.cursor.fetchall()]
            
            self.accessible_databases = databases
            logger.info(f"Found {len(databases)} accessible databases: {databases}")
            return databases
            
        except Exception as e:
            logger.error(f"Failed to get databases: {str(e)}")
            return []

    async def use_database(self, db_name: str):
        """Switch to different database"""
        try:
            if db_name == self.current_database:
                return True
            
            if self.db_type == "postgresql":
                # PostgreSQL requires new connection for different database
                self.close_connection()
                old_db = self.connection_params.get("database")
                self.connection_params["database"] = db_name
                self.connection = psycopg2.connect(**self.connection_params)
                self.cursor = self.connection.cursor()
                self.current_database = db_name
                logger.info(f"Switched to PostgreSQL database: {db_name}")
            else:  # SQL Server
                self.cursor.execute(f"USE [{db_name}]")
                self.cursor.execute("SELECT DB_NAME() AS current_db")
                result = self.cursor.fetchone()
                if result and result["current_db"] == db_name:
                    self.current_database = db_name
                    logger.info(f"Switched to SQL Server database: {db_name}")
                else:
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to switch to database {db_name}: {str(e)}")
            return False

    async def get_tables_db(self, db_name: str = None) -> List[dict]:
        """Get tables from database"""
        try:
            if db_name and db_name != self.current_database:
                if not await self.use_database(db_name):
                    return []
            
            if not await self.ensure_connection("get_tables"):
                return []
            
            tables = []
            
            if self.db_type == "postgresql":
                self.cursor.execute("""
                    SELECT 
                        schemaname as schema_name,
                        tablename as table_name
                    FROM pg_tables
                    WHERE schemaname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY schemaname, tablename
                """)
                
                for row in self.cursor.fetchall():
                    schema, table = row[0], row[1]
                    tables.append({
                        "table_name": table,
                        "schema": schema,
                        "qualified_name": f"{schema}.{table}"
                    })
            else:  # SQL Server
                self.cursor.execute("""
                    SELECT 
                        s.name as schema_name,
                        t.name as table_name
                    FROM sys.tables t
                    INNER JOIN sys.schemas s ON t.schema_id = s.schema_id
                    ORDER BY s.name, t.name
                """)
                
                for row in self.cursor.fetchall():
                    schema = row["schema_name"]
                    table = row["table_name"]
                    tables.append({
                        "table_name": table,
                        "schema": schema,
                        "qualified_name": f"{schema}.{table}"
                    })
            
            logger.info(f"Found {len(tables)} tables in database")
            return tables
            
        except Exception as e:
            logger.error(f"Failed to get tables: {str(e)}")
            return []

    async def get_table_columns_db(self, schema: str, table_name: str) -> List[TableColumn]:
        """Get table columns from database"""
        try:
            if not await self.ensure_connection(f"get_columns_{table_name}"):
                return []
            
            columns = []
            
            if self.db_type == "postgresql":
                self.cursor.execute("""
                    SELECT 
                        c.column_name,
                        c.data_type,
                        c.is_nullable,
                        CASE WHEN pk.column_name IS NOT NULL THEN true ELSE false END as is_primary_key,
                        CASE WHEN idx.column_name IS NOT NULL THEN true ELSE false END as has_index
                    FROM information_schema.columns c
                    LEFT JOIN (
                        SELECT ku.column_name
                        FROM information_schema.table_constraints tc
                        JOIN information_schema.key_column_usage ku
                            ON tc.constraint_name = ku.constraint_name
                        WHERE tc.constraint_type = 'PRIMARY KEY'
                            AND tc.table_schema = %s
                            AND tc.table_name = %s
                    ) pk ON c.column_name = pk.column_name
                    LEFT JOIN (
                        SELECT DISTINCT a.attname as column_name
                        FROM pg_index i
                        JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
                        JOIN pg_class t ON t.oid = i.indrelid
                        JOIN pg_namespace n ON n.oid = t.relnamespace
                        WHERE n.nspname = %s AND t.relname = %s
                    ) idx ON c.column_name = idx.column_name
                    WHERE c.table_schema = %s AND c.table_name = %s
                    ORDER BY c.ordinal_position
                """, (schema, table_name, schema, table_name, schema, table_name))
                
                for row in self.cursor.fetchall():
                    columns.append(TableColumn(
                        column_name=row[0],
                        data_type=row[1],
                        index=row[4],
                        primary_key=row[3]
                    ))
            else:  # SQL Server
                self.cursor.execute("""
                    SELECT 
                        c.name AS column_name,
                        t.name AS data_type,
                        c.is_nullable,
                        CASE WHEN pk.column_name IS NOT NULL THEN 1 ELSE 0 END AS is_primary_key,
                        CASE WHEN idx.column_name IS NOT NULL THEN 1 ELSE 0 END AS has_index
                    FROM sys.columns c
                    INNER JOIN sys.tables tb ON c.object_id = tb.object_id
                    INNER JOIN sys.schemas s ON tb.schema_id = s.schema_id
                    INNER JOIN sys.types t ON c.user_type_id = t.user_type_id
                    LEFT JOIN (
                        SELECT ic.object_id, c.name AS column_name
                        FROM sys.index_columns ic
                        INNER JOIN sys.indexes i ON ic.object_id = i.object_id AND ic.index_id = i.index_id
                        INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
                        WHERE i.is_primary_key = 1
                    ) pk ON c.object_id = pk.object_id AND c.name = pk.column_name
                    LEFT JOIN (
                        SELECT DISTINCT ic.object_id, c.name AS column_name
                        FROM sys.index_columns ic
                        INNER JOIN sys.indexes i ON ic.object_id = i.object_id AND ic.index_id = i.index_id
                        INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
                    ) idx ON c.object_id = idx.object_id AND c.name = idx.column_name
                    WHERE s.name = %s AND tb.name = %s
                    ORDER BY c.column_id
                """, (schema, table_name))
                
                for row in self.cursor.fetchall():
                    columns.append(TableColumn(
                        column_name=row["column_name"],
                        data_type=row["data_type"],
                        index=bool(row["has_index"]),
                        primary_key=bool(row["is_primary_key"])
                    ))
            
            logger.info(f"Retrieved {len(columns)} columns for {schema}.{table_name}")
            return columns
            
        except Exception as e:
            logger.error(f"Failed to get columns for {schema}.{table_name}: {str(e)}")
            return []

    async def get_table_row_count_db(self, schema: str, table_name: str) -> int:
        """Get row count from database"""
        try:
            if self.db_type == "postgresql":
                query = f'SELECT COUNT(*) FROM "{schema}"."{table_name}"'
                self.cursor.execute(query)
                result = self.cursor.fetchone()
                return result[0] if result else 0
            else:  # SQL Server
                query = f"SELECT COUNT(*) AS row_count FROM [{schema}].[{table_name}]"
                self.cursor.execute(query)
                result = self.cursor.fetchone()
                return int(result["row_count"]) if result else 0
                
        except Exception as e:
            logger.warning(f"Failed to get row count for {schema}.{table_name}: {str(e)}")
            return 0

    async def get_sample_data_db(self, schema: str, table_name: str) -> List[dict]:
        """Get sample data from database"""
        try:
            if not await self.ensure_connection(f"get_sample_{table_name}"):
                return []
            
            limit = min(max(int(self.sample_count or 10), 1), 1000)
            
            if self.db_type == "postgresql":
                query = f'SELECT * FROM "{schema}"."{table_name}" LIMIT {limit}'
                self.cursor.execute(query)
                columns = [desc[0] for desc in self.cursor.description]
                rows = []
                for row in self.cursor.fetchall():
                    rows.append(dict(zip(columns, row)))
            else:  # SQL Server
                query = f"SELECT TOP {limit} * FROM [{schema}].[{table_name}]"
                self.cursor.execute(query)
                rows = self.cursor.fetchall()
            
            logger.info(f"Retrieved {len(rows)} sample rows from {schema}.{table_name}")
            return rows
            
        except Exception as e:
            logger.warning(f"Failed to get sample data for {schema}.{table_name}: {str(e)}")
            return []

    async def get_access_controls(self) -> List[dict]:
        """Get database access controls and permissions"""
        try:
            if not await self.ensure_connection("get_access_controls"):
                return []
            
            access_controls = []
            
            if self.db_type == "postgresql":
                self.cursor.execute("""
                    SELECT 
                        grantee,
                        string_agg(DISTINCT privilege_type, ', ') as privileges,
                        table_schema,
                        table_name
                    FROM information_schema.role_table_grants
                    WHERE table_schema NOT IN ('pg_catalog', 'information_schema')
                    GROUP BY grantee, table_schema, table_name
                    ORDER BY grantee
                """)
                
                for row in self.cursor.fetchall():
                    grantee, privileges, schema, table = row
                    access_level = self._map_privileges_to_access(privileges)
                    access_controls.append({
                        "user_or_role": grantee,
                        "role": "role" if grantee.startswith("pg_") else "user",
                        "access": access_level,
                        "asset_name": f"{schema}.{table}"
                    })
            else:  # SQL Server
                self.cursor.execute("""
                    SELECT 
                        COALESCE(prin.name, 'UNKNOWN') AS principal_name,
                        perm.permission_name,
                        perm.state_desc,
                        obj.name AS object_name
                    FROM sys.database_permissions perm
                    JOIN sys.database_principals prin ON perm.grantee_principal_id = prin.principal_id
                    LEFT JOIN sys.objects obj ON perm.major_id = obj.object_id
                    WHERE obj.type = 'U'
                """)
                
                privileges_by_user = defaultdict(set)
                for row in self.cursor.fetchall():
                    user = row["principal_name"]
                    perm = f"{row['permission_name']} ({row['state_desc']})"
                    privileges_by_user[user].add(perm)
                
                for user, perms in privileges_by_user.items():
                    access_level = self._map_privileges_to_access_mssql(perms)
                    access_controls.append({
                        "user_or_role": user,
                        "role": "role" if user in ["db_owner", "db_datareader", "db_datawriter"] else "user",
                        "access": access_level
                    })
            
            logger.info(f"Retrieved {len(access_controls)} access control entries")
            return access_controls
            
        except Exception as e:
            logger.error(f"Failed to get access controls: {str(e)}")
            return []

    def _map_privileges_to_access(self, privileges: str) -> str:
        """Map PostgreSQL privileges to access level"""
        privs = privileges.upper()
        if "ALL" in privs or "SUPERUSER" in privs:
            return "full"
        elif any(p in privs for p in ["INSERT", "UPDATE", "DELETE", "TRUNCATE"]):
            return "write"
        elif "SELECT" in privs:
            return "read"
        else:
            return "none"

    def _map_privileges_to_access_mssql(self, privileges: set) -> str:
        """Map SQL Server privileges to access level"""
        perms = {p.split()[0].upper() for p in privileges}
        if perms & {"CONTROL", "ALTER"}:
            return "full"
        if perms & {"INSERT", "UPDATE", "DELETE"}:
            return "write"
        if "SELECT" in perms:
            return "read"
        return "none"

    async def get_encryption_status(self) -> bool:
        """Check if database has encryption enabled"""
        try:
            if not await self.ensure_connection("get_encryption"):
                return False
            
            if self.db_type == "postgresql":
                # Check SSL connection
                self.cursor.execute("SHOW ssl")
                result = self.cursor.fetchone()
                return result[0] == "on" if result else False
            else:  # SQL Server
                self.cursor.execute("SELECT encrypt_option FROM sys.dm_exec_connections WHERE session_id = @@SPID")
                result = self.cursor.fetchone()
                return result and result.get("encrypt_option", "").lower() == "true"
                
        except Exception as e:
            logger.warning(f"Failed to check encryption status: {str(e)}")
            return False

    # ===========================================
    # UNIFIED INTERFACE METHODS
    # ===========================================

    async def connect(self):
        """Connect to ONEX (unified interface)"""
        if self.integration_mode == "api":
            await self.connect_api()
        else:
            await self.connect_database()

    async def test_connection(self):
        """Test ONEX connection"""
        try:
            await self.connect()
            
            if self.integration_mode == "api":
                result = await self.api_request("health")
                logger.info(f"ONEX API health check: {result}")
                return True
            else:
                if not await self.ensure_connection("test_connection"):
                    return False
                logger.info(f"Successfully connected to ONEX database {self.host}:{self.port}")
                return True
                
        except Exception as e:
            logger.error(f"ONEX connection test failed: {str(e)}")
            return False
        finally:
            if self.integration_mode == "database":
                self.close_connection()

    async def get_databases(self) -> List[str]:
        """Get databases (unified interface)"""
        if self.integration_mode == "api":
            return await self.get_databases_api()
        else:
            return await self.get_databases_db()

    async def get_tables(self, db_name: str = None) -> List[dict]:
        """Get tables (unified interface)"""
        if self.integration_mode == "api":
            return await self.get_tables_api(db_name)
        else:
            return await self.get_tables_db(db_name)

    async def get_table_columns(self, schema: str, table_name: str) -> List[TableColumn]:
        """Get table columns (unified interface)"""
        if self.integration_mode == "api":
            return await self.get_table_columns_api(self.current_database, table_name, schema)
        else:
            return await self.get_table_columns_db(schema, table_name)

    async def get_table_row_count(self, schema: str, table_name: str) -> int:
        """Get table row count (unified interface)"""
        if self.integration_mode == "api":
            # Row count might be in metadata
            tables = await self.get_tables_api(self.current_database)
            for table in tables:
                if table["table_name"] == table_name and table["schema"] == schema:
                    return table.get("row_count", 0)
            return 0
        else:
            return await self.get_table_row_count_db(schema, table_name)

    async def get_table_sample_data(self, schema: str, table_name: str) -> List[dict]:
        """Get sample data (unified interface)"""
        if self.integration_mode == "api":
            return await self.get_sample_data_api(self.current_database, table_name, schema)
        else:
            return await self.get_sample_data_db(schema, table_name)

    # ===========================================
    # SCANNING METHODS
    # ===========================================

    async def infra_scan(self):
        """Infrastructure scan to collect metadata"""
        try:
            logger.info(f"Starting infra scan for ONEX service: {self.service_name}")
            await self.get_service_details()
            await self.connect()
            
            databases = await self.get_databases()
            
            if self.databases:
                databases = [d for d in databases if d in self.databases]
                logger.info(f"Filtered to configured databases: {databases}")
            
            if not databases:
                logger.warning("No accessible databases found")
                return {"error": "No accessible databases"}
            
            aggregated_metadata = []
            
            # Get access permissions (database mode only)
            access_permissions = []
            if self.integration_mode == "database":
                access_permissions = await self.get_access_controls()
            
            for db in databases:
                try:
                    logger.info(f"Scanning database: {db}")
                    
                    if not await self.use_database(db):
                        logger.warning(f"Skipping database {db} - no access")
                        continue
                    
                    # Get encryption status
                    encryption_status = await self.get_encryption_status()
                    
                    # Get tables
                    tables = await self.get_tables(db)
                    table_count = len(tables)
                    
                    if table_count == 0:
                        logger.info(f"Skipping database {db} - 0 tables found")
                        continue
                    
                    # Calculate total size (simplified)
                    total_size_bytes = 0
                    for table_info in tables[:10]:  # Sample first 10 tables for size estimation
                        try:
                            row_count = await self.get_table_row_count(
                                table_info["schema"], 
                                table_info["table_name"]
                            )
                            # Rough estimation: 1KB per row
                            total_size_bytes += row_count * 1024
                        except Exception:
                            pass
                    
                    total_size_mb = round(total_size_bytes / 1024 / 1024, 2)
                    
                    # Load access controls
                    db_access = [{"role": "db_owner"}, {"role": "db_datareader"}]
                    if access_permissions:
                        db_access = [
                            {**perm, "asset_name": db} 
                            for perm in access_permissions
                        ]
                        load_access_controls(db_access)
                    
                    aggregated_metadata.append({
                        "dbname": db,
                        "db_owner": self.user,
                        "total_size_mb": total_size_mb,
                        "table_count": table_count,
                        "access_permissions": db_access,
                        "region": self.region,
                        "integration_mode": self.integration_mode
                    })
                    
                    # Load asset details
                    asset = AssetsDetails(
                        asset_name=db,
                        service_provider=self.service_provider,
                        type="database",
                        category="structured",
                        location=self.region,
                        owner=self.user,
                        security=bool(encryption_status),
                        size=total_size_bytes,
                        count=table_count,
                        service_name=self.service_name,
                        steward=str(self.service_steward)
                    )
                    load_asset_details([asset])
                    
                except Exception as db_ex:
                    logger.error(f"Failed to process database {db}: {db_ex}", exc_info=True)
                    continue
            
            result = {
                "databases": databases,
                "region": self.region,
                "integration_mode": self.integration_mode,
                "metadata": aggregated_metadata
            }
            
            if self.integration_mode == "database":
                result["host"] = self.host
                result["port"] = self.port
            else:
                result["api_base_url"] = self.api_base_url
            
            logger.info(f"Infra scan completed: {len(databases)} databases, {len(aggregated_metadata)} processed")
            return result
            
        except Exception as e:
            logger.error(f"Infrastructure scan failed: {e}", exc_info=True)
            return {"error": str(e)}
        finally:
            if self.integration_mode == "database":
                self.close_connection()

    async def deep_scan(self):
        """Deep scan to extract table data and metadata"""
        try:
            await self.get_service_details()
            await self.connect()
            
            databases = await self.get_databases()
            
            if self.databases:
                databases = [d for d in databases if d in self.databases]
                logger.info(f"Filtering to configured databases: {databases}")
            
            if not databases:
                logger.warning("No accessible databases found")
                return
            
            total_tables_processed = 0
            total_tables_found = 0
            
            for db in databases:
                try:
                    logger.info(f"Deep scanning database: {db}")
                    
                    if not await self.use_database(db):
                        logger.warning(f"Skipping database {db} - no access")
                        continue
                    
                    tables = await self.get_tables(db)
                    total_tables_found += len(tables)
                    
                    if not tables:
                        logger.warning(f"No accessible tables in database: {db}")
                        continue
                    
                    logger.info(f"Found {len(tables)} tables in database {db}")
                    
                    for table_info in tables:
                        try:
                            table_name = table_info["table_name"]
                            schema = table_info["schema"]
                            qualified_name = table_info["qualified_name"]
                            
                            table_res = await self.scan_table(
                                table_name, db, schema, qualified_name
                            )
                            
                            if table_res:
                                total_tables_processed += 1
                                yield table_res
                                
                        except Exception as table_error:
                            logger.error(
                                f"Table scan failed. DB: {db}, Table: {table_info.get('table_name')}, "
                                f"Schema: {table_info.get('schema')}. Error: {str(table_error)}",
                                exc_info=True
                            )
                            continue
                    
                except Exception as db_error:
                    logger.error(
                        f"Deep scan failed for database: {db}. Error: {str(db_error)}",
                        exc_info=True
                    )
                    continue
            
            logger.info(
                f"Deep scan completed. Processed {total_tables_processed} out of "
                f"{total_tables_found} tables found"
            )
            
        except Exception as e:
            logger.error(f"Deep scan failed: {str(e)}", exc_info=True)
        finally:
            if self.integration_mode == "database":
                self.close_connection()

    async def scan_table(self, table_name: str, db_name: str, schema_name: str, 
                        qualified_name: str) -> Optional[TableMetadata]:
        """Scan individual table and extract metadata"""
        try:
            logger.info(f"Scanning ONEX table: {qualified_name} in database: {db_name}")
            
            # Get table columns
            columns = await self.get_table_columns(schema_name, table_name)
            if not columns:
                logger.warning(f"No columns found for table {qualified_name}")
                columns = []
            
            # Get row count
            try:
                row_count = await self.get_table_row_count(schema_name, table_name)
            except Exception as e:
                logger.warning(f"Could not get row count for {qualified_name}: {str(e)}")
                row_count = 0
            
            # Get sample data
            try:
                rows = await self.get_table_sample_data(schema_name, table_name)
            except Exception as e:
                logger.warning(f"Could not get sample data for {qualified_name}: {str(e)}")
                rows = []
            
            # Infer columns from sample data if needed
            if not columns and rows:
                try:
                    first_row = rows[0]
                    columns = [
                        TableColumn(
                            column_name=col_name,
                            data_type="unknown",
                            index=False,
                            primary_key=False
                        )
                        for col_name in first_row.keys()
                    ]
                    logger.info(f"Inferred {len(columns)} columns from sample data")
                except Exception as e:
                    logger.warning(f"Could not infer columns: {str(e)}")
            
            # Generate table URI
            table_uri = f"onex://{db_name}/{qualified_name}"
            logger.info(f"Constructed table_uri: {table_uri}")
            
            # Check if already processed
            try:
                if self.db.skip_table(table_uri):
                    logger.info(f"Skipping table {qualified_name} - Already processed")
                    return None
            except Exception as e:
                logger.warning(f"Could not check skip status: {str(e)}")
            
            # Create CSV file
            column_names = [col.column_name for col in columns] if columns else []
            row_lists = []
            
            try:
                for row in rows:
                    row_list = []
                    if column_names:
                        for col in column_names:
                            value = row.get(col)
                            if value is None:
                                row_list.append("")
                            else:
                                try:
                                    str_value = str(value).replace('\r\n', ' ').replace('\n', ' ').replace('\r', ' ')
                                    if len(str_value) > 32000:
                                        str_value = str_value[:32000] + "..."
                                    row_list.append(str_value)
                                except Exception:
                                    row_list.append("[CONVERSION_ERROR]")
                    else:
                        # No column info, try to get all values
                        for key, value in row.items():
                            try:
                                str_value = str(value) if value is not None else ""
                                str_value = str_value.replace('\r\n', ' ').replace('\n', ' ')
                                if len(str_value) > 32000:
                                    str_value = str_value[:32000] + "..."
                                row_list.append(str_value)
                            except Exception:
                                row_list.append("[ERROR]")
                    
                    row_lists.append(row_list)
                    
            except Exception as e:
                logger.warning(f"Error processing rows for {qualified_name}: {str(e)}")
            
            # Create CSV file
            try:
                create_folder(self.local_data_dir)
                safe_filename = f"{db_name}_{schema_name}_{table_name}_{str(uuid4())}.csv"
                output_file = os.path.join(self.local_data_dir, safe_filename)
                
                with open(output_file, mode="w", newline="", encoding="utf-8") as file:
                    writer = csv.writer(file, quoting=csv.QUOTE_ALL)
                    
                    if column_names:
                        writer.writerow(column_names)
                    elif row_lists:
                        generic_headers = [f"column_{i+1}" for i in range(len(row_lists[0]))]
                        writer.writerow(generic_headers)
                    
                    writer.writerows(row_lists)
                    
            except Exception as e:
                logger.error(f"Failed to create CSV file for {qualified_name}: {str(e)}")
                return None
            
            logger.info(
                f"Successfully scanned ONEX table {qualified_name} in database {db_name}. "
                f"Rows: {row_count}, Columns: {len(columns)}, Sample rows: {len(row_lists)}"
            )
            
            # Prepare details
            details = {
                "integration_mode": self.integration_mode,
                "region": self.region,
                "sample_rows": len(row_lists),
                "accessible": True,
                "schema": schema_name,
                "original_table_name": table_name
            }
            
            if self.integration_mode == "database":
                details.update({
                    "host": self.host,
                    "port": self.port,
                    "db_type": self.db_type
                })
            else:
                details.update({
                    "api_base_url": self.api_base_url,
                    "api_version": self.api_version
                })
            
            return TableMetadata(
                service_name=self.service_name,
                service_type=self.service_type,
                service_provider=self.service_provider,
                sub_service=self.sub_service,
                db_type=DBTypes.ONEX.value,
                db_name=db_name,
                table_name=qualified_name,
                table_size=0,
                no_of_rows=row_count,
                table_uri=table_uri,
                columns=columns,
                local_filepath=output_file,
                details=details
            )
            
        except Exception as e:
            logger.error(
                f"Table scan failed for {table_name} (schema: {schema_name}) "
                f"in database {db_name}. Error: {str(e)}",
                exc_info=True
            )
            return None

    def close_connection(self):
        """Close database connection"""
        try:
            if self.integration_mode == "api":
                if self.session:
                    self.session.close()
                    self.session = None
                logger.info("ONEX API session closed")
            else:
                if self.cursor:
                    try:
                        self.cursor.close()
                    except Exception:
                        pass
                
                if self.connection:
                    try:
                        self.connection.close()
                        logger.info("ONEX database connection closed")
                    except Exception:
                        pass
                
                self.cursor = None
                self.connection = None
            
            self.current_database = None
            self.schema_cache.clear()
            self.last_connection_time = None
            self.access_token = None
            self.token_expires_at = None
            
        except Exception as e:
            logger.debug(f"Error during connection cleanup: {str(e)}")

    # ===========================================
    # USER SCAN FUNCTIONALITY
    # ===========================================

    # async def find_primary_keys(self, table_name: str, schema_name: str) -> List[str]:
    #     """Find primary key columns for a table"""
    #     try:
    #         if self.integration_mode == "api":
    #             # API might not support this, use columns metadata
    #             columns = await self.get_table_columns_api(self.current_database, table_name, schema_name)
    #             return [col.column_name for col in columns if col.primary_key]
            
    #         if not await self.ensure_connection(f"find_primary_keys_{table_name}"):
    #             return []
            
    #         if self.db_type == "postgresql":
    #             self.cursor.execute("""
    #                 SELECT a.attname
    #                 FROM pg_index i
    #                 JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
    #                 WHERE i.indrelid = %s::regclass AND i.indisprimary
    #             """, (f"{schema_name}.{table_name}",))
    #             return [row[0] for row in self.cursor.fetchall()]
    #         else:  # SQL Server
    #             self.cursor.execute("""
    #                 SELECT c.COLUMN_NAME
    #                 FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
    #                 JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE c
    #                     ON tc.CONSTRAINT_NAME = c.CONSTRAINT_NAME
    #                 WHERE tc.TABLE_SCHEMA = %s AND tc.TABLE_NAME = %s 
    #                     AND tc.CONSTRAINT_TYPE = 'PRIMARY KEY'
    #                 ORDER BY c.ORDINAL_POSITION
    #             """, (schema_name, table_name))
    #             return [row["COLUMN_NAME"] for row in self.cursor.fetchall()]
                
    #     except Exception as e:
    #         logger.error(f"Failed to fetch primary keys for {schema_name}.{table_name}: {str(e)}")
    #         return []

    # async def find_foreign_key_tables(self, pk_table: str, pk_column: str) -> List[dict]:
    #     """Find tables that reference the given primary key"""
    #     try:
    #         if self.integration_mode == "api":
    #             logger.warning("Foreign key traversal not supported in API mode")
    #             return []
            
    #         if not await self.ensure_connection("find_foreign_keys"):
    #             return []
            
    #         fk_tables = []
            
    #         if self.db_type == "postgresql":
    #             self.cursor.execute("""
    #                 SELECT
    #                     n.nspname AS table_schema,
    #                     t.relname AS table_name,
    #                     a.attname AS column_name
    #                 FROM pg_constraint c
    #                 JOIN pg_class t ON c.conrelid = t.oid
    #                 JOIN pg_namespace n ON t.relnamespace = n.oid
    #                 JOIN pg_attribute a ON a.attrelid = c.conrelid AND a.attnum = ANY(c.conkey)
    #                 JOIN pg_class rt ON c.confrelid = rt.oid
    #                 JOIN pg_attribute ra ON ra.attrelid = c.confrelid AND ra.attnum = ANY(c.confkey)
    #                 WHERE c.contype = 'f' AND rt.relname = %s AND ra.attname = %s
    #             """, (pk_table, pk_column))
                
    #             for row in self.cursor.fetchall():
    #                 fk_tables.append({
    #                     "table_schema": row[0],
    #                     "table_name": row[1],
    #                     "column_name": row[2]
    #                 })
    #         else:  # SQL Server
    #             self.cursor.execute("""
    #                 SELECT 
    #                     s.name AS table_schema,
    #                     t.name AS table_name,
    #                     c.name AS column_name
    #                 FROM sys.foreign_key_columns fkc
    #                 JOIN sys.tables t ON fkc.parent_object_id = t.object_id
    #                 JOIN sys.schemas s ON t.schema_