import mysql.connector
from uuid import uuid4
import csv
import os
import boto3
from botocore.config import Config
from mysql.connector.constants import ClientFlag
from typing import AsyncGenerator
from collections import defaultdict
from src.utils.loaders import load_access_controls,load_asset_details
from src.ingestion.data_class import AssetsDetails
from src.common.constants import ServiceTypes, DBTypes, AuthTypes, ServiceProviders
from src.utils.logger import get_ingestion_logger
from src.modules.repos import DatabaseManager
from src.ingestion.base_source import StructuredSource
from src.ingestion.data_class import TableMetadata, TableColumn
from src.common.config import TEMP_DATA_DIR
from src.utils.helpers import create_folder
from src.utils.exceptions import ServerException
from src.ingestion.structure.mysql.connection import get_region_from_host

logger = get_ingestion_logger()


class MySQLSource(StructuredSource):
    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.MYSQL.value, service_name)
        self.db = DatabaseManager()
        self.connection = None
        self.cursor = None
        self.service_provider = ServiceProviders.LOCAL
        self.sub_service = DBTypes.MYSQL.value
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"

    async def get_service_details(self):
        """Gets service details"""
        self.service = await self.db.get_service_by_service_name(self.service_name)
        credentials = self.service.get("credentials", {})
        self.host = credentials.get("host")
        self.port = credentials.get("port", 3306)
        self.dbname = credentials.get("dbname")
        self.user = credentials.get("user")
        self.auth_type = credentials.get("auth_type")
        self.password = credentials.get("password")
        self.sample_count = credentials.get("sample_count", 10)
        self.databases = credentials.get("databases", [])
        self.region = credentials.get("region")
        self.service_steward = self.service.get("data_steward")

        return self.service

    def generate_iam_token(self):
        if not self.region or not self.host or not self.user:
            raise ServerException("Missing required fields for IAM authentication.")

        try:
            logger.info("Generating RDS IAM token using EC2 IAM role...")

            boto_config = Config(
                retries={"max_attempts": 3, "mode": "standard"},
                connect_timeout=2,
                read_timeout=2,
            )

            rds_client = boto3.client(
                "rds", region_name=self.region, config=boto_config
            )
            token = rds_client.generate_db_auth_token(
                DBHostname=self.host,
                Port=self.port,
                DBUsername=self.user,
            )
            logger.info("IAM token successfully generated.")
            return token
        except Exception as e:
            if "Unable to locate credentials" in str(e):
                raise ServerException(
                    "IAM role is not attached to the EC2 instance."
                ) from e
            raise ServerException("Failed to generate IAM token.") from e

    def connect_using_iam(self):
        """Connect to RDS using IAM token"""
        try:
            token = self.generate_iam_token()
            self.connection = mysql.connector.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=token,
                database=self.dbname,
                ssl_disabled=True,  # Disable cert check inside VPC
                client_flags=[ClientFlag.SSL],
            )
            self.cursor = self.connection.cursor()
            logger.info("Connected to MySQL using IAM authentication.")
        except Exception as e:
            raise ServerException("IAM-based MySQL connection failed.", excep=e)

    def connect_using_creds(self):
        """Connect Using Credentials"""
        try:
            self.connection = mysql.connector.connect(
                host=self.host,
                port=self.port,
                database=self.dbname,
                user=self.user,
                password=self.password,
            )
            self.cursor = self.connection.cursor()
            logger.info(f"Connected using credentials to MySQL database: {self.dbname}")
        except Exception as e:
            raise ServerException("Password Based MySQL connection failed.", excep=e)

    async def connect(self):
        """Creates MySQL connection"""
        if self.auth_type and self.auth_type.lower() == AuthTypes.BASIC.value:
            self.connect_using_creds()
        elif self.auth_type and self.auth_type.lower() == AuthTypes.IAM.value:
            self.connect_using_iam()
        else:
            raise ServerException("Invalid Authetication type.")

    async def test_connection(self):
        """Test the database connection"""
        try:
            await self.connect()
            if self.connection.is_connected():
                logger.info(f"Successfully connected to MySQL database {self.dbname}")
                self.connection.close()
                return True
            return False
        except Exception as e:
            logger.error(f"MySQL connection test failed: {str(e)}")
            return False

    async def get_databases(self):
        self.cursor.execute("SHOW DATABASES;")
        return [row[0] for row in self.cursor.fetchall()]

    async def switch_database(self, db_name):
        self.dbname = db_name
        await self.connect()

    async def get_tables(self):
        self.cursor.execute("SHOW TABLES;")
        return [row[0] for row in self.cursor.fetchall()]

    # async def infra_scan(self):
    #     """Perform infrastructure scan to get database metadata."""
    #     try:
    #         logger.info(f"Starting infra scan for service: {self.service_name}")
    #         await self.get_service_details()
    #         await self.connect()

    #         databases = await self.get_databases()
    #         region = self.region or get_region_from_host(self.host)

    #         logger.info(
    #             f"Infra scan result - Region: {region}, Host: {self.host}, Port: {self.port}, Databases: {databases}"
    #         )
    #         return {"databases": databases, "region": region}

    #     except Exception as e:
    #         logger.error(f"Infrastructure scan failed: {e}")
    #         return {"error": str(e)}
    #     finally:
    #         self.close_connection()


    async def get_encryption_status(self):
        try:
            self.cursor.execute("SHOW VARIABLES LIKE 'have_ssl';")
            result = self.cursor.fetchone()
            if result and result[1].lower() == "yes":

                return True
            else:
                return False
        except Exception as e:
            logger.warning(f"Failed to check SSL encryption status: {e}")
            return False



    async def get_database_access_controls(self):
        """Fetch real user/schema grants and map to simplified access levels"""
        access_controls = []
        try:
            # Query schema-level privileges
            self.cursor.execute("""
                SELECT grantee, privilege_type, table_schema
                FROM information_schema.schema_privileges
                WHERE table_schema NOT IN ('mysql','sys','performance_schema','information_schema');
            """)
            rows = self.cursor.fetchall()

            privileges_by_grantee = defaultdict(set)

            for grantee, privilege_type, schema in rows:
                user = grantee.split('@')[0].strip("'")

                # Skip system/internal accounts
                if user.startswith("mysql.") or user in ("root", ""):
                    continue

                privileges_by_grantee[grantee].add(privilege_type.upper())

            # Build final result
            for grantee, privileges in privileges_by_grantee.items():
                role_type = "role" if grantee.startswith("'role_") else "user"
                access_controls.append({
                    "user_or_role": grantee,
                    "role": role_type,
                    "access": self._map_privileges_to_access(privileges),
                })

            return access_controls

        except Exception as e:
            logger.error(f"Failed to fetch MySQL access controls: {e}")
            return []

    def _map_privileges_to_access(self, privileges: set) -> str:
        read_privs = {"SELECT"}
        write_privs = {"INSERT", "UPDATE", "DELETE"}
        if privileges.issuperset(read_privs | write_privs):
            return "full"
        elif privileges & write_privs and privileges & read_privs:
            return "write"
        elif privileges & read_privs:
            return "read"
        else:
            return "none"


    async def infra_scan(self):
        """Perform infrastructure scan to get database metadata and persist asset details + access controls."""
        try:
            logger.info(f"Starting infra scan for service: {self.service_name}")
            await self.get_service_details()
            await self.connect()

            # Fetch all available databases
            databases = await self.get_databases()
            exclude_dbs = {"mysql", "information_schema", "performance_schema", "sys"}
            databases = [db for db in databases if db.lower() not in exclude_dbs]

            region = self.region or get_region_from_host(self.host)
            aggregated_metadata = []

            for db in databases:
                asset = []
                await self.switch_database(db)

                # 🔑 Database owner
                self.cursor.execute("SELECT USER();")
                db_owner = self.cursor.fetchone()[0]

                # 📏 Database size in MB
                self.cursor.execute(f"""
                    SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS total_size_mb
                    FROM information_schema.tables
                    WHERE table_schema = '{db}';
                """)
                size_mb = self.cursor.fetchone()[0] or 0
                size_bytes = round(size_mb * 1024 * 1024)

                # 📊 Table count
                self.cursor.execute(f"""
                    SELECT COUNT(*) FROM information_schema.tables
                    WHERE table_schema = '{db}';
                """)
                table_count = self.cursor.fetchone()[0]

                # 🔐 Access controls
                access_permissions = await self.get_database_access_controls()
                access = [{**perm, "asset_name": db} for perm in access_permissions]
                load_access_controls(access)   # 👉 Persist to access_controls table

                # 🔒 Encryption status
                encryption_status = await self.get_encryption_status()

                # 🏷️ Create and persist Asset record
                asset.append(AssetsDetails(
                    asset_name=db,
                    service_provider=self.service_provider,
                    type="database",
                    category="structured",
                    location=region,
                    owner=db_owner if db_owner else self.service.get("data_owner"),
                    security=encryption_status,
                    size=size_bytes,
                    count=table_count,
                    access_category="restricted" if access else "none",
                    service_name=str(self.service_name),
                    steward=str(self.service_steward),
                ))
                load_asset_details(asset)   # 👉 Persist to asset_details table

                # 📦 Collect metadata for return payload
                aggregated_metadata.append({
                    "dbname": db,
                    "db_owner": db_owner,
                    "total_size_mb": size_mb,
                    "table_count": table_count,
                    "region": region,
                })

            logger.info(f"Infra scan completed for {len(aggregated_metadata)} databases")
            return {
                "databases": [db["dbname"] for db in aggregated_metadata],
                "region": region,
                "metadata": aggregated_metadata,
            }

        except Exception as e:
            logger.error(f"Infrastructure scan failed: {e}", exc_info=True)
            return {"error": str(e)}
        # finally:
        #     self.close_connection()

    async def deep_scan(self) -> AsyncGenerator[TableMetadata, None]:
        """Perform deep scan to extract table schema and sample data."""
        try:
            await self.get_service_details()
            await self.connect()
            await self.infra_scan()
            databases = await self.get_databases()
            if self.databases:
                databases = [d for d in self.databases if d in databases]

            for db in databases:
                try:
                    await self.switch_database(db)
                    tables = await self.get_tables()
                    for table in tables:
                        table_metadata = await self.scan_table(table)
                        if table_metadata:
                            yield table_metadata
                except Exception as e:
                    logger.error(
                        f"Deep scan failed. DB: {db} Service: {self.service_name}",
                        exc_info=e,
                    )
        except Exception as e:
            logger.error(f"Deep scan failed. Service: {self.service_name}", exc_info=e)
        finally:
            self.close_connection()

    async def scan_table(self, table):
        try:
            self.cursor.execute(
                f"""SELECT COLUMN_NAME, DATA_TYPE, COLUMN_KEY
                    FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '{table}'"""
            )
            columns = []
            for row in self.cursor.fetchall():
                column_name, data_type, column_key = row
                is_primary = column_key == "PRI"
                has_index = column_key in ["PRI", "UNI", "MUL"]
                columns.append(
                    TableColumn(
                        column_name=column_name,
                        data_type=data_type,
                        index=has_index,
                        primary_key=is_primary
                    )
                )

            self.cursor.execute(f"SELECT COUNT(*) FROM {table};")
            row_count = self.cursor.fetchone()[0]

            self.cursor.execute(f"SELECT * FROM {table} LIMIT {self.sample_count};")
            rows = self.cursor.fetchall()

            table_uri = f"mysql://{self.dbname}/{table}"
            logger.info(f"Constructed table_uri for {table}: {table_uri}")

            # Check if table is already processed
            if self.db.skip_table(table_uri):
                logger.info(f"Skipping table {table} - Already processed")
                return None

            temp_folder = self.local_data_dir
            create_folder(temp_folder)
            output_file = os.path.join(temp_folder, f"{table}_{str(uuid4())}.csv")
            with open(output_file, mode="w", newline="", encoding="utf-8") as file:
                writer = csv.writer(file)
                writer.writerow([col.column_name for col in columns])
                writer.writerows(rows)

            return TableMetadata(
                service_name=self.service_name,
                service_type=self.service_type,
                service_provider=self.service_provider,
                sub_service=self.sub_service,
                db_type=DBTypes.MYSQL.value,
                db_name=self.dbname,
                table_name=table,
                table_size=0,  # Size will be determined after file is created
                no_of_rows=row_count,
                table_uri=table_uri,
                columns=columns,
                local_filepath=output_file,
                details={
                    "host": self.host,
                    "port": self.port,
                    "region": self.region or get_region_from_host(self.host)
            }
            )
        except Exception as e:
            logger.error(
                f"Table scan failed. DB: {self.dbname}, Table: {table}", exc_info=e
            )
            return None

    def close_connection(self):
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
            logger.info("MySQL connection closed")

    # ------------------ Added user_scan functionality for MySQL ------------------
    async def find_primary_keys(self, table_name: str, table_schema: str):
        """Return list of primary key column names for a given table in a schema."""
        try:
            query = """
                SELECT COLUMN_NAME
                FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
                WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s AND CONSTRAINT_NAME = 'PRIMARY'
                ORDER BY ORDINAL_POSITION
            """
            # Use the actual database name instead of "public"
            actual_schema = table_schema if table_schema != "public" else self.dbname
            self.cursor.execute(query, (actual_schema, table_name))
            return [row[0] for row in self.cursor.fetchall()] or []
        except Exception as e:
            logger.error(
                f"Failed to fetch primary key details for table {actual_schema}.{table_name}", exc_info=e
            )
            return []

    async def find_foreign_key_tables(self, pk_table: str, pk_column: str):
        """Return list of referencing tables/columns for a given referenced table.column."""
        try:
            query = """
                SELECT 
                    kcu.TABLE_SCHEMA as table_schema,
                    kcu.TABLE_NAME,
                    kcu.COLUMN_NAME
                FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
                JOIN INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc 
                    ON kcu.CONSTRAINT_NAME = rc.CONSTRAINT_NAME AND kcu.TABLE_SCHEMA = rc.CONSTRAINT_SCHEMA
                JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu2
                    ON rc.UNIQUE_CONSTRAINT_NAME = kcu2.CONSTRAINT_NAME AND rc.UNIQUE_CONSTRAINT_SCHEMA = kcu2.TABLE_SCHEMA
                WHERE kcu2.TABLE_NAME = %s AND kcu2.COLUMN_NAME = %s
                ORDER BY kcu.TABLE_SCHEMA, kcu.TABLE_NAME, kcu.ORDINAL_POSITION
            """
            self.cursor.execute(query, (pk_table, pk_column))
            fk_tables = []
            for row in self.cursor.fetchall() or []:
                fk_tables.append(
                    {
                        "table_schema": row[0],  # This will be the actual MySQL database name
                        "table_name": row[1],
                        "column_name": row[2],
                    }
                )
            return fk_tables
        except Exception as e:
            logger.error(
                f"Failed to fetch foreign key details for referenced {pk_table}.{pk_column}", exc_info=e
            )
            return []

    async def find_matching_rows_from_table(
        self, table_schema: str, table_name: str, column_name: str, values
    ):
        """Find rows in schema.table where column matches provided values. Uses LIKE for strings, IN for numbers."""
        try:
            # Use the actual database name instead of "public"
            actual_schema = table_schema if table_schema != "public" else self.dbname
            
            # Determine data type
            dtype_query = """
                SELECT DATA_TYPE 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s AND COLUMN_NAME = %s
            """
            self.cursor.execute(dtype_query, (actual_schema, table_name, column_name))
            row = self.cursor.fetchone()
            if not row:
                logger.info(
                    f"Column {column_name} not found in {actual_schema}.{table_name}"
                )
                return []
            data_type = (row[0] or "").upper()

            # Build query
            quoted_table = f"`{actual_schema}`.`{table_name}`"
            quoted_column = f"`{column_name}`"
            results = []

            if data_type in (
                "VARCHAR",
                "CHAR",
                "TEXT",
                "TINYTEXT",
                "MEDIUMTEXT",
                "LONGTEXT",
                "JSON"
            ):
                # String columns - use LIKE
                if not values:
                    return []
                patterns = [f"%{v}%" for v in values]
                placeholders = ", ".join(["%s"] * len(patterns))
                where_conditions = " OR ".join([f"{quoted_column} LIKE %s"] * len(patterns))
                query = f"SELECT * FROM {quoted_table} WHERE ({where_conditions}) LIMIT 20"
                self.cursor.execute(query, patterns)
                rows = self.cursor.fetchall()
            else:
                # Numeric or other types - use IN
                casted = []
                for v in values:
                    try:
                        if "." in str(v):
                            casted.append(float(v))
                        else:
                            casted.append(int(v))
                    except Exception:
                        continue
                if not casted:
                    return []
                placeholders = ", ".join(["%s"] * len(casted))
                query = f"SELECT * FROM {quoted_table} WHERE {quoted_column} IN ({placeholders}) LIMIT 20"
                self.cursor.execute(query, casted)
                rows = self.cursor.fetchall()

            if not rows:
                return []

            # Get column names
            col_query = """
                SELECT COLUMN_NAME
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s
                ORDER BY ORDINAL_POSITION
            """
            self.cursor.execute(col_query, (actual_schema, table_name))
            col_names = [r[0] for r in self.cursor.fetchall()]

            for r in rows:
                record = {col_names[i]: r[i] for i in range(min(len(col_names), len(r)))}
                results.append(record)

            return results
        except Exception as e:
            logger.error(f"Error fetching from {actual_schema}.{table_name}: {e}")
            return []

    async def fetch_related_data_recursively(self, matching_tables):
        """Traverse FK graph to fetch related rows starting from initial matches"""
        results = []
        visited_tables = set()
        try:
            while len(matching_tables) > 0:
                next_tables = []
                for pii_match in matching_tables:
                    table_schema = pii_match.get("table_schema")
                    table_name = pii_match.get("table_name")
                    column_name = pii_match.get("column_name")
                    values = pii_match.get("values")
                    
                    # Use the actual database name instead of "public"
                    actual_schema = table_schema if table_schema != "public" else self.dbname
                    qualified = f"{actual_schema}.{table_name}"
                    
                    if qualified in visited_tables:
                        continue

                    matching_rows = await self.find_matching_rows_from_table(
                        actual_schema, table_name, column_name, values
                    )
                    if len(matching_rows) == 0:
                        continue

                    results.append(
                        {
                            "service_name": self.service_name,
                            "service_provider": self.service_provider,
                            "sub_service": self.sub_service,
                            "db_name": self.dbname,
                            "table_schema": actual_schema,  # Use the actual database name
                            "table_name": table_name,
                            "column_name": column_name,
                            "matching_rows": matching_rows,
                        }
                    )
                    visited_tables.add(qualified)

                    pk_keys = await self.find_primary_keys(table_name, actual_schema)
                    for pk_key in pk_keys:
                        values = list(set([m.get(pk_key) for m in matching_rows if m.get(pk_key) is not None]))
                        if values:  # Only proceed if we have valid values
                            fk_tables = await self.find_foreign_key_tables(table_name, pk_key)
                            for fk_table in fk_tables:
                                fk_table_schema = fk_table.get("table_schema")
                                fk_table_name = fk_table.get("table_name")
                                fk_column_name = fk_table.get("column_name")
                                next_tables.append(
                                    {
                                        "table_schema": fk_table_schema,  # This will already be the actual MySQL database
                                        "table_name": fk_table_name,
                                        "column_name": fk_column_name,
                                        "values": values,
                                    }
                                )

                matching_tables = next_tables
        except Exception as e:
            logger.error("Error while fetching user related data", exc_info=e)
        return results

    async def user_scan(self, initial_tables):
        """Perform user scan to find related data across tables using foreign key relationships"""
        try:
            await self.get_service_details()
            await self.connect()
            databases = await self.get_databases()
            
            # Filter databases if specified in configuration
            if len(self.databases) > 0:
                databases = [d for d in self.databases if d in databases]

            for db in databases:
                try:
                    logger.info(f"Scanning database: {db}")
                    
                    # Switch to the target database
                    await self.switch_database(db)
                    
                    # Map "public" schema references to actual MySQL database
                    current_db_tables = []
                    for table in initial_tables:
                        if table.get("db_name") == db:
                            # Create a copy of the table info with corrected schema
                            corrected_table = table.copy()
                            if corrected_table.get("table_schema") == "public":
                                corrected_table["table_schema"] = db  # Use the actual MySQL database name
                            current_db_tables.append(corrected_table)
                    
                    if len(current_db_tables) == 0:
                        logger.info(f"No matching tables found for database {db}")
                        continue

                    logger.info(f"db_name: {db}, current_db_tables: {current_db_tables}")
                    results = await self.fetch_related_data_recursively(current_db_tables)

                    if len(results):
                        logger.info(f"Found {len(results)} related data results for database {db}")
                        yield results
                    else:
                        logger.info(f"No related data found for database {db}")
                        
                except Exception as e:
                    logger.error(
                        f"User scan failed for database: {db}. Service: {self.service_name}",
                        exc_info=e,
                    )
        except Exception as e:
            logger.error(f"User scan failed. Service: {self.service_name}", exc_info=e)
        finally:
            self.close_connection()