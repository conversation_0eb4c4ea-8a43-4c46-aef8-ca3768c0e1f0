import aiomysql
import logging
from src.utils.exceptions import ClientException, ServerException

logger = logging.getLogger("Unstructure")


async def test_mysql_connection(data: dict) -> dict:
    creds = data.get("credentials", {})
    try:
        logger.info(f"Attempting MySQL connection to host: {creds.get('host')}")

        conn = await aiomysql.connect(
            host=creds["host"],
            port=creds["port"],
            user=creds["user"],
            password=creds["password"],
            db=creds["dbname"]
        )

        async with conn.cursor() as cur:
            await cur.execute("SELECT 1")
        conn.close()

        logger.info("MySQL connection test successful.")
        return {
            "status": True,
            "message": "MySQL connection successful"
        }

    except aiomysql.OperationalError as e:
        logger.warning(f"MySQL authentication failed: {str(e)}")
        raise ClientException("Invalid MySQL credentials or connection issue", code=401, excep=e)

    except Exception as e:
        logger.exception("Unexpected error during MySQL connection test")
        raise ServerException("MySQL test failed", excep=e)
