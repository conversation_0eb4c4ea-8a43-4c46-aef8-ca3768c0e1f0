from collections import defaultdict
from typing import AsyncGenerator, Dict, List
from src.ingestion.base_source import StructuredSource
from src.ingestion.data_class import TableMetadata, TableColumn, AssetsDetails as AssetDetailsSchema
from src.utils.loaders import load_access_controls, load_asset_details
from src.utils.logger import get_ingestion_logger
from src.modules.repos import DatabaseManager
from src.utils.helpers import create_folder
from src.common.config import TEMP_DATA_DIR
from src.common.constants import AuthTypes, ServiceTypes, DBTypes, ServiceProviders
from src.utils.exceptions import ServerException
from src.ingestion.structure.deltalake.connection import get_region_from_host

import trino
import os
import csv
from uuid import uuid4

logger = get_ingestion_logger()

class DeltaLakeSource(StructuredSource):
    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.DELTALAKE.value, service_name)
        self.db = DatabaseManager()
        self.connection = None
        self.cursor = None
        self.sub_service = DBTypes.DELTALAKE.value
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"
        self.region = None
        self.scan_stats = {
            'total_schemas': 0,
            'scanned_schemas': 0,
            'total_tables': 0,
            'scanned_tables': 0,
            'skipped_tables': 0,
            'total_columns': 0,
            'total_rows': 0,
            'errors': []
        }

    async def get_service_details(self):
        """Fetch Delta Lake connection details"""
        self.service = await self.db.get_service_by_service_name(self.service_name)
        credentials = self.service.get("credentials", {})

        self.host = credentials.get("host")
        self.port = credentials.get("port", 8080)
        self.catalog = credentials.get("catalog")
        self.user = credentials.get("user")
        self.password = credentials.get("password")
        self.auth_type = credentials.get("auth_type", AuthTypes.BASIC.value).lower()
        self.databases = credentials.get("databases", []) 
        self.sample_count = credentials.get("sample_count", 10)
        self.service_steward = self.service.get("data_steward")
        self.service_owner = self.service.get("data_owner")
        self.service_provider = self.service.get("service_provider", ServiceProviders.LOCAL.value)
        self.region = credentials.get("region")

        return self.service

    async def connect(self):
        """Create Trino connection"""
        try:
            self.connection = trino.dbapi.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                catalog=self.catalog,
                auth=trino.auth.BasicAuthentication(self.user, self.password) if self.password else None,
            )
            self.cursor = self.connection.cursor()
            logger.info(f"Connected to Delta Lake via Trino at {self.host}:{self.port}")
        except Exception as e:
            raise ServerException("Failed to connect to Delta Lake", excep=e)

    async def test_connection(self):
        try:
            await self.connect()
            self.cursor.execute("SELECT 1")
            return {"status": "success", "message": "Connection successful"}
        except Exception as e:
            return {"status": "failed", "message": str(e)}
        
    async def get_schemas(self) -> List[str]:
        """List all schemas (databases) in the catalog."""
        try:
            self.cursor.execute(f"SHOW SCHEMAS FROM {self.catalog}")
            all_schemas = [row[0] for row in self.cursor.fetchall()]

            if self.databases:
                configured_upper = {s.upper() for s in self.databases}
                schemas = [s for s in all_schemas if s.upper() in configured_upper]
            else:
                schemas = all_schemas

            self.scan_stats['total_schemas'] = len(schemas)
            logger.info(f"Discovered {len(schemas)} schemas in catalog '{self.catalog}'")
            return schemas
        except Exception as e:
            logger.error(f"Failed to fetch schemas for catalog {self.catalog}: {e}", exc_info=e)
            return []

    async def get_tables_in_schema(self, schema_name: str) -> List[str]:
        """List all tables in a given schema."""
        try:
            self.cursor.execute(f"SHOW TABLES FROM {self.catalog}.{schema_name}")
            tables = [row[0] for row in self.cursor.fetchall()]
            self.scan_stats['total_tables'] += len(tables)
            return tables
        except Exception as e:
            logger.error(f"Failed to get tables for schema {schema_name}: {e}", exc_info=e)
            return []

    async def get_database_access_controls(self, schema: str) -> List[Dict]:
        """Discover grants for a schema. Note: Trino's grant support is limited."""
        try:
            self.cursor.execute(f"SHOW GRANTS ON SCHEMA {self.catalog}.{schema}")
            rows = self.cursor.fetchall()
            privs_by_grantee = defaultdict(set)

            for row in rows:
                grantee, _, privilege, _ = row
                privs_by_grantee[grantee].add(privilege.upper())

            result = []
            for grantee, privs in privs_by_grantee.items():
                access = "read" if "SELECT" in privs else "none"
                if "INSERT" in privs or "DELETE" in privs or "UPDATE" in privs:
                    access = "write"
                result.append({
                    "user_or_role": grantee,
                    "role": "role" if "_ROLE" in grantee.upper() else "user",
                    "access": access
                })
            return result
        except Exception as e:
            logger.warning(f"Failed to fetch access controls for schema {schema}: {e}")
            return []

    async def get_tables_in_schema(self, schema_name: str) -> List[str]:
        """List all tables in a given schema."""
        try:
            self.cursor.execute(f"SHOW TABLES FROM {self.catalog}.{schema_name}")
            tables = [row[0] for row in self.cursor.fetchall()]
            logger.info(f"Discovered {len(tables)} tables in schema '{schema_name}'")
            self.scan_stats['total_tables'] += len(tables)
            return tables
        except Exception as e:
            logger.error(f"Failed to get tables for schema {schema_name}: {e}", exc_info=e)
            return []

    async def infra_scan(self):
        """Perform infrastructure-level discovery of tables."""
        try:
            logger.info(f"Starting infra scan for Delta Lake service: {self.service_name}")
            await self.get_service_details()
            await self.connect()

            schemas = await self.get_schemas()
            aggregated_metadata = []

            for schema in schemas:
                try:
                    table_count = len(await self.get_tables_in_schema(schema))
                    access_permissions = await self.get_database_access_controls(schema)
                    access_objs = [{**perm, "asset_name": schema} for perm in access_permissions]
                    if access_objs:
                        load_access_controls(access_objs)

                    asset_obj = AssetDetailsSchema(
                        asset_name=schema,
                        service_provider=self.service_provider,
                        type="database",
                        category="structured",
                        location=self.region or get_region_from_host(self.host),
                        owner=self.service_owner,
                        security="Unknown",
                        size=0,
                        count=table_count,
                        access_category="restricted" if access_objs else "none",
                        service_name=self.service_name,
                        steward=self.service_steward,
                    )
                    load_asset_details([asset_obj])

                    aggregated_metadata.append({
                        "dbname": schema,
                        "table_count": table_count,
                    })
                except Exception as e:
                    logger.error(f"Infra scan failed for schema {schema}: {e}", exc_info=e)

            logger.info(f"Infra scan completed for {len(aggregated_metadata)} schemas.")
            return {"databases": [m['dbname'] for m in aggregated_metadata], "metadata": aggregated_metadata}

        except Exception as e:
            logger.error(f"Infra scan failed: {e}", exc_info=True)
            return {"error": str(e)}

    async def deep_scan(self) -> AsyncGenerator[TableMetadata, None]:
        try:
            logger.info(f"Starting deep scan for Delta Lake service: {self.service_name}")
            await self.get_service_details()
            await self.connect()
            await self.infra_scan()
            schemas = await self.get_schemas()

            for s_idx, schema in enumerate(schemas, 1):
                logger.info(f"[{s_idx}/{len(schemas)}] Scanning schema: {schema}")
                self.scan_stats['scanned_schemas'] += 1
                tables = await self.get_tables_in_schema(schema)

                for t_idx, table in enumerate(tables, 1):
                    try:
                        logger.info(f"   [{t_idx}/{len(tables)}] Scanning table: {schema}.{table}")
                        meta = await self.scan_table(schema, table)
                        if meta:
                            self.scan_stats['scanned_tables'] += 1
                            self.scan_stats['total_columns'] += len(meta.columns)
                            self.scan_stats['total_rows'] += meta.no_of_rows
                            yield meta
                        else:
                            self.scan_stats['skipped_tables'] += 1
                    except Exception as te:
                        err = f"Table scan failed: {schema}.{table} -> {te}"
                        logger.error(err, exc_info=te)
                        self.scan_stats['errors'].append(err)
        except Exception as e:
            logger.error(f"Deep scan aborted: {e}", exc_info=e)
            self.scan_stats['errors'].append(str(e))
        finally:
            await self.close_connection()

    async def close_connection(self):
        """Close connection/cursor safely."""
        try:
            if self.cursor:
                self.cursor.close()
            if self.connection:
                self.connection.close()
            self.cursor = None
            self.connection = None
            logger.info("Trino connection closed.")
        except Exception as e:
            logger.warning(f"Error while closing Trino connection: {e}")

    async def test_connection(self) -> dict:
        try:
            await self.get_service_details()
            await self.connect()
            self.cursor.execute("SELECT 1")
            logger.info(f"Delta Lake connection test successful for {self.service_name}")
            return {"status": "success", "message": "Connection successful"}
        except Exception as e:
            logger.error(f"Delta Lake connection test failed for {self.service_name}: {e}", exc_info=True)
            return {"status": "failed", "message": str(e)}
        finally:
            await self.close_connection()

    async def scan_table(self, schema: str, table: str) -> TableMetadata:
        """Scan a Delta table and write sample data to CSV"""
        full_table_name = f'"{self.catalog}"."{schema}"."{table}"'
        table_uri = f"trino://{self.host}:{self.port}/{self.catalog}/{schema}/{table}"

        if self.db.skip_table(table_uri):
            logger.info(f"Already processed: {table_uri}, skipping")
            return None

        try:
            self.cursor.execute(f"DESCRIBE {full_table_name}")
            cols = self.cursor.fetchall()
            logger.info(f"Discovered {len(cols)} columns in table '{full_table_name}'")
            columns = [TableColumn(column_name=c[0], data_type=c[1], primary_key=False, index=False) for c in cols]
            table_properties = {}
            check_constraints = []
            try:
                self.cursor.execute(f"SHOW TBLPROPERTIES {full_table_name}")
                properties = self.cursor.fetchall()
                for key, value in properties:
                    table_properties[key] = value
                    if 'delta.constraints.' in key:
                        constraint_name = key.split('delta.constraints.')[1]
                        check_constraints.append({
                            "constraint_name": constraint_name,
                            "check_clause": value
                        })
            except Exception as prop_e:
                logger.warning(f"Could not fetch TBLPROPERTIES for {full_table_name}: {prop_e}")

            # Get row count
            try:
                self.cursor.execute(f"SELECT COUNT(*) FROM {full_table_name}")
                row_count = self.cursor.fetchone()[0]
            except Exception as count_e:
                logger.warning(f"Could not get row count for {full_table_name}: {count_e}")
                row_count = -1 

            if row_count == 0:
                logger.info(f"Empty table: {full_table_name}, still scanning for metadata")

            # Get sample rows
            self.cursor.execute(f"SELECT * FROM {full_table_name} LIMIT {self.sample_count}")
            rows = self.cursor.fetchall()

            # Write sample to CSV
            create_folder(self.local_data_dir)
            output_file = os.path.join(self.local_data_dir, f"{schema}_{table}_{uuid4()}.csv")
            with open(output_file, "w", newline="", encoding="utf-8") as f:
                writer = csv.writer(f)
                writer.writerow([col.column_name for col in columns])
                writer.writerows(rows)

            table_file_size = os.path.getsize(output_file)

            details = {
                "host": self.host,
                "port": self.port,
                "region": self.region or get_region_from_host(self.host),
                "table_properties": table_properties,
                "constraints": {
                    "check_constraints": check_constraints
                }
            }

            return TableMetadata(
                service_name=self.service_name,
                service_type=self.service_type,
                service_provider=self.service_provider,
                sub_service=self.sub_service,
                db_type=DBTypes.DELTALAKE.value,
                db_name=schema,
                table_name=table,
                table_size=table_file_size,
                no_of_rows=row_count,
                columns=columns,
                local_filepath=output_file,
                table_uri=table_uri,
                details=details
            )
        except Exception as e:
            raise ServerException(f"Failed to scan table {full_table_name}", excep=e)
