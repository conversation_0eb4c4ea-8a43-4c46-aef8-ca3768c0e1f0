import oracledb
import logging
from src.utils.exceptions import ClientException, ServerException

logger = logging.getLogger("Unstructure")

async def test_oracle11g_connection(data: dict) -> dict:
    creds = data.get("credentials", {})
    try:
        host = creds.get("host")
        port = creds.get("port", 1521)
        user = creds.get("user")
        password = creds.get("password")
        sid = creds.get("sid")
        is_sid = creds.get("is_sid", True)
        if not all([host, port, user, password, sid]):
            logger.warning("Missing required Oracle 11g credentials")
            raise ClientException(
                "Missing required credentials: host, port, user, password, sid",
                code=400,
                excep=None
            )
        logger.info(f"Testing Oracle 11g connection to {host}:{port} (SID: {sid})")
        try:
            if is_sid:
                dsn = oracledb.makedsn(host, port, sid=sid)
            else:
                dsn = oracledb.makedsn(host, port, service_name=sid)
            connection = oracledb.connect(
                user=user,
                password=password,
                dsn=dsn
            )
        except oracledb.DatabaseError as e:
            logger.warning(f"Oracle 11g authentication/connection failed: {str(e)}")
            raise ClientException(
                "Invalid Oracle 11g credentials or connection failed", code=401, excep=e
            )
        try:
            cursor = connection.cursor()
            cursor.execute("SELECT USERNAME FROM ALL_USERS WHERE ROWNUM = 1")
            result = cursor.fetchone()
            cursor.execute("SELECT * FROM USER_TABLES WHERE ROWNUM = 1")
            cursor.fetchall()
            logger.info("Oracle 11g connection test successful and read access verified.")
        except Exception as e:
            logger.warning(f"Oracle 11g read access failed: {str(e)}")
            connection.close()
            raise ClientException(
                "Oracle 11g read access failed (check read-only permissions)", code=403, excep=e
            )
        connection.close()
        return {
            "status": True,
            "message": "Oracle 11g connection successful and read access verified."
        }
    except ClientException:
        raise
    except Exception as e:
        logger.exception("Unexpected error during Oracle 11g connection test")
        raise ServerException("Oracle 11g test failed", excep=e) 