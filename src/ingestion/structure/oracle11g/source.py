import oracledb
from uuid import uuid4
import csv
import os
from typing import Async<PERSON>enerator

from src.common.constants import ServiceTypes, DBTypes, ServiceProviders
from src.utils.logger import get_ingestion_logger
from src.modules.repos import DatabaseManager
from src.ingestion.base_source import StructuredSource
from src.ingestion.data_class import TableMetadata, TableColumn
from src.common.config import TEMP_DATA_DIR
from src.utils.helpers import create_folder
from src.utils.exceptions import ServerException

logger = get_ingestion_logger()

class Oracle11gSource(StructuredSource):
    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.ORACLE11G.value, service_name)
        self.db = DatabaseManager()
        self.connection = None
        self.cursor = None
        self.service_provider = ServiceProviders.LOCAL
        self.sub_service = DBTypes.ORACLE11G.value
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"
        self.thick_mode_enabled = False
        # Initialize Oracle client for thick mode (required for Oracle 11g)
        self._init_oracle_client()

    def _init_oracle_client(self):
        """Initialize Oracle client in thick mode for Oracle 11g compatibility"""
        try:
            # Try to initialize thick mode first
            if not oracledb.is_thin_mode():
                logger.info("Already in thick mode")
                self.thick_mode_enabled = True
                return
            
            # Initialize thick mode for Oracle 11g
            oracledb.init_oracle_client()
            self.thick_mode_enabled = True
            logger.info("Oracle client initialized in thick mode for Oracle 11g")
            
        except Exception as e:
            logger.error(f"Failed to initialize Oracle thick mode: {e}")
            logger.error("Oracle Instant Client is required for Oracle 11g connections.")
            logger.error("Please install Oracle Instant Client Basic or Basic Light package.")
            
            # Try to use thin mode as fallback (may not work with Oracle 11g)
            try:
                logger.warning("Attempting to use thin mode as fallback...")
                self.thick_mode_enabled = False
            except Exception as fallback_error:
                logger.error(f"Fallback to thin mode also failed: {fallback_error}")
                raise ServerException(
                    "Oracle 11g requires thick mode with Oracle Instant Client. "
                    "Please install Oracle Instant Client and set proper environment variables."
                )

    def _validate_oracle_environment(self):
        """Validate Oracle environment variables"""
        oracle_home = os.getenv('ORACLE_HOME')
        ld_library_path = os.getenv('LD_LIBRARY_PATH', '')
        
        if not oracle_home:
            logger.warning("ORACLE_HOME environment variable not set")
        
        if oracle_home and oracle_home not in ld_library_path:
            logger.warning(f"ORACLE_HOME ({oracle_home}) not in LD_LIBRARY_PATH")
        
        logger.info(f"Oracle environment - ORACLE_HOME: {oracle_home}")
        logger.info(f"LD_LIBRARY_PATH: {ld_library_path}")

    async def get_service_details(self):
        self.service = await self.db.get_service_by_service_name(self.service_name)
        credentials = self.service.get("credentials", {})
        self.host = credentials.get("host")
        self.port = credentials.get("port", 1521)
        self.user = credentials.get("user")
        self.password = credentials.get("password")
        self.sid = credentials.get("sid")
        self.service_name_db = credentials.get("service_name")
        self.is_sid = credentials.get("is_sid", True)  # Default to SID for Oracle 11g
        self.sample_count = credentials.get("sample_count", 10)
        self.databases = credentials.get("databases", [])
        
        if not all([self.host, self.port, self.user, self.password]):
            raise ServerException("Missing required Oracle 11g credentials (host, port, user, password).")
        
        # For Oracle 11g, we need either SID or service_name
        if self.is_sid and not self.sid:
            raise ServerException("SID is required for SID-based Oracle 11g connection.")
        elif not self.is_sid and not self.service_name_db:
            raise ServerException("Service name is required for service-based Oracle 11g connection.")
        
        return self.service

    def connect(self):
        """Connect to Oracle 11g database"""
        try:
            self._validate_oracle_environment()
            
            # Create DSN for Oracle 11g
            if self.is_sid:
                dsn = oracledb.makedsn(self.host, self.port, sid=self.sid)
                logger.info(f"Connecting to Oracle 11g using SID: {self.sid}")
            else:
                dsn = oracledb.makedsn(self.host, self.port, service_name=self.service_name_db)
                logger.info(f"Connecting to Oracle 11g using service name: {self.service_name_db}")
            
            # Connection parameters for Oracle 11g
            connection_params = {
                'user': self.user,
                'password': self.password,
                'dsn': dsn
            }
            
            # Connect to Oracle 11g
            self.connection = oracledb.connect(**connection_params)
            self.cursor = self.connection.cursor()
            
            # Test the connection
            self.cursor.execute("SELECT 1 FROM DUAL")
            result = self.cursor.fetchone()
            
            db_identifier = self.sid if self.is_sid else self.service_name_db
            logger.info(f"Successfully connected to Oracle 11g at {self.host}:{self.port} ({db_identifier})")
            logger.info(f"Connection test result: {result}")
            
        except oracledb.Error as e:
            error_obj, = e.args
            logger.error(f"Oracle 11g connection failed: {error_obj.message}")
            
            # Provide specific error guidance
            if "ORA-12154" in str(e):
                logger.error("TNS:could not resolve the connect identifier - Check your SID/service name")
            elif "ORA-12541" in str(e):
                logger.error("TNS:no listener - Check if Oracle listener is running")
            elif "ORA-01017" in str(e):
                logger.error("Invalid username/password")
            elif "DPY-2019" in str(e):
                logger.error("Thick mode initialization failed - Oracle Instant Client required")
            
            raise ServerException(f"Oracle 11g connection failed: {error_obj.message}")
        except Exception as e:
            logger.error(f"Unexpected error connecting to Oracle 11g: {e}")
            raise ServerException(f"Oracle 11g connection failed: {str(e)}")

    async def connect_async(self):
        """Async wrapper for connect method"""
        self.connect()

    async def test_connection(self):
        """Test Oracle 11g connection"""
        try:
            await self.get_service_details()
            await self.connect_async()
            
            if self.cursor:
                # Test with a simple query
                self.cursor.execute("SELECT USER, SYSDATE FROM DUAL")
                result = self.cursor.fetchone()
                logger.info(f"Oracle 11g connection test successful. User: {result[0]}, Date: {result[1]}")
            
            return True
            
        except Exception as e:
            logger.error(f"Oracle 11g connection test failed: {e}")
            return False
        finally:
            self.close_connection()

    async def get_databases(self):
        """Get all schemas in the Oracle 11g database."""
        if not self.cursor:
            logger.error("Cursor is not available")
            return [self.user.upper()]
        
        self.cursor.execute("SELECT USERNAME FROM ALL_USERS ORDER BY USERNAME")
        return [row[0] for row in self.cursor.fetchall()]

    async def switch_database(self, db_name):
        """
        In Oracle, switching database means changing schema context
        """
        try:
            if not self.cursor:
                logger.error("Cursor is not available")
                return False
            
            # First check if we have access to the schema
            self.cursor.execute("""
                SELECT COUNT(*) 
                FROM ALL_TABLES 
                WHERE OWNER = :owner
            """, owner=db_name)
            
            if self.cursor.fetchone()[0] > 0:
                self.cursor.execute(f"ALTER SESSION SET CURRENT_SCHEMA = {db_name}")
                self.dbname = db_name
                logger.info(f"Successfully switched to schema: {db_name}")
            else:
                logger.warning(f"No tables found in schema {db_name}, skipping")
                return False
        except Exception as e:
            logger.warning(f"Could not switch to schema {db_name}: {str(e)}")
            return False
        return True

    async def get_tables(self):
        """Retrieve all tables in the current schema."""
        try:
            if not self.cursor:
                logger.error("Cursor is not available")
                return []
            
            tables_from_sources = []

            try:
                self.cursor.execute("""
                    SELECT TABLE_NAME
                    FROM ALL_TABLES
                    WHERE OWNER = :owner
                    ORDER BY TABLE_NAME
                """, owner=self.dbname)
                all_tables = [row[0] for row in self.cursor.fetchall()]
                tables_from_sources.extend(all_tables)
            except Exception:
                pass
            
            try:
                self.cursor.execute("""
                    SELECT OBJECT_NAME
                    FROM ALL_OBJECTS
                    WHERE OWNER = :owner
                    AND OBJECT_TYPE = 'TABLE'
                    ORDER BY OBJECT_NAME
                """, owner=self.dbname)
                all_objects = [row[0] for row in self.cursor.fetchall()]
                tables_from_sources.extend(all_objects)
            except Exception:
                pass

            try:
                self.cursor.execute("""
                    SELECT TABLE_NAME
                    FROM USER_TABLES
                    ORDER BY TABLE_NAME
                """)
                user_tables = [row[0] for row in self.cursor.fetchall()]
                tables_from_sources.extend(user_tables)
            except Exception:
                pass

            try:
                self.cursor.execute("""
                    SELECT TABLE_NAME
                    FROM DBA_TABLES
                    WHERE OWNER = :owner
                    ORDER BY TABLE_NAME
                """, owner=self.dbname)
                dba_tables = [row[0] for row in self.cursor.fetchall()]
                tables_from_sources.extend(dba_tables)
            except Exception:
                pass

            try:
                self.cursor.execute("""
                    SELECT DISTINCT table_name
                    FROM all_tab_privs
                    WHERE grantee = USER
                    AND privilege = 'SELECT'
                    AND owner = :owner
                """, owner=self.dbname)
                accessible_tables = [row[0] for row in self.cursor.fetchall()]
                tables_from_sources.extend(accessible_tables)
            except Exception:
                pass
            
            all_tables = list(set(tables_from_sources))
            filtered_tables = [
                table for table in all_tables
                if not table.startswith('LOGMNR')
                and not table.startswith('AQ$_')
                and not table.startswith('SYS_')
                and not table.startswith('REPCAT$')
                and not table.startswith('SQLPLUS_')
            ]
            return filtered_tables
        except Exception as e:
            logger.error(f"Failed to get tables for schema {self.dbname}: {str(e)}")
            return []

    async def infra_scan(self):
        """Perform infrastructure scan to get database metadata."""
        try:
            logger.info(f"Starting infra scan for service: {self.service_name}")
            await self.get_service_details()
            await self.connect_async()

            databases = await self.get_databases()

            logger.info(
                f"Infra scan result - Host: {self.host}, Port: {self.port}, Databases: {databases}"
            )
            return {"databases": databases}

        except Exception as e:
            logger.error(f"Infrastructure scan failed: {e}")
            return {"error": str(e)}
        finally:
            self.close_connection()

    async def scan_table(self, table):
        """Scan a table to extract its schema and sample data"""
        try:
            if not await self.table_exists(table):
                return None

            if not self.cursor:
                logger.error("Cursor is not available")
                return None

            self.cursor.execute(f"SELECT COUNT(*) FROM \"{table}\"")
            row_count = self.cursor.fetchone()[0]
            
            self.cursor.execute(
                f"""SELECT COLUMN_NAME, DATA_TYPE,
                        (SELECT CONSTRAINT_TYPE
                            FROM USER_CONSTRAINTS UC
                            JOIN USER_CONS_COLUMNS UCC 
                                ON UC.CONSTRAINT_NAME = UCC.CONSTRAINT_NAME
                            WHERE UC.TABLE_NAME = '{table}'
                                AND UCC.COLUMN_NAME = UTC.COLUMN_NAME
                                AND UC.CONSTRAINT_TYPE IN ('P', 'U')) 
                        AS CONSTRAINT_TYPE
                    FROM USER_TAB_COLUMNS UTC
                    WHERE TABLE_NAME = '{table}'"""
            )
            columns = []
            for row in self.cursor.fetchall():
                column_name, data_type, constraint_type = row
                is_primary = constraint_type == "P"
                has_index = constraint_type in ["P", "U"]
                columns.append(
                    TableColumn(
                        column_name=column_name,
                        data_type=data_type,
                        index=has_index,
                        primary_key=is_primary
                    )
                )

            self.cursor.execute(
                f"SELECT * FROM \"{table}\" WHERE ROWNUM <= {self.sample_count}"
            )
            rows = self.cursor.fetchall()

            db_identifier = self.sid if self.is_sid else self.service_name_db
            table_uri = f"oracle11g://{self.host}:{self.port}/{db_identifier}/{table}"

            # Check if table is already processed
            if self.db.skip_table(table_uri):
                logger.info(f"Skipping table {table} - Already processed")
                return None

            temp_folder = self.local_data_dir
            create_folder(temp_folder)
            output_file = os.path.join(
                temp_folder, 
                f"{table}_{str(uuid4())}.csv"
            )
            with open(output_file, mode="w", newline="", encoding="utf-8") as file:
                writer = csv.writer(file)
                writer.writerow([col.column_name for col in columns])
                writer.writerows(rows)

            return TableMetadata(
                service_name=self.service_name,
                service_type=self.service_type,
                service_provider=self.service_provider,
                sub_service=self.sub_service,
                db_type=DBTypes.ORACLE.value,
                db_name=db_identifier,
                table_name=table,
                table_size=0, 
                no_of_rows=row_count,
                table_uri=table_uri,
                columns=columns,
                local_filepath=output_file,
                details={
                    "host": self.host,
                    "port": self.port,
                    "sid": self.sid,
                    "service_name": self.service_name_db,
                    "region": "Unknown"
                }
            )
        except Exception as e:
            logger.error(
                f"Table scan failed. DB: {self.dbname}, Table: {table}",
                exc_info=e,
            )
            return None

    async def deep_scan(self) -> AsyncGenerator[TableMetadata, None]:
        """Perform deep scan to extract table schema and sample data."""
        try:
            await self.get_service_details()
            await self.connect_async()
            try:
                if not self.cursor:
                    logger.error("Cursor is not available")
                    return
                    
                self.cursor.execute("SELECT USER FROM DUAL")
                current_user = self.cursor.fetchone()[0]
                self.cursor.execute("SELECT TABLE_NAME FROM USER_TABLES")
                user_tables = [row[0] for row in self.cursor.fetchall()]

                if user_tables:
                    for table in user_tables:
                        try:
                            table_metadata = await self.scan_table(table)
                            if table_metadata:
                                yield table_metadata
                        except Exception as e:
                            logger.error(
                                f"Table scan failed. DB: {current_user}, Table: {table}",
                                exc_info=e
                            )
            except Exception:
                pass

            databases = await self.get_databases()

            if self.databases:
                databases = [d for d in self.databases if d in databases]

            for db in databases:
                try:
                    if await self.switch_database(db):
                        tables = await self.get_tables()

                        if not tables:
                            continue

                        for table in tables:
                            table_metadata = await self.scan_table(table)
                            if table_metadata:
                                yield table_metadata
                except Exception as e:
                    logger.error(
                        f"Deep scan failed for db: {db}. Service: {self.service_name}",
                        exc_info=e,
                    )
        except Exception as e:
            logger.error(f"Deep scan failed. Service: {self.service_name}", exc_info=e)
        finally:
            self.close_connection()

    async def table_exists(self, table):
        """Check if a table exists in the current schema."""
        try:
            if not self.cursor:
                return False
            
            self.cursor.execute("""
                SELECT COUNT(*)
                FROM ALL_TABLES
                WHERE OWNER = :owner
                AND TABLE_NAME = :table_name
            """, owner=self.dbname, table_name=table)
            return self.cursor.fetchone()[0] > 0
        except Exception:
            return False

    def close_connection(self):
        """Close the database connection and cursor"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()