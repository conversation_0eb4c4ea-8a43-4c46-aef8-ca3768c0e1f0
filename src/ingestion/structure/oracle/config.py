"""
Oracle Configuration Helper
This module provides configuration and utilities for Oracle database connections
"""

import os
import re
from typing import Dict, Optional, List
from dataclasses import dataclass
from enum import Enum

class OracleVersion(Enum):
    """Oracle version enumeration"""
    ORACLE_11G = "11g"
    ORACLE_12C = "12c"
    ORACLE_18C = "18c"
    ORACLE_19C = "19c"
    ORACLE_21C = "21c"
    ORACLE_23C = "23c"
    UNKNOWN = "unknown"

@dataclass
class OracleConnectionConfig:
    """Oracle connection configuration"""
    host: str
    port: int
    service_name: Optional[str] = None
    sid: Optional[str] = None
    user: str = None
    password: str = None
    auth_type: str = "basic"
    region: Optional[str] = None
    thick_mode: bool = True
    encoding: str = "UTF-8"
    nencoding: str = "UTF-8"
    disable_oob: bool = False
    threaded: bool = True
    events: bool = False
    
class OracleConfigHelper:
    """Helper class for Oracle configuration and compatibility"""
    
    @staticmethod
    def detect_version_from_banner(banner: str) -> OracleVersion:
        """Detect Oracle version from banner string"""
        if not banner:
            return OracleVersion.UNKNOWN
            
        version_patterns = {
            r'Oracle Database 23c': OracleVersion.ORACLE_23C,
            r'Oracle Database 21c': OracleVersion.ORACLE_21C,
            r'Oracle Database 19c': OracleVersion.ORACLE_19C,
            r'Oracle Database 18c': OracleVersion.ORACLE_18C,
            r'Oracle Database 12c': OracleVersion.ORACLE_12C,
            r'Oracle Database 11g': OracleVersion.ORACLE_11G,
        }
        
        for pattern, version in version_patterns.items():
            if re.search(pattern, banner, re.IGNORECASE):
                return version
                
        return OracleVersion.UNKNOWN
    
    @staticmethod
    def get_connection_params_for_version(
        config: OracleConnectionConfig, 
        version: OracleVersion
    ) -> Dict:
        """Get connection parameters optimized for specific Oracle version"""
        
        base_params = {
            'user': config.user,
            'password': config.password,
            'host': config.host,
            'port': config.port,
            'encoding': config.encoding,
            'nencoding': config.nencoding,
            'threaded': config.threaded,
            'events': config.events,
        }
        
        # Handle service name vs SID
        if config.service_name:
            base_params['service_name'] = config.service_name
        elif config.sid:
            base_params['sid'] = config.sid
            
        # Version-specific optimizations
        if version in [OracleVersion.ORACLE_11G, OracleVersion.ORACLE_12C]:
            # Older versions may need different settings
            base_params['disable_oob'] = True
            base_params['events'] = False
            
        elif version in [OracleVersion.ORACLE_19C, OracleVersion.ORACLE_21C, OracleVersion.ORACLE_23C]:
            # Newer versions support more features
            base_params['disable_oob'] = config.disable_oob
            
        return base_params
    
    @staticmethod
    def get_system_tables_to_exclude(version: OracleVersion) -> List[str]:
        """Get list of system tables to exclude for specific Oracle version"""
        
        base_exclusions = [
            'LOGMNR', 'AQ$_', 'SYS_', 'MDRT_', 'MDRS_', 'MDXT_', 'MDOT_',
            'BIN$', 'DR$', 'MLOG$_', 'RUPD$_', 'WM$_', 'APPLY$_', 'STREAMS$_',
            'SCHEDULER$_', 'SQLPLUS_', 'PLAN_TABLE', 'HELP'
        ]
        
        if version in [OracleVersion.ORACLE_12C, OracleVersion.ORACLE_18C, 
                      OracleVersion.ORACLE_19C, OracleVersion.ORACLE_21C, 
                      OracleVersion.ORACLE_23C]:
            # Add newer system tables
            base_exclusions.extend([
                'AUDSYS', 'OJVMSYS', 'GSMADMIN_INTERNAL', 'GSMCATUSER',
                'GSMUSER', 'SYSBACKUP', 'SYSDG', 'SYSKM', 'SYSRAC'
            ])
            
        return base_exclusions
    
    @staticmethod
    def get_schema_queries_for_version(version: OracleVersion) -> List[str]:
        """Get appropriate schema queries for specific Oracle version"""
        
        base_queries = [
            """SELECT USERNAME FROM ALL_USERS 
               WHERE USERNAME NOT IN ('SYS', 'SYSTEM', 'DBSNMP', 'SYSMAN', 'OUTLN', 
                                     'TSMSYS', 'DIP', 'ORACLE_OCM', 'APPQOSSYS', 
                                     'WMSYS', 'EXFSYS', 'CTXSYS', 'ANONYMOUS', 'XDB', 
                                     'ORDPLUGINS', 'ORDSYS', 'SI_INFORMTN_SCHEMA', 
                                     'MDSYS', 'OLAPSYS', 'MDDATA', 'SPATIAL_WFS_ADMIN_USR', 
                                     'SPATIAL_CSW_ADMIN_USR', 'FLOWS_FILES', 'APEX_030200', 
                                     'APEX_PUBLIC_USER', 'FLOWS_030200', 'OWBSYS', 
                                     'OWBSYS_AUDIT', 'SCOTT', 'HR', 'OE', 'PM', 'IX', 'SH', 'BI') 
               ORDER BY USERNAME""",
            """SELECT DISTINCT OWNER FROM ALL_TABLES 
               WHERE OWNER NOT LIKE '%SYS%' AND OWNER NOT LIKE 'APEX_%' 
               AND OWNER NOT LIKE 'FLOWS_%' ORDER BY OWNER"""
        ]
        
        if version in [OracleVersion.ORACLE_12C, OracleVersion.ORACLE_18C, 
                      OracleVersion.ORACLE_19C, OracleVersion.ORACLE_21C, 
                      OracleVersion.ORACLE_23C]:
            # Add queries for newer versions
            base_queries.extend([
                """SELECT USERNAME FROM DBA_USERS 
                   WHERE USERNAME NOT IN ('SYS', 'SYSTEM', 'AUDSYS', 'OJVMSYS') 
                   AND ORACLE_MAINTAINED = 'N' ORDER BY USERNAME""",
                """SELECT DISTINCT PDB_NAME FROM DBA_PDBS 
                   WHERE PDB_NAME != 'PDB$SEED' ORDER BY PDB_NAME"""
            ])
            
        return base_queries
    
    @staticmethod
    def get_table_queries_for_version(version: OracleVersion, schema: str) -> List[str]:
        """Get appropriate table queries for specific Oracle version"""
        
        base_queries = [
            ("ALL_TABLES", f"SELECT TABLE_NAME FROM ALL_TABLES WHERE OWNER = '{schema}' ORDER BY TABLE_NAME"),
            ("USER_TABLES", "SELECT TABLE_NAME FROM USER_TABLES ORDER BY TABLE_NAME"),
            ("ALL_OBJECTS", f"SELECT OBJECT_NAME FROM ALL_OBJECTS WHERE OWNER = '{schema}' AND OBJECT_TYPE = 'TABLE' ORDER BY OBJECT_NAME"),
        ]
        
        if version in [OracleVersion.ORACLE_12C, OracleVersion.ORACLE_18C, 
                      OracleVersion.ORACLE_19C, OracleVersion.ORACLE_21C, 
                      OracleVersion.ORACLE_23C]:
            # Add DBA queries for newer versions
            base_queries.append(
                ("DBA_TABLES", f"SELECT TABLE_NAME FROM DBA_TABLES WHERE OWNER = '{schema}' ORDER BY TABLE_NAME")
            )
            
        return base_queries
    
    @staticmethod
    def create_tnsnames_entry(config: OracleConnectionConfig, alias: str) -> str:
        """Create TNS entry for the configuration"""
        
        if config.service_name:
            connect_data = f"SERVICE_NAME = {config.service_name}"
        else:
            connect_data = f"SID = {config.sid}"
            
        return f"""
{alias} =
  (DESCRIPTION =
    (ADDRESS = (PROTOCOL = TCP)(HOST = {config.host})(PORT = {config.port}))
    (CONNECT_DATA =
      ({connect_data})
    )
  )
"""
    
    @staticmethod
    def get_region_from_host(host: str) -> Optional[str]:
        """Extract AWS region from RDS host"""
        if not host:
            return None
            
        # AWS RDS hostname pattern: instance.region.rds.amazonaws.com
        rds_pattern = r'\.([a-z]{2}-[a-z]+-\d+)\.rds\.amazonaws\.com'
        match = re.search(rds_pattern, host)
        
        if match:
            return match.group(1)
            
        return None
    
    @staticmethod
    def validate_connection_string(conn_str: str) -> bool:
        """Validate Oracle connection string format"""
        
        patterns = [
            r'^[\w]+/[\w]+@[\w\.-]+:\d+/[\w]+$',  # user/pass@host:port/service
            r'^[\w]+/[\w]+@[\w\.-]+:\d+:[\w]+$',  # user/pass@host:port:sid
            r'^[\w]+/[\w]+@\(DESCRIPTION=.*\)$',  # TNS format
        ]
        
        for pattern in patterns:
            if re.match(pattern, conn_str):
                return True
                
        return False