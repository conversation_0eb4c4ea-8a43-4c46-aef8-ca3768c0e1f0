from collections import defaultdict
import oracledb
from uuid import uuid4
import csv
import os
import boto3
from botocore.config import Config
from typing import Any, Async<PERSON><PERSON>ator, Dict, List
import asyncio
from contextlib import asynccontextmanager

from src.common.constants import ServiceTypes, DBTypes, AuthTypes, ServiceProviders
from src.utils.logger import get_ingestion_logger
from src.modules.repos import DatabaseManager
from src.ingestion.base_source import StructuredSource
from src.ingestion.data_class import AssetsDetails, TableMetadata, TableColumn
from src.common.config import TEMP_DATA_DIR
from src.utils.helpers import create_folder
from src.utils.exceptions import ServerException
from src.ingestion.structure.oracle.connection import get_region_from_host

logger = get_ingestion_logger()


class OracleSource(StructuredSource):
    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.ORACLE.value, service_name)
        self.db = DatabaseManager()
        self.connection = None
        self.cursor = None
        self.service_provider = ServiceProviders.LOCAL
        self.sub_service = DBTypes.ORACLE.value
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"
        self.oracle_version = None
        self.is_thick_mode = False
        self._initialize_oracle_client()
 
    def _initialize_oracle_client(self):
        """Initialize Oracle client in thick mode for better compatibility"""
        try:
            # Try to initialize thick mode for better compatibility
            if not oracledb.is_thin_mode():
                logger.info("Oracle client already in thick mode")
                self.is_thick_mode = True
            else:
                try:
                    # Try to initialize thick mode
                    oracledb.init_oracle_client()
                    self.is_thick_mode = True
                    logger.info("Oracle client initialized in thick mode")
                except Exception as e:
                    logger.warning(f"Could not initialize thick mode: {e}. Using thin mode.")
                    self.is_thick_mode = False
        except Exception as e:
            logger.warning(f"Oracle client initialization warning: {e}")
            self.is_thick_mode = False

    async def get_service_details(self):
        """Gets service details with enhanced error handling"""
        try:
            self.service = await self.db.get_service_by_service_name(self.service_name)
            credentials = self.service.get("credentials", {})
            self.host = credentials.get("host")
            self.port = credentials.get("port", 1521)  
            self.dbname = credentials.get("dbname")  
            self.username = credentials.get("username")
            self.auth_type = credentials.get("auth_type", AuthTypes.BASIC.value)
            self.password = credentials.get("password")
            self.sample_count = credentials.get("sample_count", 10)
            self.databases = credentials.get("databases", [])
            self.region = credentials.get("region")
            self.service_steward = self.service.get("data_steward")
            # Validate required fields
            if not all([self.host, self.username, self.dbname]):
                raise ServerException("Missing required connection parameters: host, username, or dbname")
            
            return self.service
        except Exception as e:
            logger.error(f"Failed to get service details: {e}")
            raise ServerException(f"Service configuration error: {e}")
 
    def generate_iam_token(self):
        """Generate IAM token for Oracle RDS authentication with enhanced error handling"""
        if not self.region or not self.host or not self.user:
            raise ServerException("Missing required fields for IAM authentication.")
        try:
            logger.info("Generating RDS IAM token using EC2 IAM role...")
 
            boto_config = Config(
                retries={"max_attempts": 3, "mode": "standard"},
                connect_timeout=5,
                read_timeout=10,
            )
            rds_client = boto3.client(
                "rds", region_name=self.region, config=boto_config
            )
            token = rds_client.generate_db_auth_token(
                DBHostname=self.host,
                Port=self.port,
                DBUsername=self.user,
            )
            logger.info("IAM token successfully generated.")
            return token
        except Exception as e:
            if "Unable to locate credentials" in str(e):
                raise ServerException(
                    "IAM role is not attached to the EC2 instance.", excep=e
                )
            raise ServerException("Failed to generate IAM token.", excep=e)
 
    def _get_connection_params(self, use_iam_token=False):
        dsn = f"{self.host}:{self.port}/{self.dbname}"
        params = {
            "user": self.username,
            "password": self.password,
            "dsn": dsn
        }
        if use_iam_token:
            params['password'] = self.generate_iam_token()
        return params

    def _get_minimal_connection_params(self, use_iam_token=False):
        return {
            "user": self.username,
            "password": self.password if not use_iam_token else self.generate_iam_token(),
            "dsn": f"{self.host}:{self.port}"
        }


    def connect_using_iam(self):
        """Connect to Oracle using IAM token with enhanced error handling"""
        try:
            params = self._get_connection_params(use_iam_token=True)
            self.connection = oracledb.connect(**params)
            self.cursor = self.connection.cursor()
            self._detect_oracle_version()
            logger.info(f"Connected using IAM to Oracle database: {self.dbname}")
        except Exception as e:
            logger.error(f"IAM-based Oracle connection failed: {e}")
            raise ServerException(f"IAM-based Oracle connection failed: {e}")
 
    def connect_using_creds(self):
        """Connect Using Credentials with enhanced error handling and fallback options"""
        connection_attempts = []
        
        # Primary connection attempt with full parameters
        try:
            params = self._get_connection_params(use_iam_token=False)
            self.connection = oracledb.connect(**params)
            self.cursor = self.connection.cursor()
            self._detect_oracle_version()
            logger.info(f"Connected using credentials to Oracle database: {self.dbname}")
            return
        except Exception as e:
            connection_attempts.append(f"Primary connection failed: {e}")
            logger.warning(f"Primary connection failed: {e}")
        
        # Fallback 1: Try with minimal parameters
        try:
            params = self._get_minimal_connection_params(use_iam_token=False)
            self.connection = oracledb.connect(**params)
            self.cursor = self.connection.cursor()
            self._detect_oracle_version()
            logger.info(f"Connected using minimal params to Oracle database: {self.dbname}")
            return
        except Exception as e:
            connection_attempts.append(f"Minimal params connection failed: {e}")
            logger.warning(f"Minimal params connection failed: {e}")
        
        # Fallback 2: Try with DSN format (minimal)
        try:
            dsn = f"{self.host}:{self.port}/{self.dbname}"
            params = {
              'user': self.username,   
              'password': self.password,
              'dsn': dsn,
            }
            if not self.is_thick_mode:
                params['disable_oob'] = True
                
            self.connection = oracledb.connect(**params)
            self.cursor = self.connection.cursor()
            self._detect_oracle_version()
            logger.info(f"Connected using DSN format to Oracle database: {self.dbname}")
            return
        except Exception as e:
            connection_attempts.append(f"DSN connection failed: {e}")
            logger.warning(f"DSN connection failed: {e}")
        
        # Fallback 3: Try with connection string (most basic)
        try:
            conn_str = f"{self.username}/{self.password}@{self.host}:{self.port}/{self.dbname}"
            self.connection = oracledb.connect(conn_str)
            self.cursor = self.connection.cursor()
            self._detect_oracle_version()
            logger.info(f"Connected using connection string to Oracle database: {self.dbname}")
            return
        except Exception as e:
            connection_attempts.append(f"Connection string failed: {e}")
            logger.warning(f"Connection string failed: {e}")
        
        # Fallback 4: Try with SID instead of service_name
        try:
            dsn = f"{self.host}:{self.port}/{self.dbname}"
            params = {
              'user': self.username,
              'password': self.password,
              'dsn': dsn,
            }
            # Try to create DSN with SID
            dsn_sid = oracledb.makedsn(self.host, self.port, sid=self.dbname)
            params['dsn'] = dsn_sid
            
            self.connection = oracledb.connect(**params)
            self.cursor = self.connection.cursor()
            self._detect_oracle_version()
            logger.info(f"Connected using SID format to Oracle database: {self.dbname}")
            return
        except Exception as e:
            connection_attempts.append(f"SID connection failed: {e}")
            logger.warning(f"SID connection failed: {e}")
        
        # Fallback 5: Try with service_name using makedsn
        try:
            dsn_service = oracledb.makedsn(self.host, self.port, service_name=self.dbname)
            params = {
              'user': self.username,
              'password': self.password,
              'dsn': dsn_service,
            }
            
            self.connection = oracledb.connect(**params)
            self.cursor = self.connection.cursor()
            self._detect_oracle_version()
            logger.info(f"Connected using service_name with makedsn to Oracle database: {self.dbname}")
            return
        except Exception as e:
            connection_attempts.append(f"Service name makedsn connection failed: {e}")
            logger.warning(f"Service name makedsn connection failed: {e}")
        
        # All connection attempts failed
        error_msg = "All connection attempts failed: " + "; ".join(connection_attempts)
        logger.error(error_msg)
        raise ServerException(error_msg)
        
    def _detect_oracle_version(self):
        """Detect Oracle version for version-specific handling"""
        try:
            if self.cursor:
                self.cursor.execute("SELECT BANNER FROM V$VERSION WHERE BANNER LIKE 'Oracle%'")
                version_info = self.cursor.fetchone()
                if version_info:
                    self.oracle_version = version_info[0]
                    logger.info(f"Oracle version detected: {self.oracle_version}")
        except Exception as e:
            logger.warning(f"Could not detect Oracle version: {e}")
            self.oracle_version = "Unknown"
 
    async def connect(self):
        """Creates Oracle connection with enhanced error handling"""
        try:
            if self.auth_type and self.auth_type.lower() == AuthTypes.BASIC.value:
                self.connect_using_creds()
            elif self.auth_type and self.auth_type.lower() == AuthTypes.IAM.value:
                self.connect_using_iam()
            else:
                logger.warning(f"Unknown auth type: {self.auth_type}, defaulting to BASIC")
                self.connect_using_creds()
        except Exception as e:
            logger.error(f"Connection failed: {e}")
            raise ServerException(f"Oracle connection failed: {e}")
 
    async def test_connection(self):
        """Test the database connection with comprehensive error handling"""
        try:
            await self.connect()
            if self.connection:
                # Test basic query
                self.cursor.execute("SELECT 1 FROM DUAL")
                result = self.cursor.fetchone()
                if result:
                    logger.info(f"Successfully connected to Oracle database {self.dbname}")
                    return True
            return False
        except Exception as e:
            logger.error(f"Oracle connection test failed: {str(e)}")
            return False
        finally:
            self.close_connection()
    
    async def get_databases(self):
        """Get all accessible schemas with enhanced error handling"""
        schemas = []
        
        # Try multiple approaches to get schemas
        schema_queries = [
            "SELECT USERNAME FROM ALL_USERS WHERE USERNAME NOT IN ('SYS', 'SYSTEM', 'DBSNMP', 'SYSMAN', 'OUTLN', 'TSMSYS', 'DIP', 'ORACLE_OCM', 'APPQOSSYS', 'WMSYS', 'EXFSYS', 'CTXSYS', 'ANONYMOUS', 'XDB', 'ORDPLUGINS', 'ORDSYS', 'SI_INFORMTN_SCHEMA', 'MDSYS', 'OLAPSYS', 'MDDATA', 'SPATIAL_WFS_ADMIN_USR', 'SPATIAL_CSW_ADMIN_USR', 'FLOWS_FILES', 'APEX_030200', 'APEX_PUBLIC_USER', 'FLOWS_030200', 'OWBSYS', 'OWBSYS_AUDIT', 'SCOTT', 'HR', 'OE', 'PM', 'IX', 'SH', 'BI') ORDER BY USERNAME",
            "SELECT DISTINCT OWNER FROM ALL_TABLES WHERE OWNER NOT LIKE '%SYS%' AND OWNER NOT LIKE 'APEX_%' AND OWNER NOT LIKE 'FLOWS_%' ORDER BY OWNER",
            "SELECT DISTINCT SCHEMA_NAME FROM ALL_USERS WHERE SCHEMA_NAME NOT LIKE '%SYS%' ORDER BY SCHEMA_NAME",
            "SELECT DISTINCT USERNAME FROM DBA_USERS WHERE USERNAME NOT IN ('SYS', 'SYSTEM') ORDER BY USERNAME"
        ]
        
        for query in schema_queries:
            try:
                self.cursor.execute(query)
                results = [row[0] for row in self.cursor.fetchall()]
                schemas.extend(results)
                if results:
                    break
            except Exception as e:
                logger.debug(f"Schema query failed: {query}, Error: {e}")
                continue
        unique_schemas = list(set(schemas))
        logger.info(f"Found {len(unique_schemas)} accessible schemas")
        return unique_schemas
 
    async def switch_database(self, db_name):
        """
        In Oracle, switching database means changing schema context
        """
        try:
            # First check if we have access to the schema
            self.cursor.execute("""
                SELECT COUNT(*) 
                FROM ALL_TABLES 
                WHERE OWNER = :owner
            """, owner=db_name)
            
            if self.cursor.fetchone()[0] > 0:
                self.cursor.execute(f"ALTER SESSION SET CURRENT_SCHEMA = {db_name}")
                self.dbname = db_name
                logger.info(f"Successfully switched to schema: {db_name}")
            else:
                logger.warning(f"No tables found in schema {db_name}, skipping")
                return False
        except Exception as e:
            logger.warning(f"Could not switch to schema {db_name}: {str(e)}")
            return False
        return True

    async def get_tables(self):
        """Retrieve all tables in the current schema."""
        try:
            tables_from_sources = []

            try:
                self.cursor.execute("""
                    SELECT TABLE_NAME
                    FROM ALL_TABLES
                    WHERE OWNER = :owner
                    ORDER BY TABLE_NAME
                """, owner=self.dbname)
                all_tables = [row[0] for row in self.cursor.fetchall()]
                tables_from_sources.extend(all_tables)
            except Exception:
                pass
            
            try:
                self.cursor.execute("""
                    SELECT OBJECT_NAME
                    FROM ALL_OBJECTS
                    WHERE OWNER = :owner
                    AND OBJECT_TYPE = 'TABLE'
                    ORDER BY OBJECT_NAME
                """, owner=self.dbname)
                all_objects = [row[0] for row in self.cursor.fetchall()]
                tables_from_sources.extend(all_objects)
            except Exception:
                pass

            try:
                self.cursor.execute("""
                    SELECT TABLE_NAME
                    FROM USER_TABLES
                    ORDER BY TABLE_NAME
                """)
                user_tables = [row[0] for row in self.cursor.fetchall()]
                tables_from_sources.extend(user_tables)
            except Exception:
                pass

            try:
                self.cursor.execute("""
                    SELECT TABLE_NAME
                    FROM DBA_TABLES
                    WHERE OWNER = :owner
                    ORDER BY TABLE_NAME
                """, owner=self.dbname)
                dba_tables = [row[0] for row in self.cursor.fetchall()]
                tables_from_sources.extend(dba_tables)
            except Exception:
                pass

            try:
                self.cursor.execute("""
                    SELECT DISTINCT table_name
                    FROM all_tab_privs
                    WHERE grantee = USER
                    AND privilege = 'SELECT'
                    AND owner = :owner
                """, owner=self.dbname)
                accessible_tables = [row[0] for row in self.cursor.fetchall()]
                tables_from_sources.extend(accessible_tables)
            except Exception:
                pass
            all_tables = list(set(tables_from_sources))
            filtered_tables = [
                table for table in all_tables
                if not table.startswith('LOGMNR')
                and not table.startswith('AQ$_')
                and not table.startswith('SYS_')
            ]
            return filtered_tables
        except Exception as e:
            logger.error(f"Failed to get tables for schema {self.dbname}: {str(e)}")
            return []

    async def scan_table(self, table):
        """Scan a table to extract its schema and sample data"""
        try:
            if not await self.table_exists(table):
                return None

            # Get row count
            # After switching schema, use just the table name
            try:
                self.cursor.execute(f'SELECT COUNT(*) FROM "{table}"')
                row_count = self.cursor.fetchone()[0]
            except Exception as e:
                # Fallback: try with schema qualification
                try:
                    self.cursor.execute(f'SELECT COUNT(*) FROM "{self.dbname}"."{table}"')
                    row_count = self.cursor.fetchone()[0]
                except Exception as e2:
                    logger.error(f"Failed to get row count for table {table}: {e2}")
                    return None
            
            # Get column information with proper schema handling
            column_queries = [
                # Query 1: ALL_TAB_COLUMNS (most reliable for cross-schema)
                f"""
                SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, DATA_PRECISION, DATA_SCALE, NULLABLE,
                    (SELECT CONSTRAINT_TYPE 
                        FROM ALL_CONSTRAINTS AC 
                        JOIN ALL_CONS_COLUMNS ACC ON AC.CONSTRAINT_NAME = ACC.CONSTRAINT_NAME 
                        WHERE AC.OWNER = '{self.dbname}' 
                        AND AC.TABLE_NAME = '{table}'
                        AND ACC.COLUMN_NAME = ATC.COLUMN_NAME 
                        AND AC.CONSTRAINT_TYPE = 'P') AS IS_PRIMARY_KEY,
                    (SELECT CASE WHEN COUNT(*) > 0 THEN 'Y' ELSE 'N' END
                        FROM ALL_IND_COLUMNS AIC
                        JOIN ALL_INDEXES AI ON AIC.INDEX_NAME = AI.INDEX_NAME
                        WHERE AI.TABLE_OWNER = '{self.dbname}'
                        AND AI.TABLE_NAME = '{table}'
                        AND AIC.COLUMN_NAME = ATC.COLUMN_NAME) AS HAS_INDEX
                FROM ALL_TAB_COLUMNS ATC
                WHERE OWNER = '{self.dbname}' AND TABLE_NAME = '{table}'
                ORDER BY COLUMN_ID
                """,
                
                # Query 2: DBA_TAB_COLUMNS (fallback for DBA privileges)
                f"""
                SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, DATA_PRECISION, DATA_SCALE, NULLABLE,
                    (SELECT CONSTRAINT_TYPE 
                        FROM DBA_CONSTRAINTS DC 
                        JOIN DBA_CONS_COLUMNS DCC ON DC.CONSTRAINT_NAME = DCC.CONSTRAINT_NAME 
                        WHERE DC.OWNER = '{self.dbname}' 
                        AND DC.TABLE_NAME = '{table}'
                        AND DCC.COLUMN_NAME = DTC.COLUMN_NAME 
                        AND DC.CONSTRAINT_TYPE = 'P') AS IS_PRIMARY_KEY,
                    (SELECT CASE WHEN COUNT(*) > 0 THEN 'Y' ELSE 'N' END
                        FROM DBA_IND_COLUMNS DIC
                        JOIN DBA_INDEXES DI ON DIC.INDEX_NAME = DI.INDEX_NAME
                        WHERE DI.TABLE_OWNER = '{self.dbname}'
                        AND DI.TABLE_NAME = '{table}'
                        AND DIC.COLUMN_NAME = DTC.COLUMN_NAME) AS HAS_INDEX
                FROM DBA_TAB_COLUMNS DTC
                WHERE OWNER = '{self.dbname}' AND TABLE_NAME = '{table}'
                ORDER BY COLUMN_ID
                """,
                
                # Query 3: USER_TAB_COLUMNS (only if current user owns the table)
                f"""
                SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, DATA_PRECISION, DATA_SCALE, NULLABLE,
                    (SELECT CONSTRAINT_TYPE 
                        FROM USER_CONSTRAINTS UC 
                        JOIN USER_CONS_COLUMNS UCC ON UC.CONSTRAINT_NAME = UCC.CONSTRAINT_NAME 
                        WHERE UC.TABLE_NAME = '{table}'
                        AND UCC.COLUMN_NAME = UTC.COLUMN_NAME 
                        AND UC.CONSTRAINT_TYPE = 'P') AS IS_PRIMARY_KEY,
                    (SELECT CASE WHEN COUNT(*) > 0 THEN 'Y' ELSE 'N' END
                        FROM USER_IND_COLUMNS UIC
                        JOIN USER_INDEXES UI ON UIC.INDEX_NAME = UI.INDEX_NAME
                        WHERE UI.TABLE_NAME = '{table}'
                        AND UIC.COLUMN_NAME = UTC.COLUMN_NAME) AS HAS_INDEX
                FROM USER_TAB_COLUMNS UTC
                WHERE TABLE_NAME = '{table}'
                ORDER BY COLUMN_ID
                """
            ]
            
            columns = []
            column_info_retrieved = False
            
            # Try each query until one succeeds
            for query in column_queries:
                try:
                    self.cursor.execute(query)
                    column_results = self.cursor.fetchall()
                    
                    if column_results:
                        for row in column_results:
                            column_name = row[0]
                            data_type = row[1]
                            data_length = row[2]
                            data_precision = row[3]
                            data_scale = row[4]
                            nullable = row[5]
                            is_primary_key = row[6] == 'P' if row[6] else False
                            has_index = row[7] == 'Y' if row[7] else False
                            
                            # Build complete data type string
                            complete_data_type = data_type
                            if data_type in ['VARCHAR2', 'CHAR', 'NVARCHAR2', 'NCHAR'] and data_length:
                                complete_data_type = f"{data_type}({data_length})"
                            elif data_type == 'NUMBER' and data_precision:
                                if data_scale and data_scale > 0:
                                    complete_data_type = f"NUMBER({data_precision},{data_scale})"
                                else:
                                    complete_data_type = f"NUMBER({data_precision})"
                            
                            columns.append(
                                TableColumn(
                                    column_name=column_name,
                                    data_type=complete_data_type,
                                    index=has_index,
                                    primary_key=is_primary_key
                                )
                            )
                        
                        column_info_retrieved = True
                        logger.info(f"Retrieved column info for {table} using query method")
                        break
                        
                except Exception as e:
                    logger.debug(f"Column query failed for {table}: {e}")
                    continue
            
            if not column_info_retrieved:
                logger.error(f"Failed to retrieve column information for table {table}")
                return None
                
            # Get sample data using proper schema qualification
            try:
                sample_query = f'SELECT * FROM "{self.dbname}"."{table}" WHERE ROWNUM <= {self.sample_count}'
                self.cursor.execute(sample_query)
                rows = self.cursor.fetchall()
                
                # Verify that we got the right number of columns
                if rows and len(rows[0]) != len(columns):
                    logger.warning(f"Column count mismatch for table {table}: expected {len(columns)}, got {len(rows[0])}")
                    
            except Exception as e:
                logger.error(f"Failed to get sample data for table {table}: {e}")
                # Try without schema qualification
                try:
                    sample_query = f'SELECT * FROM "{table}" WHERE ROWNUM <= {self.sample_count}'
                    self.cursor.execute(sample_query)
                    rows = self.cursor.fetchall()
                except Exception as e2:
                    logger.error(f"Failed to get sample data without schema qualification: {e2}")
                    rows = []

            table_uri = f"oracle://{self.dbname}/{table}"

            # Check if table is already processed
            if self.db.skip_table(table_uri):
                logger.info(f"Skipping table {table} - Already processed")
                return None

            temp_folder = self.local_data_dir
            create_folder(temp_folder)
            output_file = os.path.join(
                temp_folder, 
                f"{table}_{str(uuid4())}.csv"
            )
            
            # Write CSV with proper column headers
            with open(output_file, mode="w", newline="", encoding="utf-8") as file:
                writer = csv.writer(file)
                # Write actual column names as headers
                writer.writerow([col.column_name for col in columns])
                # Write sample data
                writer.writerows(rows)

            logger.info(f"Successfully scanned table {table} with {len(columns)} columns and {len(rows)} sample rows")

            return TableMetadata(
                service_name=self.service_name,
                service_type=self.service_type,
                service_provider=self.service_provider,
                sub_service=self.sub_service,
                db_type=DBTypes.ORACLE.value,
                db_name=self.dbname,
                table_name=table,
                table_size=0,  # Size will be determined after file is created
                no_of_rows=row_count,
                table_uri=table_uri,
                columns=columns,
                local_filepath=output_file,
                details={
                    "host": self.host,
                    "port": self.port,
                    "region": self.region or get_region_from_host(self.host)
                }
            )
        except Exception as e:
            logger.error(
                f"Table scan failed. DB: {self.dbname}, Table: {table}",
                exc_info=e,
            )
            return None
    
    async def infra_scan(self):
        """Perform infrastructure scan to get database metadata."""
        try:
            logger.info(f"Starting infra scan for service: {self.service_name}")
            await self.get_service_details()
            await self.connect()
 
            databases = await self.get_databases()
            region = self.region or get_region_from_host(self.host)
            db_metadata = []
            encryption_status = await self.get_encryption_status()
            for db in databases:
                try:
                    asset_objs = []
                    access_objs = []
                    switched = await self.switch_database(db)
                    if not switched:
                        logger.warning(f"Skipping schema '{db}' due to switch failure or lack of access.")
                        continue

                    # Owner
                    self.cursor.execute("SELECT USER FROM DUAL")
                    db_owner = self.cursor.fetchone()[0]

                    # Size
                    self.cursor.execute("""
                        SELECT ROUND(SUM(BYTES)/1024/1024, 2) AS total_size_mb
                        FROM USER_SEGMENTS
                    """)
                    size_mb = self.cursor.fetchone()[0] or 0
                    size_bytes = round(size_mb * 1024 * 1024)

                    # Table count
                    self.cursor.execute("SELECT COUNT(*) FROM USER_TABLES")
                    table_count = self.cursor.fetchone()[0]

                    # Access controls
                    access_permissions = await self.get_schema_access_controls(db)
                    access_objs = [{**perm, "asset_name": db} for perm in access_permissions]
                    try:
                        from src.utils.loaders import load_access_controls, load_asset_details
                        load_access_controls(access_objs)
                        logger.info(f"Access controls loaded successfully for schema {db}")
                    except Exception as e:
                        logger.error(f"Failed to load access controls for schema {db}: {e}", exc_info=True)

                    # Accessibility Info
                    accessibility_info = await self.determine_schema_accessibility_level(db)
                    access_level = accessibility_info.get("accessibility_level", "none")

                    db_metadata.append({
                        "dbname": db,
                        "db_owner": db_owner,
                        "total_size_mb": size_mb,
                        "table_count": table_count,
                        "access_permissions": access_permissions,
                        "accessibility_info": accessibility_info,
                        "region": region,
                    })

                    # Asset
                    try:

                        asset_obj = AssetsDetails(
                            asset_name=db,
                            service_provider=self.service_provider,
                            type="database",
                            category="structured",
                            location=region,
                            owner=db_owner,
                            security=encryption_status,
                            size=size_bytes,
                            count=table_count,
                            access_category=access_level,
                            service_name=str(self.service_name),
                            steward=str(self.service_steward) or db_owner,
                        )
                        asset_objs.append(asset_obj)
                        logger.info(f"Asset details created successfully for schema {db}")
                        load_asset_details(asset_objs)
                    except Exception as e:
                        logger.error(f"Failed to load asset details for schema {db}: {e}", exc_info=True)

                except Exception as e:
                    logger.error(f"Failed to process schema '{db}': {e}", exc_info=True)
                    continue
            logger.info(
                f"Infra scan result - Region: {region}, Host: {self.host}, Port: {self.port}, Databases: {databases}"
            )
            return {"databases": databases, "region": region, "metadata": db_metadata}
        except Exception as e:
            logger.error(f"Infrastructure scan failed: {e}")
            return {"error": str(e)}
        finally:
            self.close_connection()

    async def deep_scan(self) -> AsyncGenerator[TableMetadata, None]:
        """Perform deep scan with comprehensive error handling and recovery"""
        try:
            await self.get_service_details()
            await self.connect()
            await self.infra_scan()
            # Get all accessible databases/schemas
            try:
                databases = await self.get_databases()
                
                # Filter databases if specified in configuration
                if self.databases:
                    databases = [d for d in databases if d in self.databases]
                
                logger.info(f"Scanning {len(databases)} schemas")
                
                seen_tables = set()
                for db in databases:
                    try:
                        logger.info(f"Scanning schema: {db}")
                        
                        if await self.switch_database(db):
                            try:
                                tables = await self.get_tables()
                                
                                if not tables:
                                    logger.info(f"No tables found in schema {db}")
                                    continue
                                
                                logger.info(f"Found {len(tables)} tables in schema {db}")
                                
                                # Scan each table with individual error handling
                                for table in tables:
                                    fully_qualified = f"{self.dbname}.{table}"
                                    if fully_qualified in seen_tables:
                                        logger.debug(f"Skipping duplicate table reference {fully_qualified}")
                                        continue
                                    seen_tables.add(fully_qualified)
                                    try:
                                        table_metadata = await self.scan_table(table)
                                        if table_metadata:
                                            yield table_metadata
                                    except Exception as e:
                                        logger.error(f"Error scanning table {table} in schema {db}: {e}")
                                        continue
                                        
                            except Exception as e:
                                logger.error(f"Error getting tables for schema {db}: {e}")
                                continue
                        else:
                            logger.warning(f"Could not switch to schema {db}")
                            
                    except Exception as e:
                        logger.error(f"Error processing schema {db}: {e}")
                        continue
                        
            except Exception as e:
                logger.error(f"Error getting databases: {e}")
                
        except Exception as e:
            logger.error(f"Deep scan failed for service {self.service_name}: {e}")
        finally:
            self.close_connection()
    async def close_connection(self):
        """Safely close the Oracle DB connection."""
        try:
            if self.connection:
                await self.connection.close()
                logger.info("Oracle connection closed.")
        except Exception as e:
            logger.warning(f"Error closing Oracle connection: {e}")

 
    async def table_exists(self, table):
        """Check if table exists with multiple fallback approaches"""
        existence_queries = [
            # Query 1: ALL_TABLES with proper schema
            f"SELECT COUNT(*) FROM ALL_TABLES WHERE OWNER = '{self.dbname}' AND TABLE_NAME = '{table}'",
            
            # Query 2: DBA_TABLES (if DBA privileges)
            f"SELECT COUNT(*) FROM DBA_TABLES WHERE OWNER = '{self.dbname}' AND TABLE_NAME = '{table}'",
            
            # Query 3: ALL_OBJECTS 
            f"SELECT COUNT(*) FROM ALL_OBJECTS WHERE OWNER = '{self.dbname}' AND OBJECT_NAME = '{table}' AND OBJECT_TYPE = 'TABLE'",
            
            # Query 4: USER_TABLES (only if current user owns the table)
            f"SELECT COUNT(*) FROM USER_TABLES WHERE TABLE_NAME = '{table}'",
            
            # Query 5: Try direct access
            f"SELECT COUNT(*) FROM (SELECT 1 FROM \"{self.dbname}\".\"{table}\" WHERE ROWNUM <= 1)"
        ]
        
        for i, query in enumerate(existence_queries):
            try:
                self.cursor.execute(query)
                count = self.cursor.fetchone()[0]
                if count > 0:
                    logger.debug(f"Table {table} exists (confirmed by query {i+1})")
                    return True
                    
            except Exception as e:
                logger.debug(f"Table existence check {i+1} failed for {table}: {e}")
                continue
        
        logger.debug(f"Table {table} does not exist or is not accessible")
        return False
 
    # ------------------ Updated user_scan functionality for Oracle ------------------
    async def find_primary_keys(self, table_name: str, table_schema: str):
        """Return list of primary key column names for a given table in a schema."""
        try:
            query = (
                "SELECT acc.COLUMN_NAME "
                "FROM ALL_CONSTRAINTS ac "
                "JOIN ALL_CONS_COLUMNS acc ON ac.OWNER = acc.OWNER AND ac.CONSTRAINT_NAME = acc.CONSTRAINT_NAME "
                "WHERE ac.OWNER = :owner AND ac.TABLE_NAME = :table_name AND ac.CONSTRAINT_TYPE = 'P' "
                "ORDER BY acc.POSITION"
            )
            # Use the actual schema name instead of "public"
            actual_schema = table_schema if table_schema != "public" else self.dbname
            self.cursor.execute(query, owner=actual_schema, table_name=table_name)
            return [row[0] for row in self.cursor.fetchall()] or []
        except Exception as e:
            logger.error(
                f"Failed to fetch primary key details for table {actual_schema}.{table_name}", exc_info=e
            )
            return []

    async def find_foreign_key_tables(self, pk_table: str, pk_column: str):
        """Return list of referencing tables/columns for a given referenced table.column."""
        try:
            query = (
                "SELECT a.OWNER AS table_schema, a.TABLE_NAME, a.COLUMN_NAME "
                "FROM ALL_CONS_COLUMNS a "
                "JOIN ALL_CONSTRAINTS c ON a.OWNER = c.OWNER AND a.CONSTRAINT_NAME = c.CONSTRAINT_NAME "
                "JOIN ALL_CONS_COLUMNS ar ON c.R_OWNER = ar.OWNER AND c.R_CONSTRAINT_NAME = ar.CONSTRAINT_NAME AND a.POSITION = ar.POSITION "
                "WHERE c.CONSTRAINT_TYPE = 'R' AND ar.TABLE_NAME = :pk_table AND ar.COLUMN_NAME = :pk_column"
            )
            self.cursor.execute(query, pk_table=pk_table, pk_column=pk_column)
            fk_tables = []
            for row in self.cursor.fetchall() or []:
                fk_tables.append(
                    {
                        "table_schema": row[0],  # This will be the actual Oracle schema/owner name
                        "table_name": row[1],
                        "column_name": row[2],
                    }
                )
            return fk_tables
        except Exception as e:
            logger.error(
                f"Failed to fetch foreign key details for referenced {pk_table}.{pk_column}", exc_info=e
            )
            return []

    async def find_matching_rows_from_table(
        self, table_schema: str, table_name: str, column_name: str, values
    ):
        """Find rows in schema.table where column matches provided values. Uses LIKE for strings, IN for numbers."""
        try:
            # Use the actual schema name instead of "public"
            actual_schema = table_schema if table_schema != "public" else self.dbname
            
            # Determine data type
            dtype_query = (
                "SELECT DATA_TYPE FROM ALL_TAB_COLUMNS WHERE OWNER = :owner AND TABLE_NAME = :tname AND COLUMN_NAME = :cname"
            )
            self.cursor.execute(
                dtype_query, owner=actual_schema, tname=table_name, cname=column_name
            )
            row = self.cursor.fetchone()
            if not row:
                logger.info(
                    f"Column {column_name} not found in {actual_schema}.{table_name}"
                )
                return []
            data_type = (row[0] or "").upper()

            # Build query
            quoted_table = f'"{actual_schema}"."{table_name}"'
            quoted_column = f'"{column_name}"'
            results = []

            if data_type in (
                "VARCHAR2",
                "NVARCHAR2",
                "CHAR",
                "NCHAR",
                "CLOB",
                "NCLOB",
                "LONG",
            ):
                patterns = [f"%{v}%" for v in values]
                if not patterns:
                    return []
                # Build OR conditions with bind placeholders
                conds = []
                binds = {}
                for idx, p in enumerate(patterns):
                    key = f"p{idx}"
                    conds.append(f"{quoted_column} LIKE :{key}")
                    binds[key] = p
                where_clause = " OR ".join(conds)
                query = (
                    f"SELECT * FROM {quoted_table} WHERE (" + where_clause + ") AND ROWNUM <= 20"
                )
                self.cursor.execute(query, binds)
                rows = self.cursor.fetchall()
            else:
                # numeric or other: try to cast to numbers and use IN
                casted = []
                for v in values:
                    try:
                        if "." in str(v):
                            casted.append(float(v))
                        else:
                            casted.append(int(v))
                    except Exception:
                        continue
                if not casted:
                    return []
                binds = {f"v{i}": casted[i] for i in range(len(casted))}
                placeholders = ",".join([f":v{i}" for i in range(len(casted))])
                query = (
                    f"SELECT * FROM {quoted_table} WHERE {quoted_column} IN (" + placeholders + ") AND ROWNUM <= 20"
                )
                self.cursor.execute(query, binds)
                rows = self.cursor.fetchall()

            if not rows:
                return []

            # Get column names
            self.cursor.execute(
                """
                SELECT COLUMN_NAME
                FROM ALL_TAB_COLUMNS
                WHERE OWNER = :owner AND TABLE_NAME = :tname
                ORDER BY COLUMN_ID
                """,
                owner=actual_schema,
                tname=table_name,
            )
            col_names = [r[0] for r in self.cursor.fetchall()]

            for r in rows:
                record = {col_names[i]: r[i] for i in range(min(len(col_names), len(r)))}
                results.append(record)

            return results
        except Exception as e:
            logger.error(f"Error fetching from {actual_schema}.{table_name}: {e}")
            return []

    async def fetch_related_data_recursively(self, matching_tables):
        """Traverse FK graph to fetch related rows starting from initial matches"""
        results = []
        visited_tables = set()
        try:
            while len(matching_tables) > 0:
                next_tables = []
                for pii_match in matching_tables:
                    table_schema = pii_match.get("table_schema")
                    table_name = pii_match.get("table_name")
                    column_name = pii_match.get("column_name")
                    values = pii_match.get("values")
                    
                    # Use the actual schema name instead of "public"
                    actual_schema = table_schema if table_schema != "public" else self.dbname
                    qualified = f"{actual_schema}.{table_name}"
                    
                    if qualified in visited_tables:
                        continue

                    matching_rows = await self.find_matching_rows_from_table(
                        actual_schema, table_name, column_name, values
                    )
                    if len(matching_rows) == 0:
                        continue

                    results.append(
                        {
                            "service_name": self.service_name,
                            "service_provider": self.service_provider,
                            "sub_service": self.sub_service,
                            "db_name": self.dbname,
                            "table_schema": actual_schema,  # Use the actual schema name
                            "table_name": table_name,
                            "column_name": column_name,
                            "matching_rows": matching_rows,
                        }
                    )
                    visited_tables.add(qualified)

                    pk_keys = await self.find_primary_keys(table_name, actual_schema)
                    for pk_key in pk_keys:
                        values = list(set([m.get(pk_key) for m in matching_rows]))
                        fk_tables = await self.find_foreign_key_tables(table_name, pk_key)
                        for fk_table in fk_tables:
                            fk_table_schema = fk_table.get("table_schema")
                            fk_table_name = fk_table.get("table_name")
                            fk_column_name = fk_table.get("column_name")
                            next_tables.append(
                                {
                                    "table_schema": fk_table_schema,  # This will already be the actual Oracle schema
                                    "table_name": fk_table_name,
                                    "column_name": fk_column_name,
                                    "values": values,
                                }
                            )

                matching_tables = next_tables
        except Exception as e:
            logger.error("Error while fetching user related data", exc_info=e)
        return results

    async def user_scan(self, initial_tables):
        try:
            await self.get_service_details()
            await self.connect()
            databases = await self.get_databases()
            if len(self.databases) > 0:
                databases = [d for d in self.databases if d in databases]

            for db in databases:
                try:
                    logger.info(f"Scanning schema: {db}")
                    
                    switched = await self.switch_database(db)
                    if not switched:
                        continue
                    current_db_tables = []
                    for table in initial_tables:
                        if table.get("db_name") == db:
                            corrected_table = table.copy()
                            if corrected_table.get("table_schema") == "public":
                                corrected_table["table_schema"] = db
                            current_db_tables.append(corrected_table)
                    
                    if len(current_db_tables) == 0:
                        continue

                    logger.info(f"db_name: {db}, current_db_tables: {current_db_tables}")
                    results = await self.fetch_related_data_recursively(current_db_tables)

                    if len(results):
                        yield results
                except Exception as e:
                    logger.error(
                        f"User scan failed for db: {db}. Service: {self.service_name}",
                        exc_info=e,
                    )
        except Exception as e:
            logger.error(f"User scan failed. Service: {self.service_name}", exc_info=e)
    async def get_schema_access_controls(self, schema_name: str):
        try:
            schema_name = schema_name.upper()

            try:
                self.cursor.execute(f"""
                    SELECT grantee, privilege, owner, table_name
                    FROM dba_tab_privs
                    WHERE owner = '{schema_name}'
                """)
                tab_privs = self.cursor.fetchall()
                logger.info(f"tab_privs: {tab_privs}")
                self.cursor.execute(f"""
                    SELECT grantee, privilege
                    FROM dba_sys_privs
                    WHERE grantee = '{schema_name}'
                """)
                sys_privs = self.cursor.fetchall()
                logger.info(f"sys_privs: {sys_privs}")
                self.cursor.execute(f"""
                    SELECT grantee, granted_role
                    FROM dba_role_privs
                    WHERE grantee = '{schema_name}'
                """)
                role_privs = self.cursor.fetchall()
                logger.info(f"role_privs: {role_privs}")
            except Exception as dba_err:
                logger.warning(f"No DBA_* access — falling back to ALL_* views: {dba_err}")
                self.cursor.execute("""
                    SELECT grantor, privilege, table_name
                    FROM user_tab_privs
                """)
                tab_privs = [(row[0], row[1], row[2]) for row in self.cursor.fetchall()]  
                logger.info(f"tab_privs: {tab_privs}")
                # USER_SYS_PRIVS uses USERNAME instead of GRANTEE
                self.cursor.execute("""
                    SELECT username, privilege
                    FROM user_sys_privs
                """)
                sys_privs = [(row[0], row[1]) for row in self.cursor.fetchall()] 
                logger.info(f"sys_privs: {sys_privs}")
                # USER_ROLE_PRIVS has GRANTED_ROLE
                self.cursor.execute("""
                    SELECT granted_role
                    FROM user_role_privs
                """)
                roles = [r[0] for r in self.cursor.fetchall()]
                role_privs = [(schema_name, role) for role in roles] 
                logger.info(f"role_privs: {role_privs}")
            # Combine privileges
            privileges_by_grantee = defaultdict(set)
            for grantee, privilege, *_ in tab_privs + sys_privs:
                privileges_by_grantee[grantee.upper()].add(privilege.upper())

            for grantee, granted_role in role_privs:
                privileges_by_grantee[grantee.upper()].add(f"ROLE_{granted_role.upper()}")

            # Convert to structured access control objects
            access_controls = []
            for grantee, privileges in privileges_by_grantee.items():
                access_controls.append({
                    "schema": schema_name,
                    "user_or_role": grantee,
                    "role": "user" if "ROLE_" not in grantee else "role",
                    "access": self._map_privileges_to_access(privileges),
                    "privileges": list(privileges)
                })

            if not access_controls:
                logger.warning(f"No access control data found for schema {schema_name}")

            return access_controls

        except Exception as e:
            logger.error(f"Failed to get Oracle access controls for schema {schema_name}: {e}")
            return []


    def _map_privileges_to_access(self, privileges: set) -> str:
        """Map Oracle privilege names to simplified access levels."""
        normalized = {p.upper().strip() for p in privileges}

        # Broader categories
        read_privs = {
            "SELECT", "SELECT ANY TABLE", "READ", "EXECUTE", "EXECUTE ANY PROCEDURE"
        }
        write_privs = {
            "INSERT", "UPDATE", "DELETE", "INSERT ANY TABLE", "UPDATE ANY TABLE", "DELETE ANY TABLE"
        }
        ddl_privs = {
            "CREATE", "CREATE ANY TABLE", "CREATE VIEW", "CREATE PROCEDURE",
            "ALTER", "DROP", "TRUNCATE", "REFERENCES", "TRIGGER", "CREATE SESSION"
        }

        # Decide access level
        if normalized & (ddl_privs | write_privs | read_privs):
            if normalized & ddl_privs:
                return "full"
            elif normalized & write_privs:
                return "write"
            elif normalized & read_privs:
                return "read"
        return "none"



    async def determine_schema_accessibility_level(self, schema_name: str, instance_info: dict = None) -> dict:
        """Determine accessibility level for Oracle schema."""
        accessibility_analysis = {
            "database_name": schema_name,
            "accessibility_level": "restricted",
            "risk_factors": [],
            "access_details": {},
            "recommendations": []
        }
        try:
            # Get access controls
            access_controls = await self.get_schema_access_controls(schema_name)
            accessibility_analysis["access_details"]["access_controls"] = access_controls
            # Network checks (simplified for Oracle)
            # You can expand this with public IP, listener config, etc.
            if self.host:
                import socket
                try:
                    ip = socket.gethostbyname(self.host)
                    import ipaddress
                    ip_obj = ipaddress.IPv4Address(ip)
                    if ip_obj.is_global:
                        accessibility_analysis["risk_factors"].append("Host resolves to public IP")
                        accessibility_analysis["accessibility_level"] = "external"
                except Exception:
                    pass
            # If PUBLIC grantee has SELECT, mark as public
            for ac in access_controls:
                if ac["user_or_role"].upper() == "PUBLIC" and ac["access"] in ["read", "write", "full"]:
                    accessibility_analysis["accessibility_level"] = "public"
                    accessibility_analysis["risk_factors"].append("PUBLIC grantee has access")
            # Encryption status
            encryption_status = await self.get_encryption_status()
            accessibility_analysis["encryption_enabled"] = encryption_status
            if not encryption_status:
                accessibility_analysis["risk_factors"].append("Connection is not encrypted (SSL/TLS disabled)")
                accessibility_analysis["recommendations"].append("Enable SSL/TLS encryption for Oracle connections.")
            return accessibility_analysis
        except Exception as e:
            logger.error(f"Error determining accessibility for schema {schema_name}: {e}")
            return accessibility_analysis

    async def get_encryption_status(self):
        try:
            try:
                self.cursor.execute("""
                    SELECT NETWORK_SERVICE_BANNER
                    FROM V$SESSION_CONNECT_INFO
                    WHERE SID = SYS_CONTEXT('USERENV', 'SID')
                """)
                banners = self.cursor.fetchall()
                for row in banners:
                    banner = row[0].upper() if row and row[0] else ""
                    if "SSL" in banner or "TCPS" in banner:
                        return True
                    if "ENCRYPTION SERVICE" in banner or "CRYPTO-CHECKSUMMING SERVICE" in banner:
                        return True
            except Exception as e:
                if hasattr(e, 'args') and any('ORA-00942' in str(arg) for arg in e.args):
                    logger.warning("V$SESSION_CONNECT_INFO not accessible, falling back to connection string for encryption status.")
                else:
                    logger.warning(f"Failed to check Oracle SSL/TLS encryption status: {e}")

            if hasattr(self.connection, 'dsn'):
                dsn = str(self.connection.dsn).upper()
                if "TCPS" in dsn:
                    return True

            ssl_params = [
                getattr(self.connection, 'ssl_server_dn_match', None),
                getattr(self.connection, 'wallet_location', None)
            ]
            if any(ssl_params):
                return True

            return False

        except Exception as e:
            logger.warning(f"Failed to check Oracle SSL/TLS encryption status: {e}")
            return False
