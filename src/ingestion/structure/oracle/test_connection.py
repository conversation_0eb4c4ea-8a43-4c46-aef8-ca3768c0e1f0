import oracledb
import logging
from src.utils.exceptions import ClientException, ServerException

logger = logging.getLogger("Unstructure")


async def test_oracle_connection(data: dict) -> dict:
    creds = data.get("credentials", {})
    try:
        logger.info(f"Attempting Oracle connection to host: {creds.get('host')}")
        user = creds["user"]
        password = creds["password"]
        host = creds["host"]
        port = creds.get("port", 1521)
        dbname = creds["dbname"]
        conn_str = f"{user}/{password}@{host}:{port}/{dbname}"
        conn = oracledb.connect(conn_str)
        cursor = conn.cursor()
        cursor.execute("SELECT 1 FROM DUAL")
        cursor.fetchone()
        cursor.close()
        conn.close()
        logger.info("Oracle connection test successful.")
        return {
            "status": True,
            "message": "Oracle connection successful"
        }
    except oracledb.DatabaseError as e:
        error_code = e.args[0].code if e.args else None
        if error_code == 1017:
            logger.warning(f"Invalid Oracle credentials: {str(e)}")
            raise ClientException("Invalid Oracle credentials", code=401, excep=e)
        elif error_code == 12541:
            logger.warning(f"Oracle listener not available: {str(e)}")
            raise ClientException("Oracle listener not available", code=503, excep=e)
        elif error_code == 12514:
            logger.warning(f"Oracle service not found: {str(e)}")
            raise ClientException(
                "Oracle service not found", code=404, excep=e
            )
        else:
            logger.warning(f"Oracle connection error: {str(e)}")
            raise ClientException(
                "Oracle connection failed", code=500, excep=e
            )

    except Exception as e:
        logger.exception("Unexpected error during Oracle connection test")
        raise ServerException("Oracle test failed", excep=e) 