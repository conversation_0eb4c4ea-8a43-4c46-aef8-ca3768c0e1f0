import logging
from pymongo import MongoClient
from pymongo.errors import ConfigurationError, PyMongoError
from urllib.parse import quote_plus
from src.utils.exceptions import ClientException, ServerException

logger = logging.getLogger("Unstructure")

async def test_mongodb_connection(data: dict) -> dict:
    creds = data.get("credentials", {})
    username = quote_plus(creds.get("username", ""))
    password = quote_plus(creds.get("password", ""))
    host = creds.get("host")
    port = creds.get("port", 27017)
    use_srv = creds.get("use_srv", False)
    tls = creds.get("tls", False)
    database = creds.get("database")

    try:
        logger.info(f"Attempting MongoDB connection to host: {host}")


        auth_db = database if database else "admin"

        if use_srv:
            uri = f"mongodb+srv://{username}:{password}@{host}/{auth_db}"
        else:
            uri = f"mongodb://{username}:{password}@{host}:{port}/{auth_db}"

        client_options = {"tls": tls} if tls else {}

        client = MongoClient(uri, **client_options)
        client.admin.command("ping") 
        client.close()

        logger.info("MongoDB connection test successful.")
        return {
            "status": True,
            "message": "MongoDB connection successful"
        }

    except ConfigurationError as e:
        logger.warning(f"MongoDB configuration error: {str(e)}")
        raise ClientException("Invalid MongoDB configuration or credentials", code=401, excep=e)

    except PyMongoError as e:
        logger.warning(f"MongoDB connection failed: {str(e)}")
        raise ClientException("MongoDB connection failed", code=401, excep=e)

    except Exception as e:
        logger.exception("Unexpected error during MongoDB connection test")
        raise ServerException("MongoDB test failed", excep=e)