import os
from uuid import uuid4
import csv
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Generator
from pymongo import MongoClient
from pymongo.errors import ConfigurationError, OperationFailure, ServerSelectionTimeoutError
from urllib.parse import quote_plus
from bson import ObjectId
from src.common.constants import ServiceTypes, DBTypes, AuthTypes, ServiceProviders
from src.utils.logger import get_ingestion_logger
from src.modules.repos import DatabaseManager
from src.ingestion.base_source import StructuredSource
from src.ingestion.data_class import TableMetadata, TableColumn
from src.common.config import TEMP_DATA_DIR
from src.utils.helpers import create_folder
from src.utils.exceptions import ServerException

logger = get_ingestion_logger()

class MongoDBSource(StructuredSource):
    def __init__(self, service_name: str):
        super().__init__(ServiceTypes.MONGODB.value, service_name)
        self.db = DatabaseManager()
        self.connection = None
        self.service_provider = ServiceProviders.LOCAL
        self.sub_service = DBTypes.MONGODB.value
        self.local_data_dir = f"{TEMP_DATA_DIR}/{self.service_type}"
        
        # Statistics tracking
        self.scan_stats = {
            'total_databases': 0,
            'total_collections': 0,
            'total_documents': 0,
            'total_columns': 0,
            'processed_databases': 0,
            'processed_collections': 0,
            'skipped_databases': 0,
            'skipped_collections': 0,
            'errors': []
        }

    async def get_service_details(self) -> Dict[str, Any]:
        """Retrieve and validate service configuration"""
        try:
            logger.info(f"Retrieving service details for: {self.service_name}")
            self.service = await self.db.get_service_by_service_name(self.service_name)
            
            if not self.service:
                raise ServerException(f"Service '{self.service_name}' not found")
            
            credentials = self.service.get("credentials", {})
            
            # Required fields
            self.host = credentials.get("host")
            if not self.host:
                raise ServerException("MongoDB host is required")
            
            # Optional fields with defaults
            self.username = credentials.get("username", "")
            self.password = credentials.get("password", "")
            self.port = credentials.get("port", 27017)
            self.database = credentials.get("database", "")  # If empty, scan all databases
            self.auth_type = credentials.get("auth_type", AuthTypes.BASIC.value)
            self.sample_count = credentials.get("sample_count", 100)
            self.use_srv = credentials.get("use_srv", False)
            self.tls = credentials.get("tls", False)
            self.region = credentials.get("region", "Unknown")
            self.connection_timeout = credentials.get("connection_timeout", 5000)
            self.socket_timeout = credentials.get("socket_timeout", 5000)
            self.auth_source = credentials.get("auth_source", "admin")
            
            logger.info(f"Service details retrieved successfully for {self.service_name}")
            logger.info(f"Host: {self.host}, Port: {self.port}, Database: {self.database or 'All'}")
            
            return self.service
            
        except Exception as e:
            logger.error(f"Failed to get service details: {str(e)}")
            raise ServerException(f"Failed to get service details: {str(e)}", excep=e)

    async def connect(self) -> None:
        """Establish connection to MongoDB with comprehensive error handling"""
        try:
            logger.info(f"Attempting to connect to MongoDB at {self.host}:{self.port}")
            
            # Prepare connection string
            username = quote_plus(self.username or "")
            password = quote_plus(self.password or "")
            
            # Build URI based on connection type
            if self.use_srv:
                if username and password:
                    uri = f"mongodb+srv://{username}:{password}@{self.host}/"
                else:
                    uri = f"mongodb+srv://{self.host}/"
            else:
                if username and password:
                    uri = f"mongodb://{username}:{password}@{self.host}:{self.port}/"
                else:
                    uri = f"mongodb://{self.host}:{self.port}/"
            
            # Connection options
            client_options = {
                "serverSelectionTimeoutMS": self.connection_timeout,
                "socketTimeoutMS": self.socket_timeout,
                "connectTimeoutMS": self.connection_timeout,
                "maxPoolSize": 10,
                "retryWrites": True,
                "retryReads": True
            }
            
            if self.tls:
                client_options["tls"] = True
                client_options["tlsAllowInvalidCertificates"] = True
                
            if self.username and self.auth_source:
                client_options["authSource"] = self.auth_source
            
            # Create connection
            self.connection = MongoClient(uri, **client_options)
            
            # Test connection
            server_info = self.connection.admin.command("ping")
            db_info = self.connection.server_info()
            
            logger.info(f"Successfully connected to MongoDB")
            logger.info(f"MongoDB Version: {db_info.get('version', 'Unknown')}")
            logger.info(f"Server Response Time: {server_info.get('ok', 0)} ms")
            
        except ServerSelectionTimeoutError as e:
            error_msg = f"MongoDB connection timeout - unable to reach server at {self.host}:{self.port}"
            logger.error(error_msg)
            raise ServerException(error_msg, excep=e)
            
        except ConfigurationError as e:
            error_msg = f"MongoDB configuration error: {str(e)}"
            logger.error(error_msg)
            raise ServerException(error_msg, excep=e)
            
        except OperationFailure as e:
            error_msg = f"MongoDB authentication failed: {str(e)}"
            logger.error(error_msg)
            raise ServerException(error_msg, excep=e)
            
        except Exception as e:
            error_msg = f"MongoDB connection failed: {str(e)}"
            logger.error(error_msg)
            raise ServerException(error_msg, excep=e)

    async def test_connection(self) -> bool:
        """Test MongoDB connection"""
        try:
            logger.info(f"Testing connection for service: {self.service_name}")
            await self.get_service_details()
            await self.connect()
            
            # Additional connection tests
            db_names = self.connection.list_database_names()
            logger.info(f"Connection test successful - Found {len(db_names)} databases")
            
            return True
            
        except Exception as e:
            logger.error(f"MongoDB connection test failed: {str(e)}")
            return False
            
        finally:
            self.close_connection()

    async def infra_scan(self) -> Dict[str, Any]:
        """Perform infrastructure scan to get high-level database information"""
        try:
            logger.info(f"Starting infrastructure scan for service: {self.service_name}")
            await self.get_service_details()
            await self.connect()
            
            # Get all databases
            db_names = self.connection.list_database_names()
            self.scan_stats['total_databases'] = len(db_names)
            
            logger.info(f"Found {len(db_names)} databases: {db_names}")
            
            # Get database statistics
            db_stats = {}
            for db_name in db_names:
                try:
                    db = self.connection[db_name]
                    collections = db.list_collection_names()
                    
                    # Get database stats
                    try:
                        stats = db.command("dbStats")
                        db_stats[db_name] = {
                            "collections": len(collections),
                            "collection_names": collections,
                            "data_size": stats.get("dataSize", 0),
                            "storage_size": stats.get("storageSize", 0),
                            "index_size": stats.get("indexSize", 0),
                            "objects": stats.get("objects", 0)
                        }
                    except Exception as e:
                        logger.warning(f"Could not get stats for database '{db_name}': {e}")
                        db_stats[db_name] = {
                            "collections": len(collections),
                            "collection_names": collections,
                            "error": str(e)
                        }
                        
                    self.scan_stats['total_collections'] += len(collections)
                    
                except Exception as e:
                    logger.warning(f"Could not access database '{db_name}': {e}")
                    self.scan_stats['skipped_databases'] += 1
                    self.scan_stats['errors'].append(f"Database '{db_name}': {str(e)}")
            
            result = {
                "databases": db_names,
                "database_stats": db_stats,
                "host": self.host,
                "port": self.port,
                "region": self.region,
                "total_databases": len(db_names),
                "total_collections": self.scan_stats['total_collections'],
                "scan_timestamp": datetime.now().isoformat()
            }
            
            logger.info(f"Infrastructure scan completed successfully")
            logger.info(f"Total databases: {len(db_names)}, Total collections: {self.scan_stats['total_collections']}")
            
            return result
            
        except Exception as e:
            error_msg = f"Infrastructure scan failed: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg, "scan_stats": self.scan_stats}
            
        finally:
            self.close_connection()

    def safe_get_nested(self, doc: Dict[str, Any], key: str) -> Any:
        """Safely get nested values from document"""
        if not key or not isinstance(doc, dict):
            return None
            
        parts = key.split(".")
        value = doc
        
        try:
            for part in parts:
                if isinstance(value, dict) and part in value:
                    value = value[part]
                elif isinstance(value, list) and part.isdigit():
                    idx = int(part)
                    if 0 <= idx < len(value):
                        value = value[idx]
                    else:
                        return None
                else:
                    return None
            return value
        except Exception as e:
            logger.debug(f"Error getting nested value for key '{key}': {e}")
            return None

    def serialize_value(self, value: Any) -> str:
        """Convert MongoDB values to string representation"""
        try:
            if value is None:
                return ""
            elif isinstance(value, ObjectId):
                return str(value)
            elif isinstance(value, datetime):
                return value.isoformat()
            elif isinstance(value, (dict, list)):
                return json.dumps(value, default=str)
            else:
                return str(value)
        except Exception:
            return str(value)

    async def deep_scan(self) -> Generator[TableMetadata, None, None]:
        """Perform comprehensive deep scan of all MongoDB collections"""
        try:
            logger.info(f"Starting deep scan for service: {self.service_name}")
            await self.get_service_details()
            await self.connect()
            
            # Reset stats for deep scan
            self.scan_stats = {
                'total_databases': 0,
                'total_collections': 0,
                'total_documents': 0,
                'total_columns': 0,
                'processed_databases': 0,
                'processed_collections': 0,
                'skipped_databases': 0,
                'skipped_collections': 0,
                'errors': []
            }
            
            # Get databases to scan
            if self.database:
                db_names = [self.database] if self.database in self.connection.list_database_names() else []
                if not db_names:
                    raise ServerException(f"Specified database '{self.database}' not found")
            else:
                db_names = self.connection.list_database_names()
                # Filter out system databases
                db_names = [db for db in db_names if db not in ['admin', 'config', 'local']]
            
            self.scan_stats['total_databases'] = len(db_names)
            logger.info(f"Scanning {len(db_names)} databases: {db_names}")
            
            # Process each database
            for db_name in db_names:
                try:
                    logger.info(f"Processing database: {db_name}")
                    db = self.connection[db_name]
                    
                    # Get collections
                    try:
                        collections = db.list_collection_names()
                        self.scan_stats['total_collections'] += len(collections)
                        logger.info(f"Found {len(collections)} collections in database '{db_name}'")
                        
                    except Exception as e:
                        logger.warning(f"Cannot access collections in database '{db_name}': {e}")
                        self.scan_stats['skipped_databases'] += 1
                        self.scan_stats['errors'].append(f"Database '{db_name}': {str(e)}")
                        continue
                    
                    # Process each collection
                    for coll_name in collections:
                        try:
                            logger.info(f"Processing collection: {db_name}.{coll_name}")
                            collection = db[coll_name]
                            
                            # Get collection statistics
                            try:
                                coll_stats = db.command("collStats", coll_name)
                                total_docs = coll_stats.get("count", 0)
                                avg_obj_size = coll_stats.get("avgObjSize", 0)
                                storage_size = coll_stats.get("storageSize", 0)
                                
                                logger.info(f"Collection stats - Documents: {total_docs}, Avg Size: {avg_obj_size}, Storage: {storage_size}")
                                
                            except Exception as e:
                                logger.warning(f"Could not get collection stats for '{coll_name}': {e}")
                                total_docs = 0
                                avg_obj_size = 0
                                storage_size = 0
                            
                            # Sample documents for schema inference
                            try:
                                # Use aggregation pipeline for better sampling
                                sample_pipeline = [{"$sample": {"size": min(self.sample_count, total_docs or self.sample_count)}}]
                                docs = list(collection.aggregate(sample_pipeline))
                                
                                if not docs:
                                    # Fallback to find if aggregate fails
                                    docs = list(collection.find().limit(self.sample_count))
                                
                                if not docs:
                                    logger.warning(f"No accessible documents in collection '{db_name}.{coll_name}'")
                                    self.scan_stats['skipped_collections'] += 1
                                    continue
                                
                                logger.info(f"Retrieved {len(docs)} sample documents from {db_name}.{coll_name}")
                                self.scan_stats['total_documents'] += len(docs)
                                
                            except Exception as e:
                                logger.warning(f"Cannot sample documents from '{db_name}.{coll_name}': {e}")
                                self.scan_stats['skipped_collections'] += 1
                                self.scan_stats['errors'].append(f"Collection '{db_name}.{coll_name}': {str(e)}")
                                continue
                            
                            # Infer schema
                            try:
                                columns = self.infer_columns(docs, collection, db_name, coll_name)
                                self.scan_stats['total_columns'] += len(columns)
                                logger.info(f"Inferred {len(columns)} columns for {db_name}.{coll_name}")
                                
                            except Exception as e:
                                logger.error(f"Schema inference failed for '{db_name}.{coll_name}': {e}")
                                self.scan_stats['skipped_collections'] += 1
                                self.scan_stats['errors'].append(f"Schema inference '{db_name}.{coll_name}': {str(e)}")
                                continue
                            
                            # Export to CSV
                            try:
                                table_uri = f"mongodb://{db_name}/{coll_name}"
                                
                                # Check if already processed
                                if self.db.skip_table(table_uri):
                                    logger.info(f"Skipping table {table_uri} - Already processed")
                                    continue
                                
                                # Create output directory
                                create_folder(self.local_data_dir)
                                output_file = os.path.join(
                                    self.local_data_dir, 
                                    f"{db_name}_{coll_name}_{str(uuid4())}.csv"
                                )
                                
                                # Extract data and write to CSV
                                column_names = [col.column_name for col in columns]
                                
                                with open(output_file, mode="w", newline="", encoding="utf-8") as file:
                                    writer = csv.writer(file, quoting=csv.QUOTE_MINIMAL)
                                    writer.writerow(column_names)
                                    
                                    for doc in docs:
                                        row = []
                                        for col_name in column_names:
                                            value = self.safe_get_nested(doc, col_name)
                                            serialized_value = self.serialize_value(value)
                                            row.append(serialized_value)
                                        writer.writerow(row)
                                
                                file_size = os.path.getsize(output_file)
                                logger.info(f"CSV export completed: {output_file} ({file_size} bytes)")
                                
                                # Create metadata
                                metadata = TableMetadata(
                                    service_name=self.service_name,
                                    service_type=self.service_type,
                                    service_provider=self.service_provider,
                                    sub_service=self.sub_service,
                                    db_type=DBTypes.MONGODB.value,
                                    db_name=db_name,
                                    table_name=coll_name,
                                    table_uri=table_uri,
                                    no_of_rows=total_docs,
                                    table_size=storage_size,
                                    columns=columns,
                                    local_filepath=output_file,
                                    details={
                                        "host": self.host,
                                        "port": self.port,
                                        "region": self.region,
                                        "sample_count": len(docs),
                                        "total_documents": total_docs,
                                        "avg_document_size": avg_obj_size,
                                        "storage_size": storage_size,
                                        "file_size": file_size,
                                        "scan_timestamp": datetime.now().isoformat()
                                    }
                                )
                                
                                self.scan_stats['processed_collections'] += 1
                                logger.info(f"Successfully processed collection: {db_name}.{coll_name}")
                                
                                yield metadata
                                
                            except Exception as e:
                                logger.error(f"CSV export failed for '{db_name}.{coll_name}': {e}")
                                self.scan_stats['skipped_collections'] += 1
                                self.scan_stats['errors'].append(f"CSV export '{db_name}.{coll_name}': {str(e)}")
                                continue
                                
                        except Exception as e:
                            logger.error(f"Collection processing failed for '{db_name}.{coll_name}': {e}")
                            self.scan_stats['skipped_collections'] += 1
                            self.scan_stats['errors'].append(f"Collection '{db_name}.{coll_name}': {str(e)}")
                            continue
                    
                    self.scan_stats['processed_databases'] += 1
                    logger.info(f"Completed processing database: {db_name}")
                    
                except Exception as e:
                    logger.error(f"Database processing failed for '{db_name}': {e}")
                    self.scan_stats['skipped_databases'] += 1
                    self.scan_stats['errors'].append(f"Database '{db_name}': {str(e)}")
                    continue
            
            # Log final statistics
            logger.info("=== DEEP SCAN COMPLETION SUMMARY ===")
            logger.info(f"Total Databases: {self.scan_stats['total_databases']}")
            logger.info(f"Processed Databases: {self.scan_stats['processed_databases']}")
            logger.info(f"Skipped Databases: {self.scan_stats['skipped_databases']}")
            logger.info(f"Total Collections: {self.scan_stats['total_collections']}")
            logger.info(f"Processed Collections: {self.scan_stats['processed_collections']}")
            logger.info(f"Skipped Collections: {self.scan_stats['skipped_collections']}")
            logger.info(f"Total Documents Sampled: {self.scan_stats['total_documents']}")
            logger.info(f"Total Columns Inferred: {self.scan_stats['total_columns']}")
            logger.info(f"Total Errors: {len(self.scan_stats['errors'])}")
            
            if self.scan_stats['errors']:
                logger.warning("Errors encountered during scan:")
                for error in self.scan_stats['errors']:
                    logger.warning(f"  - {error}")
            
        except Exception as e:
            logger.error(f"Deep scan failed: {str(e)}")
            self.scan_stats['errors'].append(f"Deep scan: {str(e)}")
            raise ServerException(f"Deep scan failed: {str(e)}", excep=e)
            
        finally:
            self.close_connection()

    def infer_columns(self, documents: List[Dict[str, Any]], collection, db_name: str, coll_name: str) -> List[TableColumn]:
        """Infer column schema from sample documents with enhanced type detection"""
        logger.info(f"Inferring schema for collection {db_name}.{coll_name} from {len(documents)} documents")
        
        def flatten_document(doc: Dict[str, Any], parent_key: str = '', sep: str = '.') -> Dict[str, Any]:
            """Recursively flatten nested documents"""
            items = {}
            for key, value in doc.items():
                new_key = f"{parent_key}{sep}{key}" if parent_key else key
                
                if isinstance(value, dict) and value:  # Non-empty dict
                    items.update(flatten_document(value, new_key, sep=sep))
                elif isinstance(value, list) and value:  # Non-empty list
                    # Handle arrays by taking the first element for type inference
                    if isinstance(value[0], dict):
                        items.update(flatten_document(value[0], f"{new_key}.0", sep=sep))
                    else:
                        items[new_key] = value[0]  # Use first element for type
                else:
                    items[new_key] = value
            return items
        
        def get_mongodb_type(value: Any) -> str:
            """Determine MongoDB-appropriate data type"""
            if value is None:
                return "null"
            elif isinstance(value, ObjectId):
                return "objectId"
            elif isinstance(value, bool):
                return "boolean"
            elif isinstance(value, int):
                return "int32"
            elif isinstance(value, float):
                return "double"
            elif isinstance(value, str):
                return "string"
            elif isinstance(value, datetime):
                return "date"
            elif isinstance(value, list):
                return "array"
            elif isinstance(value, dict):
                return "object"
            else:
                return "string"  # fallback
        
        # Collect all fields and their types
        field_types = {}
        field_samples = {}
        
        for doc in documents:
            flattened = flatten_document(doc)
            for field_name, value in flattened.items():
                if field_name not in field_types:
                    field_types[field_name] = set()
                    field_samples[field_name] = []
                
                mongo_type = get_mongodb_type(value)
                field_types[field_name].add(mongo_type)
                
                # Keep sample values for analysis
                if len(field_samples[field_name]) < 5:
                    field_samples[field_name].append(value)
        
        # Get index information
        indexed_fields = set()
        try:
            indexes = collection.list_indexes()
            for index in indexes:
                index_keys = index.get("key", {})
                for field_name in index_keys.keys():
                    indexed_fields.add(field_name)
                    # Also add nested field paths
                    if "." in field_name:
                        parts = field_name.split(".")
                        for i in range(len(parts)):
                            partial_path = ".".join(parts[:i+1])
                            indexed_fields.add(partial_path)
                            
        except Exception as e:
            logger.warning(f"Could not fetch index information for {db_name}.{coll_name}: {e}")
        
        # Create column definitions
        columns = []
        for field_name in sorted(field_types.keys()):
            types = field_types[field_name]
            
            # Determine primary type (most specific wins)
            if "objectId" in types:
                primary_type = "objectId"
            elif "date" in types:
                primary_type = "date"
            elif "boolean" in types:
                primary_type = "boolean"
            elif "double" in types:
                primary_type = "double"
            elif "int32" in types:
                primary_type = "int32"
            elif "array" in types:
                primary_type = "array"
            elif "object" in types:
                primary_type = "object"
            else:
                primary_type = "string"
            
            # Check if field is indexed
            is_indexed = field_name in indexed_fields
            
            # Check if field is primary key
            is_primary = (field_name == "_id")
            
            # Calculate field statistics
            non_null_count = sum(1 for sample in field_samples[field_name] if sample is not None)
            null_percentage = (len(field_samples[field_name]) - non_null_count) / len(field_samples[field_name]) * 100 if field_samples[field_name] else 0
            
            column = TableColumn(
                column_name=field_name,
                data_type=primary_type,
                index=is_indexed,
                primary_key=is_primary
            )
            columns.append(column)
        
        logger.info(f"Schema inference completed: {len(columns)} columns identified")
        
        # Log column summary
        type_summary = {}
        for col in columns:
            col_type = col.data_type
            if col_type not in type_summary:
                type_summary[col_type] = 0
            type_summary[col_type] += 1
        
        logger.info(f"Column type distribution: {type_summary}")
        
        return columns

    def close_connection(self) -> None:
        """Close MongoDB connection safely"""
        if self.connection:
            try:
                self.connection.close()
                logger.info("MongoDB connection closed successfully")
            except Exception as e:
                logger.warning(f"Error closing MongoDB connection: {e}")
            finally:
                self.connection = None

    def get_scan_statistics(self) -> Dict[str, Any]:
        """Get current scan statistics"""
        return self.scan_stats.copy()

    async def validate_credentials(self) -> Dict[str, Any]:
        """Validate credentials and return connection information"""
        try:
            await self.get_service_details()
            await self.connect()
            
            # Get server information
            server_info = self.connection.server_info()
            admin_db = self.connection.admin
            
            # Test basic operations
            db_names = self.connection.list_database_names()
            
            validation_result = {
                "valid": True,
                "mongodb_version": server_info.get("version", "Unknown"),
                "available_databases": len(db_names),
                "database_names": db_names[:10],  # Limit for readability
                "connection_info": {
                    "host": self.host,
                    "port": self.port,
                    "auth_type": self.auth_type,
                    "use_srv": self.use_srv,
                    "tls": self.tls
                },
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info("Credentials validation successful")
            return validation_result
            
        except Exception as e:
            logger.error(f"Credentials validation failed: {e}")
            return {
                "valid": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        finally:
            self.close_connection()