import json
from dataclasses import asdict
from typing import List
from datetime import datetime
import pandas as pd
import os
import sys

sys.path.append(os.getcwd())
from src.utils.logger import get_ingestion_logger
from src.ingestion.base_source import UnstructureSource, StructuredSource
from src.ingestion.profile_client import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DSRP<PERSON>filer
from src.utils.exceptions import ServerException
from src.common.config import (
    PROFILE_MAX_SIZE_THRESHOLD,
    TEMP_DATA_DIR,
    WAREHOUSE_BASE_URL,
    PROFILE_PENDING,
)
from src.utils.helpers import (
    remove_file,
    create_file,
    create_folder,
    send_request,
    save_to_scv,
    save_to_json_file,
)
from src.ingestion.base_workflow import IngestionFlow
from src.ingestion.service_config import ServiceConfigFactory, ServiceConfig
from src.ingestion.rule_manager import (
    UntructureIngestionRuleManager,
    UnstructureDSRRuleManager,
    StructureIngestionRuleManager,
    StructureDSRRuleManager,
)
from src.modules.repos import DatabaseManager
from src.common.constants import StatusEnum

logger = get_ingestion_logger()


# ################ DSR Workflows ################################

"""
#### for structured
search_data = [
    {
        "service_name": "postgres",
        "db_name": "client",
        "table_schema": "public",
        "table_name": "users",
        "column_name": "email",
        "values": ["<EMAIL>"]
    }
]

"""


class StructDSRFlow:
    def __init__(
        self, dsr_id: str, service_type: str, service_name: str, search_data: list
    ):
        self.dsr_id = dsr_id
        self.service_type = service_type
        self.service_name = service_name
        self.search_data = search_data
        self.service_config: ServiceConfig = ServiceConfigFactory.get_service_config(
            service_type
        )
        self.search_results = []
        self.local_data_dir = f"{TEMP_DATA_DIR}/dsr/{self.dsr_id}/{self.service_type}"

    async def get_source(self):
        source_class = self.service_config.source_class
        source: StructuredSource = source_class(self.service_name)
        source.local_data_dir = f"{TEMP_DATA_DIR}/dsr/{self.dsr_id}/{self.service_type}"
        return source

    async def execute_internal(self) -> None:
        """Workflow-specific logic to execute safely"""
        create_folder(self.local_data_dir)

        self.source: StructuredSource = await self.get_source()
        # source.rule_manager = StructureDSRRuleManager()
        # profiler = DSRProfiler()

        # self.steps = await self.create_ingestion_pipeline()
        initial_tables = self.search_data

        logger.info(f"initial_tables to perform search: {initial_tables}")
        results = []
        async for result in self.source.user_scan(initial_tables):
            results.extend(result)

        for result in results:
            db_name = result.get("db_name")
            table_schema = result.get("table_schema")
            table_name = result.get("table_name")
            matching_rows = result.get("matching_rows")
            filepath = (
                f"{self.local_data_dir}/{db_name}_{table_schema}_{table_name}.csv"
            ).lower()
            save_to_scv(matching_rows, filepath)
            logger.info(
                f"Search data found in table {db_name}.{table_schema}.{table_name}"
            )

        self.search_results = results
        search_results_file = f"{self.local_data_dir}/search_results_file.json"
        save_to_json_file(results, search_results_file)
        logger.info(f"Search data save to file: {search_results_file}")

    async def execute(self) -> None:
        """
        Main entrypoint
        """
        try:
            await self.execute_internal()
        except Exception as err:
            logger.error(f"Ingestion failed for the service {self.service_name}, {err}")


"""
#### For unstructred
search_data =  [
        {
            "service_provider": "AWS",
            "sub_service": "S3",
            "service_name": "s3_test",
            "service_type": "aws_s3",
            "file_key": "s3://dd-testing-bucket/unstructured_data.txt",
            "file_uri": "s3://dd-testing-bucket/unstructured_data.txt",
            "file_type": "txt",
        }
    ]
"""


class UnstructDSRFlow:
    def __init__(
        self, dsr_id: str, service_type: str, service_name: str, search_data: list
    ):
        self.dsr_id = dsr_id
        self.service_type = service_type
        self.service_name = service_name
        self.search_data = search_data
        self.service_config: ServiceConfig = ServiceConfigFactory.get_service_config(
            service_type
        )
        self.search_results = []
        self.local_data_dir = f"{TEMP_DATA_DIR}/dsr/{self.dsr_id}/{self.service_type}"

    async def get_source(self):
        source_class = self.service_config.source_class
        source: UnstructureSource = source_class(self.service_name)
        source.local_data_dir = f"{TEMP_DATA_DIR}/dsr/{self.dsr_id}/{self.service_type}"
        return source

    async def execute_internal(self) -> None:
        """Workflow-specific logic to execute safely"""
        create_folder(self.local_data_dir)
        self.source: UnstructureSource = await self.get_source()
        # profiler = DSRProfiler()

        allowed_files = [file.get("file_uri") for file in self.search_data]

        self.source.rule_manager = UnstructureDSRRuleManager(allowed_files)

        # self.steps = await self.create_ingestion_pipeline()

        # Add logic to handle infra scan

        results = []
        async for batch in self.source.user_scan(self.search_data):
            results.extend(batch)

        # Persist results for downstream zip/upload handled by the orchestrator
        if not results:
            logger.warning(f"No DSR files found for {self.service_name}")
            self.search_results = []
            return

        # async for record in self.source.deep_scan():
        #     local_filepath = record.local_filepath
        #     print(local_filepath)

            # Add DSR logic here

    async def execute(self) -> None:
        """
        Main entrypoint
        """
        try:
            await self.execute_internal()
        except Exception as err:
            logger.error(f"Ingestion failed for the service {self.service_name}, {err}")


"""
Pending:
1. Improve log addition
2. Implement file skip logic
3. Profile webhook changes are required.
4. Add Force ingestion logic
5. Add extented scan logic
6. Add DSR launch script
"""
