from abc import ABC, abstractmethod
from src.modules.repos import DatabaseManager
from src.utils.logger import get_ingestion_logger

logger = get_ingestion_logger()


class RuleManager(ABC):
    def __init__(self):
        self.db = DatabaseManager()


class StructreRuleManager(RuleManager):
    def __init__(self, allowed_tables: list = []):
        super().__init__()
        self.allowed_tables = set(allowed_tables)

    @abstractmethod
    def skip_table(self, table_uri: str) -> bool:
        pass


class UnstructreRuleManager(RuleManager):
    def __init__(self, allowed_files: list = []):
        super().__init__()
        self.allowed_files = set(allowed_files)

    @abstractmethod
    def skip_file(self, file_uri: str) -> bool:
        """
        Skip file if:
        1. Already processed (all statuses are FINISHED)
        2. Not relevant
        3. File format not supported
        4. More than expected size
        """
        pass


class StructureIngestionRuleManager(StructreRuleManager):
    def __init__(self, allowed_tables: list = []):
        super().__init__()
        self.allowed_tables = set(allowed_tables)

    def skip_table(self, table_uri: str) -> bool:
        """
        Skip table if:
        1. Already processed (all statuses are FINISHED)
        2. Not relevant
        3. Table format not supported
        4. More than expected size
        """
        try:
            # Check if table is already processed
            if self.db.skip_table(table_uri):
                logger.info(f"Skipping table {table_uri} - Already processed")
                return True
            return False
        except Exception as e:
            logger.error(f"Error in skip_table for {table_uri}: {str(e)}")
            return False


class UntructureIngestionRuleManager(UnstructreRuleManager):
    def skip_file(self, file_uri: str) -> bool:
        """
        Skip file if:
        1. Already processed (all statuses are FINISHED)
        2. Not relevant
        3. File format not supported
        4. More than expected size
        """
        try:
            # Check if file is already processed
            if self.db.skip_file(file_uri):
                logger.info(f"Skipping file {file_uri} - Already processed")
                return True
            return False
        except Exception as e:
            logger.error(f"Error in skip_file for {file_uri}: {str(e)}")
            return False


class StructureDSRRuleManager(StructreRuleManager):
    def __init__(self, allowed_tables: list = []):
        super().__init__()
        self.allowed_tables = set(allowed_tables)

    def skip_table(self, table_uri: str) -> bool:
        """
        Skip table if:
        1. Already processed (all statuses are FINISHED)
        2. Not relevant
        3. Table format not supported
        4. More than expected size
        """
        try:
            # Check if table is already processed
            if self.db.skip_table(table_uri):
                logger.info(f"Skipping table {table_uri} - Already processed")
                return True
            return False
        except Exception as e:
            logger.error(f"Error in skip_table for {table_uri}: {str(e)}")
            return False


class UnstructureDSRRuleManager(UnstructreRuleManager):
    def __init__(self, allowed_files: list = []):
        super().__init__()
        self.allowed_files = set(allowed_files)

    def skip_file(self, file_uri: str) -> bool:
        """
        Skip file if:
        1. Already processed (all statuses are FINISHED)
        2. Not relevant
        3. File format not supported
        4. More than expected size
        """
        try:
            # Check if file is in the allowed list
            if file_uri not in self.allowed_files:
                logger.info(f"Skipping file {file_uri} - Not required")
                return True
            return False
        except Exception as e:
            logger.error(f"Error in skip_file for {file_uri}: {str(e)}")
            return False
