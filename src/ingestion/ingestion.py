from datetime import datetime, timezone
from src.utils.logger import get_ingestion_logger
from src.modules.repos import DatabaseManager
from src.common.constants import IngestionJobStatus, ServiceTypes
from src.utils.exceptions import CustomBaseException
from src.utils.exceptions import ServerException
from src.ingestion.unified_workflow import UnstructInges<PERSON><PERSON>low, StructIngestionFlow
from src.ingestion.scheduler import schedule_ingestion_task
from src.common.config import INGESTION_RESTART_DURATION_IN_HOUR

logger = get_ingestion_logger()


workflow_class_map = {
    ServiceTypes.MS_365_ONEDRIVE: UnstructIngestionFlow,
    ServiceTypes.MS_365_OUTLOOK: UnstructIngestionFlow,
    ServiceTypes.AWS_S3: UnstructIngestionFlow,
    ServiceTypes.MS_365_TEAMS: UnstructIngestionFlow,
    ServiceTypes.MS_365_SHAREPOINT: UnstructIngestionFlow,
    ServiceTypes.AZURE_DATA_LAKE: UnstructIngestion<PERSON>low,
    ServiceTypes.MYSQL: StructIngestion<PERSON>low,
    ServiceTypes.MSSQL: StructIngestionFlow,
    ServiceTypes.SQLITE: StructIngestionFlow,
    ServiceTypes.ORACLE: StructIngestionFlow,
    ServiceTypes.POSTGRES: StructIngestionFlow,
    ServiceTypes.DIRECTORY_SCAN: UnstructIngestionFlow,
    ServiceTypes.GOOGLE_DRIVE: UnstructIngestionFlow,
    ServiceTypes.GOOGLE_GMAIL: UnstructIngestionFlow,
    ServiceTypes.SFTP: UnstructIngestionFlow,
    ServiceTypes.SNOWFLAKE: StructIngestionFlow,
    ServiceTypes.SALESFORCE: StructIngestionFlow,
    ServiceTypes.NAS: UnstructIngestionFlow,
    ServiceTypes.ORACLE11G: StructIngestionFlow,
    ServiceTypes.MONGODB.value: StructIngestionFlow,
    ServiceTypes.TCS_CHROMA.value: StructIngestionFlow,
    ServiceTypes.ONEX.value: StructIngestionFlow,
    ServiceTypes.HAPPAY.value: UnstructIngestionFlow,
    ServiceTypes.SAP_ERP.value: StructIngestionFlow,
    ServiceTypes.HIKVISION_FR.value: StructIngestionFlow
}


async def perform_ingestion(ingestion_id: str, job_id: str):
    db = DatabaseManager()
    await db.update_ingestion_job(
        job_id=job_id, job_data={"status": IngestionJobStatus.Running}
    )
    status = IngestionJobStatus.Completed
    errors = ""
    try:
        ingestion = await db.get_ingestion_by_id(ingestion_id)
        service_name = ingestion.get("service_name", "")
        service = await db.get_service_by_service_name(service_name)
        service_type = service.get("service_type", "")
        workflow_class = workflow_class_map.get(service_type)
        if not workflow_class:
            logger.error(f"Workflow for service type {service_type} not implemented")

        if workflow_class is None:
            raise ServerException(
                f"Workflow for service type {service_type} not implemented"
            )

        logger.info(
            f"Started Ingestion for service {service_name}, ingestion: {ingestion_id}"
        )

        unstruct_ingest = workflow_class(service_type, service_name, ingestion_id, job_id)
        await unstruct_ingest.execute()
    except CustomBaseException as e:
        status = IngestionJobStatus.Failed
        errors = e.message

    except Exception as e:
        logger.info(f"Ingestion failed. {e}")
        status = IngestionJobStatus.Failed
        errors = f"Ingestion failed. {e}"

    await db.update_ingestion_job(
        job_id=job_id,
        job_data={
            "status": status,
            "errors": errors,
            "end_at": datetime.now(timezone.utc),
        },
    )
    if status == IngestionJobStatus.Failed:
        await schedule_ingestion_task(ingestion_id, INGESTION_RESTART_DURATION_IN_HOUR)

    logger.info(
        f"Finished Ingestion for service {service_name}, ingestion: {ingestion_id}"
    )


"""
1. source -> ms-onedirve
2. connect source
3. extract the data(scan of data)
4. send pii extraction
5. send data warehouse
"""