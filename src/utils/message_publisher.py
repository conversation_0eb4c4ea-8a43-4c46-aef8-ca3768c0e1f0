from typing import Optional
import json
from uuid import uuid4
from datetime import datetime
from src.utils.rabbitmq_client import RabbitMQ
from src.common.config import AUDIT_LOG_QUEUE
from src.common.constants import SourceName

rabbitmq = RabbitMQ()

"""
Message:
{
    "id": str(uuid.uuid4()),
    "type": "CREATE_USER",
    "timestamp": datetime.utcnow(),
    "source": "Ingestion",
    "correlation_id": request_id,
    "payload": {
        "name": "Alice",
        "email": "<EMAIL>"
    }
}
"""


def publish_message(
    correlation_id: str,
    queue_name: str,
    message_type: str,
    payload: dict,
    reply_to: Optional[str] = None,
):

    message = json.dumps(
        {
            "id": str(uuid4()),
            "message_type": message_type,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "source": "Profiler",
            "correlation_id": correlation_id,
            "payload": payload,
        }
    )

    rabbitmq.publish(
        queue_name, message, reply_to=reply_to, correlation_id=correlation_id
    )


def publish_audit_message(correlation_id, status: str, msg: str = ""):
    publish_message(
        correlation_id=correlation_id,
        queue_name=AUDIT_LOG_QUEUE,
        message_type="Ingestion",
        payload={
            "source": SourceName.INGESTION.value,
            "status": status,
            "error": msg,
        },
    )
