import pika
from typing import Optional
from typing import Callable
import time
import threading
from pika.adapters.blocking_connection import BlockingChannel
from pika import spec
from pika.exceptions import AMQPConnectionError
from src.utils.logger import get_util_logger
from src.utils.exceptions import ServerException
from src.common.config import (
    RABBITMQ_HOST,
    RABBITMQ_PASSWORD,
    RABBITMQ_PORT,
    RABBITMQ_USER,
)

logger = get_util_logger()


class RabbitMQ:
    def __init__(self):
        self.user = RABBITMQ_USER
        self.password = RABBITMQ_PASSWORD
        self.host = RABBITMQ_HOST
        self.port = RABBITMQ_PORT
        self.connection = None
        self.channel = None
        self.declared_queues = set()  # Keep track of queues already declared
        self.connect()

    def connect(self):
        credentials = pika.PlainCredentials(self.user, self.password)
        parameters = pika.ConnectionParameters(
            host=self.host,
            port=self.port,
            credentials=credentials,
            heartbeat=120,
            blocked_connection_timeout=300,
        )
        self.connection = pika.BlockingConnection(parameters)
        self.channel = self.connection.channel()

    def heartbeat_keeper(self):
        """Thread that keeps the connection alive by processing events."""
        while True:
            try:
                self.connection.process_data_events()  # sends heartbeats
                time.sleep(10)  # interval (tune as needed)
            except Exception as e:
                logger.info("Heartbeat thread stopped:", e)
                break

    def close(self):
        if self.connection and not self.connection.is_closed:
            self.connection.close()

    def consume(
        self,
        queue_name: str,
        callback: Optional[
            Callable[
                [BlockingChannel, spec.Basic.Deliver, spec.BasicProperties, bytes], None
            ]
        ],
    ):
        # Start heartbeat thread
        threading.Thread(target=self.heartbeat_keeper, daemon=True).start()

        if not self.channel:
            raise ServerException("Connection is not established.")
        if queue_name not in self.declared_queues:
            self.channel.queue_declare(queue=queue_name, durable=True)
            self.declared_queues.add(queue_name)

        self.channel.basic_qos(prefetch_count=1)
        self.channel.basic_consume(
            queue=queue_name, on_message_callback=callback, auto_ack=False
        )
        self.channel.start_consuming()

    def publish(
        self,
        queue_name: str,
        message: str,
        reply_to: Optional[str] = None,
        correlation_id: Optional[str] = None,
        retries: int = 3,
    ):
        for attempt in range(retries):
            try:
                if self.connection.is_closed:
                    self.connect()
                if self.channel.is_closed:
                    self.channel = self.connection.channel()

                # Declare queue only if not already declared
                if queue_name not in self.declared_queues:
                    self.channel.queue_declare(queue=queue_name, durable=True)
                    self.declared_queues.add(queue_name)

                self.channel.basic_publish(
                    exchange="",
                    routing_key=queue_name,
                    body=message,
                    properties=pika.BasicProperties(
                        delivery_mode=2,
                        reply_to=reply_to,
                        correlation_id=correlation_id,
                    ),
                    mandatory=True,
                )
                break  # Exit loop if publish succeeds

            except AMQPConnectionError as e:
                logger.error("Connection failed, retrying in 5s...", exc_info=e)
                time.sleep(5)
            except Exception as e:
                raise ServerException(
                    f"Failed to publish message on queue: {queue_name}", excep=e
                )
        else:
            raise ServerException(
                f"Failed to publish message on queue: {queue_name} after {retries} retries"
            )

    def ack_message(self, ch: BlockingChannel, delivery_tag: int):
        """Acknowledge a message."""
        ch.basic_ack(delivery_tag=delivery_tag)
