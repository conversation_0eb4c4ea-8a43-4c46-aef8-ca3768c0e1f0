from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, scoped_session, Session
from sqlalchemy.ext.declarative import declarative_base, declared_attr
from contextlib import contextmanager
from typing import Generator
from sqlalchemy.exc import OperationalError, IntegrityError

from src.utils.exceptions import ServerException, FatalException

from src.common.config import (
    DB_PASSWORD,
    DB_HOST,
    DB_NAME,
    DB_PORT,
    DB_USER,
    DB_MAX_OVERFLOW,
    DB_POOL_SIZE,
    DB_SCHEMA,
)
from src.utils.logger import get_db_logger

# Base = declarative_base()

logger = get_db_logger()



class CustomBase:
    """Ensure all models use the correct schema automatically"""
    @declared_attr
    def __table_args__(cls):
        return {"schema": DB_SCHEMA}

Base = declarative_base(cls=CustomBase)

class Database:
    """
    Singleton Database Connection Manager.
    Handles engine creation, session management, and transactions.
    """

    _instance = None

    def __new__(cls):
        """Implementing Singleton Pattern"""
        if cls._instance is None:
            cls._instance = super(Database, cls).__new__(cls)
            cls._instance.init_db()
        return cls._instance

    def init_db(self):
        """Initialize the database connection"""
        DATABASE_URL = (
            f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
        )
        try:
            self.engine = create_engine(
                DATABASE_URL,
                pool_size=DB_POOL_SIZE,
                max_overflow=DB_MAX_OVERFLOW,
                pool_timeout=30,
                pool_recycle=1800,   # Recycle idle connections every 30 min
                pool_pre_ping=True,
            )
            # with self.engine.connect() as conn:
            #     conn.execute(text(f"CREATE SCHEMA IF NOT EXISTS {DB_SCHEMA}"))
            #     conn.commit()
                
            self.autocommit_engine = create_engine(
                DATABASE_URL,
                pool_size=DB_POOL_SIZE,
                max_overflow=DB_MAX_OVERFLOW,
                pool_timeout=30,
                pool_recycle=1800,
                pool_pre_ping=True,
                isolation_level="AUTOCOMMIT",
            )

            self.SessionLocal = scoped_session(
                sessionmaker(
                    bind=self.engine,
                    autoflush=False,
                    autocommit=False,
                )
            )
        except Exception as exc:
            logger.critical("Database creation failed.", exc_info=exc)
            raise FatalException("Database creation failed.", excep=exc)

    def get_session(self) -> Session:
        """Returns a new database session"""
        return self.SessionLocal()

    @contextmanager
    def session_scope(self) -> Generator[Session, None, None]:
        """Provides a transactional scope around a series of operations"""
        session = self.get_session()
        try:
            yield session
            session.commit()
        except (OperationalError, IntegrityError) as exc:
            session.rollback()
            logger.info(f"SQLAlchemy Error Rolled Back: {exc.orig}")
            raise ServerException(f"SQLAlchemy Error: {exc.orig}", excep=exc)
        except Exception as exc:
            session.rollback()
            logger.info(f"Database Transaction Rolled Back: {exc}")
            raise ServerException("Database Error", excep=exc)
        finally:
            session.close()

    def create_tables(self):
        """Creates database tables (should be called once)"""
        try:
            Base.metadata.create_all(self.engine)
        except Exception as exc:
            logger.critical("Failed to created tables.", exc_info=exc)
