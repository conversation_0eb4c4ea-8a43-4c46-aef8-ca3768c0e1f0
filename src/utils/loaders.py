from src.utils.exceptions import ServerException
from typing import List
from dataclasses import asdict
from src.ingestion.data_class import AssetsDetails,AccessControls
from uuid import uuid4
from src.modules.repos import DatabaseManager
from src.utils.logger import get_ingestion_logger


logger = get_ingestion_logger()




def load_asset_details(assets: List[AssetsDetails]) -> None:
    try:
        logger.info("Calling load_asset_details")
        db = DatabaseManager()
        records = []

        for asset in assets:
            logger.info("Checking existing records")
            asset_name, service_provider, asset_type = (
                asset.asset_name, asset.service_provider, asset.type
            )
            logger.info(f"asset_name: {asset_name}, service_provider: {service_provider}, asset_type: {asset_type}")
            
            existing = db.asset_exists(asset_name, service_provider, asset_type)
            
            if not existing:
                asset_data = asdict(asset)
                if "access_category" not in asset_data:
                    logger.warning(f"[BUG ❌] access_category is missing for {asset.asset_name}")
                else:
                    logger.info(f"[DEBUG ✅] {asset.asset_name} → access_category = {asset_data['access_category']}")
                    asset_data["asset_id"] = str(uuid4())
                    asset_data["custom_name"] = asset_data["asset_name"] 
                    logger.info("Append the Data to be inserted")
                    records.append(asset_data)

        logger.info("Got the assets to be inserted")
        if records:
            logger.info("Inserting the data")
            db.insert_asset_details(records)
            logger.info(f"Inserted {len(records)} assets into assets_details")
    except Exception as e:
        raise ServerException("Error while loading asset details", excep=e)


def load_access_controls(access_list: List[AccessControls]) -> None:
    try:
        logger.info("Calling load_access_controls")
        db = DatabaseManager()
        records = []

        for access in access_list:
            logger.info("Checking existing access record")
            asset_name = access["asset_name"]
            user_or_role = access["user_or_role"]
            role = access["role"]
            access_type = access["access"]

            existing = db.access_control_exists(
                asset_name=asset_name,
                user_or_role=user_or_role,
                role=role,
                access=access_type
            )

            if not existing:
                logger.info(f"Adding new access record for asset: {asset_name}")
                if isinstance(access, dict):
                    access_data = dict(access)
                else:
                    access_data = asdict(access) if not hasattr(access, "as_dict") else access.as_dict()

                access_data["id"] = str(uuid4())  
                records.append(access_data)

        if records:
            logger.info("Inserting new access control records")
            db.insert_access_controls(records)
            logger.info(f"Inserted {len(records)} records into access_controls")

    except Exception as e:
        raise ServerException("Error while loading access controls", excep=e)
