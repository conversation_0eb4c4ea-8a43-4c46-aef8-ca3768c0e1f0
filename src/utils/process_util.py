import os
import errno
import signal
import subprocess
import sys

from src.utils.exceptions import ServerException
from src.utils.logger import get_task_logger

logger = get_task_logger()


def is_process_running(pid: int) -> bool:
    """
    Check whether the process with given PID is running.
    Works on Linux/Unix and Windows (with limitations).
    """
    try:
        # Signal 0 does not kill the process,
        # but raises OSError if the process does not exist
        os.kill(pid, 0)
    except OSError as e:
        if e.errno == errno.ESRCH:
            # ESRCH == No such process
            return False
        elif e.errno == errno.EPERM:
            # EPERM == Process exists but no permission to signal it
            return True
        else:
            return False
    else:
        return True


def kill_process(pid: int) -> bool:
    """
    Kill a process by PID.
    Returns True if killed successfully, False otherwise.
    """
    try:
        os.kill(pid, signal.SIGTERM)  # send termination signal
        return True
    except ProcessLookupError:
        # No such process
        logger.info(f"Process {pid} not found.")
        return False
    except PermissionError as e:
        # Not enough permissions
        logger.error(f"No permission to kill process {pid}.", exc_info=e)
        return False
    except Exception as e:
        logger.error(f"Error killing process {pid}: {e}", exc_info=e)
        return False


def launch_detached_process(python_file) -> int:
    """Launch a Python script in a detached mode."""

    if not os.path.exists(python_file):
        raise ServerException(f"Workflow file not found. file: {python_file}")
    try:
        process = subprocess.Popen(
            [sys.executable, python_file],
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
            stdin=subprocess.DEVNULL,
            start_new_session=True,
        )

        logger.info(f"Started {python_file} in detached mode with PID: {process.pid}")
        return process.pid
    except Exception as e:
        raise ServerException("failed to launch process in detached mode", excep=e)
