from typing import Optional
from urllib.parse import quote

from minio import Minio
from src.common.config import (
    MINIO_ACCESS_KEY,
    MINIO_BUCKET_NAME,
    MINIO_ENDPOINT,
    MINIO_SECRET_KEY,
    MINIO_SECURE,
    MINIO_STORAGE_LIMIT_BYTES,
)
from src.utils.exceptions import ServerException
from src.utils.logger import get_util_logger
from datetime import timed<PERSON>ta
from dotenv import load_dotenv
import os
from src.utils.helpers import get_disk_space

load_dotenv()  # will load .env file automatically

MINIO_PUBLIC_ENDPOINT = os.getenv("MINIO_PUBLIC_ENDPOINT")
logger = get_util_logger()


class MinioClient:
    def __init__(self, bucket: Optional[str] = None):
        self.access_key = MINIO_ACCESS_KEY
        self.secret_key = MINIO_SECRET_KEY
        self.secure = MINIO_SECURE
        self.endpoint = MINIO_ENDPOINT
        self.public_endpoint = MINIO_PUBLIC_ENDPOINT
        if bucket is None:
            bucket = MINIO_BUCKET_NAME

        self.bucket = bucket
        self.connect()

    def connect(self, bucket: Optional[str] = None):
        if bucket is None:
            bucket = self.bucket
        try:
            minio_client = Minio(
                self.endpoint,
                access_key=self.access_key,
                secret_key=self.secret_key,
                secure=self.secure,
            )
            self.client = minio_client
            if not minio_client.bucket_exists(MINIO_BUCKET_NAME):
                minio_client.make_bucket(MINIO_BUCKET_NAME)
                logger.info(f"Created MinIO bucket: {MINIO_BUCKET_NAME}")
            return minio_client
        except Exception as e:
            logger.error(f"Failed to initialize MinIO client: {str(e)}")
            raise ServerException(
                f"Failed to initialize MinIO storage: {str(e)}", excep=e
            )

    def close(self):
        """Close connection"""

    def upload_file(self, localfilepath: str, miniofilepath: str):
        """implement file upload logic"""
        try:
            self.client.fput_object(self.bucket, miniofilepath, localfilepath)
            logger.info(f"Successfully uploaded to MinIO: {miniofilepath}")
        except Exception as e:
            raise ServerException(f"Failed to Upload to MinIO: {str(e)}", excep=e)

    def download_file(self, localfilepath: str, miniofilepath: str):
        """implement file upload logic"""
        try:
            self.client.fget_object(self.bucket, miniofilepath, localfilepath)
            logger.info(f"Successfully Downloaded from MinIO: {miniofilepath}")
        except Exception as e:
            raise ServerException(f"Failed to download from MinIO: {str(e)}", excep=e)

    def remove_file(self, miniofilepath: str):
        """implement file upload logic"""
        try:
            self.client.remove_object(self.bucket, miniofilepath)
            logger.info(f"Successfully Removed from MinIO: {miniofilepath}")
        except Exception as e:
            raise ServerException(f"Failed to remove from MinIO: {str(e)}", excep=e)

    def get_download_url(self, miniofilepath: str):
        """constructs a download url for minio object"""
        try:
            encoded_path = quote(miniofilepath)
            url = f"{self.public_endpoint}/api/v1/buckets/{self.bucket}/objects/download?prefix={encoded_path}&version_id=null"
            return url
        except Exception as e:
            raise ServerException(
                f"Failed to construct download url for MinIO: {str(e)}", excep=e
            )

    def get_presigned_url(self, miniofilepath: str):
        """get presigned url for minio object"""
        try:
            # presigned URL for GET requests, expires in 2 days.
            url = self.client.presigned_get_object(
                self.bucket, miniofilepath, expires=timedelta(days=2)
            )
            return url.replace(self.endpoint, self.public_endpoint)
        except Exception as e:
            raise ServerException(
                f"Failed to get presigned url from MinIO: {str(e)}", excep=e
            )


    def get_log_download_url(self, minio_filepath: str) -> str:
        """Constructs a download URL for MinIO log object"""
        try:
            encoded_path = quote(minio_filepath)
            url = f"{self.public_endpoint}/api/v1/buckets/{self.bucket}/objects/download?prefix={encoded_path}&version_id=null"
            return url
        except Exception as e:
            raise ServerException(
                f"Failed to construct log download url for MinIO: {str(e)}", excep=e
            )

    def get_minio_space(self):
        # MinIO doesn’t provide direct "free space" via S3 API.
        # Instead, you can approximate from bucket usage vs. server disk.
        # Here we calculate total used size across all buckets.
        total_used = 0
        for bucket in self.client.list_buckets():
            objects = self.client.list_objects(bucket.name, recursive=True)
            for obj in objects:
                total_used += obj.size

        # You’ll still need host free space for exact available space
        disk_stats = get_disk_space("/")  # host disk where MinIO stores data
        free_space = disk_stats["free"]
        return {
            "total_used": total_used,
            "free": free_space,
            "total_disk": disk_stats["total"],
        }

    def get_minio_free_space(self):
        space = self.get_minio_space()
        total_used = space.get("total_used", 0)
        free_space = MINIO_STORAGE_LIMIT_BYTES - total_used
        if free_space <= 0:
            return 0

        return free_space
