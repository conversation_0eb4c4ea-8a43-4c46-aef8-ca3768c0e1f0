import logging
from logging.handlers import TimedRotatingFileHandler
import os
from typing import Optional
from src.common.config import (
    LOG_LEVEL,
    LOG_FILENAME,
    LOG_DEFAULT_NAME,
    LOG_DIRNAME,
)
import sys
from datetime import datetime
from zoneinfo import ZoneInfo

log_levels = {
    "ERROR": logging.ERROR,
    "WARNING": logging.WARNING,
    "INFO_4": logging.INFO,
    "DEBUG": logging.DEBUG,
}


IST_TZ = ZoneInfo("Asia/Kolkata")


def _ist_time_converter(seconds: float):
    """Return struct_time in IST for logging formatter."""
    return datetime.fromtimestamp(seconds, IST_TZ).timetuple()


def get_logger(name: Optional[str] = None):
    """Get Logger object and normalize it to use IST timestamps.

    If a logger already exists, update its handlers to use our formatter
    and enforce propagate/level settings.
    """
    if name is None:
        name = LOG_DEFAULT_NAME

    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )
    # Force timestamps to IST regardless of server timezone
    formatter.converter = _ist_time_converter

    logger = logging.getLogger(name)

    log_level = log_levels.get(LOG_LEVEL.upper(), "INFO")
    logger.setLevel(log_level)
    logger.propagate = False

    if logger.hasHandlers():
        # Normalize existing handlers to our formatter
        for h in logger.handlers:
            h.setLevel(logging.DEBUG)
            h.setFormatter(formatter)
        return logger

    # No handlers: attach one based on env
    if LOG_DIRNAME and LOG_FILENAME:
        if not os.path.exists(LOG_DIRNAME):
            os.makedirs(LOG_DIRNAME)

        log_file = os.path.join(LOG_DIRNAME, LOG_FILENAME)
        file_handler = TimedRotatingFileHandler(
            log_file, when="midnight", interval=1, backupCount=7
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    else:
        stdout_handler = logging.StreamHandler(sys.stdout)
        stdout_handler.setLevel(logging.DEBUG)
        stdout_handler.setFormatter(formatter)
        logger.addHandler(stdout_handler)

    return logger


def get_middleware_logger():
    return get_logger("Middleware")


def get_util_logger():
    return get_logger("Utils")


def get_ingestion_logger():
    return get_logger("Ingestion")


def get_dsr_logger():
    return get_logger("DSR")


def get_warehouse_logger():
    return get_logger("Warehouse")


def get_scheduler_logger():
    return get_logger("Scheduler")


def get_unstructur_logger():
    return get_logger("Unstructure")


def get_structur_logger():
    return get_logger("Structure")


def get_db_logger():
    return get_logger("Database")


def get_exception_logger():
    return get_logger("Exception")


def get_task_logger():
    return get_logger("Task Master")
