from typing import Optional, Generator, Tuple
import os
import json
import pdfplumber
import requests
import pandas as pd
import datetime
import uuid
import decimal
from fastapi.responses import JSONResponse
from fastapi.exceptions import HTTPException
from src.common.constants import SUCCESS, FAILED, HTTP_BAD_REQUEST
from src.utils.exceptions import ServerException
from src.utils.logger import get_util_logger
from typing import Literal, List
import zipfile
from typing import Any, Mapping
from zoneinfo import ZoneInfo
import shutil
from datetime import datetime, date, time

IST_TZ = ZoneInfo("Asia/Kolkata")


logger = get_util_logger()


def generate_http_success_response(
    status_code: int = 200,
    message: Optional[str] = None,
    data: list | dict = {},
):
    try:
        return JSONResponse(
            status_code=status_code,
            content={
                "status": SUCCESS,
                "status_code": status_code,
                "message": message,
                "data": serialize_datetimes_ist(data),
            },
        )
    except Exception as e:
        logger.error(f"Error while prepring json response. {e}", exc_info=e)
        HTTPException(
            status_code=500, detail=f"Error while prepring json response. {e}"
        )


def generate_http_error_response(
    status_code: int = 400,
    message: Optional[str] = None,
    data: list | dict = {},
):
    if message is None:
        message = HTTP_BAD_REQUEST

    try:
        return JSONResponse(
            status_code=status_code,
            content={
                "status": FAILED,
                "status_code": status_code,
                "message": message,
                "data": serialize_datetimes_ist(data),
            },
        )
    except Exception as e:
        logger.error(f"Error while prepring json response. {e}", exc_info=e)
        HTTPException(
            status_code=500, detail=f"Error while prepring json response. {e}"
        )


def file_exists(filepath: str) -> bool:
    if os.path.exists(filepath):
        return True

    return False

def create_file(filename: str, data: str = ""):
    try:
        if os.path.exists(filename):
            return None

        with open(filename, "w") as f:
            f.write(data)
    except Exception as e:
        raise ServerException(f"Failed to create file: {filename}", excep=e)


def save_to_scv(data: List[dict], filepath: str) -> None:
    """Save a list of dictionaries to a CSV file."""
    try:
        df = pd.DataFrame(data)
        df.to_csv(filepath, index=False, encoding="utf-8")
    except Exception as e:
        raise ServerException(f"Failed to save data to csv file: {filepath}", excep=e)


def save_to_json_file(data, filepath: str) -> None:
    """Save Python data to JSON file with support for datetime, date, time, Decimal, UUID."""

    def custom_serializer(obj):
        if isinstance(obj, (datetime, date)):
            return obj.isoformat()
        elif isinstance(obj, time):
            return obj.strftime("%H:%M:%S")
        elif isinstance(obj, decimal.Decimal):
            return float(obj)
        elif isinstance(obj, uuid.UUID):
            return str(obj)
        raise TypeError(f"Type {type(obj)} not serializable")

    try:
        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(data, f, indent=2, default=custom_serializer, ensure_ascii=False)
    except Exception as e:
        raise ServerException(f"Failed to save data to json file: {filepath}", excep=e)


def to_ist(dt):
    """Convert UTC datetime to IST formatted string."""
    if dt is None:
        return None
    if dt.tzinfo is None:
        return dt.replace(tzinfo=ZoneInfo("UTC")).astimezone(IST_TZ)
    return dt.astimezone(IST_TZ)


def _parse_iso_dt(value: str) -> datetime | None:
    try:
        if isinstance(value, str) and value.endswith("Z"):
            return datetime.fromisoformat(value.replace("Z", "+00:00"))
        return datetime.fromisoformat(value)
    except Exception:
        return None


def serialize_datetimes_ist(data: Any) -> Any:
    if isinstance(data, datetime):
        return to_ist(data).isoformat()
    if isinstance(data, str):
        parsed = _parse_iso_dt(data)
        if parsed is not None:
            return to_ist(parsed).isoformat()
        return data
    if isinstance(data, list):
        return [serialize_datetimes_ist(i) for i in data]
    if isinstance(data, tuple):
        return tuple(serialize_datetimes_ist(i) for i in data)
    if isinstance(data, Mapping):
        return {k: serialize_datetimes_ist(v) for k, v in data.items()}
    return data


def format_dates(obj: dict) -> dict:
    for field in ["created_at", "updated_at", "start_at", "end_at"]:
        if field in obj and obj[field]:
            obj[field] = to_ist(obj[field])
    return obj


def remove_file(filename: str):
    try:
        if not os.path.exists(filename):
            return None

        os.remove(filename)
    except Exception as e:
        raise ServerException(f"Failed to remove file: {filename}", excep=e)


def create_folder(folder: str):
    try:
        if os.path.exists(folder):
            return None

        os.makedirs(folder)
    except Exception as e:
        raise ServerException(f"Failed to create folder: {folder}", excep=e)


def list_files(folder: str, hidden_files: bool = False):
    try:
        files = os.listdir(folder)
        if not hidden_files:
            files = [file for file in files if not file.startswith(".")]

        return files
    except Exception as e:
        raise ServerException(f"Failed to list files: {folder}", excep=e)


def rename_file(old_filename: str, new_filename: str):
    try:
        if not os.path.exists(old_filename):
            return None

        os.rename(old_filename, new_filename)
    except Exception as e:
        raise ServerException(f"Failed to rename file: {old_filename}", excep=e)


def read_txt_file(filepath: str, chunk_size: int = 1024) -> Generator[str, None, None]:
    if not file_exists(filepath):
        raise ServerException(f"File read failed. Error: File {filepath} not found")

    try:
        with open(filepath, "rb") as f:
            while chunk := f.read(chunk_size):
                yield chunk.decode()
    except Exception as e:
        raise ServerException(f"File read failed. Error: {e}", excep=e)


def read_file_last_n_lines(filepath: str, no_of_lines: int = 100) -> list:
    if not file_exists(filepath):
        raise ServerException(f"File read failed. Error: File {filepath} not found")

    try:
        with open(filepath, "r") as f:
            lines = f.readlines()
            return lines[-no_of_lines:]
    except Exception as e:
        raise ServerException(f"File read failed. Error: {e}", excep=e)


def read_json_file(filepath: str) -> dict:
    if not file_exists(filepath):
        raise ServerException(f"File read failed. Error: File {filepath} not found")

    try:
        data = {}
        with open(filepath, "r") as f:
            data = json.load(f)
        return data
    except Exception as e:
        raise ServerException(f"File read failed. Error: {e}", excep=e)


def read_pdf_file(filepath: str) -> Generator[str, None, None]:
    if not file_exists(filepath):
        raise ServerException(f"File read failed. Error: File {filepath} not found")

    try:
        with pdfplumber.open(filepath) as pdf:
            for page in pdf.pages:
                yield page.extract_text()
    except Exception as e:
        raise ServerException(f"File read failed. Error: {e}", excep=e)


def send_request(
    url: str,
    method: str,
    headers: dict = {},
    params: dict = {},
    data: dict = {},
) -> Tuple[int, dict, dict]:
    try:
        if not headers:
            headers = {"Content-Type": "application/json"}

        response = requests.request(
            method=method,
            url=url,
            data=json.dumps(data),
            headers=headers,
            params=params,
            timeout=10,
        )

        return response.status_code, response.headers, response.json()
    except Exception as e:
        raise ServerException("Failed to send request", excep=e)


def get_file_extension(filepath: str) -> str:
    _, file_extension = os.path.splitext(filepath)
    file_extension = file_extension[1:]
    return file_extension if file_extension else "unknown"


async def get_data_category(
    service_type: str,
) -> Literal["structured", "unstructured"]:
    structured_services = {"mysql", "postgres", "oracle", "mssql","snowflake","salesforce","oracle11g"}
    return "structured" if service_type in structured_services else "unstructured"


def zip_folder(folder_path: str, output_zip: str):
    try:
        if not os.path.exists(folder_path) or not any(os.scandir(folder_path)):

            return None

        os.makedirs(os.path.dirname(output_zip), exist_ok=True)

        with zipfile.ZipFile(output_zip, "w", zipfile.ZIP_DEFLATED) as zipf:
            for root, _, files in os.walk(folder_path):
                for file in files:
                    abs_path = os.path.join(root, file)
                    rel_path = os.path.relpath(abs_path, start=folder_path)
                    zipf.write(abs_path, rel_path)

        return output_zip
    except Exception as e:
        raise ServerException("Zip file creation failed.", excep=e)


def get_disk_space(path="/"):
    total, used, free = shutil.disk_usage(path)
    return {"total": total, "used": used, "free": free}
