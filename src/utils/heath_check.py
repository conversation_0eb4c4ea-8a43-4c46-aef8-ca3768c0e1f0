import httpx
from sqlalchemy import text
from src.utils.database import Database
from src.common.config import MINIO_ENDPOINT

from typing import Any, Dict


async def check_minio_health() -> Dict[str, Any]:
    try:
        if not MINIO_ENDPOINT:
            raise ValueError("MINIO_ENDPOINT not configured")

        clean_endpoint = MINIO_ENDPOINT.replace("http://", "").replace("https://", "")
        minio_health_url = f"http://{clean_endpoint}/minio/health/live"

        async with httpx.AsyncClient(timeout=5.0) as client:
            response = await client.get(minio_health_url)

        if response.status_code == 200:
            return {"minio": True, "minio_error": ""}
        else:
            return {
                "minio": False,
                "minio_error": f"Status code {response.status_code}: {response.text}",
            }
    except Exception as e:
        return {"minio": False, "minio_error": str(e)}


async def check_db_health() -> Dict[str, Any]:
    try:
        db = Database()
        with db.engine.connect() as connection:
            connection.execute(text("SELECT 1"))
        return {"postgres": True, "postgres_error": ""}
    except Exception as e:
        return {"postgres": False, "postgres_error": str(e)}
