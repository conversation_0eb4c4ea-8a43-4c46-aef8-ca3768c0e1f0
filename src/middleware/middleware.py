import time
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
from src.utils.helpers import generate_http_error_response
from src.utils.logger import get_middleware_logger

logger = get_middleware_logger()

class CustomCORSMiddleware(BaseHTTPMiddleware):
    def __init__(
        self,
        app,
        allow_origins: list,
        allow_methods: list,
        allow_headers: list,
        allow_credentials: bool = True,
    ):
        super().__init__(app)
        self.allow_origins = allow_origins
        self.allow_methods = allow_methods
        self.allow_headers = allow_headers
        self.allow_credentials = allow_credentials

    async def dispatch(self, request: Request, call_next):
        try:
            if request.method == "OPTIONS":
                response = Response(status_code=204)
                self._set_cors_headers(response)
                return response

            response = await call_next(request)
            self._set_cors_headers(response)
            return response

        except HTTPException as exc:
            logger.error(f"HTTPException: {exc.detail}", exc_info=exc)
            return J<PERSON>NResponse(
                content={"detail": exc.detail}, status_code=exc.status_code
            )

        except Exception as exc:
            logger.error("Unhandled Exception in CustomCORSMiddleware", exc_info=exc)
            return generate_http_error_response(
                status_code=500, message=f"Internal Server Error. {exc}"
            )

    def _set_cors_headers(self, response: Response):
        if self.allow_credentials:
            response.headers["Access-Control-Allow-Credentials"] = "true"

        if "*" in self.allow_origins and self.allow_credentials:
            logger.warning(
                "Using '*' for allow_origins with allow_credentials=True is not safe. Please specify exact origins."
            )
            response.headers["Access-Control-Allow-Origin"] = self.allow_origins[0]
        else:
            response.headers["Access-Control-Allow-Origin"] = (
                ", ".join(self.allow_origins) if self.allow_origins != ["*"] else "*"
            )

        response.headers["Access-Control-Allow-Methods"] = ", ".join(
            self.allow_methods
        )
        response.headers["Access-Control-Allow-Headers"] = ", ".join(
            self.allow_headers
        )

class GatekeeperMiddleware(BaseHTTPMiddleware):
    def __init__(self, app):
        super().__init__(app)

    async def dispatch(self, request: Request, call_next):
        try:
            if request.method == "OPTIONS":
                response = Response(status_code=204)
                # Important: Set CORS headers even for OPTIONS
                response.headers["Access-Control-Allow-Origin"] = "*"
                response.headers["Access-Control-Allow-Methods"] = "*"
                response.headers["Access-Control-Allow-Headers"] = "*"
                response.headers["Access-Control-Allow-Credentials"] = "false"
                return response

            start_time = time.time()

            response = await call_next(request)
            duration = time.time() - start_time
            response.headers["response-time"] = f"{duration:.4f}s"
            return response

        except HTTPException as exc:
            logger.error(f"HTTPException: {exc.detail}", exc_info=exc)
            return JSONResponse(
                content={"detail": exc.detail}, status_code=exc.status_code
            )

        except Exception as exc:
            logger.error("Unhandled Exception in GatekeeperMiddleware", exc_info=exc)
            return generate_http_error_response(
                status_code=500, message=f"Internal Server Error. {exc}"
            )
