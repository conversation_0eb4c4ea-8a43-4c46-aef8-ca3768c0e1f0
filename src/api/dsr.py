from fastapi import APIRouter
from src.common.config import WAREHOUSE_BASE_URL
from src.utils.logger import get_ingestion_logger
from src.schemas.ingestion_schemas import IngestionRequest
from src.schemas.dsr_schemas import DSRRequest
from src.modules.repos import DatabaseManager
from src.ingestion.scheduler import schedule_ingestion_task
from uuid import uuid4
import json
import os
from typing import List, Dict, Any

logger = get_ingestion_logger()

router = APIRouter()


async def get_unique_services(warehouse_response: Dict[str, Any]) -> List[str]:
    """Extract unique service names from warehouse response"""
    if not isinstance(warehouse_response, dict):
        return []
        
    data = warehouse_response.get("data", [])
    if not isinstance(data, list):
        return []
        
    return list(
        set(
            item.get("service_name") 
            for item in data 
            if isinstance(item, dict) and item.get("service_name")
        )
    )


async def create_ingestion_for_service(service_name: str) -> Dict[str, Any]:
    """Create ingestion for a service"""
    try:
        # Create ingestion request with default values
        ingestion_request = IngestionRequest(
            service_name=service_name,
            ingestion_type="string",
            schedule="string",  # Default schedule
            profile_scan=False
        )
        
        # Create ingestion directly using DatabaseManager
        db = DatabaseManager()
        data = ingestion_request.model_dump()
        data["ingestion_id"] = str(uuid4())
        data = await db.create_ingestion(data)
        
        return data
    except Exception as e:
        logger.error(
            f"Failed to create ingestion for service {service_name}: {str(e)}"
        )
        raise


async def create_job_for_ingestion(ingestion_id: str) -> Dict[str, Any]:
    """Create job for an ingestion"""
    try:
        # Create job directly using schedule_ingestion_task
        data = await schedule_ingestion_task(ingestion_id)
        return data
    except Exception as e:
        logger.error(
            f"Failed to create job for ingestion {ingestion_id}: {str(e)}"
        )
        raise


async def process_dsr_request(request: DSRRequest, request_id: str) -> None:
    try:
        temp_file = f".dsr/{request_id}/warehouse_response.json"
        if not os.path.exists(temp_file):
            logger.error(f"Warehouse response file not found for request {request_id}")
            return
        with open(temp_file, "r") as f:
            warehouse_response = json.load(f)    
        unique_services = await get_unique_services(warehouse_response)
        logger.info(f"Processing {len(unique_services)} services for request {request_id}")
        for service_name in unique_services:
            try:
                ingestion = await create_ingestion_for_service(service_name)
                job = await create_job_for_ingestion(ingestion["ingestion_id"])
                
                logger.info(
                    f"Created ingestion and job for service {service_name}: "
                    f"ingestion_id={ingestion['ingestion_id']}, "
                    f"job_id={job['job_id']}"
                )
                warehouse_response["processed_services"].append(service_name)
                with open(temp_file, "w") as f:
                    json.dump(warehouse_response, f)
                    
            except Exception as e:
                logger.error(
                    f"Failed to process service {service_name}: {str(e)}"
                )
                continue
                
        logger.info(f"Completed processing all services for request {request_id}")
                
    except Exception as e:
        logger.error(f"Error processing DSR request: {str(e)}")