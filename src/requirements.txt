aioboto3==14.1.0
aiobotocore==2.21.1
aiofiles==24.1.0
aiohappyeyeballs==2.5.0
aiohttp==3.11.13
aioitertools==0.12.0
aiomysql==0.2.0
aiosignal==1.3.2
alembic==1.15.2
annotated-types==0.7.0
anyio==4.8.0
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
asgiref==3.8.1
async-timeout==5.0.1
asyncpg==0.30.0
attrs==25.1.0
azure-core==1.32.0
azure-identity==1.21.0
azure-storage-blob==12.25.0
azure-storage-file-datalake==12.19.0
black==25.1.0
boto3==1.37.1
botocore==1.37.1
cachetools==5.5.2
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
cryptography>=3.1.0,<43.0.0
Django==5.2
exceptiongroup==1.2.2
fastapi==0.115.11
frozenlist==1.5.0
google-api-core==2.24.2
google-api-python-client==2.166.0
google-auth==2.38.0
google-auth-httplib2==0.2.0
googleapis-common-protos==1.69.2
greenlet==3.1.1
h11>=0.16.0
httpcore>=1.0.9
httplib2==0.22.0
httptools==0.6.4
httpx>=0.28.1
idna==3.10
isodate==0.7.2
jmespath==1.0.1
Mako==1.3.9
MarkupSafe==3.0.2
minio==7.2.15
msal==1.31.1
msal-extensions==1.3.1
multidict==6.1.0
mypy-extensions==1.0.0
mysql-connector-python==9.2.0
packaging==24.2
pathspec==0.12.1
pdfminer.six==20231228
pdfplumber==0.11.5
pillow==11.1.0
platformdirs>=2.6.0,<5.0.0
propcache==0.3.0
proto-plus==1.26.1
protobuf==6.30.2
psycopg2-binary==2.9.10
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycountry==24.6.1
pycparser==2.22
pycryptodome==3.21.0
pydantic==2.10.6
pydantic_core==2.27.2
PyJWT==2.10.1
PyMySQL==1.1.1
pyparsing==3.2.3
pypdfium2==4.30.1
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
PyYAML==6.0.2
requests==2.32.3
rsa==4.9
s3transfer==0.11.3
six==1.17.0
sniffio==1.3.1
SQLAlchemy==2.0.38
sqlparse==0.5.3
starlette==0.46.0
tomli==2.2.1
typing_extensions==4.12.2
uritemplate==4.1.1
urllib3==2.3.0
uvicorn==0.34.0
uvloop==0.21.0
watchfiles==1.0.4
websockets==15.0
wrapt==1.17.2
yarl==1.18.3
asyncssh==2.14.2
oracledb==2.0.1
Cython==3.1.1
pymssql==2.3.4
setuptools==80.9.0
wheel==0.45.1
snowflake-connector-python>=3.0.0,<4.0.0
pyOpenSSL>=22.0.0,<26.0.0
pandas>=2.0.0,<3.0.0
numpy>=1.24.0
keyring>=23.1.0,<26.0.0
simple-salesforce>=1.12.4
pysmb>=1.2.9
smbprotocol==1.15.0
tenacity>=8.2.0
retrying>=1.3.4
cx_Oracle>=8.3.0
pydantic-settings>=2.0.0
pika==1.3.2
pymongo>=3.12,<4.0
pymongo[srv]==3.12.1
pyodata==1.11.2
hikvisionapi==0.3.2
