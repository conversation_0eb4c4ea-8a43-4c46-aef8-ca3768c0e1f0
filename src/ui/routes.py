from fastapi import APIRouter, HTTPException
from uuid import uuid4
from src.utils.logger import get_unstructur_logger
from src.utils.exceptions import CustomBaseException
from src.utils.helpers import (
    generate_http_success_response,
    generate_http_error_response,
)
from src.modules.repos import DatabaseManager
from fastapi import BackgroundTasks
from src.common.constants import ServiceTypes
from typing import Optional, List, Dict
from fastapi.params import Depends
from src.schemas.ingestion_schemas import PaginationQueryParams, ProfileStatusRequest, GrafanaServiceAccountCreate
from src.schemas.dsr_schemas import DSRRequest
from src.modules.warehouse_utils import process_profile_status
from src.utils.helpers import send_request
from src.common.config import WAREHOUSE_BASE_URL
from src.utils.helpers import serialize_datetimes_ist
from dotenv import load_dotenv
import os
logger = get_unstructur_logger()
load_dotenv()

dd_router = APIRouter()


@dd_router.get("/services", tags=["dd"])
async def get_services_route(
    query: PaginationQueryParams = Depends()
):
    try:
        db = DatabaseManager()
        result = await db.get_all_services_details(
            page=query.page,
            page_size=query.page_size,
            search=query.search
        )

        return generate_http_success_response(
            200,
            "Services fetched successfully",
            serialize_datetimes_ist(result)
        )
    except CustomBaseException as exc:
        return generate_http_error_response(
            status_code=exc.code,
            message=exc.message
        )
    except Exception as exc:
        logger.error(f"Unexpected error in get_services_route: {exc}")
        return generate_http_error_response(
            status_code=500, message=f"Failed to fetch all services: {exc}"
        )


@dd_router.get("/ingestion/{service_name}", tags=["dd"])
async def get_service_ingestions_route(
    service_name: str,
    query: PaginationQueryParams = Depends()
):
    try:
        db = DatabaseManager()
        result = await db.get_service_ingestions_with_category(
            service_name=service_name,
            page=query.page,
            page_size=query.page_size,
            search=query.search
        )
        return generate_http_success_response(
            200,
            "Ingestions fetched successfully",
            serialize_datetimes_ist(result)
        )
    except CustomBaseException as exc:
        return generate_http_error_response(
            status_code=exc.code,
            message=exc.message
        )
    except Exception as exc:
        logger.error(f"Unexpected error in get_service_ingestions_route: {exc}")
        return generate_http_error_response(
            status_code=500,
            message=(
                f"Failed to fetch ingestions for service {service_name}: {exc}"
            ),
        )


@dd_router.get("/ingestion/{ingestion_id}/jobs", tags=["dd"])
async def get_jobs_by_ingestion_id(
    ingestion_id: str,
    query: PaginationQueryParams = Depends()
):
    try:
        db = DatabaseManager()
        result = await db.get_jobs_by_ingestion_id(
            ingestion_id=ingestion_id,
            page=query.page,
            page_size=query.page_size,
            search=query.search
        )
        return generate_http_success_response(
            200,
            f"Jobs for ingestion '{ingestion_id}' fetched successfully",
            serialize_datetimes_ist(result)
        )
    except CustomBaseException as exc:
        return generate_http_error_response(
            status_code=exc.code,
            message=exc.message
        )
    except Exception as exc:
        logger.error(
            f"Error in get_jobs_by_ingestion_id: {exc}"
        )
        return generate_http_error_response(
            status_code=500,
            message=(
                f"Failed to fetch jobs for ingestion_id={ingestion_id}: "
                f"{exc}"
            ),
        )


@dd_router.post("/profile_status", tags=["dd"])
async def update_profile_status(
    request: ProfileStatusRequest
):
    """
    Update profile status and process PII information
    """
    try:
        result = await process_profile_status(
            token=request.token,
            status=request.status,
            piis=request.piis,
            status_code=request.status_code,
            message=request.message
        )
        if result["status"] == "FAILED":
            return generate_http_error_response(
                status_code=result.get("status_code", 400),
                message=result.get(
                    "message", "Failed to process profile status"
                )
            )
        return generate_http_success_response(
            status_code=result.get("status_code", 200),
            message=result.get("message", "Profile status updated successfully"),
            data=result
        )
    except CustomBaseException as exc:
        return generate_http_error_response(
            status_code=exc.code,
            message=exc.message
        )
    except Exception as exc:
        logger.error(f"Unexpected error in update_profile_status: {exc}")
        return generate_http_error_response(
            status_code=500,
            message=f"Internal server error: {str(exc)}"
        )


@dd_router.post("/dsr/search", tags=["dsr"])
async def search_dsr_request(request: DSRRequest):
    try:
        db = DatabaseManager()
        payload = {
            "piis": [
                {"pii_type": item.pii_type, "value": item.value} 
                for item in request.piis
            ]
        }
        dev_warehouse_url = "https://dev.gotrust.tech/warehouse-1/warehouse"
        url = f"{dev_warehouse_url}/pii_source_location_value"        
        status_code, headers, warehouse_response = send_request(
            url,
            method="POST",
            data=payload
        )
        if status_code != 200:
            return generate_http_error_response(
                status_code=status_code,
                message=(
                    f"Warehouse API error: "
                    f"{warehouse_response.get('message', 'Unknown error')}"
                )
            )            
        if not warehouse_response.get("data"):
            return generate_http_error_response(
                status_code=404,
                message="No matching data found in warehouse"
            )
        dsr_data = request.model_dump()
        dsr_data["warehouse_data"] = warehouse_response.get("data", [])
        dsr_record = await db.create_dsr_request(dsr_data)
        request_id = dsr_record["request_id"]
        return generate_http_success_response(
            200,
            "Warehouse data fetched and DSR request saved successfully",
            {
                "request_id": request_id,
                "warehouse_data": warehouse_response.get("data", []),
                "piis": payload["piis"]
            }
        )
    except CustomBaseException as exc:
        return generate_http_error_response(
            status_code=exc.code,
            message=exc.message
        )
    except Exception as exc:
        logger.error(f"Unexpected error in search_dsr_request: {exc}")
        return generate_http_error_response(
            status_code=500,
            message=f"Failed to fetch data from warehouse: {exc}"
        )


@dd_router.get("/dsr/{request_id}", tags=["dsr"])
async def get_dsr_request(request_id: str):
    try:
        db = DatabaseManager()
        dsr_record = await db.get_dsr_request(request_id)        
        if not dsr_record or not dsr_record.get("warehouse_data"):
            return generate_http_error_response(
                status_code=404,
                message=f"No warehouse data found for request ID {request_id}"
            )
        return generate_http_success_response(
            200,
            "Warehouse data fetched successfully",
            dsr_record["warehouse_data"]
        )
    except CustomBaseException as exc:
        return generate_http_error_response(
            status_code=exc.code,
            message=exc.message
        )
    except Exception as exc:
        logger.error(f"Unexpected error in get_dsr_request: {exc}")
        return generate_http_error_response(
            status_code=500,
            message=f"Failed to fetch warehouse data: {exc}"
        )


@dd_router.post("/", tags=["grafana"])
async def create_grafana_account(
    payload: GrafanaServiceAccountCreate,
    background_tasks: BackgroundTasks
):
    try:
        db = DatabaseManager()
        grafana_url = os.getenv("GRAFANA_URL")
        # grafana_url = "http://localhost:3006"

        account_data = {
            "name": payload.name,
            "token": payload.token,
            "org_id": payload.org_id,
            "grafana_url": grafana_url
        }

        result = await db.create_grafana_account(account_data)

        if result["exists"]:
            return generate_http_success_response(
                200,
                "Token already exists",
                result["account"]
            )

        # Trigger background fetch dashboards
        account_id = result["account"]["id"]
        background_tasks.add_task(db.fetch_and_update_grafana_dashboards, account_id)

        return generate_http_success_response(
            201,
            "Grafana account created and dashboard fetch scheduled",
            result["account"]
        )

    except Exception as exc:
        return generate_http_error_response(500, f"Failed to create account: {exc}")

@dd_router.get("/", tags=["grafana"])
async def list_grafana_accounts():
    try:
        db = DatabaseManager()
        accounts = await db.get_grafana_accounts()
        if not accounts:
            return generate_http_error_response(
                status_code=404, message="No Grafana accounts found"
            )
        return generate_http_success_response(
            200, "Grafana accounts fetched successfully", accounts
        )
    except CustomBaseException as exc:
        return generate_http_error_response(
            status_code=exc.code, message=exc.message
        )
    except Exception as exc:
        logger.error(f"Unexpected error in list_grafana_accounts: {exc}")
        return generate_http_error_response(
            status_code=500, message=f"Failed to fetch Grafana accounts: {exc}"
        )