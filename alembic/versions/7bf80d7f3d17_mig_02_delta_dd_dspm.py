"""mig_02_delta_dd_dspm

Revision ID: 7bf80d7f3d17
Revises: e8c2cd41e247
Create Date: 2025-09-19 17:45:42.596809

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql



# revision identifiers, used by Alembic.
revision: str = '7bf80d7f3d17'
down_revision: Union[str, None] = 'e8c2cd41e247'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None
        
userroleenum = postgresql.ENUM('VIEWER', 'ADMIN', 'EDITOR','COMPLIANCE_OFFICER','SECURITY_ANALYST', name='userroleenum')

def upgrade():

    userroleenum.create(op.get_bind(), checkfirst=True)
    # -----------------------
    # 1️⃣ Create new tables
    # -----------------------

    # users → add new columns
    with op.batch_alter_table('users') as batch_op:
        batch_op.alter_column(
            "user_id",
            type_=postgresql.UUID(as_uuid=True),
            postgresql_using="user_id::uuid",
        )
        batch_op.add_column(sa.Column('full_name', sa.String(255), nullable=True))
        batch_op.add_column(sa.Column('hashed_password', sa.String(255), nullable=False))
        batch_op.add_column(sa.Column('is_active', sa.Boolean, default=True, nullable=False))
        batch_op.add_column(sa.Column('is_verified', sa.Boolean, default=False, nullable=False))
        batch_op.add_column(sa.Column('role', userroleenum, server_default='VIEWER', nullable=False))
        batch_op.add_column(sa.Column('department', sa.String(100)))
        batch_op.add_column(sa.Column('job_title', sa.String(100)))
        batch_op.add_column(sa.Column('mfa_enabled', sa.Boolean, default=False, nullable=False))
        batch_op.add_column(sa.Column('mfa_secret', sa.String(255)))
        batch_op.add_column(sa.Column('last_login', sa.DateTime(timezone=True)))
        batch_op.add_column(sa.Column('password_changed_at', sa.DateTime(timezone=True)))
        batch_op.add_column(sa.Column('failed_login_attempts', sa.Integer, default=0))
        batch_op.add_column(sa.Column('locked_until', sa.DateTime(timezone=True)))

    # user_roles
    op.create_table(
        'user_roles',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('name', sa.String(50), unique=True, nullable=False),
        sa.Column('description', sa.Text),
        sa.Column('permissions', postgresql.JSONB, nullable=False, server_default='[]'),
        sa.Column('is_system_role', sa.Boolean, default=False, nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), onupdate=sa.func.now(), nullable=False)
    )

    # user_sessions
    op.create_table(
        'user_sessions',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('users.user_id'), nullable=False),
        sa.Column('refresh_token', sa.String(512), unique=True, nullable=False, index=True),
        sa.Column('ip_address', sa.String(45)),
        sa.Column('user_agent', sa.Text),
        sa.Column('device_info', postgresql.JSONB),
        sa.Column('location', postgresql.JSONB),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('last_activity', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('is_active', sa.Boolean, default=True, nullable=False),
        sa.Column('revoked_at', sa.DateTime(timezone=True)),
        sa.Column('revoked_reason', sa.String(100))
    )

    # user_preferences
    op.create_table(
        'user_preferences',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('users.user_id'), nullable=False),
        sa.Column('theme', sa.String(20), default='light'),
        sa.Column('language', sa.String(10), default='en'),
        sa.Column('timezone', sa.String(50), default='UTC'),
        sa.Column('email_notifications', sa.Boolean, default=True),
        sa.Column('push_notifications', sa.Boolean, default=True),
        sa.Column('notification_frequency', sa.String(20), default='immediate'),
        sa.Column('dashboard_layout', postgresql.JSONB, default={}),
        sa.Column('default_filters', postgresql.JSONB, default={}),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), onupdate=sa.func.now(), nullable=False)
    )

    # -----------------------
    # 2️⃣ Alter existing tables
    # -----------------------

    # services → add new columns
    with op.batch_alter_table('services', schema='public') as batch_op:
        batch_op.add_column(sa.Column('location', sa.String(255), nullable=True))
        batch_op.add_column(sa.Column('data_owner', sa.String(255), nullable=True))
        batch_op.add_column(sa.Column('data_steward', sa.String(255), nullable=True))


    # --- file_metadata ---
    op.add_column('file_metadata', sa.Column('custom_tags', sa.ARRAY(sa.String), nullable=True, default=None))
    op.add_column('file_metadata', sa.Column('description', sa.Text, nullable=True, default=None))
    op.add_column('file_metadata', sa.Column('dept_tags', sa.ARRAY(sa.String), nullable=True, default=None))
    op.add_column('file_metadata', sa.Column('custom_name', sa.String(255), nullable=True, default=None))

    # --- table_metadata ---
    op.add_column('table_metadata', sa.Column('last_accessed', sa.String(50), nullable=True))
    op.add_column('table_metadata', sa.Column('custom_tags', sa.ARRAY(sa.String), nullable=True, default=None))
    op.add_column('table_metadata', sa.Column('description', sa.Text, nullable=True, default=None))
    op.add_column('table_metadata', sa.Column('dept_tags', sa.ARRAY(sa.String), nullable=True, default=None))
    op.add_column('table_metadata', sa.Column('custom_name', sa.String(255), nullable=True, default=None))

    # --- piis ---
    op.alter_column('piis', 'sample_values', nullable=True)  # if not already nullable

    # --- column_table ---
    op.add_column('column_table', sa.Column('business_name', sa.String(255), nullable=True, default=None))

def downgrade():
    # Drop new tables
    op.drop_table('user_preferences')
    op.drop_table('user_sessions')
    op.drop_table('user_roles')

    # Drop new columns in services
    with op.batch_alter_table('services', schema='public') as batch_op:
        batch_op.drop_column('location')
        batch_op.drop_column('data_owner')
        batch_op.drop_column('data_steward')

    # Drop added columns in users
    with op.batch_alter_table('users') as batch_op:
        batch_op.drop_column('full_name')
        batch_op.drop_column('hashed_password')
        batch_op.drop_column('is_active')
        batch_op.drop_column('is_verified')
        batch_op.drop_column('role')
        batch_op.drop_column('department')
        batch_op.drop_column('job_title')
        batch_op.drop_column('mfa_enabled')
        batch_op.drop_column('mfa_secret')
        batch_op.drop_column('last_login')
        batch_op.drop_column('password_changed_at')
        batch_op.drop_column('failed_login_attempts')
        batch_op.drop_column('locked_until')
        
           # --- file_metadata ---
    op.drop_column('file_metadata', 'custom_tags')
    op.drop_column('file_metadata', 'description')
    op.drop_column('file_metadata', 'dept_tags')
    op.drop_column('file_metadata', 'custom_name')

    # --- table_metadata ---
    op.drop_column('table_metadata', 'last_accessed')
    op.drop_column('table_metadata', 'custom_tags')
    op.drop_column('table_metadata', 'description')
    op.drop_column('table_metadata', 'dept_tags')
    op.drop_column('table_metadata', 'custom_name')

    # --- piis ---
    op.drop_column('piis', 'pii_category')
    # Optional: revert nullable change for sample_values if needed
    # op.alter_column('piis', 'sample_values', nullable=False)

    # --- column_table ---
    op.drop_column('column_table', 'business_name')
    op.drop_column('column_table', 'pii_category')

