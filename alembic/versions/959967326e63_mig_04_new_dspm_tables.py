"""mig_04_new_dspm_tables

Revision ID: 959967326e63
Revises: 279fcd2c112c
Create Date: 2025-10-07 16:27:36.263823

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision: str = '959967326e63'
down_revision: Union[str, None] = '279fcd2c112c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column("access_controls", sa.Column("user_type", sa.String(length=255), nullable=True))
    op.add_column("access_controls", sa.Column("group", sa.String(length=255), nullable=True))
    op.add_column('policies', sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True))

    op.create_table(
        "audit_logs_trail",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("asset_name", sa.String(length=255), nullable=True),
        sa.Column("action", sa.String(length=255), nullable=True),
        sa.Column("field_name", sa.String(length=255), nullable=True),
        sa.Column("old_value", sa.String(length=1024), nullable=True),
        sa.Column("new_value", sa.String(length=1024), nullable=True),
        sa.Column("performed_by", sa.String(length=255), nullable=True),
        sa.Column("timestamp", sa.DateTime(), server_default=sa.text("now()"), nullable=True),
        sa.Column("asset_type", sa.String(length=255), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )


def downgrade() -> None:
    op.drop_table("audit_logs_trail")
    op.drop_column("access_controls", "group")
    op.drop_column("access_controls", "user_type")
    op.drop_column('policies', 'deleted_at')