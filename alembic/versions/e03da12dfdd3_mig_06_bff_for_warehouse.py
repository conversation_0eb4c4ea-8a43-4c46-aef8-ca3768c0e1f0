"""mig_06_bff_for_warehouse

Revision ID: e03da12dfdd3
Revises: a63e1e56696b
Create Date: 2025-10-13 10:54:29.994529

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

from sqlalchemy.dialects.postgresql import DOUBLE_PRECISION, JSON


# revision identifiers, used by Alembic.
revision: str = 'e03da12dfdd3'
down_revision: Union[str, None] = 'a63e1e56696b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    # structured_metadata
    op.create_table(
        'structured_metadata',
        sa.Column('id', sa.Integer, primary_key=True, autoincrement=True),
        sa.Column('label', sa.String(255), nullable=True, unique=True),
        sa.Column('value', sa.String(255)),
        sa.Column('created_at', sa.DateTime, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, server_default=sa.func.now(), onupdate=sa.func.now()),
    )

    # unstructured_metadata
    op.create_table(
        'unstructured_metadata',
        sa.Column('id', sa.Integer, primary_key=True, autoincrement=True),
        sa.Column('label', sa.String(255), nullable=True, unique=True),
        sa.Column('value', sa.String(255)),
        sa.Column('created_at', sa.DateTime, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, server_default=sa.func.now(), onupdate=sa.func.now()),
    )

    # structured_listing
    op.create_table(
        'structured_listing',
        sa.Column('id', sa.Integer, primary_key=True, autoincrement=True),
        sa.Column('service_name', sa.String(255)),
        sa.Column('service_provider', sa.String(255)),
        sa.Column('db_type', sa.String(255)),
        sa.Column('table_name', sa.String(255)),
        sa.Column('column_name', sa.String(255)),
        sa.Column('table_uri', sa.String(1024)),
        sa.Column('piis', JSON),
        sa.Column('created_at', sa.DateTime, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, server_default=sa.func.now(), onupdate=sa.func.now()),
    )

    # unstructured_listing
    op.create_table(
        'unstructured_listing',
        sa.Column('file_hash', sa.String(255), primary_key=True, unique=True, index=True),
        sa.Column('data_service', sa.String(255)),
        sa.Column('sub_service', sa.String(255)),
        sa.Column('document_name', sa.String(255)),
        sa.Column('document_type', sa.String(50)),
        sa.Column('document_size_kb', DOUBLE_PRECISION, server_default='0'),
        sa.Column('file_location', sa.String(1024), index=True),
        sa.Column('piis', JSON),
        sa.Column('created_at', sa.DateTime, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, server_default=sa.func.now(), onupdate=sa.func.now()),
    )

    # structured_pii_flow
    op.create_table(
        'structured_pii_flow',
        sa.Column('id', sa.Integer, primary_key=True, autoincrement=True),
        sa.Column('location', sa.String(255)),
        sa.Column('data_system', sa.String(255)),
        sa.Column('database', sa.String(255)),
        sa.Column('data_category', sa.String(255)),
        sa.Column('pii', sa.String(255)),
        sa.Column('flow_count', sa.Integer, server_default='0'),
        sa.Column('created_at', sa.DateTime, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, server_default=sa.func.now(), onupdate=sa.func.now()),
    )

    # unstructured_pii_flow
    op.create_table(
        'unstructured_pii_flow',
        sa.Column('id', sa.Integer, primary_key=True, autoincrement=True),
        sa.Column('location', sa.String(255)),
        sa.Column('data_service', sa.String(255)),
        sa.Column('sub_service', sa.String(255)),
        sa.Column('file_format', sa.String(50)),
        sa.Column('pii_category', sa.String(255)),
        sa.Column('detected_pii', sa.String(255)),
        sa.Column('flow_count', sa.Integer, server_default='0'),
        sa.Column('created_at', sa.DateTime, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, server_default=sa.func.now(), onupdate=sa.func.now()),
    )


def downgrade():
    op.drop_table('unstructured_pii_flow')
    op.drop_table('structured_pii_flow')
    op.drop_table('unstructured_listing')
    op.drop_table('structured_listing')
    op.drop_table('unstructured_metadata')
    op.drop_table('structured_metadata')