"""mig_01_initial_migration.py


Revision ID: e8c2cd41e247
Revises: 
Create Date: 2025-09-19 17:45:05.585713

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'e8c2cd41e247'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    # users
    op.create_table(
        'users',
        sa.Column('user_id', sa.String(length=256), primary_key=True, index=True),
        sa.Column('username', sa.String(length=256), nullable=False),
        sa.Column('email', sa.String(length=256), nullable=False),
        sa.Column('phone', sa.String(length=256), nullable=False),
        sa.Column('status', sa.String(length=255), server_default='Active'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.func.now(), onupdate=sa.func.now())
    )

    # services
    op.create_table(
        'services',
        sa.Column('service_name', sa.String(length=256), primary_key=True, index=True),
        sa.Column('description', sa.String(length=255)),
        sa.Column('service_type', sa.String(length=255), nullable=False),
        sa.Column('credentials', sa.JSON(), nullable=False),
        sa.Column('status', sa.String(length=255), server_default='Active'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.func.now(), onupdate=sa.func.now()),
        schema='public'
    )

    # ingestions
    op.create_table(
        'ingestions',
        sa.Column('ingestion_id', sa.String(length=256), primary_key=True, index=True),
        sa.Column('service_name', sa.String(length=256), sa.ForeignKey('public.services.service_name'), index=True),
        sa.Column('ingestion_type', sa.String(length=256), nullable=False),
        sa.Column('status', sa.String(length=255), server_default='Active'),
        sa.Column('schedule', sa.String(length=255)),
        sa.Column('profile_scan', sa.Boolean(), default=False),
        sa.Column('start_date', sa.DateTime(), server_default=sa.func.now()),
        sa.Column('created_at', sa.DateTime(), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.func.now(), onupdate=sa.func.now()),
        schema='public'
    )

    # ingestion_jobs
    op.create_table(
        'ingestion_jobs',
        sa.Column('job_id', sa.String(length=256), primary_key=True, index=True),
        sa.Column('ingestion_id', sa.String(length=256), sa.ForeignKey('public.ingestions.ingestion_id'), index=True),
        sa.Column('job_pid', sa.Integer()),
        sa.Column('status', sa.String(length=255), server_default='Queued'),
        sa.Column('errors', sa.String(length=255)),
        sa.Column('start_at', sa.DateTime(), server_default=sa.func.now()),
        sa.Column('end_at', sa.DateTime(), server_default=sa.func.now(), onupdate=sa.func.now()),
        schema='public'
    )

    # table_audit_logs
    op.create_table(
        'table_audit_logs',
        sa.Column('correlation_id', sa.String(255), nullable=False, index=True),
        sa.Column('service_name', sa.String(255), nullable=False),
        sa.Column('service_type', sa.String(255), nullable=False),
        sa.Column('service_provider', sa.String(255), nullable=False),
        sa.Column('sub_service', sa.String(255), nullable=False),
        sa.Column('table_uri', sa.String(512), primary_key=True),
        sa.Column('db_name', sa.String(255), nullable=False),
        sa.Column('table_name', sa.String(255), nullable=False),
        sa.Column('ingestion_id', sa.String(255)),
        sa.Column('job_id', sa.String(255)),
        sa.Column('error_message', sa.Text()),
        sa.Column('ingestion_start_time', sa.DateTime()),
        sa.Column('ingestion_status', sa.Enum('INITIATED','RUNNING','FINISHED','FAILED','CANCELLED','PARTIAL', name='status_enum'), nullable=True),
        sa.Column('ingestion_end_time', sa.DateTime()),
        sa.Column('profile_start_time', sa.DateTime()),
        sa.Column('profile_status', sa.Enum('INITIATED','RUNNING','FINISHED','FAILED','CANCELLED','PARTIAL', name='status_enum')), 
        sa.Column('profile_end_time', sa.DateTime()),
        sa.Column('minio_file', sa.String(255)),
        sa.Column('warehouse_start_time', sa.DateTime()),
        sa.Column('warehouse_status', sa.Enum('INITIATED','RUNNING','FINISHED','FAILED','CANCELLED','PARTIAL', name='status_enum')), 
        sa.Column('warehouse_end_time', sa.DateTime()),
        sa.Column('metadata', sa.JSON())
    )

    # file_audit_logs
    op.create_table(
        'file_audit_logs',
        sa.Column('correlation_id', sa.String(255), nullable=False, index=True),
        sa.Column('service_name', sa.String(255), nullable=False),
        sa.Column('service_type', sa.String(255), nullable=False),
        sa.Column('service_provider', sa.String(255), nullable=False),
        sa.Column('sub_service', sa.String(255), nullable=False),
        sa.Column('file_key', sa.String(512), nullable=False),
        sa.Column('file_uri', sa.String(512), primary_key=True),
        sa.Column('file_type', sa.String(512)),
        sa.Column('ingestion_id', sa.String(255)),
        sa.Column('job_id', sa.String(255)),
        sa.Column('error_message', sa.Text()),
        sa.Column('ingestion_start_time', sa.DateTime()),
        sa.Column('ingestion_status', sa.Enum('INITIATED','RUNNING','FINISHED','FAILED','CANCELLED','PARTIAL', name='status_enum'), nullable=True),
        sa.Column('ingestion_end_time', sa.DateTime()),
        sa.Column('profile_start_time', sa.DateTime()),
        sa.Column('profile_status', sa.Enum('INITIATED','RUNNING','FINISHED','FAILED','CANCELLED','PARTIAL', name='status_enum')), 
        sa.Column('profile_end_time', sa.DateTime()),
        sa.Column('minio_file', sa.String(255)),
        sa.Column('warehouse_start_time', sa.DateTime()),
        sa.Column('warehouse_status', sa.Enum('INITIATED','RUNNING','FINISHED','FAILED','CANCELLED','PARTIAL', name='status_enum')), 
        sa.Column('warehouse_end_time', sa.DateTime()),
        sa.Column('metadata', sa.JSON())
    )

    # audit_event
    op.create_table(
        'audit_event',
        sa.Column('correlation_id', sa.String(255), nullable=False, index=True),
        sa.Column('event_id', sa.String(255), primary_key=True),
        sa.Column('event_name', sa.String(255), nullable=False),
        sa.Column('msg', sa.String(255), nullable=False),
        sa.Column('extra_metadata', sa.JSON()),
        sa.Column('timestamp', sa.DateTime(), server_default=sa.func.now())
    )

    # data_subject_request
    op.create_table(
        'data_subject_request',
        sa.Column('dsr_id', sa.String(255), primary_key=True),
        sa.Column('request_id', sa.String(255)),
        sa.Column('request_type', sa.String(255)),
        sa.Column('customer_id', sa.String(255)),
        sa.Column('search_data', sa.JSON(), nullable=False),
        sa.Column('personal_data', sa.JSON()),
        sa.Column('created_at', sa.DateTime(), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('status', sa.String(255), server_default='Queued'),
        sa.Column('error', sa.String(255)),
        sa.Column('logs', postgresql.ARRAY(sa.String())),
        sa.Column('result_file', sa.String(255)),
        sa.Column('result_url', sa.String(1024)),
        sa.Column('log_file_content', sa.Text()),
        sa.Column('start_at', sa.DateTime()),
        sa.Column('end_at', sa.DateTime()),
        sa.Column('pii_matching_tables', sa.JSON(), nullable=True)
    )
    op.create_table('image_jobs',
    sa.Column('correlation_id', sa.String(length=256), nullable=True),
    sa.Column('image_id', sa.String(length=256), nullable=False),
    sa.Column('image_text', sa.String(length=2048), nullable=True),
    sa.Column('status', sa.String(length=255), server_default='Queued', nullable=True),
    sa.Column('start_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('end_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('image_id'),
    schema='public'
    )
    with op.batch_alter_table('image_jobs', schema='public') as batch_op:
        batch_op.create_index(batch_op.f('ix_profiler_image_jobs_correlation_id'), ['correlation_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_profiler_image_jobs_image_id'), ['image_id'], unique=False)

    op.create_table('profile_jobs',
    sa.Column('correlation_id', sa.String(length=256), nullable=True),
    sa.Column('profiler_id', sa.String(length=256), nullable=False),
    sa.Column('input_type', sa.String(length=256), nullable=False),
    sa.Column('input_data', sa.String(length=256), nullable=True),
    sa.Column('input_file', sa.String(length=256), nullable=True),
    sa.Column('file_storage', sa.String(length=256), nullable=True),
    sa.Column('profile_type', sa.String(length=256), nullable=True),
    sa.Column('status', sa.String(length=255), server_default='Queued', nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('finished_at', sa.DateTime(), nullable=True),
    sa.Column('result', sa.JSON(), nullable=True),
    sa.Column('images', sa.ARRAY(sa.Text()), nullable=True),
    sa.Column('error', sa.String(length=256), nullable=True),
    sa.PrimaryKeyConstraint('profiler_id'),
    schema='public'
    )
    with op.batch_alter_table('profile_jobs', schema='public') as batch_op:
        batch_op.create_index(batch_op.f('ix_profiler_profile_jobs_correlation_id'), ['correlation_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_profiler_profile_jobs_profiler_id'), ['profiler_id'], unique=False)

        op.create_table(
        'aws_s3',
        sa.Column('service_provider', sa.Enum("M365","AWS","GCP","AZURE","GWS","LOCAL","SFTP","NAS", name='serviceproviders'), server_default='AWS'),
        sa.Column('sub_service', sa.String(255), primary_key=True, server_default='AWS_S3'),
        sa.Column('bucket', sa.String(256), primary_key=True, index=True),
        sa.Column('service_name', sa.String(255)),
        sa.Column('bucket_size', sa.Float, server_default='0'),
        sa.Column('region', sa.String(255)),
        sa.Column('no_of_objects', sa.Integer, server_default='0'),
        sa.Column('created_at', sa.DateTime, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, server_default=sa.func.now(), onupdate=sa.func.now())
    )

    op.create_table(
        'ms_onedrive',
        sa.Column('service_provider', sa.Enum("M365","AWS","GCP","AZURE","GWS","LOCAL","SFTP","NAS", name='serviceproviders'), server_default='M365'),
        sa.Column('sub_service',sa.String(255), primary_key=True, server_default='MS_ONEDRIVE'),
        sa.Column('service_name', sa.String(255)),
        sa.Column('user_email', sa.String(255), primary_key=True),
        sa.Column('storage_size', sa.Float, server_default='0'),
        sa.Column('region', sa.String(255))
    )

    op.create_table(
        'ms_outlook',
        sa.Column('service_provider', sa.Enum("M365","AWS","GCP","AZURE","GWS","LOCAL","SFTP","NAS", name='serviceproviders'), server_default='M365'),
        sa.Column('sub_service', sa.String(255), primary_key=True, server_default='MS_OUTLOOK'),
        sa.Column('service_name', sa.String(255)),
        sa.Column('user_email', sa.String(255), primary_key=True),
        sa.Column('storage_size', sa.Float, server_default='0'),
        sa.Column('region', sa.String(255))
    )

    op.create_table(
        'file_metadata',
        sa.Column('correlation_id', sa.String(256), index=True),
        sa.Column('file_hash', sa.String(255), primary_key=True),
        sa.Column('service_provider', sa.Enum("M365","AWS","GCP","AZURE","GWS","LOCAL","SFTP","NAS", name='serviceproviders')),
        sa.Column('sub_service', sa.String(255)),
        sa.Column('service_name', sa.String(255), index=True),
        sa.Column('file_key', sa.String(256), index=True),
        sa.Column('file_uri', sa.String(1024)),
        sa.Column('file_uri_hash', sa.String(255)),
        sa.Column('file_size', sa.Float, server_default='0'),
        sa.Column('file_type', sa.String(255)),
        sa.Column('details', sa.JSON),
        sa.Column('created_at', sa.DateTime, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, server_default=sa.func.now(), onupdate=sa.func.now())
    )

    op.create_table(
        'table_metadata',
        sa.Column('correlation_id', sa.String(256), index=True),
        sa.Column('table_hash', sa.String(255), primary_key=True),
        sa.Column('service_provider', sa.Enum("M365","AWS","GCP","AZURE","GWS","LOCAL","SFTP","NAS", name='serviceproviders')),
        sa.Column('sub_service', sa.String(255)),
        sa.Column('service_name', sa.String(255), index=True),
        sa.Column('db_type', sa.String(255)),
        sa.Column('db_name', sa.String(255)),
        sa.Column('table_name', sa.String(255), index=True),
        sa.Column('table_uri', sa.String(256)),
        sa.Column('table_size', sa.Float, server_default='0'),
        sa.Column('no_of_rows', sa.Integer, server_default='0'),
        sa.Column('details', sa.JSON),
        sa.Column('created_at', sa.DateTime, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, server_default=sa.func.now(), onupdate=sa.func.now())
    )

    op.create_table(
        'piis',
        sa.Column('pii_hash', sa.String(255), primary_key=True),
        sa.Column('parent_hash', sa.String(255), index=True),
        sa.Column('pii_type', sa.String(255)),
        sa.Column('value', sa.String(1024)),
        sa.Column('score', sa.Float, server_default='0'),
        sa.Column('sample_values', sa.JSON)
    )

    op.create_table(
        'column_table',
        sa.Column('column_hash', sa.String(255), primary_key=True),
        sa.Column('table_hash', sa.String(255), index=True),
        sa.Column('column_name', sa.String(255)),
        sa.Column('data_type', sa.String(255)),
        sa.Column('index', sa.Boolean, server_default=sa.text('false')),
        sa.Column('primary_key', sa.Boolean, server_default=sa.text('false')),
        sa.Column('pii_type', sa.String(255)),
        sa.Column('pii_value', sa.JSON)
    )

    op.create_table(
        'metric_definitions',
        sa.Column('id', sa.String(255), primary_key=True),
        sa.Column('metric_name', sa.String(255), nullable=False, unique=True, index=True),
        sa.Column('query', sa.Text, nullable=False),
        sa.Column('schedule_interval', sa.Integer, server_default='3600'),
        sa.Column('enabled', sa.Boolean, server_default=sa.text('true')),
        sa.Column('created_at', sa.DateTime, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, server_default=sa.func.now(), onupdate=sa.func.now())
    )

    op.create_table(
        'metric_results',
        sa.Column('id', sa.String(255), primary_key=True),
        sa.Column('metric_name', sa.String(255), index=True, nullable=False),
        sa.Column('result', sa.JSON),
        sa.Column('error', sa.String(256)),
        sa.Column('created_at', sa.DateTime, server_default=sa.func.now()),
        sa.Column('computed_at', sa.DateTime, server_default=sa.func.now(), onupdate=sa.func.now())
    )

    op.create_table(
        'pii_details',
        sa.Column('id', sa.String(255), primary_key=True, nullable=False),
        sa.Column('data_category', sa.String(255), nullable=False),
        sa.Column('data_type', sa.String(255), primary_key=True, nullable=False),
        sa.Column('data_element', sa.String(255), primary_key=True, nullable=False),
        sa.Column('description', sa.String(500)),
        sa.Column('data_format', sa.String(255)),
        sa.Column('notes', sa.String(500)),
        sa.Column('regex_pattern', sa.String(500)),
        sa.Column('sensitivity', sa.String(50)),
        sa.Column('region', sa.String(255)),
        sa.Column('subregion', sa.String(255)),
        sa.Column('country', sa.String(100)),
        sa.Column('regulation', sa.String(255)),
        sa.Column('is_active', sa.Boolean, server_default=sa.text('true'))
    )



def downgrade():
    op.drop_table('data_subject_request')
    op.drop_table('audit_event')
    op.drop_table('file_audit_logs')
    op.drop_table('table_audit_logs')
    op.drop_table('ingestion_jobs', schema='public')
    op.drop_table('ingestions', schema='public')
    op.drop_table('services', schema='public')
    op.drop_table('users')
    op.execute('DROP TYPE status_enum')
      # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('profile_jobs', schema='public') as batch_op:
        batch_op.drop_index(batch_op.f('ix_profiler_profile_jobs_profiler_id'))
        batch_op.drop_index(batch_op.f('ix_profiler_profile_jobs_correlation_id'))

    op.drop_table('profile_jobs', schema='public')
    with op.batch_alter_table('image_jobs', schema='public') as batch_op:
        batch_op.drop_index(batch_op.f('ix_profiler_image_jobs_image_id'))
        batch_op.drop_index(batch_op.f('ix_profiler_image_jobs_correlation_id'))

    op.drop_table('image_jobs', schema='public')
    op.drop_table('pii_details')
    op.drop_table('metric_results')
    op.drop_table('metric_definitions')
    op.drop_table('column_table')
    op.drop_table('piis')
    op.drop_table('table_metadata')
    op.drop_table('file_metadata')
    op.drop_table('ms_outlook')
    op.drop_table('ms_onedrive')
    op.drop_table('aws_s3')
