"""mig_05_one_column_for dspm

Revision ID: a63e1e56696b
Revises: 959967326e63
Create Date: 2025-10-08 10:48:17.711718

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a63e1e56696b'
down_revision: Union[str, None] = '959967326e63'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('access_user_permission', sa.Column('user_type', sa.String(), nullable=True))


def downgrade() -> None:
    op.drop_column('access_user_permission', 'user_type')
