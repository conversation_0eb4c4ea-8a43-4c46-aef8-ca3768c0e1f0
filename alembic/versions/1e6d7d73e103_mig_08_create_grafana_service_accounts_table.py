"""create grafana_service_accounts table

Revision ID: 1e6d7d73e103
Revises: 402235ef6995
Create Date: 2025-10-15 12:57:55.780632

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1e6d7d73e103'
down_revision: Union[str, None] = '402235ef6995'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    op.create_table(
        'grafana_service_accounts',
        sa.Column('id', sa.Integer, primary_key=True, index=True),
        sa.Column('name', sa.String, nullable=False),
        sa.Column('grafana_url', sa.String, nullable=False),
        sa.Column('token', sa.String, nullable=False),
        sa.Column('org_id', sa.Integer, nullable=True),
        sa.Column('dashboards', sa.JSON, nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True)
    )

def downgrade():
    op.drop_table('grafana_service_accounts')