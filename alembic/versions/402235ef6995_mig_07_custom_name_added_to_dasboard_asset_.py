"""custom_name_added_to_dasboard_asset_detail

Revision ID: 402235ef6995
Revises: e03da12dfdd3
Create Date: 2025-10-14 11:45:58.109634

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '402235ef6995'
down_revision: Union[str, None] = 'e03da12dfdd3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        'dashboard_asset_detail',
        sa.Column('custom_name', sa.String(length=255), nullable=True)
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('dashboard_asset_detail', 'custom_name')
    # ### end Alembic commands ###
