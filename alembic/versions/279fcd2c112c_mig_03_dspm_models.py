"""mig_03_dspm_models

Revision ID: 279fcd2c112c
Revises: 7bf80d7f3d17
Create Date: 2025-09-19 18:07:48.615675

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision: str = '279fcd2c112c'
down_revision: Union[str, None] = '7bf80d7f3d17'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('access_data_owners_stewards',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('owner_or_steward', sa.String(), nullable=False),
    sa.Column('role', sa.String(), nullable=False),
    sa.Column('asset_count', sa.Integer(), nullable=False),
    sa.Column('status', sa.String(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('access_orphan_assets',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('service_provider', sa.String(), nullable=True),
    sa.Column('type', sa.String(), nullable=True),
    sa.Column('category', sa.String(), nullable=True),
    sa.Column('location', sa.String(), nullable=True),
    sa.Column('security', sa.String(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('custom_tags', sa.JSON(), nullable=True),
    sa.Column('service_name', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('access_user_permission',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('user_or_role', sa.String(), nullable=False),
    sa.Column('role', sa.String(), nullable=False),
    sa.Column('asset_count', sa.Integer(), nullable=False),
    sa.Column('access_list', sa.ARRAY(sa.String()), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('status', sa.String(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('assets_details',
    sa.Column('asset_id', sa.String(length=255), nullable=False),
    sa.Column('asset_name', sa.String(length=255), nullable=True),
    sa.Column('service_provider', sa.String(), nullable=True),
    sa.Column('type', sa.String(length=255), nullable=True),
    sa.Column('category', sa.String(length=255), nullable=True),
    sa.Column('location', sa.String(length=255), nullable=True),
    sa.Column('owner', sa.String(length=255), nullable=True),
    sa.Column('security', sa.String(length=255), nullable=True),
    sa.Column('size', sa.String(length=255), nullable=True),
    sa.Column('count', sa.String(length=255), nullable=True),
    sa.Column('access_category', sa.String(length=255), nullable=True),
    sa.Column('last_accessed', sa.String(length=50), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('custom_tags', sa.ARRAY(sa.String()), nullable=True),
    sa.Column('service_name', sa.String(length=255), nullable=True),
    sa.Column('custodian', sa.String(length=255), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('steward', sa.String(length=255), nullable=True),
    sa.Column('linked_policy', sa.ARRAY(sa.String()), nullable=True),
    sa.Column('dept_tags', sa.ARRAY(sa.String()), nullable=True),
    sa.Column('custom_name', sa.String(length=255), nullable=True),
    sa.Column('status', sa.String(length=255), server_default='Active', nullable=True),
    sa.Column('sharing_type', sa.Enum('Public', 'Internal', 'Third Party', 'Partner', 'Vendor', 'Customer', 'Cross-Region', 'Cross-Org', 'Regulator', 'Employee', name='sharetarget'), nullable=True),
    sa.Column('sharing_method', sa.Enum('secure_share', 'direct_share', 'public_share', 'api_share', 'federated_access', 'file_export', 'link_share', 'fdw_share', 'masked_share', 'tokenized_share', 'anonymized_share', 'contractual_share', 'transit_share', 'replication_share', name='sharemethod'), nullable=True),
    sa.PrimaryKeyConstraint('asset_id')
    )
    op.create_index(op.f('ix_assets_details_asset_name'), 'assets_details', ['asset_name'], unique=False)
    op.create_index(op.f('ix_assets_details_category'), 'assets_details', ['category'], unique=False)
    op.create_index('ix_assetsdetails_asset_name_category', 'assets_details', ['asset_name', 'category'], unique=False)
    op.create_table('classification_distribution',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('label', sa.String(length=255), nullable=False),
    sa.Column('value', sa.String(length=255), nullable=False),
    sa.Column('change_label', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_classification_distribution_label'), 'classification_distribution', ['label'], unique=False)
    op.create_index('ix_classification_label_value', 'classification_distribution', ['label', 'value'], unique=False)
    op.create_table('classification_result',
    sa.Column('asset_id', sa.String(length=50), nullable=False),
    sa.Column('asset_name', sa.String(length=255), nullable=False),
    sa.Column('asset_type', sa.String(length=255), nullable=False),
    sa.Column('data_type', sa.String(length=255), nullable=False),
    sa.Column('cloud_provider', sa.String(length=255), nullable=False),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('risk_level', sa.String(length=255), nullable=False),
    sa.Column('last_scan', sa.DateTime(), nullable=False),
    sa.Column('classification_progress', sa.Integer(), nullable=False),
    sa.Column('data_types_found', sa.JSON(), nullable=True),
    sa.Column('confidence', sa.JSON(), nullable=True),
    sa.Column('total_pii', sa.Integer(), nullable=True),
    sa.Column('unstructured_details', sa.JSON(), nullable=True),
    sa.Column('structured_details', sa.JSON(), nullable=True),
    sa.Column('overview_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.PrimaryKeyConstraint('asset_id')
    )
    op.create_index(op.f('ix_classification_result_asset_name'), 'classification_result', ['asset_name'], unique=False)
    op.create_index(op.f('ix_classification_result_asset_type'), 'classification_result', ['asset_type'], unique=False)
    op.create_index('ix_classificationresult_asset_name', 'classification_result', ['asset_name'], unique=False)
    op.create_index('ix_classificationresult_asset_name_type', 'classification_result', ['asset_name', 'asset_type'], unique=False)
    op.create_index('ix_classificationresult_asset_type_lower', 'classification_result', [sa.literal_column('lower(asset_type)')], unique=False)
    op.create_index('ix_classificationresult_data_type_lower', 'classification_result', [sa.literal_column('lower(data_type)')], unique=False)
    op.create_index('ix_classificationresult_risk_level_lower', 'classification_result', [sa.literal_column('lower(risk_level)')], unique=False)
    op.create_index('ix_classificationresult_status_lower', 'classification_result', [sa.literal_column('lower(status)')], unique=False)
    op.create_table('classification_results_summary',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('level', sa.String(), nullable=False),
    sa.Column('records', sa.Integer(), nullable=False),
    sa.Column('formatted_records', sa.String(), nullable=False),
    sa.Column('percentage', sa.Integer(), nullable=False),
    sa.Column('color', sa.String(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('cloud_connections',
    sa.Column('connection_name', sa.String(length=255), nullable=False),
    sa.Column('service_provider', sa.String(), nullable=True),
    sa.Column('region', sa.String(length=255), nullable=True),
    sa.Column('credential_type', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('connection_name')
    )
    op.create_table('dashboard_asset_detail',
    sa.Column('id', sa.String(length=50), nullable=False),
    sa.Column('type', sa.String(length=100), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('location', sa.String(length=255), nullable=True),
    sa.Column('owner', sa.String(length=255), nullable=True),
    sa.Column('classification', sa.String(length=255), nullable=True),
    sa.Column('encryption', sa.String(length=255), nullable=True),
    sa.Column('size', sa.String(length=50), nullable=True),
    sa.Column('risk_score', sa.String(length=255), nullable=True),
    sa.Column('data_types', sa.String(length=255), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('service_provider', sa.String(length=255), nullable=True),
    sa.Column('access_category', sa.String(length=100), nullable=True),
    sa.Column('category', sa.String(length=100), nullable=True),
    sa.Column('status', sa.String(length=100), nullable=True),
    sa.Column('service_type', sa.String(length=100), nullable=True),
    sa.Column('provider_key', sa.String(length=100), nullable=True),
    sa.Column('data_type', sa.String(length=255), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('dspm_access_control',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('label', sa.String(length=255), nullable=False),
    sa.Column('value', sa.String(length=255), nullable=False),
    sa.Column('change_label', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('dspm_access_pattern_anomalies',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('description', sa.String(length=255), nullable=False),
    sa.Column('value', sa.String(length=255), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('dspm_alert_configuration',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.String(length=255), nullable=False),
    sa.Column('value', sa.String(length=255), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('dspm_asset_discovery',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('label', sa.String(length=255), nullable=False),
    sa.Column('value', sa.String(length=255), nullable=False),
    sa.Column('change_label', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('dspm_classification',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('label', sa.String(length=255), nullable=False),
    sa.Column('value', sa.String(length=255), nullable=False),
    sa.Column('change_label', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_dspm_classification_label'), 'dspm_classification', ['label'], unique=False)
    op.create_table('dspm_compliance',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('label', sa.String(length=255), nullable=False),
    sa.Column('value', sa.String(length=255), nullable=False),
    sa.Column('change_label', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('dspm_configuration_changes',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.String(length=255), nullable=False),
    sa.Column('value', sa.String(length=255), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('dspm_dashboard',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('label', sa.String(length=255), nullable=False),
    sa.Column('value', sa.String(length=255), nullable=False),
    sa.Column('change_label', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('dspm_data_access_policy',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('label', sa.String(length=255), nullable=False),
    sa.Column('value', sa.String(length=255), nullable=False),
    sa.Column('change_label', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('dspm_data_activity_monitoring',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('description', sa.String(length=255), nullable=False),
    sa.Column('value', sa.String(length=255), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('dspm_data_sharing_policy',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('label', sa.String(length=255), nullable=False),
    sa.Column('value', sa.String(length=255), nullable=False),
    sa.Column('change_label', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('dspm_department',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('active', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_dspm_department_name'), 'dspm_department', ['name'], unique=True)
    op.create_table('dspm_remediation',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('label', sa.String(length=255), nullable=False),
    sa.Column('value', sa.String(length=255), nullable=False),
    sa.Column('change_label', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('dspm_reporting',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('label', sa.String(length=255), nullable=False),
    sa.Column('value', sa.String(length=255), nullable=False),
    sa.Column('change_label', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )

    op.create_table('identity_mappings',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('role', sa.String(length=255), nullable=False),
    sa.Column('issue', sa.String(length=255), nullable=False),
    sa.Column('severity', sa.String(length=50), nullable=False),
    sa.Column('last_updated', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('policies',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=False),
    sa.Column('policy_type', sa.String(length=50), nullable=False),
    sa.Column('severity', sa.String(length=20), nullable=False),
    sa.Column('environment', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('asset_types', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('data_classification', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('location', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('asset_provider', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('department', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('tags', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('violation_type', sa.String(length=255), nullable=False),
    sa.Column('alert_level', sa.String(length=20), nullable=False),
    sa.Column('auto_remediation', sa.Boolean(), nullable=False),
    sa.Column('notification_channels', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('compliance_frameworks', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('created_by', sa.String(length=255), nullable=True),
    sa.Column('updated_by', sa.String(length=255), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_policy_active', 'policies', ['is_active'], unique=False)
    op.create_index('idx_policy_environment', 'policies', ['environment'], unique=False)
    op.create_index('idx_policy_type_severity', 'policies', ['policy_type', 'severity'], unique=False)
    op.create_index(op.f('ix_policies_name'), 'policies', ['name'], unique=True)
    op.create_table('policy_audit_log',
    sa.Column('id', sa.UUID(), server_default=sa.text('gen_random_uuid()'), nullable=False),
    sa.Column('event_type', sa.String(length=50), nullable=False),
    sa.Column('entity_type', sa.String(length=50), nullable=False),
    sa.Column('entity_id', sa.UUID(), nullable=False),
    sa.Column('timestamp', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('user_id', sa.String(length=255), nullable=True),
    sa.Column('user_email', sa.String(length=255), nullable=True),
    sa.Column('user_role', sa.String(length=100), nullable=True),
    sa.Column('old_values', sa.JSON(), nullable=True),
    sa.Column('new_values', sa.JSON(), nullable=True),
    sa.Column('changes', sa.JSON(), nullable=True),
    sa.Column('action_description', sa.Text(), nullable=True),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('user_agent', sa.Text(), nullable=True),
    sa.Column('session_id', sa.String(length=255), nullable=True),
    sa.Column('policy_id', sa.UUID(), nullable=True),
    sa.Column('policy_name', sa.String(length=255), nullable=True),
    sa.Column('severity', sa.String(length=20), nullable=True),
    sa.Column('compliance_frameworks', sa.JSON(), nullable=True),
    sa.Column('retention_period', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_policy_audit_log_entity_id'), 'policy_audit_log', ['entity_id'], unique=False)
    op.create_index(op.f('ix_policy_audit_log_entity_type'), 'policy_audit_log', ['entity_type'], unique=False)
    op.create_index(op.f('ix_policy_audit_log_event_type'), 'policy_audit_log', ['event_type'], unique=False)
    op.create_index(op.f('ix_policy_audit_log_policy_id'), 'policy_audit_log', ['policy_id'], unique=False)
    op.create_index(op.f('ix_policy_audit_log_timestamp'), 'policy_audit_log', ['timestamp'], unique=False)
    op.create_index(op.f('ix_policy_audit_log_user_email'), 'policy_audit_log', ['user_email'], unique=False)
    op.create_table('priority_remediation_tasks',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('title', sa.String(), nullable=False),
    sa.Column('sensitivity', sa.String(), nullable=False),
    sa.Column('risk_score', sa.Integer(), nullable=False),
    sa.Column('resource', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=False),
    sa.Column('affected_data_source', sa.String(), nullable=False),
    sa.Column('severity_color', sa.String(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('status', sa.String(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('recent_classifications',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('asset_name', sa.String(), nullable=False),
    sa.Column('classification_type', sa.String(), nullable=False),
    sa.Column('risk_level', sa.String(), nullable=False),
    sa.Column('risk_color', sa.String(), nullable=False),
    sa.Column('classified_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )

    op.create_table('tag_categories',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('rules', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('created_by', sa.UUID(), nullable=False),
    sa.Column('updated_by', sa.UUID(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tag_categories_name'), 'tag_categories', ['name'], unique=True)

    op.create_table('policy_rule_sets',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('policy_id', sa.UUID(), nullable=False),
    sa.Column('action', sa.String(length=50), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['policy_id'], ['policies.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_rule_set_action', 'policy_rule_sets', ['action'], unique=False)
    op.create_index('idx_rule_set_policy', 'policy_rule_sets', ['policy_id'], unique=False)
    op.create_table('tags',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('category_id', sa.UUID(), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('parent_tag_id', sa.UUID(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('created_by', sa.UUID(), nullable=False),
    sa.Column('updated_by', sa.UUID(), nullable=False),
    sa.ForeignKeyConstraint(['category_id'], ['tag_categories.id'], ),
    sa.ForeignKeyConstraint(['parent_tag_id'], ['tags.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_tag_category_status', 'tags', ['category_id', 'status'], unique=False)
    op.create_index('idx_tag_parent', 'tags', ['parent_tag_id'], unique=False)
    op.create_index(op.f('ix_tags_name'), 'tags', ['name'], unique=True)
  
    op.create_table('policy_rules',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('rule_set_id', sa.UUID(), nullable=False),
    sa.Column('field', sa.String(length=500), nullable=False),
    sa.Column('operator', sa.String(length=50), nullable=False),
    sa.Column('value', sa.Text(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['rule_set_id'], ['policy_rule_sets.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_rule_field', 'policy_rules', ['field'], unique=False)
    op.create_index('idx_rule_operator', 'policy_rules', ['operator'], unique=False)
    op.create_index('idx_rule_rule_set', 'policy_rules', ['rule_set_id'], unique=False)
    op.create_table('policy_violations',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('policy_id', sa.UUID(), nullable=False),
    sa.Column('asset_id', sa.UUID(), nullable=True),
    sa.Column('rule_set_id', sa.UUID(), nullable=False),
    sa.Column('violation_type', sa.String(length=255), nullable=False),
    sa.Column('alert_level', sa.String(length=20), nullable=False),
    sa.Column('description', sa.Text(), nullable=False),
    sa.Column('context', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('resolved_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('resolved_by', sa.String(length=255), nullable=True),
    sa.Column('resolution_notes', sa.Text(), nullable=True),
    sa.Column('detected_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.CheckConstraint("alert_level IN ('Critical', 'High', 'Medium', 'Low')", name='check_alert_level'),
    sa.CheckConstraint("status IN ('open', 'acknowledged', 'resolved', 'false_positive')", name='check_status'),
    sa.ForeignKeyConstraint(['policy_id'], ['policies.id'], ),
    sa.ForeignKeyConstraint(['rule_set_id'], ['policy_rule_sets.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_violation_alert_level', 'policy_violations', ['alert_level'], unique=False)
    op.create_index('idx_violation_asset', 'policy_violations', ['asset_id'], unique=False)
    op.create_index('idx_violation_detected', 'policy_violations', ['detected_at'], unique=False)
    op.create_index('idx_violation_policy_status', 'policy_violations', ['policy_id', 'status'], unique=False)
    op.create_table('tag_assignments',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('tag_id', sa.UUID(), nullable=False),
    sa.Column('table_hash', sa.String(length=255), nullable=True),
    sa.Column('file_hash', sa.String(length=255), nullable=True),
    sa.Column('assigned_by', sa.UUID(), nullable=False),
    sa.Column('timestamp', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('source', sa.String(length=50), nullable=False),
    sa.CheckConstraint('(table_hash IS NOT NULL AND file_hash IS NULL) OR (table_hash IS NULL AND file_hash IS NOT NULL)', name='chk_exactly_one_asset_type'),
    sa.ForeignKeyConstraint(['tag_id'], ['tags.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('tag_id', 'file_hash', name='uq_tag_file_assignment'),
    sa.UniqueConstraint('tag_id', 'table_hash', name='uq_tag_table_assignment')
    )
    op.create_index('idx_assignment_file', 'tag_assignments', ['file_hash'], unique=False)
    op.create_index('idx_assignment_table', 'tag_assignments', ['table_hash'], unique=False)
    op.create_index('idx_assignment_tag_source', 'tag_assignments', ['tag_id', 'source'], unique=False)
    op.create_index('idx_assignment_timestamp', 'tag_assignments', ['timestamp'], unique=False)
    op.create_index(op.f('ix_tag_assignments_file_hash'), 'tag_assignments', ['file_hash'], unique=False)
    op.create_index(op.f('ix_tag_assignments_table_hash'), 'tag_assignments', ['table_hash'], unique=False)

    op.create_table(
        'access_controls',
        sa.Column('id', sa.String(length=255), nullable=False),
        sa.Column('asset_name', sa.String(length=255), nullable=True),
        sa.Column('user_or_role', sa.String(length=255), nullable=True),
        sa.Column('role', sa.String(length=255), nullable=True),
        sa.Column('access', sa.String(length=255), nullable=True),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_tag_assignments_table_hash'), table_name='tag_assignments')
    op.drop_table('access_controls')
    op.drop_index(op.f('ix_tag_assignments_file_hash'), table_name='tag_assignments')
    op.drop_index('idx_assignment_timestamp', table_name='tag_assignments')
    op.drop_index('idx_assignment_tag_source', table_name='tag_assignments')
    op.drop_index('idx_assignment_table', table_name='tag_assignments')
    op.drop_index('idx_assignment_file', table_name='tag_assignments')
    op.drop_table('tag_assignments')
    op.drop_index('idx_violation_policy_status', table_name='policy_violations')
    op.drop_index('idx_violation_detected', table_name='policy_violations')
    op.drop_index('idx_violation_asset', table_name='policy_violations')
    op.drop_index('idx_violation_alert_level', table_name='policy_violations')
    op.drop_table('policy_violations')
    op.drop_index('idx_rule_rule_set', table_name='policy_rules')
    op.drop_index('idx_rule_operator', table_name='policy_rules')
    op.drop_index('idx_rule_field', table_name='policy_rules')
    op.drop_table('policy_rules')

    op.drop_index(op.f('ix_tags_name'), table_name='tags')
    op.drop_index('idx_tag_parent', table_name='tags')
    op.drop_index('idx_tag_category_status', table_name='tags')
    op.drop_table('tags')
    op.drop_index('idx_rule_set_policy', table_name='policy_rule_sets')
    op.drop_index('idx_rule_set_action', table_name='policy_rule_sets')
    op.drop_table('policy_rule_sets')
    op.drop_index(op.f('ix_ingestions_service_name'), table_name='ingestions')
    op.drop_index(op.f('ix_tag_categories_name'), table_name='tag_categories')
    op.drop_table('tag_categories')

    op.drop_table('recent_classifications')
    op.drop_table('priority_remediation_tasks')
    op.drop_index(op.f('ix_policy_audit_log_user_email'), table_name='policy_audit_log')
    op.drop_index(op.f('ix_policy_audit_log_timestamp'), table_name='policy_audit_log')
    op.drop_index(op.f('ix_policy_audit_log_policy_id'), table_name='policy_audit_log')
    op.drop_index(op.f('ix_policy_audit_log_event_type'), table_name='policy_audit_log')
    op.drop_index(op.f('ix_policy_audit_log_entity_type'), table_name='policy_audit_log')
    op.drop_index(op.f('ix_policy_audit_log_entity_id'), table_name='policy_audit_log')
    op.drop_table('policy_audit_log')
    op.drop_index(op.f('ix_policies_name'), table_name='policies')
    op.drop_index('idx_policy_type_severity', table_name='policies')
    op.drop_index('idx_policy_environment', table_name='policies')
    op.drop_index('idx_policy_active', table_name='policies')
    op.drop_table('policies')

    op.drop_table('identity_mappings')

    op.drop_table('dspm_reporting')
    op.drop_table('dspm_remediation')
    op.drop_index(op.f('ix_dspm_department_name'), table_name='dspm_department')
    op.drop_table('dspm_department')
    op.drop_table('dspm_data_sharing_policy')
    op.drop_table('dspm_data_activity_monitoring')
    op.drop_table('dspm_data_access_policy')
    op.drop_table('dspm_dashboard')
    op.drop_table('dspm_configuration_changes')
    op.drop_table('dspm_compliance')
    op.drop_index(op.f('ix_dspm_classification_label'), table_name='dspm_classification')
    op.drop_table('dspm_classification')
    op.drop_table('dspm_asset_discovery')
    op.drop_table('dspm_alert_configuration')
    op.drop_table('dspm_access_pattern_anomalies')
    op.drop_table('dspm_access_control')
    op.drop_table('dashboard_asset_detail')
    op.drop_table('cloud_connections')
    op.drop_table('classification_results_summary')
    op.drop_index('ix_classificationresult_status_lower', table_name='classification_result')
    op.drop_index('ix_classificationresult_risk_level_lower', table_name='classification_result')
    op.drop_index('ix_classificationresult_data_type_lower', table_name='classification_result')
    op.drop_index('ix_classificationresult_asset_type_lower', table_name='classification_result')
    op.drop_index('ix_classificationresult_asset_name_type', table_name='classification_result')
    op.drop_index('ix_classificationresult_asset_name', table_name='classification_result')
    op.drop_index(op.f('ix_classification_result_asset_type'), table_name='classification_result')
    op.drop_index(op.f('ix_classification_result_asset_name'), table_name='classification_result')
    op.drop_table('classification_result')
    op.drop_index('ix_classification_label_value', table_name='classification_distribution')
    op.drop_index(op.f('ix_classification_distribution_label'), table_name='classification_distribution')
    op.drop_table('classification_distribution')
    op.drop_index('ix_assetsdetails_asset_name_category', table_name='assets_details')
    op.drop_index(op.f('ix_assets_details_category'), table_name='assets_details')
    op.drop_index(op.f('ix_assets_details_asset_name'), table_name='assets_details')
    op.drop_table('access_user_permission')
    op.drop_table('access_orphan_assets')
    op.drop_table('access_data_owners_stewards')

    # ### end Alembic commands ###

