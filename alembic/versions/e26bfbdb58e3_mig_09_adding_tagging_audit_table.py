"""adding_tagging_audit_table

Revision ID: e26bfbdb58e3
Revises: 1e6d7d73e103
Create Date: 2025-10-30 15:04:03.056065

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'e26bfbdb58e3'
down_revision: Union[str, None] = '1e6d7d73e103'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('tagging_audit',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('action_on', sa.String(length=255), nullable=True),
    sa.Column('action', sa.String(length=255), nullable=True),
    sa.Column('entity_type', sa.Enum('CUSTOM', 'DEPARTMENT', 'ASSET', name='entitytypeenum'), nullable=False),
    sa.Column('performed_by', sa.String(length=255), nullable=True),
    sa.Column('tag_name', sa.String(length=255), nullable=True),
    sa.Column('tag_type', sa.Enum('CUSTOM', 'DEPARTMENT', name='tagtypeenum'), nullable=True),
    sa.Column('description', sa.String(length=255), nullable=True),
    sa.Column('timestamp', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tagging_audit_action'), 'tagging_audit', ['action'], unique=False)
    op.create_index(op.f('ix_tagging_audit_action_on'), 'tagging_audit', ['action_on'], unique=False)
    op.create_index(op.f('ix_tagging_audit_performed_by'), 'tagging_audit', ['performed_by'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_tagging_audit_performed_by'), table_name='tagging_audit')
    op.drop_index(op.f('ix_tagging_audit_action_on'), table_name='tagging_audit')
    op.drop_index(op.f('ix_tagging_audit_action'), table_name='tagging_audit')
    op.drop_table('tagging_audit')
    # ### end Alembic commands ###
