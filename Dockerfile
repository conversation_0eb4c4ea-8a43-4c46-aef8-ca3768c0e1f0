FROM python:3.12-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    wget \
    unzip \
    libaio1t64 \
    libaio-dev \
    build-essential \
    libssl-dev \
    libffi-dev \
    iputils-ping \
    telnet \
    && rm -rf /var/lib/apt/lists/*

# Create the critical symlink for libaio compatibility
RUN ln -sf /lib/x86_64-linux-gnu/libaio.so.1t64 /lib/x86_64-linux-gnu/libaio.so.1

WORKDIR /opt/oracle

# Set environment variables
ENV ORACLE_BASE=/opt/oracle
ENV ORACLE_HOME=$ORACLE_BASE

# Download Oracle Instant Client Basic
RUN wget https://download.oracle.com/otn_software/linux/instantclient/1921000/instantclient-basic-linux.x64-*********.0dbru.zip \
    && unzip instantclient-basic-linux.x64-*********.0dbru.zip \
    && rm *.zip \
    && mv instantclient_19_21 lib

# Set library paths
ENV LD_LIBRARY_PATH=$ORACLE_HOME/lib:/lib/x86_64-linux-gnu:/usr/lib/x86_64-linux-gnu
ENV PATH=$ORACLE_HOME/lib:$PATH
ENV TNS_ADMIN=$ORACLE_HOME/lib/network/admin

# Create necessary directories and symlinks
RUN mkdir -p $TNS_ADMIN \
    && ln -sf $ORACLE_HOME/lib/libclntsh.so.19.1 $ORACLE_HOME/lib/libclntsh.so \
    && ln -sf $ORACLE_HOME/lib/libocci.so.19.1 $ORACLE_HOME/lib/libocci.so \
    && echo "$ORACLE_HOME/lib" > /etc/ld.so.conf.d/oracle.conf \
    && ldconfig

# Create default tnsnames.ora (will be overridden by application if needed)
RUN echo "# TNS configuration will be generated by application" > $TNS_ADMIN/tnsnames.ora

WORKDIR /app

# Copy and install Python dependencies
COPY src/requirements.txt ./src/requirements.txt
RUN pip install --no-cache-dir --upgrade pip \
    && pip install --no-cache-dir -r src/requirements.txt

# Copy application code
COPY src/ ./src/
COPY alembic alembic
COPY alembic.ini alembic.ini

# Set Python environment
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/app

RUN chmod +x src/launch.sh

EXPOSE 8000

# Improved healthcheck
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
    CMD python -c "import oracledb; print('Oracle client:', oracledb.version)" || exit 1

CMD ["sh", "-c", "./src/launch.sh"]