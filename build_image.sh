#!/bin/bash

set -e  # Exit immediately if a command exits with a non-zero status


# Prompt user for input
read -p "Enter AWS Account ID: " AWS_ACCOUNT_ID
read -p "Enter AWS Region (e.g., us-east-1): " AWS_REGION
read -p "Enter AWS Access Key ID: " ACCESS_KEY
read -p "Enter AWS Secret Access Key: " SECRET_KEY
read -p "Enter Image Tag: " ECR_IMAGE_VERSION

echo ""

# Repo Specific changes.
ECR_REPO_NAME="ingestion-1"
IMAGE_NAME="unstruct-ingestion:latest"
ECR_IMAGE_LATEST="latest"


ECR_BASE=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com

export AWS_ACCESS_KEY_ID=$ACCESS_KEY
export AWS_SECRET_ACCESS_KEY=$SECRET_KEY


# Build the Docker image
echo "🐳 Building Docker image..."
docker build --no-cache -t "$IMAGE_NAME" .


# Tag with version and latest
echo "🏷️ Tagging Docker image..."
docker tag $IMAGE_NAME $ECR_BASE/$ECR_REPO_NAME:$ECR_IMAGE_VERSION
docker tag $IMAGE_NAME $ECR_BASE/$ECR_REPO_NAME:$ECR_IMAGE_LATEST

# Authenticate with ECR
echo "🔐 Authenticating with AWS ECR..."
aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_BASE


# Push both tags
echo "🚀 Pushing Docker image with tag: latest"
docker push $ECR_BASE/$ECR_REPO_NAME:$ECR_IMAGE_LATEST

echo "🚀 Pushing Docker image with tag: $ECR_IMAGE_VERSION"
docker push $ECR_BASE/$ECR_REPO_NAME:$ECR_IMAGE_VERSION

# Output
echo "✅ Successfully pushed:"
echo "   - $ECR_IMAGE_VERSION"
echo "   - $ECR_IMAGE_LATEST"
